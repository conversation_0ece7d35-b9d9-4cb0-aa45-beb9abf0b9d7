import { Navigate } from '@tanstack/react-location';
import { useAuth } from '../hooks/auth-provider';

const RootRedirect = () => {
    const { user, doctor } = useAuth();
    
    if (!user) {
        return <Navigate to="/login" />;
    }

    // If user is a doctor, redirect to online-patients
    if (doctor?.role === 'doctor') {
        return <Navigate to="/online-patients" />;
    }

    // For admins and other cases, redirect to /doc
    return <Navigate to="/home" />;
};

export default RootRedirect; 