name: Deploy Dr App

on:
  pull_request:
    branches:
      - main
    types:
      - closed

jobs:
  build:
    if: github.event.pull_request.merged == true && github.event.pull_request.base.ref == 'main'
    name: 🎉 Deployment Pipeline
    runs-on: ubuntu-latest

    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v2

      - name: 🌐 Install Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'

      - name: 📦 Install pnpm
        run: npm install -g pnpm

      - name: 📂 Ensure remote directory exists and set permissions
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_INSTANCE_IP }}
          username: ubuntu
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            sudo mkdir -p /var/www/html/doc-app-mono/
            sudo chown -R ubuntu:ubuntu /var/www/html/doc-app-mono/
            sudo chmod -R 775 /var/www/html/doc-app-mono/

      - name:  📂 Run deployment using Rsync
        uses: burnett01/rsync-deployments@5.1
        with:
          switches: -avzr --delete --exclude 'apps/api/.env' --exclude 'apps/web/.env'
          path: ./*
          remote_path: /var/www/html/doc-app-mono/
          remote_host: ${{ secrets.EC2_INSTANCE_IP }}
          remote_user: ubuntu
          remote_key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: 📂 Set ownership and permissions on the remote server
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > private_key.pem
          chmod 600 private_key.pem

          # Create known_hosts file if it doesn't exist
          mkdir -p /home/<USER>/.ssh
          touch /home/<USER>/.ssh/known_hosts
            
          # Clear old key from known_hosts
          ssh-keygen -R "${{ secrets.EC2_INSTANCE_IP }}" >> /home/<USER>/.ssh/known_hosts

          # Run additional SSH commands if needed
          ssh -i private_key.pem -o StrictHostKeyChecking=no ubuntu@"${{ secrets.EC2_INSTANCE_IP }}" \
          "sudo chown -R ubuntu:ubuntu /var/www/html/doc-app-mono/ && sudo chmod +x /var/www/html/doc-app-mono/deploy.sh"

      - name: 🚀 Deploy to the Remote Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.EC2_INSTANCE_IP }}
          username: ubuntu
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /var/www/html/doc-app-mono/
            pnpm install  # Install dependencies using pnpm
            ./deploy.sh