# Sales Reporting S3 Migration Guide

This guide covers the migration from local file storage to Amazon S3 for sales reporting system.

## Overview

The sales reporting system has been upgraded to use Amazon S3 for file storage instead of local filesystem. This provides better scalability, reliability, and performance.

### What Changed

**Before (Local Storage):**
- Files stored in `apps/api/src/services/assets/` directory
- Served via Express static middleware at `/api/assets/`
- No database tracking of files
- Limited by server disk space

**After (S3 Storage):**
- Files uploaded to Amazon S3 bucket
- Database tracking with metadata
- Organized folder structure by date and report type
- Scalable cloud storage with 99.999999999% durability

## Migration Steps

### 1. AWS S3 Setup

#### Create S3 Bucket
```bash
# Using AWS CLI
aws s3 mb s3://zenith-sales-reports --region ap-southeast-2

# Or create via AWS Console
# - Go to S3 service in AWS Console
# - Click "Create bucket"
# - Name: zenith-sales-reports
# - Region: Asia Pacific (Sydney) ap-southeast-2
# - Keep default settings for now
```

#### Configure Bucket Permissions
```bash
# Set bucket policy for application access
aws s3api put-bucket-policy --bucket zenith-sales-reports --policy file://bucket-policy.json
```

**bucket-policy.json:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "AllowApplicationAccess",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::YOUR-ACCOUNT-ID:user/zenith-sales-app"
      },
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:GetObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::zenith-sales-reports/*"
    }
  ]
}
```

#### Create IAM User
```bash
# Create IAM user for application
aws iam create-user --user-name zenith-sales-app

# Attach S3 policy
aws iam attach-user-policy --user-name zenith-sales-app --policy-arn arn:aws:iam::aws:policy/AmazonS3FullAccess

# Create access keys
aws iam create-access-key --user-name zenith-sales-app
```

### 2. Environment Configuration

Add the following environment variables to your `.env` file:

```bash
# AWS S3 Configuration
AWS_REGION=ap-southeast-2
AWS_ACCESS_KEY_ID=your_access_key_from_step_1
AWS_SECRET_ACCESS_KEY=your_secret_key_from_step_1
AWS_BUCKET_NAME=zenith-sales-reports
```

### 3. Database Migration

Run the database migration to create the sales report files tracking table:

```bash
# Connect to your database
psql -h your-db-host -U your-db-user -d your-db-name

# Run the migration
\i migrations/create_sales_report_files_table.sql

# Verify table creation
\d sales_report_files
```

### 4. Application Deployment

1. **Install Dependencies** (already done if following the implementation):
   ```bash
   cd apps/api
   pnpm add @aws-sdk/client-s3 @aws-sdk/s3-request-presigner
   ```

2. **Deploy Updated Code:**
   - The SalesReportingService has been updated to use S3
   - New S3SalesReportsService handles all S3 operations
   - Database tracking is automatically handled

3. **Restart Application:**
   ```bash
   # Restart your application to load new environment variables
   pm2 restart your-app-name
   # or
   systemctl restart your-service-name
   ```

### 5. Verification

#### Test S3 Integration
```bash
# Test report generation
curl -X POST http://localhost:5000/api/sales/v1/test-report

# Check S3 bucket for uploaded files
aws s3 ls s3://zenith-sales-reports/sales-reports/ --recursive

# Verify database records
psql -c "SELECT report_date, report_type, file_url, status FROM sales_report_files ORDER BY created_at DESC LIMIT 5;"
```

#### Monitor Logs
```bash
# Check application logs for S3 operations
tail -f /path/to/your/app/logs/app.log | grep -i s3

# Look for successful upload messages
# "Sales report uploaded to S3 successfully"
```

## Migration Benefits

### Scalability
- No disk space limitations on server
- Automatic scaling with usage
- Global content delivery via CloudFront (optional)

### Reliability
- 99.999999999% (11 9's) durability
- Automatic redundancy across multiple facilities
- Built-in versioning and backup capabilities

### Performance
- Faster file delivery through AWS infrastructure
- Reduced server load (no file serving)
- Parallel uploads and downloads

### Cost Efficiency
- Pay only for storage used
- Lifecycle policies for automatic archival
- No need for server storage upgrades

### Security
- Encryption at rest and in transit
- Fine-grained access control with IAM
- Audit logging with CloudTrail

## Rollback Plan

If issues occur, you can temporarily rollback to local storage:

1. **Revert Code Changes:**
   ```bash
   git revert <commit-hash-of-s3-changes>
   ```

2. **Remove Environment Variables:**
   ```bash
   # Comment out or remove AWS variables from .env
   # AWS_REGION=ap-southeast-2
   # AWS_ACCESS_KEY_ID=...
   # AWS_SECRET_ACCESS_KEY=...
   # AWS_BUCKET_NAME=...
   ```

3. **Restart Application:**
   ```bash
   pm2 restart your-app-name
   ```

**Note:** New reports will use local storage, but S3-stored reports will remain accessible via their URLs.

## Monitoring and Maintenance

### CloudWatch Metrics
Monitor S3 usage through AWS CloudWatch:
- Number of requests
- Data transfer
- Storage usage
- Error rates

### Cost Monitoring
Set up billing alerts for S3 usage:
```bash
aws budgets create-budget --account-id YOUR-ACCOUNT-ID --budget file://s3-budget.json
```

### Lifecycle Policies
Consider setting up lifecycle policies for cost optimization:
```json
{
  "Rules": [
    {
      "ID": "ArchiveOldReports",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "sales-reports/"
      },
      "Transitions": [
        {
          "Days": 90,
          "StorageClass": "STANDARD_IA"
        },
        {
          "Days": 365,
          "StorageClass": "GLACIER"
        }
      ]
    }
  ]
}
```

## Troubleshooting

### Common Issues

**403 Forbidden Errors:**
- Check IAM permissions
- Verify bucket policy
- Ensure access keys are correct

**File Upload Failures:**
- Check network connectivity to AWS
- Verify bucket exists and is accessible
- Check application logs for detailed error messages

**Database Connection Issues:**
- Ensure migration was run successfully
- Check database connection settings
- Verify table exists: `\d sales_report_files`

### Support

For additional support:
1. Check AWS S3 documentation
2. Review application logs
3. Test with AWS CLI to isolate issues
4. Contact AWS support if needed

---

**Migration Date:** 2025-01-24  
**Version:** 1.0  
**Status:** Ready for Production
