import { WebClient } from '@slack/web-api';
import { StreamChat } from 'stream-chat';
import config from '../config';
import { db } from '../utils/db';
import { DateTime } from 'luxon';

// Attachment data interface for moderation
interface AttachmentData {
  type?: string;
  title?: string;
  title_link?: string;
  image_url?: string;
  asset_url?: string;
  file_size?: number;
  mime_type?: string;
}

export interface ModerationMessage {
  id: string;
  messageId: string;
  channelId: string;
  patientId: string;
  messageText: string;
  patientName?: string;
  createdAt: string;
  hasAttachments?: boolean;
  attachmentCount?: number;
  attachmentTypes?: string[];
  attachmentData?: AttachmentData[] | null;
}

export interface ConversationModeration {
  id: string;
  channelId: string;
  patientId: string;
  treatmentPlanId?: string;
  patientName?: string;
  moderationStatus: 'pending' | 'approved' | 'rejected';
  moderatedBy?: string;
  moderatedAt?: string;
  moderationReason?: string;
  isVisible: boolean;
  createdAt: string;
  updatedAt: string;
  slackMessageTs?: string;
  firstMessageAt?: string;
  lastMessageAt?: string;
  messageCount: number;
  hasAttachments: boolean;
  firstMessageText?: string;
  lastMessageText?: string;
  messagePreview?: string;
  messageIds?: string[];
  treatmentOutcome?: string;
  doctorName?: string;
}

class ModerationService {
  private slack: WebClient;
  private streamChat: StreamChat;

  constructor() {
    this.slack = new WebClient(config.slackToken);
    this.streamChat = StreamChat.getInstance(config.streamChat.apiKey, config.streamChat.apiSecret);
  }

  /**
   * Store or update a conversation for moderation
   */
  async storeConversationForModeration(conversationData: {
    channelId: string;
    patientId: string;
    treatmentPlanId?: string;
    patientName?: string;
    messageText?: string;
    hasAttachments?: boolean;
  }): Promise<{ id: string; slackMessageTs?: string; isNewConversation: boolean }> {
    const client = await db.connect();
    try {
      // Check if conversation already exists
      const existingQuery = `
        SELECT id, "slackMessageTs", "messageCount", "moderationStatus"
        FROM chat_conversation_moderation
        WHERE "channelId" = $1
      `;
      const existingResult = await client.query(existingQuery, [conversationData.channelId]);

      if (existingResult.rows.length > 0) {
        const existingConversation = existingResult.rows[0];
        const moderationStatus = existingConversation.moderationStatus;

        // Only update message tracking for pending conversations
        // Once approved or rejected, preserve the original message snapshot
        if (moderationStatus === 'pending') {
          // For pending conversations, update message tracking to include new messages
          // Fetch current messages from Stream Chat to get updated count and message IDs
          const messagesResult = await this.getChannelMessages(conversationData.channelId, 100);
          const messageIds = messagesResult.messages.map(msg => msg.id);

          // Update message preview to show current message count and latest content
          const messagePreview = messagesResult.messages.length > 0
            ? `Message ${messagesResult.messages.length}: ${(conversationData.messageText || messagesResult.messages[messagesResult.messages.length - 1]?.text || '').substring(0, 100)}${(conversationData.messageText || messagesResult.messages[messagesResult.messages.length - 1]?.text || '').length > 100 ? '...' : ''}`
            : 'No text content';

          const updateQuery = `
            UPDATE chat_conversation_moderation
            SET "lastMessageAt" = CURRENT_TIMESTAMP,
                "hasAttachments" = "hasAttachments" OR $2,
                "lastMessageText" = $3,
                "messageCount" = $4,
                "messageIds" = $5,
                "messagePreview" = $6,
                "updatedAt" = CURRENT_TIMESTAMP
            WHERE "channelId" = $1
          `;

          await client.query(updateQuery, [
            conversationData.channelId,
            conversationData.hasAttachments || false,
            conversationData.messageText || null,
            messageIds.length,
            messageIds,
            messagePreview
          ]);
        } else {
          // For approved/rejected conversations, only update basic fields without changing message tracking
          const updateQuery = `
            UPDATE chat_conversation_moderation
            SET "lastMessageAt" = CURRENT_TIMESTAMP,
                "hasAttachments" = "hasAttachments" OR $2,
                "lastMessageText" = $3,
                "updatedAt" = CURRENT_TIMESTAMP
            WHERE "channelId" = $1
          `;

          await client.query(updateQuery, [
            conversationData.channelId,
            conversationData.hasAttachments || false,
            conversationData.messageText || null
          ]);
        }

        return {
          id: existingResult.rows[0].id,
          slackMessageTs: existingResult.rows[0].slackMessageTs,
          isNewConversation: false
        };
      } else {
        // Get treatment plan ID using the helper function
        const treatmentPlanResult = await client.query(
          'SELECT link_conversation_to_treatment_plan($1, $2) as treatment_plan_id',
          [conversationData.channelId, conversationData.patientId]
        );
        const treatmentPlanId = treatmentPlanResult.rows[0].treatment_plan_id;

        // Fetch current messages from Stream Chat to store message IDs and set initial messageCount
        // This creates a snapshot of the conversation at the time of moderation submission
        const messagesResult = await this.getChannelMessages(conversationData.channelId, 100);
        const messageIds = messagesResult.messages.map(msg => msg.id);

        // Create new conversation with messageCount matching the number of messages at submission time
        const messagePreview = conversationData.messageText
          ? `Message 1: ${conversationData.messageText.substring(0, 100)}${conversationData.messageText.length > 100 ? '...' : ''}`
          : 'No text content';

        const insertQuery = `
          INSERT INTO chat_conversation_moderation (
            "channelId", "patientId", "treatmentPlanId", "patientName",
            "firstMessageAt", "lastMessageAt", "messageCount", "hasAttachments",
            "firstMessageText", "lastMessageText", "messagePreview", "messageIds"
          ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $5, $6, $7, $7, $8, $9)
          RETURNING id
        `;

        const values = [
          conversationData.channelId,
          conversationData.patientId,
          treatmentPlanId,
          conversationData.patientName || null,
          messageIds.length,
          conversationData.hasAttachments || false,
          conversationData.messageText || null,
          messagePreview,
          messageIds
        ];

        const result = await client.query(insertQuery, values);
        return {
          id: result.rows[0].id,
          isNewConversation: true
        };
      }
    } finally {
      client.release();
    }
  }

  /**
   * Store a message for moderation (legacy method - kept for backward compatibility)
   */
  async storeMessageForModeration(messageData: {
    messageId: string;
    channelId: string;
    patientId: string;
    messageText: string;
    patientName?: string;
    hasAttachments?: boolean;
    attachmentCount?: number;
    attachmentTypes?: string[];
    attachmentData?: AttachmentData[] | null;
  }): Promise<{ id: string; slackMessageTs?: string }> {
    // For the new system, we'll store the conversation instead
    const conversationResult = await this.storeConversationForModeration({
      channelId: messageData.channelId,
      patientId: messageData.patientId,
      patientName: messageData.patientName,
      messageText: messageData.messageText,
      hasAttachments: messageData.hasAttachments
    });

    return {
      id: conversationResult.id,
      slackMessageTs: conversationResult.slackMessageTs
    };
  }

  /**
   * Get pending conversations for moderation
   */
  async getPendingConversations(limit: number = 50, page: number = 1): Promise<{
    conversations: ConversationModeration[];
    pagination: { total: number; page: number; limit: number; totalPages: number; hasNext: boolean; hasPrev: boolean };
  }> {
    const client = await db.connect();
    try {
      const offset = (page - 1) * limit;

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM conversation_moderation_summary
        WHERE "moderationStatus" = 'pending'
      `;
      const countResult = await client.query(countQuery);
      const total = parseInt(countResult.rows[0].total);

      // Get conversations
      const query = `
        SELECT * FROM conversation_moderation_summary
        WHERE "moderationStatus" = 'pending'
        ORDER BY "createdAt" DESC
        LIMIT $1 OFFSET $2
      `;

      const result = await client.query(query, [limit, offset]);

      const totalPages = Math.ceil(total / limit);

      return {
        conversations: result.rows,
        pagination: {
          total,
          page,
          limit,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };
    } finally {
      client.release();
    }
  }

  /**
   * Get pending messages for moderation (legacy method)
   */
  async getPendingMessages(limit: number = 50): Promise<ModerationMessage[]> {
    // For backward compatibility, return empty array or redirect to conversations
    // Note: limit parameter is kept for API compatibility but not used
    console.log(`getPendingMessages called with limit ${limit} - returning empty array (deprecated)`);
    return [];
  }

  /**
   * Fetch messages from Stream Chat for a specific channel
   * @param channelId - The channel ID to fetch messages from
   * @param limit - Maximum number of messages to fetch (default: 50)
   * @param filterMessageIds - Optional array of message IDs to filter by (only return these specific messages)
   */
  async getChannelMessages(channelId: string, limit: number = 50, filterMessageIds?: string[]): Promise<{
    messages: Array<{
      id: string;
      text: string;
      user: {
        id: string;
        name?: string;
      };
      created_at: string;
      attachments?: Array<{
        id?: string;
        type?: string;
        title?: string;
        asset_url?: string;
        image_url?: string;
        file_size?: number;
        mime_type?: string;
        thumb_url?: string;
        title_link?: string;
        og_scrape_url?: string;
        fallback?: string;
        text?: string;
      }>;
    }>;
    error?: string;
  }> {
    try {
      // Get the channel from Stream Chat
      const channel = this.streamChat.channel('messaging', channelId);

      // Query messages from the channel
      // If we have specific message IDs to filter by, we need to fetch more messages
      // to ensure we get all the original messages (they might be pushed down by newer ones)
      const queryLimit = filterMessageIds && filterMessageIds.length > 0
        ? Math.max(limit, filterMessageIds.length + 50) // Fetch extra to account for newer messages
        : limit;

      const response = await channel.query({
        messages: { limit: queryLimit }
      });

      if (!response.messages) {
        return { messages: [], error: 'No messages found' };
      }

      // Transform Stream Chat messages to our format
      let messages = response.messages.map(msg => {
        return {
          id: msg.id,
          text: msg.text || '',
          user: {
            id: msg.user?.id || 'unknown',
            name: msg.user?.name || msg.user?.id || 'Unknown User'
          },
          created_at: msg.created_at || new Date().toISOString(),
          attachments: msg.attachments?.map(att => {
            // Stream Chat might store URLs in different fields depending on upload method
            // Define a type for the extended attachment properties
            interface ExtendedAttachment {
              name?: string;
              filename?: string;
              url?: string;
              file_url?: string;
              size?: number;
              content_type?: string;
            }

            const extAtt = att as ExtendedAttachment;

            const processedAttachment = {
              id: typeof att.id === 'string' ? att.id : undefined,
              type: typeof att.type === 'string' ? att.type : undefined,
              title: typeof att.title === 'string' ? att.title : (typeof extAtt.name === 'string' ? extAtt.name : (typeof extAtt.filename === 'string' ? extAtt.filename : undefined)),
              // Try multiple possible URL fields
              asset_url: typeof att.asset_url === 'string' ? att.asset_url : (typeof extAtt.url === 'string' ? extAtt.url : (typeof extAtt.file_url === 'string' ? extAtt.file_url : undefined)),
              image_url: typeof att.image_url === 'string' ? att.image_url : (typeof att.thumb_url === 'string' ? att.thumb_url : undefined),
              file_size: typeof att.file_size === 'number' ? att.file_size : (typeof extAtt.size === 'number' ? extAtt.size : undefined),
              mime_type: typeof att.mime_type === 'string' ? att.mime_type : (typeof extAtt.content_type === 'string' ? extAtt.content_type : undefined),
              // Additional fields that might contain URLs
              thumb_url: typeof att.thumb_url === 'string' ? att.thumb_url : undefined,
              title_link: typeof att.title_link === 'string' ? att.title_link : undefined,
              og_scrape_url: typeof att.og_scrape_url === 'string' ? att.og_scrape_url : undefined,
              fallback: typeof att.fallback === 'string' ? att.fallback : undefined,
              text: typeof att.text === 'string' ? att.text : undefined
            };

            return processedAttachment;
          })
        };
      });

      // Filter messages by specific IDs if provided
      if (filterMessageIds && filterMessageIds.length > 0) {
        //const originalCount = messages.length;
        messages = messages.filter(msg => filterMessageIds.includes(msg.id));


       
      }

      return { messages };
    } catch (error) {
      console.error('Error fetching messages from Stream Chat:', error);
      return {
        messages: [],
        error: error instanceof Error ? error.message : 'Failed to fetch messages'
      };
    }
  }

  /**
   * Moderate a conversation (approve or reject)
   */
  async moderateConversation(
    channelId: string,
    status: 'approved' | 'rejected',
    moderatedBy: string,
    reason?: string
  ): Promise<{ success: boolean; message: string; previousStatus?: string }> {
    const client = await db.connect();
    try {
      // First, check the current status of the conversation
      const checkQuery = `
        SELECT "moderationStatus", "moderatedBy" FROM chat_conversation_moderation
        WHERE "channelId" = $1
      `;

      const checkResult = await client.query(checkQuery, [channelId]);

      if (checkResult.rows.length === 0) {
        return { success: false, message: 'Conversation not found' };
      }

      const currentStatus = checkResult.rows[0].moderationStatus;

      // Check if trying to set the same status
      if (currentStatus === status) {
        return {
          success: false,
          message: `Conversation is already ${status}`,
          previousStatus: currentStatus
        };
      }

      // Allow the moderation if:
      // 1. Current status is 'pending' (first time moderation)
      // 2. Current status is different from new status (toggling between approved/rejected)
      if (currentStatus !== 'pending' && currentStatus !== status) {
        // This is a toggle operation - log it
        console.log(`Toggling conversation ${channelId} from ${currentStatus} to ${status} by ${moderatedBy}`);
      }

      const updateQuery = `
        UPDATE chat_conversation_moderation
        SET "moderationStatus" = $1, "moderatedBy" = $2, "moderatedAt" = CURRENT_TIMESTAMP,
            "moderationReason" = $3, "isVisible" = $4, "updatedAt" = CURRENT_TIMESTAMP
        WHERE "channelId" = $5
      `;

      const isVisible = status === 'approved';
      await client.query(updateQuery, [status, moderatedBy, reason, isVisible, channelId]);

      return {
        success: true,
        message: currentStatus === 'pending'
          ? `Conversation ${status} successfully`
          : `Conversation status changed from ${currentStatus} to ${status}`,
        previousStatus: currentStatus
      };
    } finally {
      client.release();
    }
  }

  /**
   * Moderate a message (legacy method - redirects to conversation moderation)
   */
  async moderateMessage(
    messageId: string,
    status: 'approved' | 'rejected',
    moderatedBy: string,
    reason?: string
  ): Promise<{ success: boolean; message: string; previousStatus?: string }> {
    // For the new system, we need to find the channel ID from the message ID
    // and moderate the entire conversation
    const client = await db.connect();
    try {
      // Log the legacy call for debugging
      console.log(`Legacy moderateMessage called: messageId=${messageId}, status=${status}, moderatedBy=${moderatedBy}, reason=${reason || 'none'}`);

      // This would need to be implemented based on how we store the relationship
      // For now, return a message indicating the system has changed
      return {
        success: false,
        message: 'Message-level moderation has been replaced with conversation-level moderation'
      };
    } finally {
      client.release();
    }
  }

  /**
   * Send Slack notification when a conversation has been moderated (as a thread reply)
   */
  async sendConversationModerationResultNotification(
    channelId: string,
    action: 'approved' | 'rejected',
    moderatedBy: string,
    reason?: string
  ): Promise<void> {
    const slackChannel = process.env.SLACK_MODERATION_CHANNEL;

    if (!slackChannel) {
   
      return;
    }

    // Get the original Slack message timestamp and moderator name
    const client = await db.connect();
    let slackMessageTs: string | null = null;
    let moderatorName = moderatedBy;
    let conversationInfo = '';

    try {
      const query = `
        SELECT "slackMessageTs", "patientName", "messageCount", "treatmentPlanId"
        FROM chat_conversation_moderation
        WHERE "channelId" = $1
      `;
      const result = await client.query(query, [channelId]);

      if (result.rows.length > 0) {
        const conversation = result.rows[0];
        slackMessageTs = conversation.slackMessageTs;
        conversationInfo = `\n📊 Patient: ${conversation.patientName || 'Unknown'} | Messages: ${conversation.messageCount}`;
      }

      // Get moderator name if it's not 'system-moderator'
      if (moderatedBy !== 'system-moderator') {
        const doctorQuery = `
          SELECT "name", "username" FROM Dr
          WHERE "accessID" = $1
        `;
        const doctorResult = await client.query(doctorQuery, [moderatedBy]);
        if (doctorResult.rows.length > 0) {
          const doctor = doctorResult.rows[0];
          moderatorName = doctor.name || doctor.username || moderatedBy;
        }
      }
    } finally {
      client.release();
    }

    if (!slackMessageTs) {
     
      return;
    }

    const emoji = action === 'approved' ? '✅' : '❌';
    const actionText = action === 'approved' ? 'CONVERSATION APPROVED' : 'CONVERSATION REJECTED';
    const reasonText = reason ? `\nReason: ${reason}` : '';

    // Format timestamp consistently with the application (12-hour format with AM/PM)
    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');

    const messageText = `${emoji} **${actionText}** by ${moderatorName}${conversationInfo}${reasonText}\nTime: ${timestamp}`;

    try {
      await this.slack.chat.postMessage({
        channel: slackChannel,
        text: messageText,
        thread_ts: slackMessageTs, // This makes it a threaded reply
        username: 'Chat Moderation Bot',
        icon_emoji: action === 'approved' ? ':white_check_mark:' : ':x:'
      });
    } catch (error) {
      console.error('Failed to send Slack conversation moderation result notification:', error);
    }
  }

  /**
   * Send Slack notification when a message has been moderated (legacy method)
   */
  async sendModerationResultNotification(
    messageId: string,
    action: 'approved' | 'rejected',
    moderatedBy: string,
    reason?: string
  ): Promise<void> {
    // Legacy method - no longer used in conversation-based system
    console.log(`Legacy sendModerationResultNotification called: messageId=${messageId}, action=${action}, moderatedBy=${moderatedBy}, reason=${reason || 'none'} - use sendConversationModerationResultNotification instead`);
  }

  /**
   * Send Slack notification for new conversation requiring moderation
   */
  async sendConversationModerationNotification(conversation: ConversationModeration, firstMessage?: string): Promise<string | null> {
    // Get Slack channel from environment or use default
    const slackChannel = process.env.SLACK_MODERATION_CHANNEL;

    if (!slackChannel) {
      console.log('No Slack moderation channel configured');
      return null;
    }

    // Build attachment info if present
    let attachmentInfo = '';
    if (conversation.hasAttachments) {
      attachmentInfo = `\n📎 *Has Attachments:* Yes`;
    }

    // Build message content - show preview of conversation
    const messageContent = conversation.messagePreview ||
      (firstMessage && firstMessage.trim()
        ? `"${firstMessage.length > 200 ? firstMessage.substring(0, 200) + '...' : firstMessage}"`
        : conversation.hasAttachments
          ? '[Attachment only - no text]'
          : '[No content available]');

    const treatmentInfo = conversation.treatmentOutcome
      ? `\n🏥 *Treatment:* ${conversation.treatmentOutcome}`
      : '';

    const doctorInfo = conversation.doctorName
      ? `\n👨‍⚕️ *Doctor:* ${conversation.doctorName}`
      : '';

    // Format the start time with proper error handling
    let startTimeFormatted = 'Unknown';
    try {
      const startTime = conversation.firstMessageAt || conversation.createdAt;
      if (startTime) {
        const parsedTime = DateTime.fromISO(startTime);
        if (parsedTime.isValid) {
          startTimeFormatted = parsedTime.setZone('Australia/Sydney').toFormat('h:mm a, dd MMM yyyy');
        } else {
          // Try parsing as a different format or use current time as fallback
          const fallbackTime = DateTime.now().setZone('Australia/Sydney');
          startTimeFormatted = fallbackTime.toFormat('h:mm a, dd MMM yyyy');
          console.warn(`Invalid DateTime for conversation ${conversation.channelId}: ${startTime}, using current time as fallback`);
        }
      } else {
        // No timestamp available, use current time
        const fallbackTime = DateTime.now().setZone('Australia/Sydney');
        startTimeFormatted = fallbackTime.toFormat('h:mm a, dd MMM yyyy');
        console.warn(`No timestamp available for conversation ${conversation.channelId}, using current time as fallback`);
      }
    } catch (error) {
      // If all else fails, use current time
      const fallbackTime = DateTime.now().setZone('Australia/Sydney');
      startTimeFormatted = fallbackTime.toFormat('h:mm a, dd MMM yyyy');
      console.error(`Error formatting DateTime for conversation ${conversation.channelId}:`, error);
    }

    const messageText = `
� *New Conversation Requires Moderation*

👤 *Patient:* ${conversation.patientName || 'Unknown'} (ID: ${conversation.patientId})
📊 *Messages:* ${conversation.messageCount} message(s)
💬 *Preview:* ${messageContent}${attachmentInfo}${treatmentInfo}${doctorInfo}
ℹ️ *Note:* Approving this conversation will make ALL messages visible to doctors
⏰ *Started:* ${startTimeFormatted}

🔗 *Moderate:* ${config.devMode ? 'https://zenith.ad' : 'https://doctor.zenith.clinic'}/admin/moderation?channelId=${conversation.channelId}
    `.trim();

    try {
      const response = await this.slack.chat.postMessage({
        channel: slackChannel,
        text: messageText,
        username: 'Chat Moderation Bot',
        icon_emoji: ':speech_balloon:'
      });

      // Store the Slack message timestamp in the database for threading
      if (response.ts) {
        const client = await db.connect();
        try {
          const updateQuery = `
            UPDATE chat_conversation_moderation
            SET "slackMessageTs" = $1
            WHERE "channelId" = $2
          `;
          await client.query(updateQuery, [response.ts, conversation.channelId]);
        } finally {
          client.release();
        }
      }

      return response.ts || null;
    } catch (error) {
      console.error('Failed to send Slack conversation moderation notification:', error);
      return null;
    }
  }

  /**
   * Send Slack notification for new message requiring moderation (legacy method)
   */
  async sendModerationNotification(message: ModerationMessage): Promise<string | null> {
    // For the new system, we'll send conversation notifications instead
    // This method is kept for backward compatibility but should not be used
    console.log(`Legacy sendModerationNotification called for message ${message.id} - consider using sendConversationModerationNotification`);
    return null;
  }

  /**
   * Fix messageIds for existing conversations that were corrupted by including post-moderation messages
   * This should be run once to clean up existing data
   */
  async fixConversationMessageIds(channelId: string): Promise<{ success: boolean; message: string }> {
    const client = await db.connect();
    try {
      // Get the conversation details
      const conversationQuery = `
        SELECT "messageCount", "moderatedAt", "moderationStatus"
        FROM chat_conversation_moderation
        WHERE "channelId" = $1
      `;
      const conversationResult = await client.query(conversationQuery, [channelId]);

      if (conversationResult.rows.length === 0) {
        return { success: false, message: 'Conversation not found' };
      }

      const conversation = conversationResult.rows[0];
      const originalMessageCount = conversation.messageCount;

      // Fetch all messages from Stream Chat
      const messagesResult = await this.getChannelMessages(channelId, 200); // Fetch more to ensure we get all
      if (messagesResult.error) {
        return { success: false, message: `Failed to fetch messages: ${messagesResult.error}` };
      }

      // Sort messages by timestamp (oldest first) and take only the original count
      const sortedMessages = messagesResult.messages.sort((a, b) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

      const originalMessages = sortedMessages.slice(0, originalMessageCount);
      const originalMessageIds = originalMessages.map(msg => msg.id);

      // Update the database with the corrected message IDs
      const updateQuery = `
        UPDATE chat_conversation_moderation
        SET "messageIds" = $2
        WHERE "channelId" = $1
      `;

      await client.query(updateQuery, [channelId, originalMessageIds]);

      return {
        success: true,
        message: `Fixed conversation ${channelId}: reset to ${originalMessageIds.length} original messages`
      };
    } finally {
      client.release();
    }
  }
}

export default new ModerationService();
