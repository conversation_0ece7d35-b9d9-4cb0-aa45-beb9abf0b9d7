import React, { createContext, useContext, useState } from "react";
import { ConsultationFilter } from "../types";

type HistoryContextType = {
    selectedMenu: string
    setSelectedMenu: React.Dispatch<React.SetStateAction<string>>
    searchTerm: string | undefined
    setSearchTerm: React.Dispatch<React.SetStateAction<string | undefined>>
    consultations: ConsultationFilter[] | null
    setConsultations: React.Dispatch<React.SetStateAction<ConsultationFilter[] | null>>
    selectedDateCalendar: string
    setSelectedDateCalendar: React.Dispatch<React.SetStateAction<string>>
    selectedConsultation: ConsultationFilter | null
    setSelectedConsultation: React.Dispatch<React.SetStateAction<ConsultationFilter | null>>
    calendarValue: moment.Moment | null
    setCalendarValue: React.Dispatch<React.SetStateAction<moment.Moment | null>>
};

const HistoryContext = createContext<HistoryContextType | undefined>(undefined);

export const HistoryProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

    const [selectedMenu, setSelectedMenu] = useState('inbox')
    const [searchTerm, setSearchTerm] = useState<string | undefined>(undefined)
    const [consultations, setConsultations] = useState<ConsultationFilter[] | null>(null)
    const [selectedConsultation, setSelectedConsultation] = useState<ConsultationFilter | null>(null)
    const [selectedDateCalendar, setSelectedDateCalendar] = useState('')
    const [calendarValue, setCalendarValue] = useState<moment.Moment | null>(null)

    const contextValue: HistoryContextType = {
        selectedMenu,
        setSelectedMenu,
        searchTerm,
        setConsultations,
        selectedConsultation,
        setSelectedConsultation,
        selectedDateCalendar,
        setSelectedDateCalendar,
        setSearchTerm,
        consultations,
        calendarValue,
        setCalendarValue
    };

    return <HistoryContext.Provider value={contextValue}>{children}</HistoryContext.Provider>;
};

export const useHistory = () => {
    const context = useContext(HistoryContext);
    if (context === undefined) {
        throw new Error("useHistory must be used within an HistoryProvider");
    }
    return context;
};
