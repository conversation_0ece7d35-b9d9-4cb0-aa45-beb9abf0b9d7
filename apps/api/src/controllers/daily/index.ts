import httpStatus from 'http-status';
import { catchAll } from '../../utils/catchAll';
import { RequestHandler } from 'express';
import { ApiError } from '../../utils/ApiError';
import axios, { AxiosError } from 'axios';
import { logger } from '../../config/logger';

export const postDaily: RequestHandler = catchAll((req, res) => {
  const body = req.body;
  res.status(200).send(body);
});

const createDailyRoomOnDateUpdate = async (member_id: string) => {
  let roomExist = false;
  const url = 'https://api.daily.co/v1/rooms';
  const headers = {
    Authorization: `Bearer 1c15aebedf7bd2f33c7b0ed3e230da2b5a0891a7ed628b88868c0b7e82aa028d`,
    'Content-Type': 'application/json',
  };

  // Check if room exists and refresh if necessary
  try {
    const roomResponse = await axios.get(`https://api.daily.co/v1/rooms/${member_id}`, { headers });
    if (roomResponse.status === 200 && roomResponse.data.name === member_id) {
      roomExist = true;
      return {
        status: 'Room already exist',
        ...roomResponse.data,
      };
    } else {
      logger.info(`Room name does not match the member_id, skipping delete :: ${member_id}`);
    }
  } catch (e) {
    const error = e as AxiosError;
    if (error.response?.status !== 404) {
      console.error('Error checking if room exists:', error.response?.data || error);
      throw new Error(`Error checking if room exists :: ${member_id}`);
    }
  } finally {
    if (!roomExist) {
      const data = {
        name: member_id,
        privacy: 'public',
        properties: {
          enable_prejoin_ui: false,
          enable_chat: true,
          enable_knocking: false,
          enable_screenshare: true,
          enable_recording: false,
          start_video_off: false,
          start_audio_off: false,
          owner_only_broadcast: false,
          lang: 'en',
        },
      };

      try {
        await axios.post(url, data, { headers });
      } catch (e) {
        const error = e as AxiosError;
        logger.error(`${error.message}`)
      }
    }
  }
};

export const createDailyRoom: RequestHandler = catchAll(async (req, res) => {
  const roomID = req.body.roomID;
  const patientID = req.body.patientID;
  try {
    if (roomID && patientID) {
      await createDailyRoomOnDateUpdate(roomID);
      res.status(200).send({});
    } else {
      res.status(400).json('Something went wrong... Verify Patient ID and Room ID');
    }
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  }
});

export const deleteDailyRoom: RequestHandler = catchAll(async (req, res) => {
  const headers = {
    Authorization: `Bearer 1c15aebedf7bd2f33c7b0ed3e230da2b5a0891a7ed628b88868c0b7e82aa028d`,
    "Content-Type": "application/json",
  };
  const roomID = req.body.roomID
  try {
    const result = await axios.delete(`https://api.daily.co/v1/rooms/${roomID}`, {
      headers,
    });
    res.status(200).send(result.data);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  }
})
