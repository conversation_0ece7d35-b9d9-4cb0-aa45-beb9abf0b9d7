import express from 'express';
import { validateDr } from '../middlewares/validationMiddleware';
import {
  getPendingMessages,
  getPendingConversations,
  getModeratedMessages,
  getModerationStats,
  moderateMessage,
  moderateConversation,
  bulkModerateMessages,
  sendTestNotification,
  getMessageDetails,
  getConversationDetails,
  getConversationMessages,
  getPatientModerationHistory,
  checkMessageModerationStatus,
  checkConversationModerationStatus,
  fixConversationMessageIds
} from '../controllers/moderation';

const router = express.Router();

const currentVersion = 'v1.0';

// All moderation routes require doctor/admin authentication
router.use(validateDr);

// Get pending conversations for moderation (new primary endpoint)
router.get(`/${currentVersion}/pending`, getPendingConversations);

// Get pending messages for moderation (legacy endpoint)
router.get(`/${currentVersion}/pending-messages`, getPendingMessages);

// Get moderated messages (approved/rejected)
router.get(`/${currentVersion}/moderated`, getModeratedMessages);

// Get moderation statistics
router.get(`/${currentVersion}/stats`, getModerationStats);

// Get conversation details by channel ID
router.get(`/${currentVersion}/conversation/:channelId`, getConversationDetails);

// Get messages for a conversation from Stream Chat
router.get(`/${currentVersion}/conversation/:channelId/messages`, getConversationMessages);

// Get message details by ID (legacy)
router.get(`/${currentVersion}/message/:messageId`, getMessageDetails);

// Check conversation moderation status
router.post(`/${currentVersion}/check-conversations`, checkConversationModerationStatus);

// Check moderation status for multiple messages (legacy)
router.post(`/${currentVersion}/check-messages`, checkMessageModerationStatus);

// Get patient moderation history
router.get(`/${currentVersion}/patient/:patientId/history`, getPatientModerationHistory);

// Moderate a single conversation (new primary endpoint)
router.post(`/${currentVersion}/moderate-conversation`, moderateConversation);

// Moderate a single message (legacy endpoint)
router.post(`/${currentVersion}/moderate`, moderateMessage);

// Bulk moderate messages
router.post(`/${currentVersion}/bulk-moderate`, bulkModerateMessages);

// Send test notification
router.post(`/${currentVersion}/test-notification`, sendTestNotification);

// Fix conversation messageIds (remove post-moderation messages)
router.post(`/${currentVersion}/fix-conversation/:channelId`, fixConversationMessageIds);

export default router;
