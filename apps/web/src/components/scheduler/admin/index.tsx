import {
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  useMediaQuery,
  FormControl,
  FormHelperText,
  Divider,
  IconButton,
  Dialog,
  DialogContent,
  Select,
  MenuItem,
  InputLabel,
  SelectChangeEvent,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useState, useEffect, Fragment } from "react";
import Scheduler from "../patient";
import AddIcon from "@mui/icons-material/Add";
import { useTheme } from "@mui/material/styles";
import EditIcon from "@mui/icons-material/Edit";
import { getDayOfWeek } from "../../../utils";
import { v4 as uuidv4 } from "uuid";
import DeleteIcon from "@mui/icons-material/Delete";
import { AvailableDate, Dr } from "../../../types";
import { ApiClient } from "../../../services";
import LoadingScreen from "../../../utils/loading-screen";
import DangerousIcon from "@mui/icons-material/Dangerous";
import CancelIcon from "@mui/icons-material/Cancel";
import { usePatient } from "../../../hooks/patient-provider";
import { AxiosError } from "axios";

const inputStyle = {
  "& .MuiInputBase-root": {
    borderRadius: "5px",
    fontSize: "0.8rem",
    width: "200px",
  },
  "& .MuiOutlinedInput-root": {
    "&:hover": {
      borderColor: "green",
    },
    "&.Mui-focused fieldset": {
      borderColor: "green",
    },
  },
  "& .MuiInputLabel-root": {
    color: "#3B3B3B",
    fontSize: "0.75rem",
  },
  "& .MuiInputLabel-root.Mui-focused": {
    color: "green",
  },
};

const sections = {
  availability: "Availability",
  booking: "booking",
};

const STATUS = {
  active: "active",
  pending: "pending",
  deleted: "deleted",
  editing: "editing",
};

const shortDays = {
  Monday: "Mon",
  Tuesday: "Tue",
  Wednesday: "Wed",
  Thursday: "Thu",
  Friday: "Fri",
};

type RequiredFields =
  | "date"
  | "start"
  | "end"
  | "interval"
  | "availability"
  | "doctorID";

const AdminScheduler = () => {
  const [section, setSection] = useState(sections.availability);
  const [patientID, setPatientID] = useState("");
  const [availabilities, setAvailabilities] = useState<AvailableDate[]>([]);
  const [selectedAvailability, setSelectedAvailability] = useState<
    AvailableDate | undefined
  >(undefined);
  const [localError, setLocalError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAvailabilityToBeDeleted, setSelectedAvailabilityToBeDeleted] =
    useState<AvailableDate | undefined>(undefined);
  const [openDialog, setOpenDialog] = useState(false);
  const [warningMessage, setWarningMessage] = useState("");
  const [rangeConflict, setRangeConflict] = useState(false);
  const theme = useTheme();
  const isDesktopOrTablet = useMediaQuery(theme.breakpoints.up("md"));
  const requiredFields: RequiredFields[] = [
    "date",
    "start",
    "end",
    "interval",
    "availability",
    "doctorID",
  ];
  const [openFormDialog, setOpenFormDialog] = useState(false);
  const [formMessage, setFormMessage] = useState("");
  const { setError } = usePatient();
  const [doctors, setDoctors] = useState<Dr[]>([]);
  const [selectedDoctor, setSelectedDoctor] = useState<Dr | undefined>(
    undefined
  );
  const [bookingDoctor, setBookingDoctor] = useState<Dr | undefined>(undefined);
  const handleSectionChange = (section: string) => {
    setSection(section);
  };

  const handleTextChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setPatientID(e.target.value);
    return;
  };

  const handleDoctorChange = (e: SelectChangeEvent) => {
    const doctorID = e.target.value as string;
    const doctor = doctors.find((d) => d.id === doctorID);
    setSelectedDoctor(doctor);

    setSelectedAvailability((prev) => {
      if (prev) {
        return {
          ...prev,
          doctorID: doctorID,
          doctorName: doctor?.name,
        };
      }
      // Initialize with doctor information if no previous state
      return {
        doctorID: doctorID,
        doctorName: doctor?.name,
        // Initialize with empty values for required fields to avoid validation errors
        date: "",
        start: "",
        end: "",
        interval: 0,
        availability: 0,
      };
    });
  };

  const handleBookingDoctorChange = (e: SelectChangeEvent) => {
    const doctorID = e.target.value as string;
    const doctor = doctors.find((d) => d.id === doctorID);
    setBookingDoctor(doctor);
  };

  const handleFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setSelectedAvailability((prev) => {
      const startTime = name === "start" ? value : prev?.start;
      const endTime = name === "end" ? value : prev?.end;

      if (startTime && endTime && startTime >= endTime) {
        setLocalError("Start time must be earlier than End time.");
        return prev;
      }

      if (name === "date") {
        const day = getDayOfWeek(value);

        if (day === "Sunday" || day === "Saturday") {
          setLocalError("Select working days");
          return prev;
        }

        return {
          ...prev,
          [name]: value,
          day,
        };
      }

      setLocalError(null);

      return {
        ...prev,
        [name]: value,
      };
    });

    return;
  };

  const validForm = () => {
    // First check if doctor is selected
    if (!selectedDoctor) {
      setFormMessage(
        "Please select a doctor before adding availability slots."
      );
      setOpenFormDialog(true);
      return false;
    }

    for (const field of requiredFields) {
      if (selectedAvailability) {
        console.log(selectedAvailability);

        const value = selectedAvailability[field];
        if (
          value === undefined ||
          value === null ||
          value === "" ||
          (typeof value === "number" && value === 0)
        ) {
          setFormMessage(
            `The ${field} field is mandatory and must be filled out before submitting the form. Please provide the required information.`
          );
          setOpenFormDialog(true);
          return false;
        }
      } else {
        setFormMessage(
          `The form cannot be added because no fields have been filled out. Please complete the required fields and try again`
        );
        setOpenFormDialog(true);
        return false;
      }
    }

    return true;
  };

  const handleAddAvailability = () => {
    // Validate doctor selection first
    if (!selectedDoctor) {
      setFormMessage(
        "Please select a doctor before adding availability slots."
      );
      setOpenFormDialog(true);
      return;
    }

    const day = getDayOfWeek(selectedAvailability?.date);
    const range_id = uuidv4();

    if (selectedAvailability?.range_id) {
      setAvailabilities((prev) => {
        if (prev) {
          return prev.map((e) => {
            if (e.range_id === selectedAvailability?.range_id) {
              return {
                ...selectedAvailability,
                status: "pending",
                day,
              };
            }
            return e;
          });
        }
        return [];
      });
    } else {
      setAvailabilities((prev) => {
        if (prev) {
          if (validForm()) {
            setSelectedAvailability({
              date: "",
              start: "",
              end: "",
              status: "pending",
              range_id: "",
              doctorID: selectedDoctor?.id,
              doctorName: selectedDoctor?.name,
            });

            return [
              {
                ...selectedAvailability,
                status: "pending",
                range_id,
                doctorID: selectedDoctor?.id,
                doctorName: selectedDoctor?.name,
              },
              ...prev,
            ];
          }

          return prev;
        }
        return [];
      });
    }
    console.log(availabilities);
    return;
  };

  const handleConfirm = async () => {
    // Check if doctor is selected
    if (!selectedDoctor) {
      setFormMessage(
        "Please select a doctor before confirming availabilities."
      );
      setOpenFormDialog(true);
      return;
    }

    setIsLoading(true);
    const data = availabilities.filter(
      (e) => e.status !== "active" && e.status !== "editing"
    );
    if (data.length > 0) {
      try {
        // Ensure all pending availabilities have a doctorID
        const validData = data.map((item) => {
          if (!item.doctorID && selectedDoctor) {
            return {
              ...item,
              doctorID: selectedDoctor.id,
              doctorName: selectedDoctor.name,
            };
          }
          return item;
        });

        const result = await ApiClient.postAvailabilities(validData);

        if (result) {
          validData.forEach((entry) => {
            setAvailabilities((prev) => {
              if (prev) {
                return prev.map((e) => {
                  if (e.range_id === entry?.range_id) {
                    return {
                      ...entry,
                      status: "active",
                    };
                  }
                  return e;
                });
              }
              return [];
            });
          });
        }

        return;
      } catch (error) {
        setError(error as AxiosError<unknown, any>);
      } finally {
        setIsLoading(false);
      }
    } else {
      setFormMessage("No pending availabilities to confirm.");
      setOpenFormDialog(true);
      setIsLoading(false);
      return;
    }
    setSelectedAvailability(undefined);
    setIsLoading(false);
  };

  const handleEdit = (entry: AvailableDate) => {
    setSelectedAvailability(entry);
    setAvailabilities((prev) => {
      if (prev) {
        return prev.map((e) => {
          if (e.range_id === entry?.range_id) {
            return {
              ...entry,
              status: "editing",
            };
          }
          return e;
        });
      }
      return [];
    });
  };

  const handleDelete = (entry: AvailableDate) => {
    if (entry.status === "editing" || entry.status === "pending") {
      setAvailabilities((prev) => {
        if (prev) {
          return prev.filter((e) => e.range_id !== entry.range_id);
        }
        return prev;
      });

      return;
    }

    setSelectedAvailabilityToBeDeleted(entry);
    setWarningMessage(
      "We strongly discourage deleting active ranges, as it may result in patients losing their scheduled appointments."
    );
    setOpenDialog(true);
  };

  const handleDeleteConfirm = async () => {
    setIsLoading(true);
    try {
      if (selectedAvailabilityToBeDeleted) {
        const result = await ApiClient.deleteAvailability(
          selectedAvailabilityToBeDeleted
        );
        if (result.length > 0) {
          setIsLoading(false);
          setWarningMessage(
            "This range has active schedules associated with it. Do you still want to delete?"
          );
          setOpenDialog(true);
          setRangeConflict(true);
          setSelectedAvailability(undefined);
        } else {
          {
            setAvailabilities((prev) => {
              if (prev) {
                return prev.filter(
                  (e) => e.range_id !== selectedAvailabilityToBeDeleted.range_id
                );
              }
              return prev;
            });
          }
        }
      }
      return;
    } catch (error) {
      setError(error as AxiosError<unknown, any>);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnforceDelete = async () => {
    setIsLoading(true);
    try {
      if (selectedAvailabilityToBeDeleted) {
        const result = await ApiClient.deleteAvailabilityForce(
          selectedAvailabilityToBeDeleted
        );
        if (result) {
          setAvailabilities((prev) => {
            if (prev) {
              return prev.filter(
                (e) => e.range_id !== selectedAvailabilityToBeDeleted.range_id
              );
            }
            return prev;
          });
          setSelectedAvailability(undefined);
        }
      }
      return;
    } catch (error) {
      setError(error as AxiosError<unknown, any>);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelButton = (entry: AvailableDate) => {
    setAvailabilities((prev) => {
      if (prev) {
        return prev.map((e) => {
          if (e.range_id === entry?.range_id) {
            setSelectedAvailability(undefined);
            return {
              ...entry,
              status: "active",
            };
          }
          return e;
        });
      }
      return [];
    });
  };

  // Separate useEffect to only fetch doctors on initial mount
  useEffect(() => {
    const fetchDoctors = async () => {
      setIsLoading(true);
      try {
        const doctorsResult = await ApiClient.getActiveDoctors();
        setDoctors(doctorsResult);
      } catch (error) {
        setError(error as AxiosError<unknown, any>);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDoctors();
  }, []);

  // Fetch availabilities only when a doctor is selected or for all doctors in admin view
  useEffect(() => {
    const fetchAvailabilities = async () => {
      if (!selectedDoctor && section === sections.availability) {
        // Clear availabilities if no doctor is selected in availability section
        setAvailabilities([]);
        return;
      }

      setIsLoading(true);
      try {
        const result = await ApiClient.getAvailabilitiesForAdmin();

        // If a doctor is selected, filter availabilities for that doctor
        if (selectedDoctor && section === sections.availability) {
          const filteredResult = result.filter(
            (item) => item.doctorID === selectedDoctor.id
          );
          // Replace the availabilities array with just the filtered results
          if (filteredResult.length !== 0) {
            setAvailabilities(filteredResult);
          } else {
            // Set empty array if no availabilities for selected doctor
            setAvailabilities([]);
          }
        } else {
          if (result.length !== 0) {
            setAvailabilities(result);
          } else {
            // Set empty array if no availabilities at all
            setAvailabilities([]);
          }
        }
      } catch (error) {
        setError(error as AxiosError<unknown, any>);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAvailabilities();
  }, [selectedDoctor, section]);

  return (
    <>
      {isLoading && <LoadingScreen />}

      <Dialog
        open={openDialog}
        fullWidth={true}
        maxWidth={"xs"}
        onClose={() => setOpenDialog(false)}
      >
        <DialogContent>
          <Grid
            container
            direction={"column"}
            sx={{ width: "100%" }}
            justifyContent={"center"}
            alignItems={"center"}
          >
            <Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }}>
              Dangerous Action
            </Typography>
            <DangerousIcon
              sx={{ width: "150px", height: "150px", color: "orange", mb: 2 }}
            />
            <Typography sx={{ fontSize: "18px" }} align="center">
              {warningMessage}
            </Typography>
            <Grid sx={{ mt: 2 }} container spacing={2}>
              {rangeConflict ? (
                <Button
                  variant="contained"
                  sx={{
                    color: "white",
                    backgroundColor: "orange",
                    width: "150px",
                  }}
                  onClick={() => {
                    handleEnforceDelete();
                    setOpenDialog(false);
                  }}
                >
                  Yes, Continue
                </Button>
              ) : (
                <Button
                  variant="contained"
                  sx={{
                    color: "white",
                    backgroundColor: "orange",
                    width: "120px",
                  }}
                  onClick={() => {
                    handleDeleteConfirm();
                    setOpenDialog(false);
                  }}
                >
                  Continue
                </Button>
              )}

              <Button
                variant="contained"
                sx={{
                  color: "white",
                  backgroundColor: "green",
                  width: "120px",
                }}
                onClick={() => {
                  setOpenDialog(false);
                  setSelectedAvailabilityToBeDeleted(undefined);
                }}
              >
                Close
              </Button>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>

      <Dialog
        open={openFormDialog}
        fullWidth={true}
        maxWidth={"xs"}
        onClose={() => setOpenFormDialog(false)}
      >
        <DialogContent>
          <Grid
            container
            direction={"column"}
            sx={{ width: "100%" }}
            justifyContent={"center"}
            alignItems={"center"}
          >
            <Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }}>
              Missing Fields
            </Typography>
            <DangerousIcon
              sx={{ width: "150px", height: "150px", color: "orange", mb: 2 }}
            />
            <Typography sx={{ fontSize: "18px" }} align="center">
              {formMessage}
            </Typography>
            <Grid sx={{ mt: 2 }} container spacing={2}>
              <Button
                variant="contained"
                sx={{
                  color: "white",
                  backgroundColor: "green",
                  width: "120px",
                }}
                onClick={() => {
                  setOpenFormDialog(false);
                }}
              >
                Close
              </Button>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>

      <Grid
        container
        direction="column"
        sx={{ width: "100%", p: 2 }}
        justifyContent={"center"}
        alignItems={"start"}
      >
        <Grid
          sx={{ mt: 2, width: "100%" }}
          spacing={1}
          container
          justifyContent={"center"}
          alignItems={"center"}
        >
          <Grid size={{ lg: 12, xs: 12 }} container justifyContent={"center"}>
            <Button
              variant={
                section === sections.availability ? "contained" : "outlined"
              }
              sx={{
                color: section === sections.availability ? "white" : "green",
                textTransform: "none",
                borderColor: "green",
                width: "200px",
                backgroundColor:
                  section === sections.availability ? "green" : undefined,
              }}
              onClick={() => handleSectionChange(sections.availability)}
            >
              Set Availabilities
            </Button>
            <Button
              variant={section === sections.booking ? "contained" : "outlined"}
              sx={{
                color: section === sections.booking ? "white" : "green",
                textTransform: "none",
                borderColor: "green",
                width: "200px",
                backgroundColor:
                  section === sections.booking ? "green" : undefined,
              }}
              onClick={() => handleSectionChange(sections.booking)}
            >
              Book for a patient
            </Button>
          </Grid>
          <Grid
            container
            sx={{ width: "50%", mt: 2 }}
            justifyContent={"center"}
            alignItems={"center"}
          >
            {section === sections.booking && (
              <>
                <TextField
                  fullWidth
                  value={patientID}
                  onChange={handleTextChange}
                  size="small"
                  sx={{
                    ...inputStyle,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                    },
                  }}
                  label="Enter Patient ID"
                />

                <FormControl
                  fullWidth
                  sx={{
                    ...inputStyle,
                    mt: 2,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                    },
                  }}
                  size="small"
                >
                  <InputLabel id="booking-doctor-select-label">
                    Select Doctor
                  </InputLabel>
                  <Select
                    labelId="booking-doctor-select-label"
                    id="booking-doctor-select"
                    value={bookingDoctor?.id || ""}
                    onChange={handleBookingDoctorChange}
                    label="Select Doctor"
                  >
                    {doctors.map((doctor) => (
                      <MenuItem key={doctor.id} value={doctor.id}>
                        {doctor.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>
                    Please select a doctor to see their available slots
                  </FormHelperText>
                </FormControl>
              </>
            )}
          </Grid>
        </Grid>
        {section === sections.booking && (
          <Grid
            container
            direction="column"
            sx={{ width: "100%", height: "78vh", overflow: "auto" }}
          >
            {!bookingDoctor ? (
              <Grid
                container
                justifyContent="center"
                alignItems="center"
                sx={{ height: "50%" }}
              >
                <Grid container sx={{ textAlign: "center" }} size={{ xs: 12 }}>
                  <Typography
                    variant="h6"
                    sx={{ color: "gray", mb: 2, width: "100%" }}
                  >
                    Please select a doctor to view available appointment slots
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ color: "gray", width: "100%" }}
                  >
                    Use the dropdown above to choose a doctor
                  </Typography>
                </Grid>
              </Grid>
            ) : (
              <Scheduler
                patientID={patientID}
                hideMobileHeader={true}
                isAdmin={true}
                selectedDoctorId={bookingDoctor?.id}
              />
            )}
          </Grid>
        )}

        {section === sections.availability && (
          <Grid container sx={{ width: "100%", mt: 5 }}>
            <Grid
              container
              size={{ lg: 6, xs: 12 }}
              sx={{ width: "100%" }}
              justifyContent={"start"}
              alignItems="center"
              direction="column"
            >
              <Grid
                container
                sx={{ width: "100%" }}
                justifyContent={"center"}
                alignItems="start"
              >
                <FormControl
                  fullWidth={isDesktopOrTablet ? false : true}
                  sx={{
                    ...inputStyle,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                      width: { lg: "400px", xs: "100%" },
                    },
                  }}
                  size="small"
                >
                  <InputLabel id="doctor-select-label">
                    Select Doctor
                  </InputLabel>
                  <Select
                    labelId="doctor-select-label"
                    id="doctor-select"
                    value={selectedDoctor?.id || ""}
                    onChange={handleDoctorChange}
                    label="Select Doctor"
                  >
                    {doctors.map((doctor) => (
                      <MenuItem key={doctor.id} value={doctor.id}>
                        {doctor.name}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>
                    You must select a doctor before setting availability
                  </FormHelperText>
                </FormControl>
              </Grid>

              <Grid
                container
                sx={{ width: "100%" }}
                justifyContent={"center"}
                alignItems="start"
              >
                <FormControl
                  fullWidth={isDesktopOrTablet ? false : true}
                  sx={{
                    ...inputStyle,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                      width: { lg: "400px", xs: "100%" },
                    },
                    mt: 2,
                  }}
                  size="small"
                >
                  <TextField
                    value={selectedAvailability?.date || ""}
                    type="date"
                    name="date"
                    onChange={handleFormChange}
                    size="small"
                    label="Enter available date"
                    disabled={!selectedDoctor}
                    slotProps={{
                      inputLabel: {
                        shrink: true,
                      },
                    }}
                  />
                  <FormHelperText
                    sx={{
                      fontSize: "10px",
                      color: localError ? "red" : undefined,
                    }}
                  >
                    {localError || "Format: MM/DD/YYYY"}
                  </FormHelperText>
                </FormControl>
              </Grid>

              <Grid sx={{ width: "100%" }} container justifyContent={"center"}>
                <FormControl
                  fullWidth={isDesktopOrTablet ? false : true}
                  sx={{
                    ...inputStyle,
                    mt: 2,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                      width: { lg: "400px", xs: "100%" },
                    },
                  }}
                  size="small"
                >
                  <TextField
                    value={selectedAvailability?.start || ""}
                    type="time"
                    name="start"
                    onChange={handleFormChange}
                    size="small"
                    label="Start"
                    disabled={!selectedDoctor}
                    slotProps={{
                      inputLabel: {
                        shrink: true,
                      },
                    }}
                  />
                  <FormHelperText
                    sx={{
                      fontSize: "10px",
                      color: localError ? "red" : undefined,
                    }}
                  >
                    {localError || "Format: HH:MM"}
                  </FormHelperText>{" "}
                </FormControl>
              </Grid>

              <Grid sx={{ width: "100%" }} container justifyContent={"center"}>
                <FormControl
                  fullWidth={isDesktopOrTablet ? false : true}
                  sx={{
                    ...inputStyle,
                    mt: 2,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                      width: { lg: "400px", xs: "100%" },
                    },
                  }}
                  size="small"
                >
                  <TextField
                    value={selectedAvailability?.end || ""}
                    type="time"
                    name="end"
                    onChange={handleFormChange}
                    size="small"
                    label="End"
                    disabled={!selectedDoctor}
                    slotProps={{
                      inputLabel: {
                        shrink: true,
                      },
                    }}
                  />
                  <FormHelperText
                    sx={{
                      fontSize: "10px",
                      color: localError ? "red" : undefined,
                    }}
                  >
                    {localError || "Format: HH:MM"}
                  </FormHelperText>
                </FormControl>
              </Grid>

              <Grid sx={{ width: "100%" }} container justifyContent={"center"}>
                <FormControl
                  fullWidth={isDesktopOrTablet ? false : true}
                  sx={{
                    ...inputStyle,
                    mt: 2,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                      width: { lg: "400px", xs: "100%" },
                    },
                  }}
                  size="small"
                >
                  <TextField
                    value={selectedAvailability?.interval || ""}
                    name="interval"
                    onChange={(e) => {
                      const value = e.target.value;
                      // Allow only digits and update state
                      if (/^\d*$/.test(value)) {
                        handleFormChange(e);
                      }
                    }}
                    size="small"
                    disabled={!selectedDoctor}
                    label="Enter Intervals (in minutes)"
                  />
                  <FormHelperText
                    sx={{
                      fontSize: "10px",
                      color: localError ? "red" : undefined,
                    }}
                  >
                    {localError || "Only Digits"}
                  </FormHelperText>
                </FormControl>
              </Grid>
              <Grid sx={{ width: "100%" }} container justifyContent={"center"}>
                <FormControl
                  fullWidth={isDesktopOrTablet ? false : true}
                  sx={{
                    ...inputStyle,
                    mt: 2,
                    "& .MuiInputBase-root": {
                      borderRadius: "5px",
                      fontSize: "0.8rem",
                      width: { lg: "400px", xs: "100%" },
                    },
                  }}
                  size="small"
                >
                  <TextField
                    value={selectedAvailability?.availability || ""}
                    name="availability"
                    onChange={(e) => {
                      const value = e.target.value;
                      // Allow only digits and update state
                      if (/^\d*$/.test(value)) {
                        handleFormChange(e);
                      }
                    }}
                    size="small"
                    disabled={!selectedDoctor}
                    label="Enter number of consultations per interval"
                  />
                  <FormHelperText
                    sx={{
                      fontSize: "10px",
                      color: localError ? "red" : undefined,
                    }}
                  >
                    {localError || "Only Digits"}
                  </FormHelperText>
                </FormControl>
              </Grid>
              <Grid
                sx={{ width: "100%" }}
                container
                justifyContent={"center"}
                alignItems={"center"}
              >
                <Grid
                  sx={{ width: "100%" }}
                  container
                  justifyContent={"center"}
                  alignItems={"center"}
                  spacing={2}
                >
                  <Button
                    variant="outlined"
                    endIcon={<AddIcon />}
                    sx={{
                      color: "green",
                      textTransform: "none",
                      borderColor: "green",
                      width: { lg: "190px", xs: "100%" },
                      mt: 2,
                      fontWeight: "bold",
                    }}
                    disabled={!selectedDoctor}
                    onClick={() => handleAddAvailability()}
                  >
                    Add
                  </Button>

                  <Button
                    variant="contained"
                    sx={{
                      color: "white",
                      textTransform: "none",
                      backgroundColor: "green",
                      width: { lg: "190px", xs: "100%" },
                      mt: 2,
                    }}
                    disabled={
                      !selectedDoctor ||
                      !availabilities.some((a) => a.status === "pending")
                    }
                    onClick={() => handleConfirm()}
                  >
                    Confirm
                  </Button>
                </Grid>
              </Grid>
              {/* <Calendar/> */}
            </Grid>

            <Grid
              container
              size={{ lg: 6, xs: 12 }}
              sx={{ width: "100%", mt: { lg: 0, xs: 2 } }}
              direction={"column"}
            >
              <Grid
                sx={{ width: "100%", mb: 2 }}
                container
                justifyContent={"center"}
              >
                <Typography sx={{ whiteSpace: "nowrap" }} display={"inline"}>
                  {selectedDoctor
                    ? `Availabilities for Dr. ${selectedDoctor.name}`
                    : "Please select a doctor to view availabilities"}
                </Typography>
              </Grid>
              <Divider style={{ width: "100%" }} />

              {/* Show message when no doctor is selected */}
              {!selectedDoctor && (
                <Grid
                  container
                  sx={{ width: "100%", mt: 3 }}
                  justifyContent={"center"}
                  alignItems={"center"}
                >
                  <Typography
                    sx={{
                      fontSize: "14px",
                      fontStyle: "italic",
                      color: "gray",
                    }}
                  >
                    Please select a doctor from the dropdown above to view and
                    manage their availabilities.
                  </Typography>
                </Grid>
              )}

              {selectedDoctor && (
                <>
                  <Grid>
                    <Grid
                      sx={{ width: "100%", mt: 2 }}
                      container
                      justifyContent={"start"}
                      alignItems={"center"}
                    >
                      <Grid size={{ lg: 1 }}>
                        <Typography
                          display={"inline"}
                          sx={{ whiteSpace: "nowrap", fontSize: "12px" }}
                        >
                          Day
                        </Typography>
                      </Grid>

                      <Grid size={{ lg: 1 }} sx={{ mr: 2 }}>
                        <Typography
                          display={"inline"}
                          sx={{ whiteSpace: "nowrap", fontSize: "12px" }}
                        >
                          Date
                        </Typography>
                      </Grid>

                      <Grid size={{ lg: 1 }}>
                        <Typography
                          display={"inline"}
                          sx={{ whiteSpace: "nowrap", fontSize: "12px" }}
                        >
                          Start
                        </Typography>
                      </Grid>

                      <Grid size={{ lg: 1 }}>
                        <Typography
                          display={"inline"}
                          sx={{ whiteSpace: "nowrap", fontSize: "12px" }}
                        >
                          End
                        </Typography>
                      </Grid>
                      <Grid size={{ lg: 1 }}>
                        <Typography
                          display={"inline"}
                          sx={{ whiteSpace: "nowrap", fontSize: "12px" }}
                        >
                          Interval
                        </Typography>
                      </Grid>
                      <Grid size={{ lg: 1 }}>
                        <Typography
                          display={"inline"}
                          sx={{ whiteSpace: "nowrap", fontSize: "12px" }}
                        >
                          Bookings
                        </Typography>
                      </Grid>
                      <Grid sx={{ flexGrow: 1 }}>
                        <Typography
                          display={"inline"}
                          sx={{ whiteSpace: "nowrap", fontSize: "12px" }}
                        >
                          Doctor
                        </Typography>
                      </Grid>
                    </Grid>
                    <Divider style={{ width: "100%", marginTop: "15px" }} />
                  </Grid>

                  <Grid sx={{ height: "60vh", overflow: "auto" }}>
                    {availabilities.length > 0 ? (
                      availabilities.map((value) => {
                        return (
                          <Fragment key={value.range_id}>
                            <Grid
                              sx={{ width: "100%", mt: 2 }}
                              container
                              justifyContent={"start"}
                              alignItems={"center"}
                            >
                              <Grid size={{ lg: 1 }}>
                                <Typography
                                  display={"inline"}
                                  sx={{
                                    whiteSpace: "nowrap",
                                    fontSize: "12px",
                                  }}
                                >
                                  {value.day
                                    ? shortDays[
                                        value.day as keyof typeof shortDays
                                      ]
                                    : ""}
                                </Typography>
                              </Grid>

                              <Grid size={{ lg: 1 }} sx={{ mr: 2 }}>
                                <Typography
                                  display={"inline"}
                                  sx={{
                                    whiteSpace: "nowrap",
                                    fontSize: "12px",
                                  }}
                                >
                                  {value.date}
                                </Typography>
                              </Grid>

                              <Grid size={{ lg: 1 }}>
                                <Typography
                                  display={"inline"}
                                  sx={{
                                    whiteSpace: "nowrap",
                                    fontSize: "12px",
                                  }}
                                >
                                  {value.start}
                                </Typography>
                              </Grid>

                              <Grid size={{ lg: 1 }}>
                                <Typography
                                  display={"inline"}
                                  sx={{
                                    whiteSpace: "nowrap",
                                    fontSize: "12px",
                                  }}
                                >
                                  {value.end}
                                </Typography>
                              </Grid>
                              <Grid size={{ lg: 1 }}>
                                <Typography
                                  display={"inline"}
                                  sx={{
                                    whiteSpace: "nowrap",
                                    fontSize: "12px",
                                  }}
                                >
                                  {value.interval} (min)
                                </Typography>
                              </Grid>
                              <Grid size={{ lg: 1 }}>
                                <Typography
                                  display={"inline"}
                                  sx={{
                                    whiteSpace: "nowrap",
                                    fontSize: "12px",
                                  }}
                                >
                                  {value.availability}
                                </Typography>
                              </Grid>
                              <Grid sx={{ flexGrow: 1 }}>
                                <Typography
                                  display={"inline"}
                                  sx={{
                                    whiteSpace: "nowrap",
                                    fontSize: "12px",
                                  }}
                                >
                                  {value.doctorName || "Unassigned"}
                                </Typography>
                              </Grid>

                              <Grid container>
                                <Grid>
                                  <Button
                                    variant="outlined"
                                    sx={{
                                      fontSize: "8px",
                                      fontWeight: "bold",
                                      borderRadius: 5,
                                      width: "9px",
                                      color:
                                        value.status === "active"
                                          ? "green"
                                          : value.status === "pending"
                                            ? "orange"
                                            : value.status === "editing"
                                              ? "blue"
                                              : "inherit",
                                      borderColor:
                                        value.status === "active"
                                          ? "green"
                                          : value.status === "pending"
                                            ? "orange"
                                            : value.status === "editing"
                                              ? "blue"
                                              : "inherit",
                                    }}
                                  >
                                    {value &&
                                    value.status &&
                                    STATUS[value.status]
                                      ? STATUS[value.status]
                                      : ""}
                                  </Button>
                                </Grid>
                                <IconButton
                                  onClick={() => handleDelete(value)}
                                  sx={{
                                    ml: 2,
                                    width: "25px",
                                    height: "25px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                >
                                  <DeleteIcon
                                    sx={{
                                      color: "red",
                                      width: "15px",
                                      height: "15px",
                                    }}
                                  />
                                </IconButton>
                                <IconButton
                                  onClick={() => handleEdit(value)}
                                  sx={{
                                    ml: 2,
                                    width: "25px",
                                    height: "25px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                >
                                  <EditIcon
                                    sx={{
                                      color: "green",
                                      width: "15px",
                                      height: "15px",
                                    }}
                                  />
                                </IconButton>
                                <IconButton
                                  onClick={() => handleCancelButton(value)}
                                  sx={{
                                    ml: 2,
                                    width: "25px",
                                    height: "25px",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                  }}
                                  disabled={
                                    value.status === "active" ? true : false
                                  }
                                >
                                  <CancelIcon
                                    sx={{
                                      color:
                                        value.status === "active"
                                          ? "grey"
                                          : "black",
                                      width: "15px",
                                      height: "15px",
                                    }}
                                  />
                                </IconButton>
                              </Grid>
                            </Grid>
                          </Fragment>
                        );
                      })
                    ) : (
                      <>
                        <Grid
                          container
                          sx={{ width: "100%" }}
                          justifyContent={"start"}
                        >
                          <Typography
                            sx={{ fontSize: "12px", mt: 5, fontWeight: "bold" }}
                          >
                            {selectedDoctor
                              ? `No availabilities found for Dr. ${selectedDoctor.name}. Please add availability slots using the form.`
                              : "Urgent: No availabilities set! Patients are unable to book appointments because no active ranges have been added. Please set up Doctors' availability immediately to enable bookings."}
                          </Typography>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </>
              )}
            </Grid>
          </Grid>
        )}
      </Grid>
    </>
  );
};

export default AdminScheduler;
