import React, { useEffect, useState } from "react";
import Grid from "@mui/material/Grid2";
import { Button, Typography } from "@mui/material";
import { Link as LocationLink } from '@tanstack/react-location'
import { useSearch, MakeGenerics } from "@tanstack/react-location";
import { ApiClient } from "../../services";
import { PatientData } from "../../types";
import { getDateTime } from "../../utils";
import Daily from "@daily-co/daily-js";

type UrlProps = MakeGenerics<{
    Search: {
        token: string
    }
}>

const WelcomeRoom: React.FC = () => {
    const [dailyRoomID, setDailyRoomID] = useState<string>('')
    const { token } = useSearch<UrlProps>();
    const [patient, setPatient] = useState<PatientData | undefined>(undefined)
    useEffect(() => {

        const init = async () => {
            const existingFrame = Daily.getCallInstance()
            if(existingFrame){
                existingFrame.leave()
                existingFrame.destroy()
            }
            try {
                if (token) {
                    const patient = await ApiClient.getPatientById(token)
                    setPatient(patient)
                    // Update DB that the user has landed on the website. 
                }
            }
            catch (error) {
                const incomingError: Error = error as unknown as any
                throw new Error(incomingError.message)
            }

            if (token) {
                setDailyRoomID(token)
            }
        }
        init()
    }, [])

    return (
        <>
            <Grid container direction={'column'} justifyContent={'center'} alignItems={'center'} spacing={1} sx={{ mt: 3 }}>
                <Grid>
                    <Typography sx={{ fontSize: '24px', mb: 2 }} align="center">
                        Your appointment is scheduled for <span style={{ fontWeight: 'bold' }}>{getDateTime(patient?.consultation?.consultationDateAndTime)?.date || '--/--/---'} at {getDateTime(patient?.consultation?.consultationDateAndTime)?.time || '--/--'}</span>
                    </Typography>
                </Grid>
                <Grid>
                    <Typography sx={{ fontWeight: '500' }} align='center'>
                        Join the waiting room, the Doctor will be with you shortly.
                    </Typography>
                </Grid>

                <Grid sx={{ mb: 2 }}>
                    <Typography align='center' sx={{ fontSize: '12px' }}>
                        If you encounter any issues, please reach out to our support team via the chatbox.
                    </Typography>
                </Grid>

                <Grid>
                    <LocationLink to={'/consultation'} search={{ token: dailyRoomID }} style={{ textDecoration: 'none' }}>
                        <Button sx={{ backgroundColor: "green", textTransform: 'none' }} variant='contained'>
                            Join Waiting Room
                        </Button>
                    </LocationLink>
                </Grid>
            </Grid>
        </>
    )

}

export default WelcomeRoom