import { db } from './db';
import { logger } from '../config/logger';

/**
 * Get real-time patient online status directly from PatientQueue table
 * @param patientID - The patient ID to check
 * @returns Promise<boolean> - true if patient is online (ONLINE or JOINED status)
 */
export const getPatientOnlineStatus = async (patientID: string): Promise<boolean> => {
  const client = await db.connect();
  try {
    const query = `
      SELECT status 
      FROM PatientQueue 
      WHERE "patientID" = $1 
   
    `;
    const result = await client.query(query, [patientID]);
    
    if (result.rows.length <= 0) {
      return false;
    }
    
    const status = result.rows[0].status;

    console.log(`Patient ${patientID} status: ${status}`);
    return status === 'ONLINE';
  } catch (error) {
    logger.error(`Error checking patient online status for ${patientID}:`, error);
    return false;
  } finally {
    client.release();
  }
};

/**
 * Get real-time online status for multiple patients in a single query
 * @param patientIDs - Array of patient IDs to check
 * @returns Promise<Record<string, boolean>> - Map of patientID to online status
 */
export const getBatchPatientOnlineStatus = async (patientIDs: string[]): Promise<Record<string, boolean>> => {
  if (patientIDs.length === 0) {
    return {};
  }

  const client = await db.connect();
  try {
    const query = `
      SELECT "patientID", status 
      FROM PatientQueue 
      WHERE "patientID" = ANY($1) 
      AND "completedAt" IS NULL
    `;
    const result = await client.query(query, [patientIDs]);
    
    // Initialize all patients as offline
    const statusMap: Record<string, boolean> = {};
    patientIDs.forEach(id => {
      statusMap[id] = false;
    });
    
    // Update status for patients found in queue
    result.rows.forEach((row: { patientID: string; status: string }) => {
      const status = row.status;
      statusMap[row.patientID] = status === 'ONLINE' || status === 'JOINED';
    });
    
    return statusMap;
  } catch (error) {
    logger.error(`Error checking batch patient online status:`, error);
    // Return all patients as offline on error
    const statusMap: Record<string, boolean> = {};
    patientIDs.forEach(id => {
      statusMap[id] = false;
    });
    return statusMap;
  } finally {
    client.release();
  }
};

/**
 * Get detailed patient queue status information
 * @param patientID - The patient ID to check
 * @returns Promise<{status: string, isOnline: boolean} | null>
 */
export const getPatientQueueStatus = async (patientID: string): Promise<{status: string, isOnline: boolean} | null> => {
  const client = await db.connect();
  try {
    const query = `
      SELECT status, "createdAt", "updatedAt", "joinedAt", "notificationSent"
      FROM PatientQueue 
      WHERE "patientID" = $1 
      AND "completedAt" IS NULL
    `;
    const result = await client.query(query, [patientID]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    const row = result.rows[0];
    const status = row.status;
    const isOnline = status === 'ONLINE' || status === 'JOINED';
    
    return {
      status,
      isOnline
    };
  } catch (error) {
    logger.error(`Error getting patient queue status for ${patientID}:`, error);
    return null;
  } finally {
    client.release();
  }
};
