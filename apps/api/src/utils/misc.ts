import { PatientTreatmentPlan } from "../types";

const formatDateForTimeZone = (date: Date, timeZoneOffset: number): string => {
  // Adjust date to the specified timezone offset
  const adjustedDate = new Date(date.getTime() + timeZoneOffset * 60 * 60 * 1000);

  // Format date and time
  const datePart = adjustedDate.toISOString().split("T")[0]; // YYYY-MM-DD
  const timePart = adjustedDate.toTimeString().split(" ")[0]; // HH:MM:SS

  // Format timezone offset
  const offsetSign = timeZoneOffset >= 0 ? "+" : "-";
  const offsetHours = String(Math.abs(timeZoneOffset)).padStart(2, "0");
  const offsetMinutes = "00"; // Fixed as no fractional offsets

  // Combine parts
  return `${datePart}T${timePart}${offsetSign}${offsetHours}:${offsetMinutes}`;
};

// Function to get yesterday's date adjusted to a specific timezone
export const getYesterdayInTimeZone = (timeZoneOffset: number = 11): string => {
  const now = new Date();
  now.setDate(now.getDate() - 1);
  return formatDateForTimeZone(now, timeZoneOffset);
};


export const getFormatedZohoDate = (date?: string) => {

  const today = date ? new Date(date) : new Date()
  const offset = -today.getTimezoneOffset(); // Offset in minutes
  const offsetSign = offset >= 0 ? "+" : "-";
  const absOffset = Math.abs(offset);
  const offsetHours = String(Math.floor(absOffset / 60)).padStart(2, "0");
  const offsetMinutes = String(absOffset % 60).padStart(2, "0");

  const isoString = today.toISOString().split(".")[0]; // Remove milliseconds
  return `${isoString}${offsetSign}${offsetHours}:${offsetMinutes}`;
};


export const getStrength = (body: PatientTreatmentPlan) => {

  let strenghtAndConcentration = '';

  if (body[22] && body[29]) {
    strenghtAndConcentration = '22% & 29%';
  } else if (body[29]) {
    strenghtAndConcentration = '29%';
  } else if (body[22]) {
    strenghtAndConcentration = '22%';
  }

  return strenghtAndConcentration

}

export const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const extractValues = (text: string) => {
  // Define a pattern to match "PL-xxxxx" and a trailing number
  const pattern = /(PL-\d+)-(\d+)$/;
  const match = text.match(pattern);

  if (match) {
    return {
      plCode: match[1],
      number: match[2]
    };
  }

  return null;
}

export const extractOrderId = (text: string) => {
  // Define a pattern to match "PL-xxxxx" and a trailing number
  const pattern = /#(\d+)$/;
  const match = text.match(pattern);

  if (match) {
    return match[1];
  }

  return null;
}