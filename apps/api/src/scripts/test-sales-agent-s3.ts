#!/usr/bin/env ts-node

/**
 * Test script for Sales Agent Report S3 integration
 * 
 * This script tests the sales agent report S3 integration without sending actual Slack messages.
 * Run with: npx ts-node src/scripts/test-sales-agent-s3.ts
 */

import S3SalesReportsService from '../services/s3SalesReports.service';
import { logger } from '../config/logger';
import config from '../config';

async function testSalesAgentS3Integration(): Promise<void> {
  console.log('🚀 Testing Sales Agent Report S3 Integration\n');
  
  try {
    // Check configuration
    console.log('📋 Checking AWS S3 Configuration...');
    
    const requiredVars = [
      'AWS_REGION',
      'AWS_ACCESS_KEY_ID', 
      'AWS_SECRET_ACCESS_KEY',
      'AWS_BUCKET_NAME'
    ];
    
    const missing = requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
      throw new Error(`Missing environment variables: ${missing.join(', ')}`);
    }
    
    console.log('✅ Configuration check passed');
    console.log(`   Region: ${config.awsRegion}`);
    console.log(`   Bucket: ${config.awsBucketName}`);
    console.log(`   Has Credentials: ${!!(config.awsAccessKeyId && config.awsSecretAccessKey)}\n`);
    
    // Test S3 service initialization
    console.log('🔧 Initializing S3 Service...');
    const s3Service = new S3SalesReportsService();
    console.log('✅ S3 Service initialized successfully\n');
    
    // Create test image buffer (minimal PNG)
    console.log('🖼️  Creating test sales agent report image...');
    const testImageData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', 'base64');
    console.log(`✅ Test image created (${testImageData.length} bytes)\n`);
    
    // Test upload to S3
    console.log('📤 Testing S3 Upload for Sales Agent Report...');
    const testDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    
    const uploadResult = await s3Service.uploadSalesReport(
      testImageData,
      testDate,
      'daily',
      'png',
      {
        reportTitle: 'Test Sales Agent Report',
        reportDescription: 'Integration test for sales agent report',
        reportData: {
          reportType: 'sales-agent',
          testData: true,
          agentCount: 3,
          totalCalls: 150,
          timestamp: new Date().toISOString(),
        },
        slackChannel: 'C0904E5SW4W', // Same channel as sales agent reports
      }
    );
    
    if (!uploadResult.success) {
      throw new Error(uploadResult.error || 'Upload failed');
    }
    
    console.log('✅ S3 upload successful');
    console.log(`   File URL: ${uploadResult.fileUrl}`);
    console.log(`   S3 Key: ${uploadResult.s3Key}\n`);
    
    // Test database retrieval
    console.log('🗄️  Testing Database Retrieval...');
    const report = await s3Service.getSalesReport(testDate, 'daily');
    
    if (!report) {
      throw new Error('Report not found in database');
    }
    
    console.log('✅ Database retrieval successful');
    console.log(`   Report ID: ${report.id}`);
    console.log(`   Report Title: ${report.reportTitle}`);
    console.log(`   Status: ${report.status}\n`);
    
    // Test Slack info update (simulate)
    console.log('💬 Testing Slack Info Update...');
    await s3Service.updateSlackInfo(
      testDate,
      'daily',
      'test-message-ts-' + Date.now(),
      'C0904E5SW4W'
    );
    
    // Verify the update
    const updatedReport = await s3Service.getSalesReport(testDate, 'daily');
    
    if (!updatedReport || !updatedReport.slackMessageTs) {
      throw new Error('Slack info update failed');
    }
    
    console.log('✅ Slack info update successful');
    console.log(`   Slack Message TS: ${updatedReport.slackMessageTs}`);
    console.log(`   Updated Status: ${updatedReport.status}\n`);
    
    // Test presigned URL generation
    console.log('🔗 Testing Presigned URL Generation...');
    const presignedUrl = await s3Service.getPresignedUrl(report.s3Key, 300); // 5 minutes
    
    if (!presignedUrl || !presignedUrl.includes('X-Amz-Signature')) {
      throw new Error('Invalid presigned URL generated');
    }
    
    console.log('✅ Presigned URL generated successfully');
    console.log(`   URL Length: ${presignedUrl.length} characters\n`);
    
    // Cleanup - delete test report
    console.log('🧹 Cleaning up test data...');
    const deleted = await s3Service.deleteSalesReport(testDate, 'daily');
    
    if (!deleted) {
      throw new Error('Failed to delete test report');
    }
    
    // Verify deletion
    const deletedReport = await s3Service.getSalesReport(testDate, 'daily');
    
    if (deletedReport) {
      throw new Error('Report still exists after deletion');
    }
    
    console.log('✅ Cleanup successful\n');
    
    // Final success message
    console.log('🎉 All Sales Agent Report S3 Integration Tests Passed!');
    console.log('');
    console.log('📊 Test Summary:');
    console.log('   ✅ Configuration validation');
    console.log('   ✅ S3 service initialization');
    console.log('   ✅ Image upload to S3');
    console.log('   ✅ Database metadata storage');
    console.log('   ✅ Database retrieval');
    console.log('   ✅ Slack info updates');
    console.log('   ✅ Presigned URL generation');
    console.log('   ✅ Cleanup operations');
    console.log('');
    console.log('🚀 Sales Agent Reports are ready to use S3 storage!');
    
  } catch (error) {
    console.error('❌ Sales Agent S3 Integration Test Failed:');
    console.error('   Error:', error instanceof Error ? error.message : 'Unknown error');
    console.log('');
    console.log('🔧 Troubleshooting Tips:');
    console.log('   1. Check AWS credentials are correct');
    console.log('   2. Verify S3 bucket exists and is accessible');
    console.log('   3. Ensure database migration has been run');
    console.log('   4. Check network connectivity to AWS');
    console.log('   5. Verify IAM permissions for S3 operations');
    
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testSalesAgentS3Integration().catch(error => {
    logger.error('Test execution failed:', error);
    console.error('💥 Test execution failed:', error.message);
    process.exit(1);
  });
}

export default testSalesAgentS3Integration;
