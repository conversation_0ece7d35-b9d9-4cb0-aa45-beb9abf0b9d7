import React from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import { useChatEmail } from '../../../hooks/chat-email-provider';

interface ChatEmailPreviewProps {
    onSubmit: () => void;
}

const ChatEmailPreview: React.FC<ChatEmailPreviewProps> = ({ onSubmit }) => {
    const { patient, treatmentPlan, strainAdvice } = useChatEmail();

    const emailTemplateValues = () => {
        const activeIngredient = treatmentPlan?.outcome === 'Approve 22% Subject To CBD Trial' ? 'CBD Oil' : treatmentPlan?.outcome ? 'Tetrahydrocannabinol (THC)' : 'None';
        const strength = treatmentPlan?.[22] && treatmentPlan[29] ? '22% and 29%' : treatmentPlan?.[22] ? '22%' : treatmentPlan?.[29] ? '29%' : 'none';

        let quantity;
        let numRepeat;
        let interval;
        let dosePerDay;
        let maxDosePerDay;
        if (treatmentPlan?.[22]) {
            quantity = treatmentPlan?.[22]?.totalQuantity || 'none';
            numRepeat = treatmentPlan?.[22]?.numberOfRepeat || 'none';
            interval = treatmentPlan?.[22]?.supplyInterval || 'none';
            dosePerDay = treatmentPlan?.[22]?.dosePerDay || 'none';
            maxDosePerDay = treatmentPlan?.[22]?.maxDosePerDay || 'none';
        }
        else if (treatmentPlan?.[29]) {
            quantity = treatmentPlan?.[29]?.totalQuantity || 'none';
            numRepeat = treatmentPlan?.[29]?.numberOfRepeat || 'none';
            interval = treatmentPlan?.[29]?.supplyInterval || 'none';
            dosePerDay = treatmentPlan?.[29]?.dosePerDay || 'none';
            maxDosePerDay = treatmentPlan?.[29]?.maxDosePerDay || 'none';
        }

        return {
            activeIngredient,
            strength,
            quantity,
            numRepeat,
            interval,
            dosePerDay,
            maxDosePerDay
        };
    };

    const getNextFollowUp = () => {
        const today = new Date();
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + 28);
        return futureDate.toISOString().split('T')[0];
    };

    const emailContent = `Dear ${patient?.fullName},

I hope this email finds you well. I am writing to confirm your treatment plan details following our consultation.

Treatment Plan Overview:

Your treatment plan includes a script for medicinal cannabis as follows:

Active Ingredient: ${emailTemplateValues().activeIngredient}
Strength/Concentration: ${emailTemplateValues().strength}
Quantity Prescribed: ${emailTemplateValues().quantity} (g)
Number of Repeats: ${emailTemplateValues().numRepeat}
Repeat Interval: ${emailTemplateValues().interval} days
When and how often to use: ${emailTemplateValues().dosePerDay} (g) daily max ${emailTemplateValues().maxDosePerDay} (g)

When to stop using:
• If you experience any adverse effects
• If the medication is not helping your condition
• As advised by your healthcare provider

${strainAdvice ? `\nStrain Advice:\n${strainAdvice}` : ''}

${treatmentPlan?.drNotes ? `\nAdditional Notes:\n${treatmentPlan.drNotes}` : ''}

Follow-up appointment: ${getNextFollowUp()}

Please note that mental health supporting documentation ${treatmentPlan?.mentalHealthSupportingDocumentation ? 'has' : 'has not'} been provided.

If you have any questions or concerns about your treatment plan, please don't hesitate to contact us.

Best regards,
Dr. Hussain Anjum
APHRA: ${treatmentPlan?.drAphraNumber || '[APHRA Number]'}`;

    return (
        <Paper 
            elevation={0} 
            sx={{ 
                p: { xs: 2, sm: 3 }, 
                bgcolor: '#f8f9fa',
                borderRadius: '8px',
                width: '100%'
            }}
        >
            <Typography variant="h6" sx={{ 
                color: '#2d3748',
                fontWeight: 'bold',
                mb: 1.5,
                fontSize: { xs: '18px', sm: '22px' }
            }}>
                Email Preview
            </Typography>

            <Paper 
                elevation={0}
                sx={{ 
                    p: { xs: 2, sm: 2.5 }, 
                    bgcolor: '#ffffff',
                    borderRadius: '6px',
                    mb: 2,
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    fontSize: '14px',
                    color: '#4a5568',
                    lineHeight: '1.5',
                    width: '100%',
                    overflowX: 'auto'
                }}
            >
                {emailContent}
            </Paper>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                    variant="contained"
                    onClick={onSubmit}
                    sx={{
                        bgcolor: 'green',
                        '&:hover': {
                            bgcolor: 'darkgreen',
                        },
                        textTransform: 'none',
                        fontSize: '14px',
                        height: '35px'
                    }}
                >
                    Submit Treatment Plan
                </Button>
            </Box>
        </Paper>
    );
};

export default ChatEmailPreview; 