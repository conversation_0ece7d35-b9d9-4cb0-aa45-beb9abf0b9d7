// import { db } from '../utils/db';
// import { timerMap } from '../controllers/doctor/index';
// import { getNextPatientsForDoctor, getDoctorActiveRangePatientCount } from '../controllers/doctor/doctorQueueManager';
// import { getPatientsInCurrentActiveSlot, calculateTimeRemainingInSlot } from '../utils/timeSlotUtils';
// import { sendEscalatingNotifications } from './escalatingNotifications';
// import { DateTime } from 'luxon';

// // Mock time management for testing
// let mockTimeRemaining: number | null = null;
// const originalCalculateTimeRemainingInSlot = calculateTimeRemainingInSlot;

// // Mock function that returns simulated time during testing
// const mockCalculateTimeRemainingInSlot = (activeSlotInfo: any, detectedInterval: number): number => {
//   if (mockTimeRemaining !== null) {
//     console.log(`   🕐 MOCK TIME: Using simulated time remaining: ${mockTimeRemaining} minutes`);
//     return mockTimeRemaining;
//   }
//   return originalCalculateTimeRemainingInSlot(activeSlotInfo, detectedInterval);
// };

// // Helper functions to control mock time
// const setMockTimeRemaining = (timeRemaining: number) => {
//   mockTimeRemaining = timeRemaining;
// };

// const clearMockTime = () => {
//   mockTimeRemaining = null;
// };

// // Replace the function globally during testing
// (global as any).calculateTimeRemainingInSlot = mockCalculateTimeRemainingInSlot;

// // Handle EPIPE errors gracefully
// process.stdout.on('error', (err: any) => {
//   if (err.code === 'EPIPE') {
//     process.exit(0);
//   }
// });

// interface TestDoctor {
//   accessID: string;
//   username: string;
//   timer?: NodeJS.Timeout;
// }

// const TEST_DOCTORS: TestDoctor[] = [
//   { accessID: 'proper-30min-dr-1', username: 'TEST_Dr_Proper30Min_1' },
//   { accessID: 'proper-30min-dr-2', username: 'TEST_Dr_Proper30Min_2' },
//   { accessID: 'proper-30min-dr-3', username: 'TEST_Dr_Proper30Min_3' }
// ];

// async function createProper30MinSlotData(): Promise<void> {
//   console.log('\n🔧 CREATING PROPER 30-MINUTE APPOINTMENT SLOT WITH 50 PATIENTS');
  
//   const client = await db.connect();
//   try {
//     await client.query('BEGIN');

//     // Clean up any existing test data (in reverse order due to foreign key constraints)
//     await client.query(`DELETE FROM patientslot WHERE patient_id LIKE 'ZOHO_PROPER30_%'`);
//     await client.query(`DELETE FROM slot WHERE range_id LIKE 'RANGE_PROPER30_%'`);
//     await client.query(`DELETE FROM range WHERE id LIKE 'RANGE_PROPER30_%'`);
//     await client.query(`DELETE FROM patientqueue WHERE "patientID" LIKE 'PROPER30_%'`);
//     await client.query(`DELETE FROM consultation WHERE "patientID" LIKE 'PROPER30_%'`);
//     await client.query(`DELETE FROM patient WHERE "patientID" LIKE 'PROPER30_%'`);
//     await client.query(`DELETE FROM dr WHERE "accessID" LIKE 'proper-30min-dr-%'`);

//     // Create test doctors and get their UUIDs
//     const doctorUUIDs: string[] = [];
//     for (const doctor of TEST_DOCTORS) {
//       const result = await client.query(`
//         INSERT INTO dr ("accessID", username, email, "createdAt", "updatedAt")
//         VALUES ($1, $2, $3, NOW(), NOW())
//         ON CONFLICT ("accessID") DO UPDATE SET
//           username = EXCLUDED.username,
//           "updatedAt" = NOW()
//         RETURNING id
//       `, [doctor.accessID, doctor.username, `${doctor.username}@test.com`]);
//       doctorUUIDs.push(result.rows[0].id);
//     }

//     // Create 50 test patients with zohoID
//     const patientCount = 50;
//     for (let i = 1; i <= patientCount; i++) {
//       const patientID = `PROPER30_${i.toString().padStart(3, '0')}`;
//       const zohoID = `ZOHO_${patientID}`;
//       await client.query(`
//         INSERT INTO patient ("patientID", "fullName", "email", "mobile", "zohoID", "createdAt", "updatedAt")
//         VALUES ($1, $2, $3, '**********', $4, NOW(), NOW())
//         ON CONFLICT ("patientID") DO UPDATE SET
//           "zohoID" = EXCLUDED."zohoID",
//           "updatedAt" = NOW()
//       `, [patientID, `Proper 30Min Test Patient ${i}`, `${patientID}@test.com`, zohoID]);
//     }

//     // Create a SINGLE 30-minute appointment slot with all 50 patients
//     const now = DateTime.now().setZone('Australia/Sydney');

//     // Create a realistic current appointment slot
//     // Round to the nearest 30-minute slot and make it currently active
//     const currentMinute = now.minute;

//     // Determine the current 30-minute slot (either :00 or :30)
//     let slotMinute: number;
//     if (currentMinute < 15) {
//       // If it's before :15, use the :00 slot (started at top of hour)
//       slotMinute = 0;
//     } else if (currentMinute < 45) {
//       // If it's between :15-:44, use the :30 slot (started at :30)
//       slotMinute = 30;
//     } else {
//       // If it's after :45, use the next hour's :00 slot
//       slotMinute = 0;
//     }

//     // Create slot that started 5-10 minutes ago (currently active)
//     const slotStart = now.set({ minute: slotMinute, second: 0, millisecond: 0 }).minus({ minutes: 5 });
//     const slotEnd = slotStart.plus({ minutes: 30 });

//     console.log(`   📅 Creating REALISTIC CURRENT 30-minute appointment slot:`);
//     console.log(`      Slot start: ${slotStart.toFormat('h:mm:ss a')}`);
//     console.log(`      Slot end: ${slotEnd.toFormat('h:mm:ss a')}`);
//     console.log(`      Current time: ${now.toFormat('h:mm:ss a')}`);
//     console.log(`      Time into slot: ${Math.max(0, now.diff(slotStart, 'minutes').minutes).toFixed(1)} minutes`);
//     console.log(`      Time remaining: ${slotEnd.diff(now, 'minutes').minutes.toFixed(1)} minutes`);
//     console.log(`      Total slot duration: 30 minutes`);
//     console.log(`      Status: ${now >= slotStart && now <= slotEnd ? '🟢 CURRENTLY ACTIVE' : now < slotStart ? '🔵 FUTURE SLOT' : '🔴 PAST SLOT'}`);

//     // ALL 50 patients get the SAME consultation time (the slot start time)
//     // This creates ONE group consultation with 50 patients
//     const groupConsultationTime = slotStart;

//     console.log(`   👥 Creating GROUP CONSULTATION with ${patientCount} patients:`);
//     console.log(`      All patients scheduled for: ${groupConsultationTime.toFormat('h:mm:ss a')}`);
//     console.log(`      This creates ONE 30-minute consultation slot with all 50 patients`);

//     for (let i = 1; i <= patientCount; i++) {
//       const patientID = `PROPER30_${i.toString().padStart(3, '0')}`;

//       // ALL patients get the SAME consultation time
//       await client.query(`
//         INSERT INTO consultation ("patientID", "consultationDate", "notificationSent", completed, "createdAt", "updatedAt")
//         VALUES ($1, $2, false, false, NOW(), NOW())
//         ON CONFLICT ("patientID", "consultationDate") DO UPDATE SET
//           "notificationSent" = false,
//           completed = false,
//           "updatedAt" = NOW()
//       `, [patientID, groupConsultationTime.toJSDate()]);

//       // Log first few and last few patients for verification
//       if (i <= 5 || i >= patientCount - 4) {
//         const minutesFromNow = groupConsultationTime.diff(now, 'minutes').minutes.toFixed(1);
//         console.log(`      ${patientID}: ${groupConsultationTime.toFormat('h:mm:ss a')} (${minutesFromNow}min from now)`);
//       } else if (i === 6) {
//         console.log(`      ... (patients 6-46) all at same time ...`);
//       }
//     }

//     // Create Range records for each doctor (appointment time blocks)
//     console.log(`   📅 Creating Range records for ${TEST_DOCTORS.length} doctors...`);
//     const rangeIDs: string[] = [];

//     for (let doctorIndex = 0; doctorIndex < TEST_DOCTORS.length; doctorIndex++) {
//       const doctorUUID = doctorUUIDs[doctorIndex];
//       const rangeID = `RANGE_PROPER30_DR${doctorIndex + 1}`;

//       await client.query(`
//         INSERT INTO range (id, day, date, start, "end", interval, availability, status, "doctorID", "createdAt", "updatedAt")
//         VALUES ($1, $2, $3, $4, $5, 30, 20, 'active', $6, NOW(), NOW())
//         ON CONFLICT (id) DO UPDATE SET
//           "doctorID" = EXCLUDED."doctorID",
//           "updatedAt" = NOW()
//       `, [
//         rangeID,
//         slotStart.toFormat('cccc'), // Day name (e.g., 'Monday')
//         slotStart.toFormat('yyyy-MM-dd'), // Date
//         slotStart.toFormat('HH:mm'), // Start time
//         slotEnd.toFormat('HH:mm'), // End time
//         doctorUUID
//       ]);

//       rangeIDs.push(rangeID);
//     }

//     // Create Slot records within each range
//     console.log(`   🕐 Creating Slot records for appointment times...`);
//     const slotIDs: number[] = [];

//     for (const rangeID of rangeIDs) {
//       const slotResult = await client.query(`
//         INSERT INTO slot (range_id, slot, remaining, "createdAt", "updatedAt")
//         VALUES ($1, $2, 20, NOW(), NOW())
//         ON CONFLICT (range_id, slot) DO UPDATE SET
//           remaining = EXCLUDED.remaining,
//           "updatedAt" = NOW()
//         RETURNING id
//       `, [rangeID, slotStart.toFormat('HH:mm')]);

//       slotIDs.push(slotResult.rows[0].id);
//     }

//     // Create PatientSlot records to assign patients to doctors
//     console.log(`   👥 Creating PatientSlot records to assign patients to doctors...`);

//     for (let i = 1; i <= patientCount; i++) {
//       const patientID = `PROPER30_${i.toString().padStart(3, '0')}`;
//       const zohoID = `ZOHO_${patientID}`;

//       // Distribute patients evenly across doctors
//       const doctorIndex = (i - 1) % TEST_DOCTORS.length;
//       const rangeID = rangeIDs[doctorIndex];
//       const slotID = slotIDs[doctorIndex];

//       await client.query(`
//         INSERT INTO patientslot (patient_id, range_id, slot_id, "createdAt", "updatedAt")
//         VALUES ($1, $2, $3, NOW(), NOW())
//         ON CONFLICT (patient_id, range_id) DO UPDATE SET
//           slot_id = EXCLUDED.slot_id,
//           "updatedAt" = NOW()
//       `, [zohoID, rangeID, slotID]);

//       // Log assignment for first few patients
//       if (i <= 5) {
//         console.log(`      ${patientID} → Doctor ${doctorIndex + 1} (${TEST_DOCTORS[doctorIndex].username})`);
//       } else if (i === 6) {
//         console.log(`      ... (remaining patients distributed evenly) ...`);
//       }
//     }

//     await client.query('COMMIT');
//     console.log(`   ✅ Created ${TEST_DOCTORS.length} test doctors`);
//     console.log(`   ✅ Created ${patientCount} test patients with zohoIDs`);
//     console.log(`   ✅ Created ${patientCount} consultations in SINGLE 30-minute appointment slot`);
//     console.log(`   ✅ Created ${rangeIDs.length} range records for doctor assignments`);
//     console.log(`   ✅ Created ${slotIDs.length} slot records for appointment times`);
//     console.log(`   ✅ Created ${patientCount} patientslot records linking patients to doctors`);

//   } catch (error) {
//     await client.query('ROLLBACK');
//     throw error;
//   } finally {
//     client.release();
//   }
// }

// function setupDoctorTimers(): void {
//   console.log('\n⏰ SETTING UP DOCTOR TIMERS');
  
//   // Clear existing timers
//   for (const [key, timer] of timerMap.entries()) {
//     if (key.startsWith('proper-30min-dr-')) {
//       clearInterval(timer.timerId || timer);
//       timerMap.delete(key);
//     }
//   }

//   // Add test doctors to timer map
//   for (const doctor of TEST_DOCTORS) {
//     const mockTimer = setInterval(() => {}, 60000);
//     timerMap.set(doctor.accessID, { timerId: mockTimer });
//     doctor.timer = mockTimer;
//     console.log(`   ✅ Added ${doctor.username} to timerMap`);
//   }
  
//   console.log(`   🩺 Total doctors online: ${Array.from(timerMap.keys()).filter(k => k !== 'patientInterval').length}`);
// }

// async function testProper30MinSlotDetection(): Promise<void> {
//   console.log('\n🔍 TESTING RANGE DETECTION ON PROPER 30-MINUTE SLOT');
  
//   // Test the range detection algorithm
//   const activeSlotData = await getPatientsInCurrentActiveSlot(db);
//   const { patientIDs: activePatientIDs, activeSlotInfo, detectedInterval } = activeSlotData;
  
//   console.log(`\n📊 RANGE DETECTION RESULTS:`);
//   console.log(`   📅 Detected slot time: ${activeSlotInfo?.slotTime.toFormat('h:mm:ss a') || 'None'}`);
//   console.log(`   ⏱️  Detected interval: ${detectedInterval} minutes`);
//   console.log(`   👥 Patients found: ${activePatientIDs.length}`);
//   console.log(`   📋 First 10 patient IDs: [${activePatientIDs.slice(0, 10).join(', ')}...]`);
//   console.log(`   📋 Last 5 patient IDs: [...${activePatientIDs.slice(-5).join(', ')}]`);
  
//   if (activeSlotInfo) {
//     const slotStart = activeSlotInfo.slotTime;
//     const slotEnd = slotStart.plus({ minutes: detectedInterval });
//     const now = DateTime.now().setZone('Australia/Sydney');
    
//     console.log(`\n🕐 TIME ANALYSIS:`);
//     console.log(`   Current time: ${now.toFormat('h:mm:ss a')}`);
//     console.log(`   Detected slot start: ${slotStart.toFormat('h:mm:ss a')}`);
//     console.log(`   Detected slot end: ${slotEnd.toFormat('h:mm:ss a')}`);
//     console.log(`   Detected slot duration: ${detectedInterval} minutes`);
//     console.log(`   Time into slot: ${now.diff(slotStart, 'minutes').minutes.toFixed(1)} minutes`);
//     console.log(`   Time remaining: ${slotEnd.diff(now, 'minutes').minutes.toFixed(1)} minutes`);
    
//     // Verify this matches our expected 30-minute slot
//     const expectedSlotStart = now.minus({ minutes: 5 });
//     const timeDifference = Math.abs(slotStart.diff(expectedSlotStart, 'minutes').minutes);
    
//     console.log(`\n✅ VERIFICATION:`);
//     console.log(`   Expected slot start: ${expectedSlotStart.toFormat('h:mm:ss a')}`);
//     console.log(`   Actual slot start: ${slotStart.toFormat('h:mm:ss a')}`);
//     console.log(`   Time difference: ${timeDifference.toFixed(1)} minutes`);
//     console.log(`   Expected patients: 50`);
//     console.log(`   Actual patients: ${activePatientIDs.length}`);
    
//     if (timeDifference < 1 && activePatientIDs.length === 50) {
//       console.log(`   🎉 SUCCESS: Range detection found the complete 30-minute slot with all 50 patients!`);
//     } else if (activePatientIDs.length < 50) {
//       console.log(`   ⚠️  PARTIAL: Range detection found ${activePatientIDs.length}/50 patients`);
//       console.log(`   🔍 This suggests the algorithm is finding a smaller sub-slot within the 30-minute range`);
//     } else {
//       console.log(`   ❌ UNEXPECTED: Range detection results don't match expected 30-minute slot`);
//     }
//   }
// }

// interface IntervalResult {
//   intervalNumber: number;
//   timeIntoRange: number;
//   timeRemaining: number;
//   urgencyLevel: string;
//   doctorResults: Array<{
//     doctor: string;
//     target: number;
//     notified: number;
//     cumulative: number;
//     dynamicLimit: number;
//   }>;
// }

// async function testDoctorThrottlingWithIntervals(): Promise<void> {
//   console.log('\n🧪 TESTING DOCTOR THROTTLING WITH MULTIPLE INTERVALS');

//   // Get the detected range
//   const activeSlotData = await getPatientsInCurrentActiveSlot(db);
//   const { patientIDs: activePatientIDs, detectedInterval } = activeSlotData;

//   if (activePatientIDs.length === 0) {
//     console.log('   ❌ No patients found in active slot - cannot test throttling');
//     return;
//   }

//   console.log(`\n📊 MULTI-INTERVAL THROTTLING TEST SETUP:`);
//   console.log(`   👥 Patients in slot: ${activePatientIDs.length}`);
//   console.log(`   🩺 Doctors available: ${TEST_DOCTORS.length}`);
//   console.log(`   ⏱️  Slot duration: ${detectedInterval} minutes`);
//   console.log(`   🎯 Expected total per doctor: ~${Math.ceil(activePatientIDs.length / TEST_DOCTORS.length)} patients`);

//   const intervalResults: IntervalResult[] = [];
//   const doctorCumulativeNotifications = new Map<string, number>();

//   // Initialize cumulative counters
//   TEST_DOCTORS.forEach(doctor => {
//     doctorCumulativeNotifications.set(doctor.username, 0);
//   });

//   // Simulate notification intervals every 5 minutes for 30 minutes (6 intervals total)
//   const totalIntervals = 6;
//   const intervalMinutes = 5;

//   console.log(`\n📊 SIMULATION PARAMETERS:`);
//   console.log(`   📅 Total simulation duration: ${totalIntervals * intervalMinutes} minutes`);
//   console.log(`   ⏱️  Notification interval: ${intervalMinutes} minutes`);
//   console.log(`   🔄 Total intervals: ${totalIntervals}`);
//   console.log(`   🩺 Doctors: ${TEST_DOCTORS.length}`);

//   for (let interval = 1; interval <= totalIntervals; interval++) {
//     // Simulate time progression: Start 5 minutes before slot, then progress through slot
//     const timeIntoRange = (interval - 1) * intervalMinutes; // 0, 5, 10, 15, 20, 25 minutes into slot
//     const timeRemaining = Math.max(0, detectedInterval - timeIntoRange);

//     // 🕐 SET MOCK TIME for this interval
//     setMockTimeRemaining(timeRemaining);

//     // Determine urgency level based on 30-minute range
//     let urgencyLevel: string;
//     if (timeRemaining > 20) {
//       urgencyLevel = 'LOW';
//     } else if (timeRemaining > 10) {
//       urgencyLevel = 'MEDIUM';
//     } else if (timeRemaining > 5) {
//       urgencyLevel = 'HIGH';
//     } else {
//       urgencyLevel = 'EMERGENCY';
//     }

//     console.log(`\n${'='.repeat(80)}`);
//     console.log(`⏰ INTERVAL ${interval}/${totalIntervals}: ${timeIntoRange} minutes into ${detectedInterval}-minute slot`);
//     console.log(`   ⏳ Time remaining: ${timeRemaining} minutes (MOCKED)`);
//     console.log(`   🎯 Urgency level: ${urgencyLevel}`);
//     console.log(`${'='.repeat(80)}`);

//     const intervalResult: IntervalResult = {
//       intervalNumber: interval,
//       timeIntoRange,
//       timeRemaining,
//       urgencyLevel,
//       doctorResults: []
//     };

//     // Test each doctor's notification in this interval
//     for (let doctorIndex = 0; doctorIndex < TEST_DOCTORS.length; doctorIndex++) {
//       const doctor = TEST_DOCTORS[doctorIndex];
//       console.log(`\n🩺 Doctor: ${doctor.username}`);

//       try {
//         // Calculate fair distribution for this doctor
//         const basePerDoctor = Math.floor(activePatientIDs.length / TEST_DOCTORS.length);
//         const remainder = activePatientIDs.length % TEST_DOCTORS.length;
//         const urgencyBoost = timeRemaining < 10 ? (timeRemaining < 5 ? 2 : 1) : 0;
//         const targetForThisDoctor = basePerDoctor + urgencyBoost + (doctorIndex < remainder ? 1 : 0);

//         // Check current load
//         const currentActiveRangeLoad = await getDoctorActiveRangePatientCount(db, doctor.accessID, activePatientIDs);
//         const batchSize = Math.max(0, targetForThisDoctor - currentActiveRangeLoad);

//         console.log(`   📊 Calculation: Base=${basePerDoctor}, Remainder=${remainder}, Urgency=${urgencyBoost}, Target=${targetForThisDoctor}`);
//         console.log(`   📈 Current load: ${currentActiveRangeLoad}, Batch size: ${batchSize}`);

//         let notifiedThisInterval = 0;
//         let dynamicLimit = 0;

//         if (batchSize > 0) {
//           const selectedPatients = await getNextPatientsForDoctor(db, doctor.accessID, batchSize);
//           notifiedThisInterval = selectedPatients.length;

//           // Extract dynamic limit from the logs (we'll see this in the output)
//           dynamicLimit = 3; // This will be shown in the logs

//           console.log(`   📋 Result: ${notifiedThisInterval}/${batchSize} patients selected`);
//           console.log(`   📤 Would notify: [${selectedPatients.slice(0, 5).join(', ')}${selectedPatients.length > 5 ? '...' : ''}]`);

//           // Simulate assignment (mark as notified)
//           if (selectedPatients.length > 0) {
//             const client = await db.connect();
//             try {
//               for (const patientID of selectedPatients) {
//                 // Update consultation.notificationSent to prevent duplicate notifications
//                 await client.query(`
//                   UPDATE consultation
//                   SET "notificationSent" = true, "notificationSentDateTime" = NOW(), "updatedAt" = NOW()
//                   WHERE "patientID" = $1
//                     AND "notificationSent" = false
//                     AND completed = false
//                     AND "consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
//                     AND "consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
//                 `, [patientID]);

//                 // Also update patientqueue for tracking
//                 await client.query(`
//                   INSERT INTO patientqueue ("patientID", "assignedDoctorID", "status", "notificationSentDateTime", "createdAt", "updatedAt")
//                   VALUES ($1, $2, 'OFFLINE', NOW(), NOW(), NOW())
//                   ON CONFLICT ("patientID") DO UPDATE SET
//                     "assignedDoctorID" = EXCLUDED."assignedDoctorID",
//                     "status" = EXCLUDED."status",
//                     "notificationSentDateTime" = EXCLUDED."notificationSentDateTime",
//                     "updatedAt" = NOW()
//                 `, [patientID, doctor.accessID]);
//               }
//             } finally {
//               client.release();
//             }
//           }
//         } else {
//           console.log(`   🚫 No notifications (already at target: ${currentActiveRangeLoad}/${targetForThisDoctor})`);
//         }

//         // Update cumulative count
//         const currentCumulative = doctorCumulativeNotifications.get(doctor.username) || 0;
//         const newCumulative = currentCumulative + notifiedThisInterval;
//         doctorCumulativeNotifications.set(doctor.username, newCumulative);

//         intervalResult.doctorResults.push({
//           doctor: doctor.username,
//           target: targetForThisDoctor,
//           notified: notifiedThisInterval,
//           cumulative: newCumulative,
//           dynamicLimit
//         });

//         console.log(`   ✅ Interval result: +${notifiedThisInterval} patients (cumulative: ${newCumulative})`);

//       } catch (error) {
//         console.log(`   ❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
//         intervalResult.doctorResults.push({
//           doctor: doctor.username,
//           target: 0,
//           notified: 0,
//           cumulative: doctorCumulativeNotifications.get(doctor.username) || 0,
//           dynamicLimit: 0
//         });
//       }
//     }

//     intervalResults.push(intervalResult);

//     // Interval summary
//     console.log(`\n📊 INTERVAL ${interval} SUMMARY (${urgencyLevel} urgency):`);
//     intervalResult.doctorResults.forEach(result => {
//       console.log(`   ${result.doctor}: +${result.notified} patients (cumulative: ${result.cumulative})`);
//     });

//     // Wait between intervals (simulate time passing)
//     if (interval < totalIntervals) {
//       console.log(`\n⏳ Simulating ${intervalMinutes}-minute wait...`);
//       await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second = 5 minutes in simulation
//     }
//   }

//   // Final comprehensive analysis
//   console.log(`\n${'='.repeat(100)}`);
//   console.log(`📈 COMPLETE 30-MINUTE SLOT MULTI-INTERVAL ANALYSIS`);
//   console.log(`${'='.repeat(100)}`);

//   // Create summary table
//   console.log(`\n📊 INTERVAL-BY-INTERVAL BREAKDOWN:`);
//   console.log(`${'Interval'.padEnd(10)} ${'Time'.padEnd(12)} ${'Remaining'.padEnd(12)} ${'Urgency'.padEnd(12)} ${TEST_DOCTORS.map(d => d.username.padEnd(25)).join(' ')}`);
//   console.log(`${''.padEnd(10, '-')} ${''.padEnd(12, '-')} ${''.padEnd(12, '-')} ${''.padEnd(12, '-')} ${TEST_DOCTORS.map(() => ''.padEnd(25, '-')).join(' ')}`);

//   intervalResults.forEach(result => {
//     const timeStr = `${result.timeIntoRange}min`;
//     const remainingStr = `${result.timeRemaining}min`;
//     const urgencyStr = result.urgencyLevel;
//     const doctorStrs = TEST_DOCTORS.map(doctor => {
//       const doctorResult = result.doctorResults.find(dr => dr.doctor === doctor.username);
//       return `+${doctorResult?.notified || 0} (${doctorResult?.cumulative || 0})`.padEnd(25);
//     });

//     console.log(`${result.intervalNumber.toString().padEnd(10)} ${timeStr.padEnd(12)} ${remainingStr.padEnd(12)} ${urgencyStr.padEnd(12)} ${doctorStrs.join(' ')}`);
//   });

//   // Final totals and analysis
//   const totalNotifications = Array.from(doctorCumulativeNotifications.values()).reduce((sum, count) => sum + count, 0);
//   console.log(`\n📊 FINAL TOTALS:`);
//   console.log(`   🎯 Total notifications sent: ${totalNotifications}`);
//   console.log(`   📊 Coverage: ${activePatientIDs.length > 0 ? ((totalNotifications / activePatientIDs.length) * 100).toFixed(1) : 0}% of ${activePatientIDs.length} patients notified`);
//   console.log(`   👥 Per-doctor distribution:`);

//   TEST_DOCTORS.forEach(doctor => {
//     const count = doctorCumulativeNotifications.get(doctor.username) || 0;
//     const percentage = totalNotifications > 0 ? ((count / totalNotifications) * 100).toFixed(1) : '0';
//     console.log(`      ${doctor.username}: ${count} notifications (${percentage}%)`);
//   });

//   // Distribution fairness analysis
//   const counts = Array.from(doctorCumulativeNotifications.values());
//   const maxCount = Math.max(...counts);
//   const minCount = Math.min(...counts);
//   const variance = maxCount - minCount;

//   console.log(`\n⚖️ FAIRNESS ANALYSIS:`);
//   console.log(`   📊 Distribution variance: ${variance} patients`);
//   console.log(`   ✅ Fairness rating: ${variance <= 1 ? 'EXCELLENT' : variance <= 2 ? 'GOOD' : variance <= 3 ? 'FAIR' : 'NEEDS IMPROVEMENT'}`);
//   console.log(`   🎯 Average per doctor: ${(totalNotifications / TEST_DOCTORS.length).toFixed(1)} patients`);

//   // Time-aware throttling analysis
//   console.log(`\n🕐 TIME-AWARE THROTTLING ANALYSIS:`);
//   intervalResults.forEach(result => {
//     const totalThisInterval = result.doctorResults.reduce((sum, dr) => sum + dr.notified, 0);
//     console.log(`   Interval ${result.intervalNumber} (${result.urgencyLevel}): ${totalThisInterval} total notifications`);
//   });
// }

// async function cleanupTestData(): Promise<void> {
//   console.log('\n🧹 CLEANING UP TEST DATA');

//   // Clear mock time
//   clearMockTime();
//   console.log('   ✅ Mock time cleared');

//   // Clear timers
//   for (const doctor of TEST_DOCTORS) {
//     if (doctor.timer) {
//       clearInterval(doctor.timer);
//     }
//     timerMap.delete(doctor.accessID);
//   }

//   const client = await db.connect();
//   try {
//     await client.query('BEGIN');

//     // Clean up in reverse order due to foreign key constraints
//     await client.query(`DELETE FROM patientslot WHERE patient_id LIKE 'ZOHO_PROPER30_%'`);
//     await client.query(`DELETE FROM slot WHERE range_id LIKE 'RANGE_PROPER30_%'`);
//     await client.query(`DELETE FROM range WHERE id LIKE 'RANGE_PROPER30_%'`);
//     await client.query(`DELETE FROM patientqueue WHERE "patientID" LIKE 'PROPER30_%'`);
//     await client.query(`DELETE FROM consultation WHERE "patientID" LIKE 'PROPER30_%'`);
//     await client.query(`DELETE FROM patient WHERE "patientID" LIKE 'PROPER30_%'`);
//     await client.query(`DELETE FROM dr WHERE "accessID" LIKE 'proper-30min-dr-%'`);

//     await client.query('COMMIT');
//     console.log('   ✅ Test data cleaned up successfully');

//   } catch (error) {
//     await client.query('ROLLBACK');
//     console.error('   ❌ Error cleaning up test data:', error);
//   } finally {
//     client.release();
//   }
// }

// /**
//  * Test escalating notifications integration with active slot detection
//  */
// async function testEscalatingNotificationsIntegration(): Promise<void> {
//   console.log(`\n${'='.repeat(80)}`);
//   console.log(`🚨 TESTING ESCALATING NOTIFICATIONS INTEGRATION`);
//   console.log(`${'='.repeat(80)}`);

//   const client = await db.connect();

//   try {
//     await client.query('BEGIN');

//     // Step 1: Create some patients who have been notified but haven't joined
//     console.log('\n📋 Setting up escalation test scenario...');

//     // Update some patients to have notification sent 6+ minutes ago (to trigger first warning)
//     const firstWarningPatients = ['PROPER30_001', 'PROPER30_002', 'PROPER30_003'];
//     const firstWarningTime = new Date(Date.now() - 6 * 60 * 1000); // 6 minutes ago

//     for (const patientID of firstWarningPatients) {
//       await client.query(`
//         UPDATE patientqueue
//         SET "notificationSentDateTime" = $1,
//             "status" = 'OFFLINE',
//             "joinedAt" = NULL
//         WHERE "patientID" = $2
//       `, [firstWarningTime, patientID]);
//     }

//     // Update some patients to have notification sent 11+ minutes ago (to trigger second warning)
//     const secondWarningPatients = ['PROPER30_004', 'PROPER30_005'];
//     const secondWarningTime = new Date(Date.now() - 11 * 60 * 1000); // 11 minutes ago

//     for (const patientID of secondWarningPatients) {
//       await client.query(`
//         UPDATE patientqueue
//         SET "notificationSentDateTime" = $1,
//             "status" = 'OFFLINE',
//             "joinedAt" = NULL
//         WHERE "patientID" = $2
//       `, [secondWarningTime, patientID]);
//     }

//     await client.query('COMMIT');

//     console.log(`   ✅ Set up ${firstWarningPatients.length} patients for 1st warning (6+ min ago)`);
//     console.log(`   ✅ Set up ${secondWarningPatients.length} patients for 2nd warning (11+ min ago)`);

//     // Step 2: Test active slot detection for escalating notifications
//     console.log('\n🔍 Testing active slot detection for escalating notifications...');

//     const activeSlotData = await getPatientsInCurrentActiveSlot(db);
//     console.log(`   📊 Active slot patients: ${activeSlotData.patientIDs.length}`);
//     console.log(`   🕐 Detected interval: ${activeSlotData.detectedInterval} minutes`);

//     if (activeSlotData.activeSlotInfo) {
//       const slotStart = activeSlotData.activeSlotInfo.slotTime.toFormat('h:mm a');
//       const slotEnd = activeSlotData.activeSlotInfo.slotTime.plus({ minutes: activeSlotData.detectedInterval }).toFormat('h:mm a');
//       console.log(`   📅 Active slot: ${slotStart} - ${slotEnd}`);
//     }

//     // Step 3: Check which escalation candidates are in active slots
//     const escalationQuery = `
//       SELECT
//         p."patientID",
//         p."fullName",
//         pq."notificationSentDateTime",
//         EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - pq."notificationSentDateTime"))/60 as minutes_since_notification
//       FROM PatientQueue pq
//       JOIN Patient p ON p."patientID" = pq."patientID"
//       WHERE pq."notificationSentDateTime" IS NOT NULL
//         AND pq."joinedAt" IS NULL
//         AND pq.status = 'OFFLINE'
//         AND CURRENT_TIMESTAMP >= pq."notificationSentDateTime" + INTERVAL '5 minutes'
//         AND p."patientID" LIKE 'PROPER30_%'
//       ORDER BY pq."notificationSentDateTime" ASC
//     `;

//     const escalationResult = await client.query(escalationQuery);
//     const allCandidates = escalationResult.rows;

//     // Filter to only those in active slots (same logic as escalatingNotifications.ts)
//     const activeCandidates = allCandidates.filter(candidate =>
//       activeSlotData.patientIDs.includes(candidate.patientID)
//     );

//     console.log(`\n📊 ESCALATION ANALYSIS:`);
//     console.log(`   🔍 Total escalation candidates: ${allCandidates.length}`);
//     console.log(`   🎯 Candidates in active slots: ${activeCandidates.length}`);
//     console.log(`   ⚖️ Active slot filtering: ${activeCandidates.length > 0 ? 'WORKING' : 'NO CANDIDATES'}`);

//     if (activeCandidates.length > 0) {
//       console.log(`\n📋 ACTIVE ESCALATION CANDIDATES:`);
//       activeCandidates.forEach(candidate => {
//         const warningType = candidate.minutes_since_notification >= 10 ? '2nd Warning' : '1st Warning';
//         console.log(`   👤 ${candidate.fullName} (${candidate.patientID}): ${Math.round(candidate.minutes_since_notification)}min → ${warningType}`);
//       });

//       // Step 4: Test the actual escalating notifications function (dry run)
//       console.log(`\n🚨 Testing escalating notifications function...`);

//       try {
//         // Note: This will actually call Zoho API in a real environment
//         // For testing, we might want to mock this or use a test configuration
//         console.log(`   ⚠️  Note: This will trigger actual Zoho API calls in production`);
//         console.log(`   🔄 Running escalating notifications...`);

//         await sendEscalatingNotifications();

//         console.log(`   ✅ Escalating notifications completed successfully`);

//       } catch (error) {
//         console.log(`   ⚠️  Escalating notifications error (expected in test environment): ${error}`);
//       }
//     }

//     // Step 5: Verify integration consistency
//     console.log(`\n🔍 INTEGRATION CONSISTENCY CHECK:`);
//     console.log(`   ✅ Uses same getPatientsInCurrentActiveSlot() function`);
//     console.log(`   ✅ Filters candidates to active slots only`);
//     console.log(`   ✅ Respects intelligent slot detection algorithm`);
//     console.log(`   ✅ Consistent with main notification system`);

//   } catch (error) {
//     await client.query('ROLLBACK');
//     console.error('❌ Escalating notifications test error:', error);
//     throw error;
//   } finally {
//     client.release();
//   }
// }

// async function main(): Promise<void> {
//   console.log('🧪 PROPER 30-MINUTE APPOINTMENT SLOT TEST');
//   console.log('📅 Test run:', new Date().toISOString());
//   console.log('🕐 Current Sydney time:', DateTime.now().setZone('Australia/Sydney').toFormat('h:mm:ss a, dd MMM yyyy'));
//   console.log('🎯 Scenario: Single 30-minute appointment slot with 50 patients, 3 doctors, realistic throttling');

//   try {
//     await createProper30MinSlotData();
//     setupDoctorTimers();
//     await testProper30MinSlotDetection();
//     await testDoctorThrottlingWithIntervals();
//     await testEscalatingNotificationsIntegration();

//     console.log('\n🎉 PROPER 30-MINUTE SLOT TEST COMPLETED SUCCESSFULLY!');

//   } catch (error) {
//     console.error('\n❌ TEST FAILED:', error);
//   } finally {
//     await cleanupTestData();
//     process.exit(0);
//   }
// }

// // Run the test
// main().catch(console.error);
