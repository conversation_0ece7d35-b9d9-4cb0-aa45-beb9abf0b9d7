import { WebClient } from '@slack/web-api';
import config from '../config';
import { logger } from '../config/logger';
import { DateTime } from 'luxon';
import { db } from '../utils/db';

export interface RequestModerationData {
  id: string;
  type: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  patient_id: string;
  patient_name?: string;
  email: string;
  total_score: number;
  max_score: number;
  is_eligible: boolean;
  status: string;
  created_at: string;
}

class RequestModerationService {
  private slack: WebClient;

  constructor() {
    this.slack = new WebClient(config.slackToken);
  }

  /**
   * Send Slack notification when a new request is submitted
   */
  async sendRequestSubmissionNotification(requestData: RequestModerationData): Promise<string | null> {
    const slackChannel = process.env.SLACK_MODERATION_CHANNEL;
    
    if (!slackChannel || !config.slackToken) {
      logger.info('No Slack moderation channel or token configured for request notifications');
      return null;
    }

    const requestType = requestData.type === 'thc_increase' ? 'THC Increase' :
                        requestData.type === 'extend_tp' ? 'Treatment Plan Extension' :
                        requestData.type === 'add_22_thc' ? '22% THC Addition' :
                        'Quantity Increase';
    const eligibilityStatus = requestData.is_eligible ? '✅ Eligible' : '❌ Not Eligible';
    const riskScore = `${requestData.total_score}/${requestData.max_score}`;
    
    // Format timestamp consistently with the application (12-hour format with AM/PM)
    const submittedAt = DateTime.fromISO(requestData.created_at).setZone('Australia/Sydney');
    const timestamp = submittedAt.toFormat('h:mm a, dd MMM yyyy');

    const messageText = `
🔔 **NEW ${requestType.toUpperCase()} REQUEST**

👤 *Patient:* (ID: ${requestData.patient_id})
📧 *Email:* ${requestData.email}
📊 *Risk Score:* ${riskScore} (${eligibilityStatus})
📝 *Request Type:* ${requestType}
⏰ *Submitted:* ${timestamp}

🔗 *Review:* ${config.devMode ? 'https://zenith.ad' : 'https://doctor.zenith.clinic'}/admin/requests
    `.trim();

    try {
      const response = await this.slack.chat.postMessage({
        channel: slackChannel,
        text: messageText,
        username: 'Request Moderation Bot',
        icon_emoji: ':clipboard:'
      });

      logger.info(`Slack notification sent for ${requestType} request ${requestData.id}`);

      // Store the Slack message timestamp in the database for threading
      if (response.ts) {
        await this.storeSlackMessageTimestamp(requestData.id, requestData.type, response.ts);
      }

      return response.ts || null;
    } catch (error) {
      logger.error('Failed to send Slack request submission notification:', error);
      return null;
    }
  }

  /**
   * Send Slack notification when a request is approved/rejected
   */
  async sendRequestModerationResultNotification(
    requestData: RequestModerationData,
    action: 'approved' | 'rejected',
    doctorName: string
  ): Promise<void> {
    const slackChannel = process.env.SLACK_MODERATION_CHANNEL;

    if (!slackChannel || !config.slackToken) {
      return;
    }

    // Get the original Slack message timestamp for threading
    const slackMessageTs = await this.getSlackMessageTimestamp(requestData.id, requestData.type);

    const requestType = requestData.type === 'thc_increase' ? 'THC Increase' :
                        requestData.type === 'extend_tp' ? 'Treatment Plan Extension' :
                        requestData.type === 'add_22_thc' ? '22% THC Addition' :
                        'Quantity Increase';
    const emoji = action === 'approved' ? '✅' : '❌';
    const actionText = action === 'approved' ? 'REQUEST APPROVED' : 'REQUEST REJECTED';

    // Format timestamp consistently with the application (12-hour format with AM/PM)
    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');

    const messageText = `${emoji} **${actionText}** by Dr. ${doctorName}
Patient: (${requestData.patient_id})
Request: ${requestType}
Time: ${timestamp}`;

    try {
      const messageOptions: {
        channel: string;
        text: string;
        username: string;
        icon_emoji: string;
        thread_ts?: string;
      } = {
        channel: slackChannel,
        text: messageText,
        username: 'Request Moderation Bot',
        icon_emoji: action === 'approved' ? ':white_check_mark:' : ':x:'
      };

      // Add thread_ts only if we have a valid Slack message timestamp
      if (slackMessageTs) {
        messageOptions.thread_ts = slackMessageTs;
      }

      await this.slack.chat.postMessage(messageOptions);

      logger.info(`Slack moderation result notification sent for ${requestType} request ${requestData.id}: ${action}`);
    } catch (error) {
      logger.error('Failed to send Slack request moderation result notification:', error);
    }
  }

  /**
   * Send test notification to verify Slack integration
   */
  async sendTestNotification(): Promise<void> {
    const slackChannel = process.env.SLACK_MODERATION_CHANNEL;
    
    if (!slackChannel || !config.slackToken) {
      throw new Error('Slack moderation channel or token not configured');
    }

    const now = DateTime.now().setZone('Australia/Sydney');
    const timestamp = now.toFormat('h:mm a, dd MMM yyyy');

    const messageText = `🧪 **TEST REQUEST MODERATION NOTIFICATION**

This is a test message to verify the request moderation Slack integration is working correctly.

Time: ${timestamp}
Environment: ${config.devMode ? 'Development/Staging' : 'Production'}`;

    try {
      await this.slack.chat.postMessage({
        channel: slackChannel,
        text: messageText,
        username: 'Request Moderation Bot',
        icon_emoji: ':test_tube:'
      });

      logger.info('Test request moderation notification sent successfully');
    } catch (error) {
      logger.error('Failed to send test request moderation notification:', error);
      throw error;
    }
  }

  /**
   * Store Slack message timestamp in the database for threading
   */
  private async storeSlackMessageTimestamp(
    requestId: string,
    requestType: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase',
    slackMessageTs: string
  ): Promise<void> {
    const client = await db.connect();
    try {
      const table = requestType === 'thc_increase' ? 'thc_increase_questionnaire' :
                    requestType === 'extend_tp' ? 'extend_tp_questionnaire' :
                    requestType === 'add_22_thc' ? 'add_22_thc_questionnaire' :
                    'quantity_increase_questionnaire';
      const updateQuery = `
        UPDATE ${table}
        SET slack_message_ts = $1
        WHERE id = $2
      `;
      await client.query(updateQuery, [slackMessageTs, requestId]);
      logger.info(`Stored Slack message timestamp for ${requestType} request ${requestId}: ${slackMessageTs}`);
    } catch (error) {
      logger.error(`Failed to store Slack message timestamp for ${requestType} request ${requestId}:`, error);
    } finally {
      client.release();
    }
  }

  /**
   * Retrieve Slack message timestamp from the database for threading
   */
  private async getSlackMessageTimestamp(
    requestId: string,
    requestType: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase'
  ): Promise<string | null> {
    const client = await db.connect();
    try {
      const table = requestType === 'thc_increase' ? 'thc_increase_questionnaire' :
                    requestType === 'extend_tp' ? 'extend_tp_questionnaire' :
                    requestType === 'add_22_thc' ? 'add_22_thc_questionnaire' :
                    'quantity_increase_questionnaire';
      const selectQuery = `
        SELECT slack_message_ts
        FROM ${table}
        WHERE id = $1
      `;
      const result = await client.query(selectQuery, [requestId]);

      if (result.rows.length > 0) {
        const slackMessageTs = result.rows[0].slack_message_ts;
        logger.info(`Retrieved Slack message timestamp for ${requestType} request ${requestId}: ${slackMessageTs || 'none'}`);
        return slackMessageTs;
      }

      logger.warn(`No Slack message timestamp found for ${requestType} request ${requestId}`);
      return null;
    } catch (error) {
      logger.error(`Failed to retrieve Slack message timestamp for ${requestType} request ${requestId}:`, error);
      return null;
    } finally {
      client.release();
    }
  }
}

// Export singleton instance
export const requestModerationService = new RequestModerationService();
