import { Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { usePatient } from "../../../hooks/patient-provider";
import { QuestionnaireShape } from "../../../types";
import { questionnaireQuestions } from "../../../utils";
import { useEffect, useState } from "react";

const QuestionnaireForm: React.FC = () => {
    const { selectedPatientHistory } = usePatient()
    const [data, setData] = useState<QuestionnaireShape | undefined>(undefined)
    // let registrationQuestionnaire = selectedPatientHistory as QuestionnaireShape

    useEffect(() => {
        setData(selectedPatientHistory as QuestionnaireShape)
    }, [selectedPatientHistory])

    // Split questions into two columns
    const leftColumnQuestions = [
        'Condition',
        'What_condition_or_symptom_are_you_having_issues_wi',
        'Are_you_planning_to_have_children_in_the_near_futu',
        'Do_you_suffer_from_any_cardiovascular_diseases_in'
    ];

    const rightColumnQuestions = [
        'Do_you_have_an_addiction_to_any_psychoactive_subst',
        'What_was_your_gender_at_birth',
        'Please_add_the_first_medication_treatment_or_ther',
        'Please_add_the_second_medication_treatment_or_the'
    ];

    const renderQuestionColumn = (questions: string[]) => {
        return questions.map((key, index) => {
            const q = data?.data.find(d => d.question === key)
            return (
                <Grid container direction={'column'} spacing={1} sx={{ mb: 3 }} key={index}>
                    <Typography sx={{ fontSize: '14px', fontWeight: 'medium' }}>
                        {index + 1}. {questionnaireQuestions[key]}
                    </Typography>
                    <Grid sx={{ 
                        backgroundColor: '#E9FFD1', 
                        p: 1,
                        maxHeight: key === 'Condition' ? '40px' : 'auto',
                        overflow: 'auto'
                    }}>
                        <Typography sx={{ 
                            fontWeight: 'bold', 
                            fontSize: '14px',
                            whiteSpace: key === 'Condition' ? 'nowrap' : 'normal',
                            textOverflow: key === 'Condition' ? 'ellipsis' : 'clip'
                        }}>
                            {q?.answers || 'No Answer'}
                        </Typography>
                    </Grid>
                </Grid>
            )
        })
    }

    return (
        <Grid container spacing={4}>
            <Grid size={6}>
                {renderQuestionColumn(leftColumnQuestions)}
            </Grid>
            <Grid size={6}>
                {renderQuestionColumn(rightColumnQuestions)}
            </Grid>
        </Grid>
    )
}

export default QuestionnaireForm
