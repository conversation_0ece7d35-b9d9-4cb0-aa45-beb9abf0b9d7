import React, { createContext, useContext, useState, useEffect } from 'react';
import { PatientData, PatientTreatmentPlan } from '../types';

export interface ChatEmailContextType {
    patient: PatientData | null;
    treatmentPlan: PatientTreatmentPlan | null;
    setTreatmentPlan: (plan: PatientTreatmentPlan | null) => void;
    emailContent: string;
    setEmailContent: (content: string) => void;
    strainAdvice: string;
    setStrainAdvice: (advice: string) => void;
}

const ChatEmailContext = createContext<ChatEmailContextType | undefined>(undefined);

export const ChatEmailProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [patient, setPatient] = useState<PatientData | null>(null);
    const [treatmentPlan, setTreatmentPlan] = useState<PatientTreatmentPlan | null>(null);
    const [emailContent, setEmailContent] = useState<string>('');
    const [strainAdvice, setStrainAdvice] = useState<string>('');

    // Load data from localStorage on component mount
    useEffect(() => {
        const savedPlan = localStorage.getItem('TpPlan');
        const savedStrainAdvice = localStorage.getItem('strainAdvice');
        
        
        
        if (savedPlan) {
            
            const plan = JSON.parse(savedPlan) as PatientTreatmentPlan;
            setTreatmentPlan(plan);
            setPatient(plan.patient || null);
        }
        
        if (savedStrainAdvice) {
            console.log('ChatEmailProvider: Found saved strain advice');
            setStrainAdvice(savedStrainAdvice);
        }
    }, []);
    
    // Save strain advice to localStorage whenever it changes
    useEffect(() => {
        if (strainAdvice) {
            
            localStorage.setItem('strainAdvice', strainAdvice);
        }
    }, [strainAdvice]);
    
    // Create a custom setter that enhances the built-in setState with logging
    const setStrainAdviceWithLogging = (advice: string) => {
        
        setStrainAdvice(advice);
    };

    const value = {
        patient,
        treatmentPlan,
        setTreatmentPlan,
        emailContent,
        setEmailContent,
        strainAdvice,
        setStrainAdvice: setStrainAdviceWithLogging,
    };

    return (
        <ChatEmailContext.Provider value={value}>
            {children}
        </ChatEmailContext.Provider>
    );
};

export const useChatEmail = () => {
    const context = useContext(ChatEmailContext);
    if (context === undefined) {
        throw new Error('useChatEmail must be used within a ChatEmailProvider');
    }
    return context;
};

export default ChatEmailProvider; 