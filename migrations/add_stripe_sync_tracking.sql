-- Migration: Add Stripe Sync Tracking
-- Description: Add sync tracking fields to stripe_payments table and create stripe_sync_log table
-- Date: 2025-01-21

-- Add sync tracking fields to existing stripe_payments table
ALTER TABLE stripe_payments 
ADD COLUMN IF NOT EXISTS synced_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS sync_source TEXT DEFAULT 'webhook';

-- <PERSON>reate index on sync tracking fields for performance
CREATE INDEX IF NOT EXISTS idx_stripe_payments_synced_at ON stripe_payments(synced_at);
CREATE INDEX IF NOT EXISTS idx_stripe_payments_sync_source ON stripe_payments(sync_source);

-- Create stripe_sync_log table for tracking sync operations
CREATE TABLE IF NOT EXISTS stripe_sync_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sync_date DATE NOT NULL,
  sync_type TEXT NOT NULL CHECK (sync_type IN ('daily', 'weekly', 'manual', 'backfill')),
  payments_synced INTEGER DEFAULT 0,
  sync_status TEXT NOT NULL CHECK (sync_status IN ('running', 'success', 'partial', 'failed')),
  error_message TEXT,
  started_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for stripe_sync_log table
CREATE INDEX IF NOT EXISTS idx_stripe_sync_log_sync_date ON stripe_sync_log(sync_date);
CREATE INDEX IF NOT EXISTS idx_stripe_sync_log_sync_type ON stripe_sync_log(sync_type);
CREATE INDEX IF NOT EXISTS idx_stripe_sync_log_sync_status ON stripe_sync_log(sync_status);
CREATE INDEX IF NOT EXISTS idx_stripe_sync_log_started_at ON stripe_sync_log(started_at);

-- Add comments for documentation
COMMENT ON TABLE stripe_sync_log IS 'Tracks Stripe API sync operations and their status';
COMMENT ON COLUMN stripe_sync_log.sync_date IS 'The date being synced (YYYY-MM-DD format)';
COMMENT ON COLUMN stripe_sync_log.sync_type IS 'Type of sync operation: daily, weekly, manual, or backfill';
COMMENT ON COLUMN stripe_sync_log.payments_synced IS 'Number of payments processed in this sync';
COMMENT ON COLUMN stripe_sync_log.sync_status IS 'Status of sync: running, success, partial, or failed';
COMMENT ON COLUMN stripe_sync_log.error_message IS 'Error details if sync failed or had issues';

COMMENT ON COLUMN stripe_payments.synced_at IS 'Timestamp when this payment was last synced from Stripe API';
COMMENT ON COLUMN stripe_payments.sync_source IS 'Source of payment data: webhook or api';

-- Update existing webhook-sourced payments to have sync_source = 'webhook'
UPDATE stripe_payments 
SET sync_source = 'webhook' 
WHERE sync_source IS NULL;

-- Make sync_source NOT NULL after updating existing records
ALTER TABLE stripe_payments 
ALTER COLUMN sync_source SET NOT NULL;

-- Verify the migration
DO $$
BEGIN
  -- Check if columns were added successfully
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'stripe_payments' AND column_name = 'synced_at'
  ) THEN
    RAISE EXCEPTION 'Migration failed: synced_at column not added to stripe_payments';
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'stripe_payments' AND column_name = 'sync_source'
  ) THEN
    RAISE EXCEPTION 'Migration failed: sync_source column not added to stripe_payments';
  END IF;

  -- Check if stripe_sync_log table was created
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'stripe_sync_log'
  ) THEN
    RAISE EXCEPTION 'Migration failed: stripe_sync_log table not created';
  END IF;

  RAISE NOTICE 'Migration completed successfully: Stripe sync tracking added';
END $$;
