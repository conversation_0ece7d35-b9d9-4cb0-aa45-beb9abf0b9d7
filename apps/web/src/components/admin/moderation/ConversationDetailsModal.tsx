import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  <PERSON>ton,
  Box,
  Typography,
  Chip,
  Divider,
  Card,
  CardContent,
  IconButton,
  CircularProgress,
  List,
  ListItem,
  Avatar
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Close as CloseIcon,
  AttachFile as AttachFileIcon,
  Person as PersonIcon,
  MedicalServices as TreatmentIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
  AudioFile as AudioIcon,
  InsertDriveFile as FileIcon,
  Download as DownloadIcon,
  OpenInNew as OpenIcon,
  Fullscreen as FullscreenIcon,
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import { ConversationModeration } from '../../../services/moderation.service';
import moderationService from '../../../services/moderation.service';

// Type definitions for Stream Chat attachments
interface StreamChatAttachment {
  id?: string;
  type?: string;
  title?: string;
  asset_url?: string;
  image_url?: string;
  file_size?: number;
  mime_type?: string;
  fallback?: string;
  text?: string;
  thumb_url?: string;
  title_link?: string;
  og_scrape_url?: string;
}

// interface StreamChatMessage {
//   id: string;
//   text: string;
//   user: {
//     id: string;
//     name?: string;
//   };
//   created_at: string;
//   attachments?: StreamChatAttachment[];
// }

interface ConversationDetailsModalProps {
  open: boolean;
  conversation: ConversationModeration | null;
  onClose: () => void;
  onModerate?: (channelId: string, action: 'approve' | 'reject') => void;
  processing?: boolean;
}

const ConversationDetailsModal: React.FC<ConversationDetailsModalProps> = ({
  open,
  conversation,
  onClose,
  onModerate,
  processing = false
}) => {
  const [conversationMessages, setConversationMessages] = useState<any[]>([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [messagesError, setMessagesError] = useState<string | null>(null);
  const [fullscreenImage, setFullscreenImage] = useState<{ url: string; title: string } | null>(null);

  // Fetch real messages from Stream Chat when the modal opens
  useEffect(() => {
    if (open && conversation?.channelId) {
      const fetchMessages = async () => {
        setLoadingMessages(true);
        setMessagesError(null);
        try {
          const response = await moderationService.getConversationMessages(conversation.channelId);
          if (response.success) {
           
            setConversationMessages(response.data.messages);
          } else {
            setMessagesError('Failed to load messages');
          }
        } catch (error) {
          console.error('Error fetching conversation messages:', error);
          setMessagesError('Failed to load messages');
        } finally {
          setLoadingMessages(false);
        }
      };

      fetchMessages();
    }
  }, [open, conversation?.channelId]);

  if (!conversation) return null;

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('h:mm a, dd MMM yyyy');
  };

  const formatTimeOnly = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('h:mm a');
  };

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType?: string, type?: string): JSX.Element => {
    if (!mimeType && !type) return <FileIcon />;

    const mime = mimeType?.toLowerCase() || '';
    const fileType = type?.toLowerCase() || '';

    if (mime.startsWith('image/') || fileType === 'image') {
      return <ImageIcon sx={{ color: '#4CAF50' }} />;
    }
    if (mime.startsWith('video/') || fileType === 'video') {
      return <VideoIcon sx={{ color: '#2196F3' }} />;
    }
    if (mime.startsWith('audio/') || fileType === 'audio') {
      return <AudioIcon sx={{ color: '#FF9800' }} />;
    }
    return <FileIcon sx={{ color: '#757575' }} />;
  };

  const handleModerate = (action: 'approve' | 'reject') => {
    if (onModerate) {
      onModerate(conversation.channelId, action);
    }
  };

  // Check if conversation can be reversed (for moderated conversations)
  const canReverse = (conversation: ConversationModeration): boolean => {
    if (conversation.moderationStatus === 'pending' || !conversation.moderatedAt) return false;

    const moderatedTime = new Date(conversation.moderatedAt);
    const currentTime = new Date();
    const diffMinutes = (currentTime.getTime() - moderatedTime.getTime()) / (1000 * 60);

    return diffMinutes <= 30;
  };

  // Mock conversation messages - in real implementation, fetch from Stream Chat API
  const mockMessages = [
    {
      id: '1',
      text: conversation.firstMessageText || 'Hello, I need help with my prescription',
      timestamp: conversation.firstMessageAt || conversation.createdAt,
      user: { id: `p_${conversation.patientId}`, name: conversation.patientName }
    },
    {
      id: '2', 
      text: conversation.lastMessageText || 'When can I expect to receive it?',
      timestamp: conversation.lastMessageAt || conversation.createdAt,
      user: { id: `p_${conversation.patientId}`, name: conversation.patientName }
    }
  ];

  const renderConversationMessages = () => {
    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Messages ({conversation.messageCount})
        </Typography>

        {loadingMessages ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={24} />
            <Typography variant="body2" sx={{ ml: 2 }}>Loading messages...</Typography>
          </Box>
        ) : messagesError ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="error">{messagesError}</Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Showing preview from database instead.
            </Typography>
            {/* Fallback to mock messages */}
            <List sx={{ bgcolor: 'grey.50', borderRadius: 2, maxHeight: 300, overflow: 'auto', mt: 2 }}>
              {mockMessages.map((msg, index) => (
                <ListItem key={msg.id || index} sx={{
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  borderBottom: index < mockMessages.length - 1 ? '1px solid #e0e0e0' : 'none'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1, width: '100%' }}>
                    <Avatar sx={{ width: 24, height: 24, bgcolor: 'primary.main' }}>
                      <PersonIcon sx={{ fontSize: 16 }} />
                    </Avatar>
                    <Typography variant="body2" fontWeight="bold">
                      {msg.user?.name || conversation.patientName}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
                      {formatTimeOnly(msg.timestamp)}
                    </Typography>
                  </Box>
                  <Box sx={{
                    bgcolor: 'white',
                    p: 2,
                    borderRadius: 2,
                    width: '100%',
                    border: '1px solid #e0e0e0'
                  }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {msg.text}
                    </Typography>
                  </Box>
                </ListItem>
              ))}
            </List>
          </Box>
        ) : (
          <>
            <List sx={{ bgcolor: 'grey.50', borderRadius: 2, maxHeight: 300, overflow: 'auto' }}>
              {conversationMessages.map((msg, index) => (
                <ListItem key={msg.id || index} sx={{
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  borderBottom: index < conversationMessages.length - 1 ? '1px solid #e0e0e0' : 'none'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1, width: '100%' }}>
                    <Avatar sx={{ width: 24, height: 24, bgcolor: 'primary.main' }}>
                      <PersonIcon sx={{ fontSize: 16 }} />
                    </Avatar>
                    <Typography variant="body2" fontWeight="bold">
                      {msg.user?.name || conversation.patientName}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 'auto' }}>
                      {formatTimeOnly(msg.created_at || msg.timestamp)}
                    </Typography>
                  </Box>
                  <Box sx={{
                    bgcolor: 'white',
                    p: 2,
                    borderRadius: 2,
                    width: '100%',
                    border: '1px solid #e0e0e0'
                  }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {msg.text}
                    </Typography>
                    {msg.attachments && msg.attachments.length > 0 && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                          Attachments ({msg.attachments.length}):
                        </Typography>
                        {msg.attachments.map((attachment: StreamChatAttachment, attIndex: number) => {
                          // Get the best available URL for the attachment - try all possible URL fields
                          const attachmentUrl = attachment.asset_url || attachment.image_url || attachment.thumb_url || attachment.title_link || attachment.og_scrape_url;
                          const attachmentTitle = attachment.title || attachment.fallback || `Attachment ${attIndex + 1}`;
                          const attachmentType = attachment.type || 'file';

                          return (
                          <Card key={attIndex} sx={{ mb: 1, border: '1px solid #e0e0e0' }}>
                            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                {getFileIcon(attachment.mime_type, attachmentType)}
                                <Box sx={{ flex: 1, minWidth: 0 }}>
                                  <Typography variant="body2" fontWeight="bold" sx={{
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap'
                                  }}>
                                    {attachmentTitle}
                                  </Typography>
                                  <Box sx={{ display: 'flex', gap: 2, mt: 0.5 }}>
                                    <Typography variant="caption" color="text.secondary">
                                      Type: {attachmentType}
                                    </Typography>
                                    {attachment.mime_type && (
                                      <Typography variant="caption" color="text.secondary">
                                        MIME: {attachment.mime_type}
                                      </Typography>
                                    )}
                                    {attachment.file_size && (
                                      <Typography variant="caption" color="text.secondary">
                                        Size: {formatFileSize(attachment.file_size)}
                                      </Typography>
                                    )}
                                  </Box>

                                </Box>
                                {attachmentUrl && (
                                  <Box sx={{ display: 'flex', gap: 1 }}>
                                    {(attachment.mime_type?.startsWith('image/') || attachmentType === 'image') && (
                                      <IconButton
                                        size="small"
                                        onClick={() => window.open(attachmentUrl, '_blank')}
                                        title="View image"
                                      >
                                        <OpenIcon sx={{ fontSize: 16 }} />
                                      </IconButton>
                                    )}
                                    <IconButton
                                      size="small"
                                      onClick={() => window.open(attachmentUrl, '_blank')}
                                      title="Download file"
                                    >
                                      <DownloadIcon sx={{ fontSize: 16 }} />
                                    </IconButton>
                                  </Box>
                                )}
                              </Box>

                              {/* Show message when no URL is available */}
                              {!attachmentUrl && (
                                <Box sx={{ mt: 2, p: 2, backgroundColor: '#fff3cd', borderRadius: 1, border: '1px solid #ffeaa7' }}>
                                  <Typography variant="body2" color="text.secondary">
                                    ⚠️ No URL available for this attachment. This might be a Stream Chat attachment that hasn't been fully processed or uploaded yet.
                                  </Typography>
                                </Box>
                              )}

                              {attachmentUrl && (
                                <Box sx={{ mt: 2 }}>
                                  {/* Image Preview */}
                                  {(attachment.mime_type?.startsWith('image/') || attachmentType === 'image') && attachmentUrl && (
                                    <Box sx={{
                                      border: '1px solid #e0e0e0',
                                      borderRadius: 2,
                                      overflow: 'hidden',
                                      backgroundColor: '#f5f5f5',
                                      position: 'relative',
                                      '&:hover .image-overlay': {
                                        opacity: 1
                                      }
                                    }}>
                                      <img
                                        src={attachmentUrl}
                                        alt={attachmentTitle}
                                        style={{
                                          width: '100%',
                                          maxHeight: '300px',
                                          objectFit: 'contain',
                                          display: 'block',
                                          cursor: 'pointer'
                                        }}
                                        onClick={() => setFullscreenImage({
                                          url: attachmentUrl!,
                                          title: attachmentTitle
                                        })}
                                        onError={(e) => {
                                          console.error('Failed to load image:', attachmentUrl);
                                          (e.target as HTMLImageElement).style.display = 'none';
                                        }}
                                      />
                                      <Box
                                        className="image-overlay"
                                        sx={{
                                          position: 'absolute',
                                          top: 8,
                                          right: 8,
                                          opacity: 0,
                                          transition: 'opacity 0.2s',
                                          display: 'flex',
                                          gap: 1
                                        }}
                                      >
                                        <IconButton
                                          size="small"
                                          sx={{
                                            backgroundColor: 'rgba(0,0,0,0.7)',
                                            color: 'white',
                                            '&:hover': {
                                              backgroundColor: 'rgba(0,0,0,0.9)'
                                            }
                                          }}
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            setFullscreenImage({
                                              url: attachmentUrl!,
                                              title: attachmentTitle
                                            });
                                          }}
                                          title="View fullscreen"
                                        >
                                          <FullscreenIcon sx={{ fontSize: 16 }} />
                                        </IconButton>
                                      </Box>
                                    </Box>
                                  )}

                                  {/* Video Preview */}
                                  {attachment.mime_type?.startsWith('video/') && (
                                    <Box sx={{
                                      border: '1px solid #e0e0e0',
                                      borderRadius: 2,
                                      overflow: 'hidden',
                                      backgroundColor: '#000',
                                      position: 'relative'
                                    }}>
                                      <video
                                        controls
                                        style={{
                                          width: '100%',
                                          maxHeight: '300px',
                                          display: 'block'
                                        }}
                                        preload="metadata"
                                        poster="" // You could add a poster image here
                                      >
                                        <source src={attachment.asset_url} type={attachment.mime_type} />
                                        Your browser does not support the video tag.
                                      </video>
                                      <Box sx={{
                                        position: 'absolute',
                                        top: 8,
                                        right: 8,
                                        display: 'flex',
                                        gap: 1
                                      }}>
                                        <IconButton
                                          size="small"
                                          sx={{
                                            backgroundColor: 'rgba(0,0,0,0.7)',
                                            color: 'white',
                                            '&:hover': {
                                              backgroundColor: 'rgba(0,0,0,0.9)'
                                            }
                                          }}
                                          onClick={() => window.open(attachment.asset_url, '_blank')}
                                          title="Open in new tab"
                                        >
                                          <OpenIcon sx={{ fontSize: 16 }} />
                                        </IconButton>
                                      </Box>
                                    </Box>
                                  )}

                                  {/* Audio Preview */}
                                  {attachment.mime_type?.startsWith('audio/') && (
                                    <Box sx={{
                                      border: '1px solid #e0e0e0',
                                      borderRadius: 2,
                                      p: 2,
                                      backgroundColor: '#f5f5f5'
                                    }}>
                                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                                        <AudioIcon sx={{ fontSize: 32, color: '#FF9800' }} />
                                        <Typography variant="body2" fontWeight="bold">
                                          Audio File
                                        </Typography>
                                      </Box>
                                      <audio
                                        controls
                                        style={{
                                          width: '100%'
                                        }}
                                        preload="metadata"
                                      >
                                        <source src={attachment.asset_url} type={attachment.mime_type} />
                                        Your browser does not support the audio tag.
                                      </audio>
                                    </Box>
                                  )}

                                  {/* PDF Preview */}
                                  {attachment.mime_type === 'application/pdf' && (
                                    <Box sx={{
                                      border: '1px solid #e0e0e0',
                                      borderRadius: 2,
                                      overflow: 'hidden'
                                    }}>
                                      <iframe
                                        src={`${attachment.asset_url}#toolbar=1&navpanes=1&scrollbar=1`}
                                        style={{
                                          width: '100%',
                                          height: '400px',
                                          border: 'none'
                                        }}
                                        title={attachment.title || 'PDF Document'}
                                      />
                                    </Box>
                                  )}

                                  {/* Text File Preview */}
                                  {(attachment.mime_type?.startsWith('text/') ||
                                    attachment.mime_type === 'application/json' ||
                                    attachment.title?.endsWith('.txt') ||
                                    attachment.title?.endsWith('.json') ||
                                    attachment.title?.endsWith('.csv')) && (
                                    <Box sx={{
                                      border: '1px solid #e0e0e0',
                                      borderRadius: 2,
                                      p: 2,
                                      backgroundColor: '#f5f5f5',
                                      maxHeight: '300px',
                                      overflow: 'auto'
                                    }}>
                                      <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                                        Text File Preview:
                                      </Typography>
                                      <iframe
                                        src={attachment.asset_url}
                                        style={{
                                          width: '100%',
                                          height: '250px',
                                          border: 'none',
                                          backgroundColor: 'white',
                                          borderRadius: '4px'
                                        }}
                                        title={attachment.title || 'Text Document'}
                                      />
                                    </Box>
                                  )}

                                  {/* Generic File Preview for unsupported types */}
                                  {!attachment.mime_type?.startsWith('image/') &&
                                   attachmentType !== 'image' &&
                                   !attachment.mime_type?.startsWith('video/') &&
                                   !attachment.mime_type?.startsWith('audio/') &&
                                   !attachment.mime_type?.startsWith('text/') &&
                                   attachment.mime_type !== 'application/pdf' &&
                                   attachment.mime_type !== 'application/json' &&
                                   !attachment.title?.endsWith('.txt') &&
                                   !attachment.title?.endsWith('.json') &&
                                   !attachment.title?.endsWith('.csv') && (
                                    <Box sx={{
                                      border: '1px solid #e0e0e0',
                                      borderRadius: 2,
                                      p: 3,
                                      backgroundColor: '#f5f5f5',
                                      textAlign: 'center'
                                    }}>
                                      <FileIcon sx={{ fontSize: 48, color: '#757575', mb: 1 }} />
                                      <Typography variant="body2" color="text.secondary">
                                        Preview not available for this file type
                                      </Typography>
                                      <Typography variant="caption" color="text.secondary">
                                        Click download to view the file
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                              )}
                            </CardContent>
                          </Card>
                          )
                        })}
                      </Box>
                    )}
                  </Box>
                </ListItem>
              ))}
            </List>

            {conversationMessages.length > 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
                Showing all {conversationMessages.length} messages from this conversation.
              </Typography>
            )}
          </>
        )}
      </Box>
    );
  };

  return (
    <React.Fragment>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        Conversation Details
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {/* Patient Information */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary">
              👤 Patient Information
            </Typography>
            <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
              <Box>
                <Typography variant="body2" color="text.secondary">Name</Typography>
                <Typography variant="body1" fontWeight="bold">{conversation.patientName}</Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Patient ID</Typography>
                <Typography variant="body1">{conversation.patientId}</Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Channel ID</Typography>
                <Typography variant="body1" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                  {conversation.channelId}
                </Typography>
              </Box>
              {conversation.treatmentOutcome && (
                <Box>
                  <Typography variant="body2" color="text.secondary">Treatment Plan</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TreatmentIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                    <Typography variant="body1">{conversation.treatmentOutcome}</Typography>
                  </Box>
                </Box>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Conversation Content */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary">
              💬 Conversation Content
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 4, mb: 2 }}>
              <Box>
                <Typography variant="body2" color="text.secondary">Message Count</Typography>
                <Typography variant="body1" fontWeight="bold">{conversation.messageCount} messages</Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Started</Typography>
                <Typography variant="body1">{formatTimestamp(conversation.firstMessageAt || conversation.createdAt)}</Typography>
              </Box>
              {conversation.hasAttachments && (
                <Box>
                  <Typography variant="body2" color="text.secondary">Attachments</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AttachFileIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                    <Typography variant="body1">Yes</Typography>
                  </Box>
                </Box>
              )}
            </Box>

            {renderConversationMessages()}
          </CardContent>
        </Card>

        {/* Timeline */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary">
              ⏰ Timeline
            </Typography>
            <Box sx={{ display: 'flex', gap: 4, flexWrap: 'wrap' }}>
              <Box>
                <Typography variant="body2" color="text.secondary">Started</Typography>
                <Typography variant="body1">{formatTimestamp(conversation.firstMessageAt || conversation.createdAt)}</Typography>
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">Last Message</Typography>
                <Typography variant="body1">{formatTimestamp(conversation.lastMessageAt || conversation.createdAt)}</Typography>
              </Box>
              {conversation.moderatedAt && (
                <Box>
                  <Typography variant="body2" color="text.secondary">Moderated</Typography>
                  <Typography variant="body1">{formatTimestamp(conversation.moderatedAt)}</Typography>
                </Box>
              )}
            </Box>
          </CardContent>
        </Card>

        {/* Moderation Status */}
        {conversation.moderationStatus !== 'pending' && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                🛡️ Moderation Status
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Chip
                  icon={conversation.moderationStatus === 'approved' ? <ApproveIcon /> : <RejectIcon />}
                  label={`CONVERSATION ${conversation.moderationStatus.toUpperCase()}`}
                  size="medium"
                  sx={{
                    backgroundColor: conversation.moderationStatus === 'approved' ? '#008000' : undefined,
                    color: conversation.moderationStatus === 'approved' ? 'white' : undefined,
                    '& .MuiChip-icon': {
                      color: conversation.moderationStatus === 'approved' ? 'white' : undefined
                    }
                  }}
                  color={conversation.moderationStatus === 'approved' ? undefined : 'error'}
                />
              </Box>
              <Box sx={{ display: 'flex', gap: 4 }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Moderator</Typography>
                  <Typography variant="body1">{conversation.moderatorName || conversation.moderatedBy || 'N/A'}</Typography>
                </Box>
                {conversation.moderationReason && (
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" color="text.secondary">Reason</Typography>
                    <Typography variant="body1">{conversation.moderationReason}</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>

        {/* Show moderation buttons for pending conversations */}
        {conversation.moderationStatus === 'pending' && onModerate && (
          <>
            <Button
              onClick={() => handleModerate('reject')}
              variant="contained"
              color="error"
              startIcon={processing ? <CircularProgress size={16} /> : <RejectIcon />}
              disabled={processing}
            >
              {processing ? 'Processing...' : 'Reject Conversation'}
            </Button>
            <Button
              onClick={() => handleModerate('approve')}
              variant="contained"
              startIcon={processing ? <CircularProgress size={16} /> : <ApproveIcon />}
              disabled={processing}
              sx={{
                backgroundColor: '#008000',
                color: 'white',
                '&:hover': {
                  backgroundColor: '#006600'
                }
              }}
            >
              {processing ? 'Processing...' : 'Approve Conversation'}
            </Button>
          </>
        )}

        {/* Show reverse buttons for moderated conversations (within 30 minutes) */}
        {conversation.moderationStatus !== 'pending' && onModerate && canReverse(conversation) && (
          <Button
            onClick={() => handleModerate(conversation.moderationStatus === 'approved' ? 'reject' : 'approve')}
            variant="contained"
            color={conversation.moderationStatus === 'approved' ? 'error' : 'primary'}
            startIcon={processing ? <CircularProgress size={16} /> :
              (conversation.moderationStatus === 'approved' ? <RejectIcon /> : <ApproveIcon />)}
            disabled={processing}
            sx={conversation.moderationStatus === 'rejected' ? {
              backgroundColor: '#008000',
              color: 'white',
              '&:hover': {
                backgroundColor: '#006600'
              }
            } : undefined}
          >
            {processing ? 'Processing...' :
              `Reverse to ${conversation.moderationStatus === 'approved' ? 'Rejected' : 'Approved'}`}
          </Button>
        )}
      </DialogActions>
      </Dialog>

      {/* Fullscreen Image Modal */}
      <Dialog
        open={!!fullscreenImage}
        onClose={() => setFullscreenImage(null)}
        maxWidth={false}
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: 'rgba(0,0,0,0.9)',
            boxShadow: 'none',
            margin: 0,
            maxHeight: '100vh',
            maxWidth: '100vw'
          }
        }}
      >
        <DialogTitle sx={{
          color: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: 'rgba(0,0,0,0.8)'
        }}>
          <Typography variant="h6" sx={{ color: 'white' }}>
            {fullscreenImage?.title || 'Image Preview'}
          </Typography>
          <IconButton
            onClick={() => setFullscreenImage(null)}
            sx={{ color: 'white' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{
          p: 0,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0,0,0,0.9)',
          minHeight: '70vh'
        }}>
          {fullscreenImage && (
            <img
              src={fullscreenImage.url}
              alt={fullscreenImage.title}
              style={{
                maxWidth: '100%',
                maxHeight: '80vh',
                objectFit: 'contain',
                cursor: 'zoom-in'
              }}
              onClick={() => window.open(fullscreenImage.url, '_blank')}
            />
          )}
        </DialogContent>
        <DialogActions sx={{
          backgroundColor: 'rgba(0,0,0,0.8)',
          justifyContent: 'center'
        }}>
          <Button
            onClick={() => fullscreenImage && window.open(fullscreenImage.url, '_blank')}
            startIcon={<OpenIcon />}
            sx={{ color: 'white' }}
          >
            Open in New Tab
          </Button>
          <Button
            onClick={() => fullscreenImage && window.open(fullscreenImage.url, '_blank')}
            startIcon={<DownloadIcon />}
            sx={{ color: 'white' }}
          >
            Download
          </Button>
          <Button
            onClick={() => setFullscreenImage(null)}
            sx={{ color: 'white' }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </React.Fragment>
  );
};

export default ConversationDetailsModal;
