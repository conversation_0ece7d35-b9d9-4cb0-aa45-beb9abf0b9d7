import React, { useState } from "react";
import { Checkbox, List, ListItem, ListItemButton, ListItemIcon, TextField, Typography, IconButton, Collapse } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useEmailContext } from "../../../hooks/email-provider";
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

type ListType = 'option1' | 'option2' | 'option3' | 'option4' | 'option5' | 'option6'
const inputStyle = {
    width: '100%',
    '& .MuiInputBase-root': {
        borderRadius: '5px',
        borderColor: 'transparent',
        backgroundColor: 'white',
        fontSize: '14px'
    },
    '& .MuiOutlinedInput-root': {
        '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.2)',
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.5)',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'rgba(0, 0, 0, 0.5)',
        }
    },
}

const EmailStrainAdvice: React.FC = () => {
    const {
        checkedSativa,
        setCheckedSativa,
        checkedIndica,
        setCheckedIndica,
        checkedHybrid,
        setCheckedHybrid,
        listValuesSativa,
        setListValuesSativa,
        listValuesIndica,
        setListValuesIndica,
        listValuesHybrid,
        setListValuesHybrid
    } = useEmailContext()

    const [open, setOpen] = useState(true);
    const wordLimit = 240 

    const handleToggleSativa = (value: ListType) => () => {
        const currentIndex = checkedSativa.indexOf(listValuesSativa[value])
        const newChecked = [...checkedSativa];

        if (currentIndex === -1) {
            newChecked.push(listValuesSativa[value]);
        } else {
            newChecked.splice(currentIndex, 1);
        }

        setCheckedSativa(newChecked);
    };

    const handleListTextChangeSativa = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target
        const currentIndex = checkedSativa.indexOf(listValuesSativa[name as ListType])

        if (value.length <= wordLimit) {
            setListValuesSativa((prevValues) => ({
                ...prevValues,
                [name]: value
            }))

            if (currentIndex > -1) {
                setCheckedSativa(prevChecked => {
                    const newChecked = [...prevChecked];
                    newChecked[currentIndex] = value;
                    return newChecked;
                });
            }
        }
    }

    const handleToggleIndica = (value: ListType) => () => {
        const currentIndex = checkedIndica.indexOf(listValuesIndica[value])
        const newChecked = [...checkedIndica];

        if (currentIndex === -1) {
            newChecked.push(listValuesIndica[value]);
        } else {
            newChecked.splice(currentIndex, 1);
        }

        setCheckedIndica(newChecked);
    };
    const handleListTextChangeIndica = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target
        const currentIndex = checkedIndica.indexOf(listValuesIndica[name as ListType])

        if (value.length <= wordLimit) {
            setListValuesIndica((prevValues) => ({
                ...prevValues,
                [name]: value
            }))

            if (currentIndex > -1) {
                setCheckedIndica(prevChecked => {
                    const newChecked = [...prevChecked];
                    newChecked[currentIndex] = value;
                    return newChecked;
                });
            }
        }

    }

    const handleToggleHybrid = (value: ListType) => () => {
        const currentIndex = checkedHybrid.indexOf(listValuesHybrid[value])
        const newChecked = [...checkedHybrid];

        if (currentIndex === -1) {
            newChecked.push(listValuesHybrid[value]);
        } else {
            newChecked.splice(currentIndex, 1);
        }

        setCheckedHybrid(newChecked);
    };
    const handleListTextChangeHybrid = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target
        const currentIndex = checkedHybrid.indexOf(listValuesHybrid[name as ListType])

        if (value.length <= wordLimit) {
            setListValuesHybrid((prevValues) => ({
                ...prevValues,
                [name]: value
            }))

            if (currentIndex > -1) {
                setCheckedHybrid(prevChecked => {
                    const newChecked = [...prevChecked];
                    newChecked[currentIndex] = value;
                    return newChecked;
                });
            }
        }
    }

    const handleToggle = () => {
        setOpen((prev) => !prev);
    };

    // Helper function to get non-Other options (option1-option5)
    const getNonOtherOptions = (listValues: any) => {
        return ['option1', 'option2', 'option3', 'option4', 'option5'].map(key => listValues[key]);
    };

    // Check if all non-Other options are selected
    const areAllNonOtherSelected = (checked: string[], listValues: any) => {
        const nonOtherOptions = getNonOtherOptions(listValues);
        return nonOtherOptions.every(option => checked.includes(option));
    };

    // Handle select/deselect all for Sativa
    const handleSelectAllSativa = () => {
        const nonOtherOptions = getNonOtherOptions(listValuesSativa);
        const allSelected = areAllNonOtherSelected(checkedSativa, listValuesSativa);

        if (allSelected) {
            // Deselect all non-Other options, keep Other if selected
            const otherOption = listValuesSativa.option6;
            const newChecked = checkedSativa.filter(item => item === otherOption);
            setCheckedSativa(newChecked);
        } else {
            // Select all non-Other options, keep Other if already selected
            const otherOption = listValuesSativa.option6;
            const hasOther = checkedSativa.includes(otherOption);
            const newChecked = [...nonOtherOptions];
            if (hasOther) {
                newChecked.push(otherOption);
            }
            setCheckedSativa(newChecked);
        }
    };

    // Handle select/deselect all for Indica
    const handleSelectAllIndica = () => {
        const nonOtherOptions = getNonOtherOptions(listValuesIndica);
        const allSelected = areAllNonOtherSelected(checkedIndica, listValuesIndica);

        if (allSelected) {
            // Deselect all non-Other options, keep Other if selected
            const otherOption = listValuesIndica.option6;
            const newChecked = checkedIndica.filter(item => item === otherOption);
            setCheckedIndica(newChecked);
        } else {
            // Select all non-Other options, keep Other if already selected
            const otherOption = listValuesIndica.option6;
            const hasOther = checkedIndica.includes(otherOption);
            const newChecked = [...nonOtherOptions];
            if (hasOther) {
                newChecked.push(otherOption);
            }
            setCheckedIndica(newChecked);
        }
    };

    // Handle select/deselect all for Hybrid
    const handleSelectAllHybrid = () => {
        const nonOtherOptions = getNonOtherOptions(listValuesHybrid);
        const allSelected = areAllNonOtherSelected(checkedHybrid, listValuesHybrid);

        if (allSelected) {
            // Deselect all non-Other options, keep Other if selected
            const otherOption = listValuesHybrid.option6;
            const newChecked = checkedHybrid.filter(item => item === otherOption);
            setCheckedHybrid(newChecked);
        } else {
            // Select all non-Other options, keep Other if already selected
            const otherOption = listValuesHybrid.option6;
            const hasOther = checkedHybrid.includes(otherOption);
            const newChecked = [...nonOtherOptions];
            if (hasOther) {
                newChecked.push(otherOption);
            }
            setCheckedHybrid(newChecked);
        }
    };

    return (
        <>
            <Grid container sx={{ width: '100%', fontSize: '12px' }} direction={'column'}>
                <Grid container alignItems={'center'}>
                    <IconButton
                        onClick={handleToggle}
                        sx={{
                            transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                            width: '20px',
                            height: '20px',
                            mr: 2
                        }}
                    >
                        <ArrowRightIcon sx={{ color: 'black' }} />
                    </IconButton>
                    <span style={{ fontWeight: 'bold', fontSize: '24px' }}>Strain Advice</span>
                </Grid>

                <Collapse in={open} timeout={10}>
                    <div>
                        <Grid container direction='column' sx={{ width: '100%', mt: 2 }}>
                            <Grid container alignItems='center'>
                                <Grid container alignItems='center' sx={{ width: 'auto', mr: 2 }}>
                                    <Checkbox
                                        checked={areAllNonOtherSelected(checkedSativa, listValuesSativa)}
                                        onChange={handleSelectAllSativa}
                                        sx={{
                                            '&.Mui-checked': {
                                                color: 'green',
                                            }
                                        }}
                                    />
                                    <Typography sx={{ fontSize: '14px', color: 'gray' }}>
                                        Select All
                                    </Typography>
                                </Grid>
                                <Typography sx={{ fontWeight: 'bold', fontSize: '18px' }}>
                                    Sativa
                                </Typography>
                            </Grid>
                            <Grid>
                                <List sx={{ width: '100%' }}>
                                    {['option1', 'option2', 'option3', 'option4', 'option5', 'option6'].map((value) => {
                                        const labelId = `checkbox-list-label-${value}`;

                                        return (
                                            <ListItem
                                                key={value}
                                                disablePadding
                                            >
                                                <ListItemButton role={undefined} disableRipple>
                                                    <ListItemIcon onClick={handleToggleSativa(value as ListType)}>
                                                        <Checkbox
                                                            edge="start"
                                                            checked={checkedSativa.includes(listValuesSativa[value as ListType])}
                                                            disableRipple
                                                            sx={{
                                                                '&.Mui-checked': {
                                                                    color: 'green',
                                                                }
                                                            }}
                                                            inputProps={{ 'aria-labelledby': labelId }}
                                                        />
                                                    </ListItemIcon>
                                                    <TextField
                                                        multiline
                                                        fullWidth
                                                        rows={2}
                                                        value={listValuesSativa[value as ListType]}
                                                        onChange={handleListTextChangeSativa}
                                                        name={value}
                                                        sx={{ ...inputStyle }}
                                                    />
                                                </ListItemButton>
                                            </ListItem>
                                        );
                                    })}
                                </List>
                            </Grid>
                        </Grid>

                        <Grid container direction='column' sx={{ width: '100%', mt: 2 }}>
                            <Grid container alignItems='center'>
                                <Grid container alignItems='center' sx={{ width: 'auto', mr: 2 }}>
                                    <Checkbox
                                        checked={areAllNonOtherSelected(checkedIndica, listValuesIndica)}
                                        onChange={handleSelectAllIndica}
                                        sx={{
                                            '&.Mui-checked': {
                                                color: 'green',
                                            }
                                        }}
                                    />
                                    <Typography sx={{ fontSize: '14px', color: 'gray' }}>
                                        Select All
                                    </Typography>
                                </Grid>
                                <Typography sx={{ fontWeight: 'bold', fontSize: '18px' }}>
                                    Indica
                                </Typography>
                            </Grid>
                            <Grid>
                                <List sx={{ width: '100%' }}>
                                    {['option1', 'option2', 'option3', 'option4', 'option5', 'option6'].map((value) => {
                                        const labelId = `checkbox-list-label-${value}`;

                                        return (
                                            <ListItem
                                                key={value}
                                                disablePadding
                                            >
                                                <ListItemButton role={undefined} disableRipple>
                                                    <ListItemIcon onClick={handleToggleIndica(value as ListType)}>
                                                        <Checkbox
                                                            edge="start"
                                                            checked={checkedIndica.includes(listValuesIndica[value as ListType])}
                                                            disableRipple
                                                            sx={{
                                                                '&.Mui-checked': {
                                                                    color: 'green',
                                                                }
                                                            }}
                                                            inputProps={{ 'aria-labelledby': labelId }}
                                                        />
                                                    </ListItemIcon>
                                                    <TextField
                                                        multiline
                                                        fullWidth
                                                        rows={2}
                                                        value={listValuesIndica[value as ListType]}
                                                        onChange={handleListTextChangeIndica}
                                                        name={value}
                                                        sx={{ ...inputStyle }}
                                                    />
                                                </ListItemButton>
                                            </ListItem>
                                        );
                                    })}
                                </List>
                            </Grid>
                        </Grid>

                        <Grid container direction='column' sx={{ width: '100%', mt: 2 }}>
                            <Grid container alignItems='center'>
                                <Grid container alignItems='center' sx={{ width: 'auto', mr: 2 }}>
                                    <Checkbox
                                        checked={areAllNonOtherSelected(checkedHybrid, listValuesHybrid)}
                                        onChange={handleSelectAllHybrid}
                                        sx={{
                                            '&.Mui-checked': {
                                                color: 'green',
                                            }
                                        }}
                                    />
                                    <Typography sx={{ fontSize: '14px', color: 'gray' }}>
                                        Select All
                                    </Typography>
                                </Grid>
                                <Typography sx={{ fontWeight: 'bold', fontSize: '18px' }}>
                                    Hybrid
                                </Typography>
                            </Grid>
                            <Grid>
                                <List sx={{ width: '100%' }}>
                                    {['option1', 'option2', 'option3', 'option4', 'option5', 'option6'].map((value) => {
                                        const labelId = `checkbox-list-label-${value}`;

                                        return (
                                            <ListItem
                                                key={value}
                                                disablePadding
                                            >
                                                <ListItemButton role={undefined} disableRipple>
                                                    <ListItemIcon onClick={handleToggleHybrid(value as ListType)}>
                                                        <Checkbox
                                                            edge="start"
                                                            checked={checkedHybrid.includes(listValuesHybrid[value as ListType])}
                                                            disableRipple
                                                            sx={{
                                                                '&.Mui-checked': {
                                                                    color: 'green',
                                                                }
                                                            }}
                                                            inputProps={{ 'aria-labelledby': labelId }}
                                                        />
                                                    </ListItemIcon>
                                                    <TextField
                                                        multiline
                                                        fullWidth
                                                        rows={2}
                                                        value={listValuesHybrid[value as ListType]}
                                                        onChange={handleListTextChangeHybrid}
                                                        name={value}
                                                        sx={{ ...inputStyle }}
                                                    />
                                                </ListItemButton>
                                            </ListItem>
                                        );
                                    })}
                                </List>
                            </Grid>
                        </Grid>
                    </div>
                </Collapse>
            </Grid>
        </>
    )
}

export default EmailStrainAdvice