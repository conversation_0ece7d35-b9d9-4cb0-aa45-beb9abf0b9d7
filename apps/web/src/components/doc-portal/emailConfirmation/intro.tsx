import { TextField, Box, useMediaQ<PERSON>y, Icon<PERSON>utton, Collapse, Typography } from '@mui/material';
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles"
import { useEmailContext } from '../../../hooks/email-provider';
import { usePatient } from '../../../hooks/patient-provider';
import ArrowRightIcon from '@mui/icons-material/ArrowRight';
import { useState } from 'react';

const inputStyle = {
    width: '100%',
    '& .MuiInputBase-root': {
        borderRadius: '5px',
        backgroundColor: 'white',
        fontSize: '14px'
    },
    '& .MuiOutlinedInput-root': {
        '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.2)',
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.5)',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'rgba(0, 0, 0, 0.5)',
        }
    },
}

const ConsultationMessage = () => {
    const { introMessage, setIntroMessage } = useEmailContext()
    const { selectedPatient, patientTreatmentPlan, setPatientTreatmentPlan } = usePatient()
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
    const wordLimit = 240
    const [open, setOpen] = useState(false);

    const handleToggle = () => {
        setOpen(!open);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target
        setIntroMessage((prevValues) => {
            if (value.length <= wordLimit) {
                return {
                    ...prevValues,
                    [name]: value
                }
            }
            return prevValues
        })
    };

    const handleDiagnosisChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const newValue = e.target.value;
        // Apply 2000 character limit
        if (newValue.length <= 2000) {
            setPatientTreatmentPlan((prev) => ({
                ...prev,
                diagnosis: newValue
            }));
        }
    };

    return (
        <Grid container sx={{ width: '100%', fontSize: '12px' }} direction={'column'}>
            <Grid container alignItems={'center'}>
                <IconButton
                    onClick={handleToggle}
                    sx={{
                        transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                        width: '20px',
                        height: '20px',
                        mr: 2,
                        transition: 'transform 0.3s'
                    }}
                >
                    <ArrowRightIcon sx={{ color: 'black' }} />
                </IconButton>
                <span style={{ fontWeight: 'bold', fontSize: '24px' }}>Introduction</span>
            </Grid>

            <Collapse in={open} timeout={10}>
                <Grid container justifyContent={'center'} alignItems={'center'} sx={{ width: '100%', fontSize: '14px', mt: 2 }}>
                    <Box display={'inline'} sx={{ width: '100%' }}>
                        Dear {selectedPatient?.fullName || ' - - -'},
                        <br /><br />
                        Thank you for completing your consultation and providing your consent. <br /><br />

                        {/* Diagnosis Section */}
                        <Typography sx={{ fontWeight: 'bold', fontSize: '14px', mb: 1 }}>
                            Clinical Assessment:
                        </Typography>
                        <TextField
                            multiline
                            fullWidth
                            rows={3}
                            value={
                                patientTreatmentPlan?.diagnosis?.includes('Based on your consultation')
                                    ? patientTreatmentPlan.diagnosis
                                    : `Based on your consultation and the condition you reported (${patientTreatmentPlan?.diagnosis || 'No Answer'}), we have carefully assessed your health needs. Following our clinical evaluation, your treatment plan has been designed to address your specific requirements and symptoms.`
                            }
                            onChange={handleDiagnosisChange}
                            placeholder="Enter clinical assessment information..."
                            slotProps={{
                                htmlInput: {
                                    maxLength: 2000
                                }
                            }}
                            helperText={`${(patientTreatmentPlan?.diagnosis || '').length}/2000 characters`}
                            sx={{
                                mb: 2,
                                ...inputStyle
                            }}
                        />
                        <Typography sx={{ fontWeight: 'bold', fontSize: '14px', mb: 1 }}>
                            Treatment Plan Overview:
                        </Typography>

                        <TextField
                            multiline
                            placeholder="Type your notes here"
                            fullWidth
                            rows={isDesktop ? 2 : 5}
                            value={introMessage.intro}
                            onChange={handleChange}
                            name='intro'
                            sx={{ ...inputStyle }}
                        />
                        <br /><br />
                        <TextField
                            multiline
                            placeholder="Type your notes here"
                            fullWidth
                            onChange={handleChange}
                            rows={isDesktop ? 2 : 5}
                            value={introMessage.conclusion}
                            name='conclusion'
                            sx={{ ...inputStyle }}
                        />
                    </Box>
                </Grid>
            </Collapse>
        </Grid>
    );
};

export default ConsultationMessage;
