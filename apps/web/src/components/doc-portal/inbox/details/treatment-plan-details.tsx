import { Typography, FormControl, Box, Toolbar, TextField, Divider, useMediaQuery, IconButton } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { convertTimeStampToReadableFormat } from "../../../../utils";
import { useTheme } from "@mui/material/styles"
import { usePatient } from "../../../../hooks/patient-provider";
import { TreatmentPlanHistory } from "../../../../types";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { ApiClient } from "../../../../services";

const inputStyle = {
    '& .MuiInputBase-root': {
        borderRadius: '5px',
        fontSize: '0.8rem',
        height: '30px',
        padding: '4px 8px',
    },
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
        fontSize: '0.75rem'
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

const formFields = {
    dosePerDay22: 'Dose Per Day',
    maxDose22: 'Maximum Dose Per Day',
    totalQuantity22: 'Total Quantity (g)',
    numberOfRepeat22: 'Number of Repeat',
    supplyInterval22: 'Supply Interval (Days)',
    dosePerDay29: 'Dose Per Day',
    maxDose29: 'Maximum Dose Per Day',
    totalQuantity29: 'Total Quantity (g)',
    numberOfRepeat29: 'Number of Repeat',
    supplyInterval29: 'Supply Interval (Days)'
}

type RequiredFields = 'dosePerDay22' | 'maxDose22' | 'totalQuantity22' | 'numberOfRepeat22' | 'supplyInterval22' | 'dosePerDay29' | 'maxDose29' | 'totalQuantity29' | 'numberOfRepeat29' | 'supplyInterval29'


const TreatmentPlanDetails: React.FC<{ selectedSearchHistory?: TreatmentPlanHistory }> = ({ selectedSearchHistory }) => {
    const { selectedHistory, setSelectedInboxItem, updateInboxData, currentSearchPage, selectedSearchedPatient } = usePatient()
    const selectedTreatmentPlan = selectedSearchHistory ? selectedSearchHistory : selectedHistory as TreatmentPlanHistory
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

    const requiredFields22: RequiredFields[] = [
        'dosePerDay22',
        'maxDose22',
        'totalQuantity22',
        'numberOfRepeat22',
        'supplyInterval22'
    ];

    const requiredFields29: RequiredFields[] = [
        'dosePerDay29',
        'maxDose29',
        'totalQuantity29',
        'numberOfRepeat29',
        'supplyInterval29'
    ]

    const handleClickBackButton = async () => {
        setSelectedInboxItem(null)
        const result = selectedSearchedPatient && selectedSearchedPatient?.email ? await ApiClient.fetchInboxByEmail(selectedSearchedPatient.email, currentSearchPage) : await ApiClient.fetchAllInbox(currentSearchPage)
        updateInboxData(result)
    }

 
    return (
        <>
            <Grid container sx={{mb: 2}}>
                <IconButton sx={{width: '25px', height: '25px'}} onClick={handleClickBackButton}>
                    <ArrowBackIcon sx={{color: 'black'}}/>
                </IconButton>
                <Toolbar sx={{flexGrow: 1}}/>
                <Grid>
                    <Typography sx={{fontSize: '12px'}}>
                        TP Details for: {selectedSearchHistory?.fullName}
                    </Typography>
                </Grid>
            </Grid>
            <Grid container direction={'column'} sx={{ mt: 0, width: '100%' }} spacing={2}>

                <Grid container sx={{ width: '100%' }}>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Dr Name :
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {selectedTreatmentPlan?.drName}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Treatment Plan :
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {selectedTreatmentPlan?.outcome}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Mental Health Document :
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {selectedTreatmentPlan.mentalHealthSupprtingDocument ? selectedTreatmentPlan.mentalHealthSupprtingDocument : 'No'}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Date :
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {convertTimeStampToReadableFormat(selectedTreatmentPlan?.updatedAt)}
                            </Typography>
                        </Grid>
                    </Grid>
                </Grid>


                <Grid container direction={'column'}>
                    {selectedTreatmentPlan?.strengthAndConcentration22 &&
                        <Box sx={{ mt: isDesktop ? 0 : 20 }}>
                            <Divider sx={{ mt: 3, mb: 2 }} />

                            <Grid size={{ xs: 12 }} direction="column" container>

                                <Grid container spacing={2} sx={{ width: '100%', mb: 2 }}>
                                    <Grid size={{ lg: 2 }} container>
                                        <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: 'bold' }}>
                                            Strength:
                                        </Typography>
                                    </Grid>
                                    <Grid size={{ lg: 2 }} container >
                                        <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: 'bold' }}>
                                            22%
                                        </Typography>
                                    </Grid>
                                </Grid>
                                <Grid container direction='row' alignItems={'start'} justifyContent={'start'} spacing={1} sx={{ height: '30px', mb: 5 }}>
                                    {selectedTreatmentPlan && Object.entries(selectedTreatmentPlan)
                                        .filter(([key]) => requiredFields22.includes(key as RequiredFields))
                                        .map(([key, value]) => (
                                            <Grid key={`${key}${value}`} sx={{ width: isDesktop ? null : '100%' }}>
                                                <Typography sx={{ fontSize: '14px' }}>
                                                    {formFields[key as RequiredFields]}
                                                </Typography>
                                                <FormControl fullWidth={isDesktop ? false : true} sx={{ ...inputStyle }} size="small">
                                                    <TextField value={value} />
                                                </FormControl>
                                            </Grid>
                                        ))}
                                </Grid>
                            </Grid>

                        </Box>
                    }

                    {selectedTreatmentPlan?.strengthAndConcentration29 &&
                        <Box sx={{ mt: isDesktop ? 0 : 20 }}>
                            <Divider sx={{ mt: 1, mb: 2 }} />

                            <Grid size={{ xs: 12 }} direction="column">

                                <Grid container spacing={2} sx={{ width: '100%', mb: 2 }}>
                                    <Grid size={{ lg: 2 }} container>
                                        <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: 'bold' }}>
                                            Strength:
                                        </Typography>
                                    </Grid>
                                    <Grid size={{ lg: 2 }} container >
                                        <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: 'bold' }}>
                                            29%
                                        </Typography>
                                    </Grid>
                                </Grid>
                                <Grid container direction='row' alignItems={'start'} justifyContent={'start'} spacing={1} sx={{ height: '30px', mb: 5 }}>
                                    {selectedTreatmentPlan && Object.entries(selectedTreatmentPlan)
                                        .filter(([key]) => requiredFields29.includes(key as RequiredFields))
                                        .map(([key, value]) => (
                                            <Grid key={`${key}${value}`} sx={{ width: isDesktop ? null : '100%' }}>
                                                <Typography sx={{ fontSize: '14px' }}>
                                                    {formFields[key as RequiredFields]}
                                                </Typography>
                                                <FormControl fullWidth={isDesktop ? false : true} sx={{ ...inputStyle }} size="small">
                                                    <TextField value={value} />
                                                </FormControl>
                                            </Grid>
                                        ))}
                                </Grid>
                            </Grid>

                        </Box>
                    }
                    <Grid sx={{ mb: 1, mt: { lg: 1, xs: 25 }, width: '100%' }}>
                        <Grid container sx={{ width: '100%', mt: 1, mb: 2 }}>
                            <TextField
                                multiline
                                fullWidth
                                rows={6}
                                value={selectedTreatmentPlan?.drNotes}
                                sx={{
                                    width: '100%',
                                    '& .MuiInputBase-root': {
                                        borderRadius: '15px',
                                        borderColor: 'transparent',
                                        backgroundColor: '#F4F4F4',
                                        '&:hover': {
                                            borderColor: 'red',
                                        },
                                    },
                                    '& .MuiOutlinedInput-root': {
                                        '& .MuiOutlinedInput-notchedOutline': {
                                            borderColor: 'rgba(0, 0, 0, 0.05)',
                                        },
                                        '&:hover .MuiOutlinedInput-notchedOutline': {
                                            borderColor: 'rgba(0, 0, 0, 0.08)',
                                        },
                                        '&.Mui-focused fieldset': {
                                            borderColor: 'rgba(0, 0, 0, 0.05)',
                                        }
                                    },
                                }}
                            />
                        </Grid>
                    </Grid>
                    
                </Grid>
                
            </Grid>
        </>
    )
}

export default TreatmentPlanDetails
