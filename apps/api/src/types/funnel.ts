import moment from 'moment';
export type RegistrationData = {
  firstname: string;
  lastname: string;
  fullName?: string;
  email: string;
  password: string;
  phone: string;
};

// Define a type for device information
export type DeviceInfo = {
  userAgent?: string;
  platform?: string;
  language?: string;
  screenWidth?: number;
  screenHeight?: number;
  timeZone?: string;
  timestamp?: string;
  // Add other potential properties with specific types
  [key: string]: string | number | boolean | undefined;
};

export type ConsentFormData = {
  voluntary_consent: boolean;
  legally_competent: boolean;
  sufficient_information: boolean;
  understanding_risks: boolean;
  medical_cannabis_unapproved: boolean;
  illegal_prescription: boolean;
  drug_interactions: boolean;
  no_use_while_treated: boolean;
  illegal_to_minors: boolean;
  signature: string;
  signatureEvidence?: {
    ipAddress: string;
    deviceInfo: string | DeviceInfo;
  };
  // Keep these for backward compatibility
  ip_address?: string;
  device_details?: string;

  // New fields for the updated consent form flow
  userDetails?: {
    id: string;
    fullName: string;
    email: string;
    phone: string | null;
    phoneVerified: boolean;
    lastCompletedForm: string | null;
    patientID: string;
    zohoID: string;
    returningPatient: boolean;
    dob: string;
    state: string;
    consent_form_completed: boolean;
  };
  userId?: string;
  userFullName?: string;
  userEmail?: string;
  contactId?: string;
  zohoID?: string;
  updateZoho?: boolean;
};

export type Otp = {
  id: number;
  otp: string;
  validated: boolean;
  active: boolean;
  expirationdate: moment.Moment;
  createdAt: moment.Moment;
  updateAt: moment.Moment;
};

export type AICheckerResponse = {
  condition: string;
  response: 'YES' | 'NO';
  tried_medications: string[];
  explanation: string;
  risk_score?: number;
  pregnancy?: string;
  psychotic_disorder?: string;
  cardiovascular_diseases?: string;
};