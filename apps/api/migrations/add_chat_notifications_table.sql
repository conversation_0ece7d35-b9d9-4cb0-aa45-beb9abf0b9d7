-- Migration: Add chat_notifications table
-- Date: 2025-07-10
-- Purpose: Track chat notification states for patients when doctors send messages

-- Create chat_notifications table
CREATE TABLE IF NOT EXISTS chat_notifications (
    id SERIAL PRIMARY KEY,
    patient_id VARCHAR(255) NOT NULL,
    chat_notification BOOLEAN DEFAULT FALSE,
    last_doctor_message_at TIMESTAMP,
    doctor_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_patient_id UNIQUE(patient_id)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_chat_notifications_patient_id ON chat_notifications(patient_id);
CREATE INDEX IF NOT EXISTS idx_chat_notifications_flag ON chat_notifications(chat_notification);
CREATE INDEX IF NOT EXISTS idx_chat_notifications_doctor_id ON chat_notifications(doctor_id);
CREATE INDEX IF NOT EXISTS idx_chat_notifications_last_message ON chat_notifications(last_doctor_message_at);

-- Add comments to document the purpose of columns
COMMENT ON TABLE chat_notifications IS 'Tracks chat notification states for patients when doctors send messages';
COMMENT ON COLUMN chat_notifications.patient_id IS 'Internal patient ID - links to patient records';
COMMENT ON COLUMN chat_notifications.chat_notification IS 'Boolean flag indicating if patient has unread doctor messages';
COMMENT ON COLUMN chat_notifications.last_doctor_message_at IS 'Timestamp of the last message sent by a doctor to this patient';
COMMENT ON COLUMN chat_notifications.doctor_id IS 'ID of the doctor who sent the last message';
COMMENT ON COLUMN chat_notifications.created_at IS 'Timestamp when the notification record was first created';
COMMENT ON COLUMN chat_notifications.updated_at IS 'Timestamp when the notification record was last updated';
