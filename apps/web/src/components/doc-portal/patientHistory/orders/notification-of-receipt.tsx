import Grid from "@mui/material/Grid2";
import { Typography } from "@mui/material";
import { usePatient } from "../../../../hooks/patient-provider";
import { PatientOrder } from "../../../../types";
import { convertTimeStampToReadableFormat } from "../../../../utils";
import OrderDetailsTable from "./order-details-table";

const NotificationReceipt: React.FC = () => {
    const { selectedPatientHistory } = usePatient()
    const orderSelected = selectedPatientHistory as PatientOrder

    return <>
        <Grid container direction='column' sx={{ width: '100%' }}>
            <Grid>
                <Typography>
                    {convertTimeStampToReadableFormat(orderSelected?.updatedAt)}
                </Typography>
            </Grid>
            <Grid>
                <Typography>
                    <br />
                    Medicinal Cannabis Wholesale Australia Pty Ltd<br />
                    114/545-553 Pacific Highway<br />
                    St Leonards  NSW  2065<br /><br />
                    Subject : Notification of Receipt of Medicinal Cannabis (Drug of Addiction)<br /><br />
                    This letter serves as formal notification that Zenith Clinics Pty Ltd has received the following medicinal cannabis products from Medicinal Cannabis Wholesale Australia Pty Ltd on {convertTimeStampToReadableFormat(orderSelected?.updatedAt)}:<br />
                </Typography>
            </Grid>
            <OrderDetailsTable />
            <Grid>
                <Typography>
                    <br />
                    NOTE: Only provide supply if the Quantity field contains an amount.<br/><br/>
                    This notification is provided in compliance with the requirements set out under Clause 95(3) and 95(4) of the Poisons and Therapeutic Goods Regulation 2008 (NSW). Please consider this letter as our official acknowledgment of the receipt of the specified medicinal cannabis product(s).
                    <br/><br/>Please retain this notice for your records. If you have any questions or require further information, please do not hesitate to contact me.
                    <br/><br/>Thank you for your prompt attention to this matter.
                    <br/><br/>Yours sincerely,,<br /><br />

                    <span style={{ fontStyle: 'italic', fontSize: '14px' }}>Your signature</span><br /><br />

                    {orderSelected?.drName} <br />

                    Zenith Clinics Pty Ltd <br />

                    114A/545-553 Pacific Highway <br />

                    St Leonards  NSW  2065 <br />
                </Typography>
            </Grid>

            <Grid>
                <Typography sx={{ fontSize: '12px' }}>
                    <br /><br />
                    Disclaimer:

                    This email and any attachments are intended solely for the designated recipient and may contain confidential or privileged information. If you are not the intended recipient:

                    <br />(a) Please notify us immediately by phone and ensure the permanent deletion of this email from your system;

                    <br />(b) Be advised that any unauthorized use, sharing, distribution, or reproduction of this email’s content is strictly forbidden.

                    <br /><br />Opinions expressed herein are those of the author and do not necessarily represent the views of Zenith Clinics unless explicitly stated. Zenith Clinics and its directors disclaim any liability for potential harm from computer viruses transmitted through this email.
                </Typography>
            </Grid>
        </Grid>
    </>
}

export default NotificationReceipt