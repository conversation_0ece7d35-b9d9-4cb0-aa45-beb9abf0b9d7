import { DateTime } from 'luxon';
import { db } from '../utils/db';
import { logger } from '../config/logger';
import StripeSyncService from './stripeSyncService';
import StripeAPIService from './stripeAPI.service';


interface MonitoringMetrics {
  syncPerformance: {
    averageSyncDuration: number;
    successRate: number;
    totalSyncsLast7Days: number;
    failedSyncsLast7Days: number;
  };
  dataQuality: {
    totalPayments: number;
    paymentsLast24Hours: number;
    duplicatePayments: number;
    orphanedPayments: number;
  };
  systemHealth: {
    stripeApiConnected: boolean;
    databaseConnected: boolean;
    lastSuccessfulSync?: string;
  };
  alerts: Array<{
    level: 'info' | 'warning' | 'error';
    message: string;
    timestamp: string;
  }>;
}

interface SyncLogRow {
  id: string;
  sync_date: string;
  sync_type: string;
  payments_synced: number;
  sync_status: string;
  error_message: string | null;
  started_at: Date;
  completed_at: Date | null;
  duration_seconds: number;
}

class SyncMonitoringService {
  private stripeSyncService: StripeSyncService;
  private stripeAPIService: StripeAPIService;

  constructor() {
    this.stripeSyncService = new StripeSyncService();
    this.stripeAPIService = new StripeAPIService();
  }

  /**
   * Get comprehensive monitoring metrics
   */
  async getMonitoringMetrics(): Promise<MonitoringMetrics> {
    const alerts: Array<{ level: 'info' | 'warning' | 'error'; message: string; timestamp: string }> = [];
    const now = DateTime.now().setZone('Australia/Sydney');

    try {
      // Get sync performance metrics
      const syncPerformance = await this.getSyncPerformanceMetrics();
      
      // Get data quality metrics
      const dataQuality = await this.getDataQualityMetrics();
      
      // Get system health
      const systemHealth = await this.getSystemHealthMetrics();

      // Generate alerts based on metrics
      this.generateAlerts(syncPerformance, dataQuality, systemHealth, alerts);

      return {
        syncPerformance,
        dataQuality,
        systemHealth,
        alerts,
      };

    } catch (error) {
      logger.error('Failed to get monitoring metrics:', error);
      alerts.push({
        level: 'error',
        message: `Failed to collect monitoring metrics: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: now.toISO() || '',
      });

      // Return minimal metrics with error
      return {
        syncPerformance: {
          averageSyncDuration: 0,
          successRate: 0,
          totalSyncsLast7Days: 0,
          failedSyncsLast7Days: 0,
        },
        dataQuality: {
          totalPayments: 0,
          paymentsLast24Hours: 0,
          duplicatePayments: 0,
          orphanedPayments: 0,
        },
        systemHealth: {
          stripeApiConnected: false,
          databaseConnected: false,
        },
        alerts,
      };
    }
  }

  /**
   * Get sync performance metrics from the last 7 days
   */
  private async getSyncPerformanceMetrics(): Promise<MonitoringMetrics['syncPerformance']> {
    const client = await db.connect();
    
    try {
      const sevenDaysAgo = DateTime.now().setZone('Australia/Sydney').minus({ days: 7 }).toISO();

      const query = `
        SELECT 
          COUNT(*) as total_syncs,
          COUNT(CASE WHEN sync_status = 'success' THEN 1 END) as successful_syncs,
          COUNT(CASE WHEN sync_status = 'failed' THEN 1 END) as failed_syncs,
          AVG(EXTRACT(EPOCH FROM (completed_at - started_at))) as avg_duration_seconds
        FROM stripe_sync_log 
        WHERE started_at >= $1
      `;

      const result = await client.query(query, [sevenDaysAgo]);
      const row = result.rows[0];

      const totalSyncs = parseInt(row.total_syncs, 10);
      const successfulSyncs = parseInt(row.successful_syncs, 10);
      const failedSyncs = parseInt(row.failed_syncs, 10);
      const avgDurationSeconds = parseFloat(row.avg_duration_seconds) || 0;

      return {
        averageSyncDuration: Math.round(avgDurationSeconds * 1000), // Convert to milliseconds
        successRate: totalSyncs > 0 ? Math.round((successfulSyncs / totalSyncs) * 100) : 0,
        totalSyncsLast7Days: totalSyncs,
        failedSyncsLast7Days: failedSyncs,
      };

    } finally {
      client.release();
    }
  }

  /**
   * Get data quality metrics
   */
  private async getDataQualityMetrics(): Promise<MonitoringMetrics['dataQuality']> {
    const client = await db.connect();
    
    try {
      const yesterday = DateTime.now().setZone('Australia/Sydney').minus({ days: 1 }).toISO();

      // Get total payments
      const totalQuery = `SELECT COUNT(*) as total FROM stripe_payments`;
      const totalResult = await client.query(totalQuery);
      const totalPayments = parseInt(totalResult.rows[0].total, 10);

      // Get payments from last 24 hours
      const recentQuery = `
        SELECT COUNT(*) as recent 
        FROM stripe_payments 
        WHERE created_at >= $1
      `;
      const recentResult = await client.query(recentQuery, [yesterday]);
      const paymentsLast24Hours = parseInt(recentResult.rows[0].recent, 10);

      // Check for potential duplicates (same amount, customer, and date)
      const duplicateQuery = `
        SELECT COUNT(*) as duplicates
        FROM (
          SELECT amount_cents, customer_email, DATE(created_at)
          FROM stripe_payments
          GROUP BY amount_cents, customer_email, DATE(created_at)
          HAVING COUNT(*) > 1
        ) as duplicate_groups
      `;
      const duplicateResult = await client.query(duplicateQuery);
      const duplicatePayments = parseInt(duplicateResult.rows[0].duplicates, 10);

      // Check for orphaned payments (payments without proper metadata)
      const orphanedQuery = `
        SELECT COUNT(*) as orphaned
        FROM stripe_payments
        WHERE customer_name = 'Unknown' OR customer_email = '<EMAIL>'
      `;
      const orphanedResult = await client.query(orphanedQuery);
      const orphanedPayments = parseInt(orphanedResult.rows[0].orphaned, 10);

      return {
        totalPayments,
        paymentsLast24Hours,
        duplicatePayments,
        orphanedPayments,
      };

    } finally {
      client.release();
    }
  }

  /**
   * Get system health metrics
   */
  private async getSystemHealthMetrics(): Promise<MonitoringMetrics['systemHealth']> {
    // Test Stripe API connection
    let stripeApiConnected = false;
    try {
      stripeApiConnected = await this.stripeAPIService.testConnection();
    } catch (error) {
      logger.warn('Stripe API connection test failed:', error);
    }

    // Test database connection
    let databaseConnected = false;
    try {
      const client = await db.connect();
      await client.query('SELECT 1');
      client.release();
      databaseConnected = true;
    } catch (error) {
      logger.warn('Database connection test failed:', error);
    }

    // Get last successful sync
    let lastSuccessfulSync: string | undefined;
    try {
      const recentSyncs = await this.stripeSyncService.getRecentSyncHistory(1);
      const lastSync = recentSyncs.find(sync => sync.sync_status === 'success');
      if (lastSync && lastSync.completed_at) {
        lastSuccessfulSync = DateTime.fromJSDate(lastSync.completed_at).toISO() || undefined;
      }
    } catch (error) {
      logger.warn('Failed to get last successful sync:', error);
    }

    return {
      stripeApiConnected,
      databaseConnected,
      lastSuccessfulSync,
    };
  }

  /**
   * Generate alerts based on metrics
   */
  private generateAlerts(
    syncPerformance: MonitoringMetrics['syncPerformance'],
    dataQuality: MonitoringMetrics['dataQuality'],
    systemHealth: MonitoringMetrics['systemHealth'],
    alerts: MonitoringMetrics['alerts']
  ): void {
    const now = DateTime.now().setZone('Australia/Sydney').toISO() || '';

    // Sync performance alerts
    if (syncPerformance.successRate < 90 && syncPerformance.totalSyncsLast7Days > 0) {
      alerts.push({
        level: 'error',
        message: `Low sync success rate: ${syncPerformance.successRate}% (last 7 days)`,
        timestamp: now,
      });
    }

    if (syncPerformance.averageSyncDuration > 300000) { // 5 minutes
      alerts.push({
        level: 'warning',
        message: `Slow sync performance: average ${Math.round(syncPerformance.averageSyncDuration / 1000)}s`,
        timestamp: now,
      });
    }

    // Data quality alerts
    if (dataQuality.duplicatePayments > 0) {
      alerts.push({
        level: 'warning',
        message: `${dataQuality.duplicatePayments} potential duplicate payments detected`,
        timestamp: now,
      });
    }

    if (dataQuality.orphanedPayments > dataQuality.totalPayments * 0.1) { // More than 10%
      alerts.push({
        level: 'warning',
        message: `High number of orphaned payments: ${dataQuality.orphanedPayments}`,
        timestamp: now,
      });
    }

    // System health alerts
    if (!systemHealth.stripeApiConnected) {
      alerts.push({
        level: 'error',
        message: 'Stripe API connection failed',
        timestamp: now,
      });
    }

    if (!systemHealth.databaseConnected) {
      alerts.push({
        level: 'error',
        message: 'Database connection failed',
        timestamp: now,
      });
    }

    // Check if last sync is too old
    if (systemHealth.lastSuccessfulSync) {
      const lastSync = DateTime.fromISO(systemHealth.lastSuccessfulSync);
      const hoursSinceLastSync = DateTime.now().setZone('Australia/Sydney').diff(lastSync, 'hours').hours;
      
      if (hoursSinceLastSync > 30) {
        alerts.push({
          level: 'error',
          message: `Last successful sync was ${Math.round(hoursSinceLastSync)} hours ago`,
          timestamp: now,
        });
      } else if (hoursSinceLastSync > 25) {
        alerts.push({
          level: 'warning',
          message: `Last successful sync was ${Math.round(hoursSinceLastSync)} hours ago`,
          timestamp: now,
        });
      }
    } else {
      alerts.push({
        level: 'error',
        message: 'No successful sync found in recent history',
        timestamp: now,
      });
    }

    // Add info alerts for good metrics
    if (alerts.length === 0) {
      alerts.push({
        level: 'info',
        message: 'All systems operating normally',
        timestamp: now,
      });
    }
  }

  /**
   * Get detailed sync logs with filtering
   */
  async getSyncLogs(options: {
    limit?: number;
    status?: string;
    syncType?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<SyncLogRow[]> {
    const client = await db.connect();
    
    try {
      const { limit = 50, status, syncType, startDate, endDate } = options;
      
      let query = `
        SELECT 
          id,
          sync_date,
          sync_type,
          payments_synced,
          sync_status,
          error_message,
          started_at,
          completed_at,
          (EXTRACT(EPOCH FROM (completed_at - started_at))) as duration_seconds
        FROM stripe_sync_log
        WHERE 1=1
      `;
      
      const params: (string | number)[] = [];
      let paramCount = 0;

      if (status) {
        paramCount++;
        query += ` AND sync_status = $${paramCount}`;
        params.push(status);
      }

      if (syncType) {
        paramCount++;
        query += ` AND sync_type = $${paramCount}`;
        params.push(syncType);
      }

      if (startDate) {
        paramCount++;
        query += ` AND started_at >= $${paramCount}`;
        params.push(startDate);
      }

      if (endDate) {
        paramCount++;
        query += ` AND started_at <= $${paramCount}`;
        params.push(endDate);
      }

      query += ` ORDER BY started_at DESC LIMIT $${paramCount + 1}`;
      params.push(limit);

      const result = await client.query(query, params);
      
      return result.rows.map(row => ({
        ...row,
        duration_seconds: parseFloat(row.duration_seconds) || 0,
      }));

    } finally {
      client.release();
    }
  }
}

export default SyncMonitoringService;
