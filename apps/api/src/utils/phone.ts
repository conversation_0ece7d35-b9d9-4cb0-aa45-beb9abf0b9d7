/**
 * Validates Australian phone numbers in these formats:
 * - 04XX XXX XXX (with or without spaces)
 * - 4XX XXX XXX
 * - 614XX XXX XXX
 * - +614XX XXX XXX
 */
export function validateAustralianPhone(phone: string): boolean {
    const cleaned = phone.replace(/\D/g, '');
    return /^(04|4|614|61\d{9})$/.test(cleaned) && cleaned.length >= 9;
  }
  
  /**
   * Formats Australian phone numbers to E.164 standard:
   * - +614XXXXXXXX
   */
  export function formatAustralianPhone(phone: string): string {
    const cleaned = phone.replace(/\D/g, '');
    
    // Handle 04XXXXXXXX (10 digits)
    if (/^04\d{8}$/.test(cleaned)) {
      return `+61${cleaned.substring(1)}`;
    }
    
    // Handle 4XXXXXXXX (9 digits)
    if (/^4\d{8}$/.test(cleaned)) {
      return `+61${cleaned}`;
    }
    
    // Handle 614XXXXXXXX (11 digits)
    if (/^614\d{8}$/.test(cleaned)) {
      return `+${cleaned}`;
    }
    
    // Handle 61XXXXXXXXXX (already international)
    if (/^61\d{9}$/.test(cleaned)) {
      return `+${cleaned}`;
    }
    
    throw new Error('Invalid Australian phone number format');
  }