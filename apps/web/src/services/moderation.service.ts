import axios from 'axios';
import { config } from '../config';

// Use the shared API URL from config
const API_BASE_URL = config.apiUrl || 'http://localhost:8080/api';

export interface ModerationMessage {
  id: string;
  messageId: string;
  channelId: string;
  patientId: string;
  messageText: string;
  patientName: string;
  moderationStatus: 'pending' | 'approved' | 'rejected';
  isVisible: boolean;
  createdAt: string;
  moderatedAt?: string;
  moderatedBy?: string;
  moderatorName?: string;
  moderationReason?: string;
  hasAttachments?: boolean;
  attachmentCount?: number;
  attachmentTypes?: string[];
  attachmentData?: any;
}

export interface ModerationStats {
  pending: number;
  approved: number;
  rejected: number;
  total: number;
  todayTotal: number;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: PaginationInfo;
}

export interface ModerateMessageRequest {
  messageId: string;
  action: 'approve' | 'reject';
  reason?: string;
}

export interface BulkModerateRequest {
  messageIds: string[];
  action: 'approve' | 'reject';
  reason?: string;
}

export interface ConversationModeration {
  id: string;
  channelId: string;
  patientId: string;
  treatmentPlanId?: string;
  patientName?: string;
  moderationStatus: 'pending' | 'approved' | 'rejected';
  moderatedBy?: string;
  moderatorName?: string;
  moderatedAt?: string;
  moderationReason?: string;
  isVisible: boolean;
  createdAt: string;
  updatedAt: string;
  slackMessageTs?: string;
  firstMessageAt?: string;
  lastMessageAt?: string;
  messageCount: number;
  hasAttachments: boolean;
  firstMessageText?: string;
  lastMessageText?: string;
  messagePreview?: string;
  messageIds?: string[];
  treatmentOutcome?: string;
  doctorName?: string;
}

export interface ModerateConversationRequest {
  channelId: string;
  action: 'approve' | 'reject';
  reason?: string;
}

export interface PaginatedConversationResponse {
  conversations: ConversationModeration[];
  pagination: PaginationInfo;
}

export interface ConversationDetailsResponse {
  success: boolean;
  data: ConversationModeration;
}

/**
 * Service to handle moderation API interactions
 */
class ModerationService {
  /**
   * Get pending conversations for moderation
   * @param limit - Maximum number of conversations to return
   * @param page - Page number (1-based)
   * @returns Paginated response with pending conversations
   */
  async getPendingConversations(limit: number = 5, page: number = 1): Promise<PaginatedConversationResponse> {
    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/pending?limit=${limit}&page=${page}`,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Get pending messages for moderation (legacy method)
   * @param limit - Maximum number of messages to return
   * @param page - Page number (1-based)
   * @returns Paginated response with pending messages
   */
  async getPendingMessages(limit: number = 50, page: number = 1): Promise<PaginatedResponse<ModerationMessage>> {
    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/pending-messages?limit=${limit}&page=${page}`,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Get moderated conversations (approved/rejected)
   * @param limit - Maximum number of conversations to return
   * @param page - Page number (1-based)
   * @param status - Filter by status ('approved', 'rejected', or undefined for both)
   * @returns Paginated response with moderated conversations
   */
  async getModeratedMessages(limit: number = 50, page: number = 1, status?: 'approved' | 'rejected'): Promise<PaginatedResponse<ConversationModeration>> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      page: page.toString()
    });
    if (status) {
      params.append('status', status);
    }

    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/moderated?${params.toString()}`,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Get moderation statistics
   * @returns Moderation statistics
   */
  async getModerationStats(): Promise<ModerationStats> {
    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/stats`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get message details by ID
   * @param messageId - The message ID to get details for
   * @returns Message details
   */
  async getMessageDetails(messageId: string): Promise<ModerationMessage> {
    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/message/${messageId}`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get patient moderation history
   * @param patientId - The patient ID
   * @param limit - Maximum number of messages to return
   * @returns Array of patient's moderation history
   */
  async getPatientModerationHistory(patientId: string, limit: number = 20): Promise<ModerationMessage[]> {
    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/patient/${patientId}/history?limit=${limit}`,
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Moderate a single conversation
   * @param request - Conversation moderation request
   * @returns Moderation result
   */
  async moderateConversation(request: ModerateConversationRequest): Promise<{ success: boolean; message: string }> {
    const response = await axios.post(
      `${API_BASE_URL}/moderation/v1.0/moderate-conversation`,
      request,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Moderate a single message (legacy method)
   * @param request - Moderation request
   * @returns Moderation result
   */
  async moderateMessage(request: ModerateMessageRequest): Promise<{ success: boolean; message: string }> {
    const response = await axios.post(
      `${API_BASE_URL}/moderation/v1.0/moderate`,
      request,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Bulk moderate multiple messages
   * @param request - Bulk moderation request
   * @returns Bulk moderation result
   */
  async bulkModerateMessages(request: BulkModerateRequest): Promise<{
    success: boolean;
    message: string;
    results: { successful: number; failed: number; total: number }
  }> {
    const response = await axios.post(
      `${API_BASE_URL}/moderation/v1.0/bulk-moderate`,
      request,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Send test notification
   * @returns Test result
   */
  async sendTestNotification(): Promise<{ success: boolean; message: string }> {
    const response = await axios.post(
      `${API_BASE_URL}/moderation/v1.0/test-notification`,
      {},
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Check moderation status for multiple messages
   * @param messageIds - Array of message IDs to check
   * @returns Map of messageId to moderation status
   */
  async checkMessageModerationStatus(messageIds: string[]): Promise<Record<string, { status: string; isVisible: boolean }>> {
    const response = await axios.post(
      `${API_BASE_URL}/moderation/v1.0/check-messages`,
      { messageIds },
      { withCredentials: true }
    );
    return response.data.data;
  }

  /**
   * Get conversation details by channel ID
   * @param channelId - The channel ID to get details for
   * @returns Conversation details
   */
  async getConversationDetails(channelId: string): Promise<ConversationDetailsResponse> {
    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/conversation/${channelId}`,
      { withCredentials: true }
    );
    return response.data;
  }

  /**
   * Get messages for a conversation from Stream Chat
   * @param channelId - The channel ID to get messages for
   * @param limit - Maximum number of messages to return (default: 50)
   * @returns Messages from the conversation
   */
  async getConversationMessages(channelId: string, limit: number = 50): Promise<{
    success: boolean;
    data: {
      conversation: {
        channelId: string;
        patientName: string;
        moderationStatus: string;
      };
      messages: Array<{
        id: string;
        text: string;
        user: {
          id: string;
          name?: string;
        };
        created_at: string;
        attachments?: Array<{
          id?: string;
          type?: string;
          title?: string;
          asset_url?: string;
          image_url?: string;
          file_size?: number;
          mime_type?: string;
          thumb_url?: string;
          title_link?: string;
          og_scrape_url?: string;
          fallback?: string;
          text?: string;
        }>;
      }>;
      messageCount: number;
    };
  }> {
    const response = await axios.get(
      `${API_BASE_URL}/moderation/v1.0/conversation/${channelId}/messages?limit=${limit}`,
      { withCredentials: true }
    );
    return response.data;
  }
}

// Create a singleton instance
const moderationService = new ModerationService();

export default moderationService;
