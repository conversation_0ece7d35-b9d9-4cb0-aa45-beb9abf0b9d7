import { MakeGenerics, useSearch } from "@tanstack/react-location";
import { useEffect, useState } from "react";
import { PatientData } from "../../../types";
import { usePatient } from "../../../hooks/patient-provider";
import Histories from "./histories";
import Grid from "@mui/material/Grid2";
import HistoryDetails from "./history-details";
import LoadingScreen from "../../../utils/loading-screen";
import { useTheme } from "@mui/material/styles"
import { useMediaQuery } from "@mui/material";

type UrlProps = MakeGenerics<{
    Search: {
        token: string
    }
}>

const PatientHistories: React.FC<{ mobile?: boolean, heightVh?: string }> = ({ mobile = false, heightVh = '70vh' }) => {
    const { token } = useSearch<UrlProps>();
    const { getPatientById, patients, selectedHistory, setSelectedHistory } = usePatient()
    const [patientData, setPatientData] = useState<PatientData>()
    const [isLoading, setIsLoading] = useState(true)
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

    useEffect(() => {
        if (token) {
            const patient = getPatientById(token)
            if (patient) {
                setPatientData(patient)
                // setSelectedHistory(patient?.history?.[0])
            }
        }
        setIsLoading(false)
    }, [patients])


    return (
        <>
            {isLoading && !mobile && <LoadingScreen />}
            <Grid container sx={{ width: '100%' }}>
                <Grid sx={{ width: '100%' }} size={{ lg: mobile ? null : 4 }}>
                    <Histories patient={patientData} selectedHistory={selectedHistory} setSelectedHistory={setSelectedHistory} mobile={mobile} heightVh={heightVh} />
                </Grid>
                {!mobile &&
                    <Grid size={{ xs: 12, lg: 8 }}
                        sx={{
                            mt: 2,
                            overflow: 'auto',
                            maxHeight: isDesktop ? 'calc(100vh - 100px)' : null,
                        }}>
                        <HistoryDetails selectedHistory={selectedHistory} />
                    </Grid>
                }
            </Grid>
        </>
    )
}

export default PatientHistories
