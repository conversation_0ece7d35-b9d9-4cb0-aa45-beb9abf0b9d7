# 22% THC Addition Request Implementation Plan

## Overview
Implement a new treatment plan request type for patients with 29% THC to add 22% THC as an additional option. This follows the existing pattern of `thc_increase_questionnaire` and `extend_tp_questionnaire`.

## Database Changes

### 1. Create add_22_thc_questionnaire Table
```sql
-- Migration: Create add_22_thc_questionnaire table
-- Date: 2025-01-17
-- Purpose: Store questionnaire responses for 22% THC addition requests

CREATE TABLE IF NOT EXISTS add_22_thc_questionnaire (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id VARCHAR(255) NOT NULL,
    email TEXT NOT NULL,
    zoho_id TEXT,
    questionnaire_data JSONB NOT NULL,
    total_score INTEGER NOT NULL,
    max_score INTEGER NOT NULL DEFAULT 50,
    is_eligible BOOLEAN NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'submitted',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMPTZ,
    approved_at TIMESTAMPTZ,
    approved_by TEX<PERSON>,
    reviewed_by TEX<PERSON>,
    review_notes TEXT,
    slack_message_ts TEXT,
    
    -- Foreign key constraints
    CONSTRAINT fk_add_22_thc_patient_id 
        FOREIGN KEY (patient_id) REFERENCES Patient("patientID") ON DELETE CASCADE,
    CONSTRAINT fk_add_22_thc_email 
        FOREIGN KEY (email) REFERENCES Patient(email) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- Check constraints
    CONSTRAINT chk_add_22_thc_status 
        CHECK (status IN ('submitted', 'pending', 'approved', 'rejected')),
    CONSTRAINT chk_add_22_thc_score_range 
        CHECK (total_score >= 0 AND total_score <= max_score)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_add_22_thc_patient_id ON add_22_thc_questionnaire(patient_id);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_email ON add_22_thc_questionnaire(email);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_status ON add_22_thc_questionnaire(status);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_created_at ON add_22_thc_questionnaire(created_at);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_slack_message_ts ON add_22_thc_questionnaire(slack_message_ts) 
    WHERE slack_message_ts IS NOT NULL;

-- Add comments
COMMENT ON TABLE add_22_thc_questionnaire IS 'Stores questionnaire responses for patients requesting to add 22% THC to their existing 29% THC treatment plan';
COMMENT ON COLUMN add_22_thc_questionnaire.questionnaire_data IS 'JSONB containing the complete questionnaire responses and scoring details';
COMMENT ON COLUMN add_22_thc_questionnaire.is_eligible IS 'Boolean indicating if patient scored above threshold (35+ points)';
COMMENT ON COLUMN add_22_thc_questionnaire.slack_message_ts IS 'Slack message timestamp for threaded replies when moderating requests';
```

## Backend API Implementation

### 1. Funnel Controller - Patient Submission
**File**: `apps/api/src/controllers/funnel/index.ts`

Add new endpoints:
- `POST /funnel/v1.0/patient/add-22-thc-questionnaire` - Submit questionnaire
- `GET /funnel/v1.0/patient/add-22-thc-questionnaire/status` - Check status

### 2. Requests Controller - Doctor Approval
**File**: `apps/api/src/controllers/requests/index.ts`

Update existing functions:
- `getPendingRequests` - Include add_22_thc_questionnaire in UNION query
- `approveRequest` - Add handling for 'add_22_thc' type
- `rejectRequest` - Add handling for 'add_22_thc' type

### 3. Routes
**File**: `apps/api/src/routes/funnel.route.ts`
Add new routes for questionnaire submission and status checking.

## Eligibility Validation Logic

### Patient Eligibility Requirements
Before allowing questionnaire submission:
1. Patient has active treatment plan
2. Patient has 29% THC allowance > 0 (check `totalQuantity29` > 0)
3. Patient does NOT have 22% THC allowance (check `totalQuantity22` IS NULL OR = 0)
4. Treatment plan is currently active (within date range)

### Validation Query
```sql
-- Check if patient is eligible for 22% THC addition
SELECT 
    tp."patientID",
    tp."totalQuantity29",
    tp."totalQuantity22",
    tp."createdAt",
    CASE 
        WHEN tp."totalQuantity29" > 0 AND (tp."totalQuantity22" IS NULL OR tp."totalQuantity22" = 0) 
        THEN true 
        ELSE false 
    END as is_eligible
FROM TreatmentPlan tp
WHERE tp."patientID" = $1 OR tp.email = $2
ORDER BY tp."createdAt" DESC
LIMIT 1;
```

## Approval Logic

### When Doctor Approves add_22_thc Request
1. Get patient details from questionnaire
2. Get latest treatment plan to preserve 29% THC settings
3. Create prescription lead in Zoho CRM
4. Build treatment plan with BOTH 22% and 29% strengths
5. **IMPORTANT**: Use same numberOfRepeat for 22% as existing 29% plan
6. Insert new treatment plan into database
7. Update questionnaire status to 'approved'
8. Send notifications

### Treatment Plan Structure for 22% Addition
```javascript
const treatmentPlan = {
    patient: {
        patientID: questionnaire.patient_id,
        fullName: patientName,
        returningPatient: true,
        email: questionnaire.email
    },
    outcome: 'Approve Unrestricted',
    drId: doctorId,
    drName: doctorName,
    drNotes: `Approved 22% THC addition based on questionnaire. Risk score: ${questionnaire.total_score}/${questionnaire.max_score}`,
    date: new Date().toISOString(),
    zohoID: prescriptionLeadId,
    // Add 22% strength with same repeats as existing 29% plan
    '22': {
        dosePerDay: '0.1',
        maxDosePerDay: '1.0',
        totalQuantity: '28',
        numberOfRepeat: latestPlan.numberOfRepeat29 || '6', // Match 29% repeats
        supplyInterval: '28'
    },
    // Preserve existing 29% strength from latest treatment plan exactly
    '29': {
        dosePerDay: latestPlan.dosePerDay29,
        maxDosePerDay: latestPlan.maxDose29,
        totalQuantity: latestPlan.totalQuantity29,
        numberOfRepeat: latestPlan.numberOfRepeat29 || '6', // Keep existing repeats
        supplyInterval: latestPlan.supplyInterval29
    }
}
```

## Frontend Integration

### 1. Request Type Addition
**File**: `apps/web/src/components/doc-portal/requests/index.tsx`

Update Request interface:
```typescript
interface Request {
  id: string;
  type: 'thc_increase' | 'extend_tp' | 'add_22_thc'; // Add new type
  // ... existing fields
}
```

### 2. Request Card Updates
**File**: `apps/web/src/components/doc-portal/requests/RequestCard.tsx`

Add handling for new request type:
```typescript
const getRequestTitle = () => {
  switch(request.type) {
    case 'thc_increase':
      return 'Request: Increase to 29% THC Plan';
    case 'extend_tp':
      return 'Request: Extend Treatment Plan';
    case 'add_22_thc':
      return 'Request: Add 22% THC Option';
    default:
      return 'Treatment Plan Request';
  }
};
```

### 3. API Routes
**File**: `apps/web/src/services/apiRoutes.ts`

Add new routes:
```typescript
const routes = {
  // ... existing routes
  POST_ADD_22_THC_QUESTIONNAIRE: `${baseFunnel}/patient/add-22-thc-questionnaire`,
  GET_ADD_22_THC_STATUS: `${baseFunnel}/patient/add-22-thc-questionnaire/status`,
  // ... rest of routes
};
```

## Scoring and Eligibility

### Scoring Logic
- **Total Possible Score**: 50 points (5 questions with varying weights)
- **Eligibility Threshold**: 35+ points (70% of total)
- **Questions**:
  1. Side effects with 29% (4 points)
  2. Current treatment effectiveness (3 points)
  3. Health/medication changes (3 points)
  4. Usage plan for 22% (4 points)
  5. Consent for review (5 points)

### Question Scoring Details
```javascript
const questionScoring = {
  reasonSideEffects: { maxScore: 4, weight: 'high' },
  symptomImprovement: { maxScore: 3, weight: 'medium' },
  healthChanges: { maxScore: 3, weight: 'medium' },
  usagePlan: { maxScore: 4, weight: 'high' },
  consent: { maxScore: 5, weight: 'required' }
};
```

## Integration Points

### 1. Slack Notifications
Follow existing pattern in `requestModerationService` for:
- New request notifications
- Approval/rejection notifications
- Threaded replies using `slack_message_ts`

### 2. Zoho CRM Integration
- Create prescription lead using existing `createPrescriptionLead` function
- Submit treatment plan data with `Approved_Using_Messenger: 'Yes'`
- Use `Messenger_Approval_Notes` field for doctor notes

### 3. WebSocket Notifications
Use existing WebSocket infrastructure for real-time updates to doctor dashboard.

## Testing Strategy

### 1. Database Testing
- Test table creation and constraints
- Test eligibility validation queries
- Test data integrity with foreign keys

### 2. API Testing
- Test questionnaire submission with valid/invalid data
- Test eligibility validation
- Test approval/rejection workflows
- Test error handling

### 3. Integration Testing
- Test complete flow from submission to approval
- Test Slack notifications
- Test Zoho CRM integration
- Test WebSocket updates

## Security Considerations

### 1. Input Validation
- Validate questionnaire data structure
- Sanitize all user inputs
- Verify scoring calculations

### 2. Authorization
- Ensure patients can only submit for themselves
- Verify doctor permissions for approval/rejection
- Validate patient eligibility before submission

### 3. Data Protection
- Log all questionnaire submissions
- Audit approval/rejection actions
- Protect sensitive medical information

## Migration Strategy

### 1. Database Migration
Create migration file: `apps/api/migrations/create_add_22_thc_questionnaire_table.sql`

### 2. Deployment Steps
1. Run database migration
2. Deploy backend API changes
3. Deploy frontend changes
4. Test end-to-end functionality
5. Monitor for issues

## Success Metrics

### 1. Functional Metrics
- Questionnaire submission success rate
- Approval/rejection processing time
- System error rates

### 2. Business Metrics
- Number of 22% THC addition requests
- Approval rates by eligibility score
- Patient satisfaction with process

### 3. Technical Metrics
- API response times
- Database query performance
- WebSocket notification delivery
