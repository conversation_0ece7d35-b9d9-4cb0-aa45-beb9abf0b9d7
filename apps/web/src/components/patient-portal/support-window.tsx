import { useEffect } from 'react';

// Custom hook to load the Zoho Sales IQ script
const useScript = (url: string, widgetCode: string) => {
    useEffect(() => {
        const script = document.createElement('script');
        script.type = "text/javascript";
        script.defer = true;

        // Zoho SalesIQ widget script with dynamic widgetCode
        script.textContent = `
            var $zoho = $zoho || {};
            $zoho.salesiq = $zoho.salesiq || {
                widgetcode: "b7fd66b676f247e36c2a3ee26c51c772d64fad6d61c5efd3afe53f8d44876388",
                values: {},
                ready: function() { }
            };
            var d = document;
            var s = d.createElement("script");
            s.type = "text/javascript";
            s.id = "zsiqscript";
            s.defer = true;
            s.style.backgroundColor = "#red"
            s.src = "https://salesiq.zohopublic.com.au/widget";
            var t = d.getElementsByTagName("script")[0];
            t.parentNode.insertBefore(s, t);
            `;
        document.body.appendChild(script);
        return () => {
            document.body.removeChild(script);
        };
    }, [url, widgetCode]);
};

// Zoho Sales IQ Component
export default function SalesIQ() {
    useScript('https://salesiq.zohopublic.com.au/widget', 'b7fd66b676f247e36c2a3ee26c51c772d64fad6d61c5efd3afe53f8d44876388');

    return null;
}
