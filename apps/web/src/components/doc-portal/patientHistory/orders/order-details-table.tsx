import Grid from "@mui/material/Grid2";
import { Typography } from "@mui/material";
import { supplyLetterTable } from "../../../../utils";
import { usePatient } from "../../../../hooks/patient-provider";
import { PatientOrder } from "../../../../types";

const OrderDetailsTable: React.FC = () => {

    const { selectedPatientHistory } = usePatient()
    const orderSelected = selectedPatientHistory as PatientOrder


    return (
        <Grid container direction='column' sx={{ mt: 2 }}>
            <Grid container>
                <Grid size={{ xs: 6 }} sx={{ border: '1px solid rgba(0, 0, 0, 0.1)', backgroundColor: '#D3D3D3', p: 1 }}>
                    <Typography sx={{ fontWeight: 'bold' }}>
                        Tradename
                    </Typography>
                </Grid>

                <Grid size={{ xs: 3 }} sx={{ border: '1px solid rgba(0, 0, 0, 0.1)', backgroundColor: '#D3D3D3', p: 1 }}>
                    <Typography sx={{ fontWeight: 'bold' }}>
                        Quantity
                    </Typography>
                </Grid>

                <Grid size={{ xs: 3 }} sx={{ border: '1px solid rgba(0, 0, 0, 0.1)', backgroundColor: '#D3D3D3', p: 1 }}>
                    <Typography sx={{ fontWeight: 'bold' }}>
                        Strength
                    </Typography>
                </Grid>
            </Grid>
            <Grid container>
                {supplyLetterTable.map((value, index) => {
                    return (
                        <Grid key={`${index}`} sx={{ width: "100%" }} container>
                            <Grid size={{ xs: 6 }} sx={{ border: '1px solid rgba(0, 0, 0, 0.1)', p: 1 }}>
                                <Typography sx={{ whiteSpace: 'nowrap' }}>
                                    {value?.tradeName}
                                </Typography>
                            </Grid>

                            <Grid size={{ xs: 3 }} sx={{ border: '1px solid rgba(0, 0, 0, 0.1)', p: 1 }}>
                                <Typography sx={{ whiteSpace: 'nowrap' }}>
                                    {orderSelected?.items.find(i => i?.trade_name === value?.tradeName)?.quantity}
                                </Typography>
                            </Grid>

                            <Grid size={{ xs: 3 }} sx={{ border: '1px solid rgba(0, 0, 0, 0.1)', p: 1 }}>
                                <Typography sx={{ whiteSpace: 'nowrap' }}>
                                    {value?.strength}
                                </Typography>
                            </Grid>
                        </Grid>
                    )
                })}

            </Grid>
        </Grid>
    )
}

export default OrderDetailsTable