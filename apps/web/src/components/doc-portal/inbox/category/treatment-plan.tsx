import { Box, Divider, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { TreatmentPlanHistory } from "../../../../types";
import TreatmentPlanDetails from "../details/treatment-plan-details";
import { convertTimeStampToReadableFormat } from "../../../../utils";
import { usePatient } from "../../../../hooks/patient-provider";
import { Fragment } from "react";

const TreatmentPlan: React.FC<{ data: TreatmentPlanHistory | null, onSelect: (data: TreatmentPlanHistory) => void }> = ({ data, onSelect }) => {

    const { selectedInboxItem } = usePatient()

    return (
        <Fragment key={`${data?.id}`}>
            {!selectedInboxItem &&
                <Box sx={{ width: '100%' }}>
                    <Grid container direction='column' sx={{ width: '100%' }}>
                        <div style={{width: '100%', marginBottom: '5px'}} onClick={() => data && onSelect(data)}>
                            <Grid container sx={{
                                p: 1.5,
                                width: '100%',
                                cursor: 'pointer',
                                "&:hover": {
                                    boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.3)",
                                    borderRadius: 2
                                }
                            }}>
                                <Grid size={{lg: 3}}>
                                    <Typography
                                        sx={{ fontSize: '14px', overflowWrap: 'break-word' }}
                                        tabIndex={0}
                                        aria-label={`Submitted by Dr. ${data?.drName} for ${data?.fullName}`}
                                        className="focus:outline-none focus:ring-2 focus:ring-green-600"
                                    >
                                        <span className="text-gray-700 font-semibold">Submitted by Dr. {data?.drName}</span>
                                        <span className="ml-1"> for {data?.fullName ? data?.fullName.length > 25 ? `${data?.fullName?.substring(0, 25)}...` : data?.fullName : ''}</span>
                                    </Typography>
                                </Grid>
                                <Grid size={{lg: 6}} sx={{ ml: 1, mr: 'auto' }}>
                                    <Typography sx={{ fontSize: '14px', overflowWrap: 'break-word' }}>
                                        <span style={{ fontWeight: 'bold' }}>{data?.outcome}</span> - {data?.drNotes ? data?.drNotes.length > 50 ? `${data?.drNotes?.substring(0, 50)}...` : data?.drNotes : ''}
                                    </Typography>
                                </Grid>
                                <Grid>
                                    <Typography sx={{ fontSize: '12px' }}>
                                        {
                                            convertTimeStampToReadableFormat(data?.createdAt)
                                        }
                                    </Typography>
                                </Grid>
                            </Grid>
                            <Divider />
                        </div>
                    </Grid>
                </Box>}
            {data && selectedInboxItem === data && <TreatmentPlanDetails selectedSearchHistory={data}/>}
        </Fragment>
    )
}

export default TreatmentPlan
