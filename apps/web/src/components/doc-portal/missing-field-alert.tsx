import { Dialog, DialogContent, Typography } from "@mui/material"
import Grid from "@mui/material/Grid2";
import { ReactElement } from "react";

type MissingFieldAlertProps = {
    handleDialogClose: () => void
    open: boolean
    title: string
    Icon: ReactElement<any, any>
    message: string
}

const MissingFieldAlert: React.FC<MissingFieldAlertProps> = ({ handleDialogClose, open, title, Icon, message }) => (
    <Dialog open={open} fullWidth={true} maxWidth={'xs'} onClose={handleDialogClose}>

        <DialogContent>
            <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }}>
                    {title}
                </Typography>
                    {Icon}
                <Typography sx={{ fontSize: '18px' }} align="center">
                    {message}
                </Typography>
            </Grid>
        </DialogContent>
    </Dialog>
)

export default MissingFieldAlert