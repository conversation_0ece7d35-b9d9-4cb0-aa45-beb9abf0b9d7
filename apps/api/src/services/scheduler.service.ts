// import cron from 'node-cron';
// import moderationService from './moderation.service';
// import { logger } from '../config/logger';

// class SchedulerService {
//   private reminderJob: cron.ScheduledTask | null = null;

//   /**
//    * Initialize the scheduler service
//    */
//   async initialize(): Promise<void> {
//     try {
//       await this.setupModerationReminders();
//       logger.info('Scheduler service initialized successfully');
//     } catch (error) {
//       logger.error('Failed to initialize scheduler service:', error);
//     }
//   }

//   /**
//    * Setup moderation reminder notifications
//    */
//   private async setupModerationReminders(): Promise<void> {
//     try {
//       const settings = await moderationService.getSettings();
      
//       if (!settings.notificationEnabled) {
//         logger.info('Moderation notifications are disabled');
//         return;
//       }

//       // Stop existing job if running
//       if (this.reminderJob) {
//         this.reminderJob.stop();
//         this.reminderJob = null;
//       }

//       // Calculate cron expression based on reminder frequency
//       const hours = settings.reminderFrequencyHours || 12;
//       const cronExpression = `0 */${hours} * * *`; // Every X hours

//       // Create new scheduled job
//       this.reminderJob = cron.schedule(cronExpression, async () => {
//         try {
//           logger.info('Running moderation reminder check...');
//           await moderationService.sendReminderNotification();
//           logger.info('Moderation reminder check completed');
//         } catch (error) {
//           logger.error('Error in moderation reminder job:', error);
//         }
//       }, {
//         scheduled: true,
//         timezone: 'Australia/Sydney'
//       });

//       logger.info(`Moderation reminder job scheduled to run every ${hours} hours`);
//     } catch (error) {
//       logger.error('Failed to setup moderation reminders:', error);
//     }
//   }

//   /**
//    * Update reminder frequency
//    */
//   async updateReminderFrequency(hours: number): Promise<void> {
//     try {
//       await moderationService.updateSetting('reminder_frequency_hours', hours, 'system');
//       await this.setupModerationReminders(); // Restart with new frequency
//       logger.info(`Moderation reminder frequency updated to ${hours} hours`);
//     } catch (error) {
//       logger.error('Failed to update reminder frequency:', error);
//     }
//   }

//   /**
//    * Stop all scheduled jobs
//    */
//   stop(): void {
//     if (this.reminderJob) {
//       this.reminderJob.stop();
//       this.reminderJob = null;
//       logger.info('Scheduler service stopped');
//     }
//   }

//   /**
//    * Get status of scheduled jobs
//    */
//   getStatus(): { reminderJob: { running: boolean; frequency?: number } } {
//     return {
//       reminderJob: {
//         running: this.reminderJob ? true : false,
//         frequency: this.reminderJob ? undefined : undefined // Could be enhanced to store frequency
//       }
//     };
//   }
// }

// export default new SchedulerService();
