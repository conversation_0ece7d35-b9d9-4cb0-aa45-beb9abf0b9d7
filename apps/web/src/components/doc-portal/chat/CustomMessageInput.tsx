import { MessageInput, MessageInputProps, DefaultStreamChatGenerics, useChannelStateContext, useChatContext } from 'stream-chat-react';
import React, { useEffect, useRef } from 'react';
import { useAuth } from '../../../hooks/auth-provider';
import chatService from '../../../services/chat.service';

export type CustomMessageInputProps = Omit<MessageInputProps<DefaultStreamChatGenerics>, 'overrideSubmitHandler'> & {
  onMessageSend?: (text: string) => Promise<void>;
  placeholder?: string;
  patientEmail?: string; // Optional patient email for direct notification setting
};

const CustomMessageInput: React.FC<CustomMessageInputProps> = ({ onMessageSend, placeholder, patientEmail, ...props }) => {
  const { channel } = useChannelStateContext();
  const { client } = useChatContext();

  // Try to get doctor from auth context, but don't fail if not available (e.g., in patient chat)
  let doctor = null;
  try {
    const authContext = useAuth();
    doctor = authContext.doctor;
  } catch (error) {
    // useAuth not available (e.g., in patient chat context), continue without doctor
  
  }

  const lastMessageIdRef = useRef<string | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement | null>(null);

  // Helper function to extract patient email from channel
  const extractPatientEmailFromChannel = (channel: any): string | null => {
    try {
      console.log('🔍 Extracting patient email from channel:', {
        channelId: channel.id || channel.cid,
        hasState: !!channel.state,
        hasMembers: !!channel.state?.members,
        memberCount: channel.state?.members ? Object.keys(channel.state.members).length : 0
      });

      // First try to get email from channel members
      if (channel.state?.members) {
        const members = Object.values(channel.state.members) as any[];
        console.log('🔍 Channel members:', members.map(m => ({
          userId: m.user?.id,
          email: m.user?.email,
          role: m.user?.role,
          startsWithP: m.user?.id?.startsWith('p_')
        })));

        const patientMember = members.find(member =>
          member.user?.id?.startsWith('p_') || member.user?.role === 'patient'
        );

        if (patientMember?.user?.email) {
          console.log('✅ Found patient email from member:', patientMember.user.email);
          return patientMember.user.email;
        }
      }

      // Fallback: try to extract from channel ID if email is encoded there
      const channelId = channel.id || channel.cid;
      if (!channelId) {
        console.log('❌ No channel ID found');
        return null;
      }

      console.log('🔍 Trying to extract email from channel ID:', channelId);

      // If channel ID contains email pattern, extract it
      const emailMatch = channelId.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
      if (emailMatch) {
        console.log('✅ Found email in channel ID:', emailMatch[1]);
        return emailMatch[1];
      }

      console.log('❌ No email found in channel');
      return null;
    } catch (error) {
      console.error('Failed to extract patient email from channel:', error);
      return null;
    }
  };
  
  // Listen for new messages and trigger onMessageSend when a message from the current user is detected
  useEffect(() => {
    if (!channel || !client) return;
    
    const handleNewMessage = async (event: any) => {
      const message = event.message;

      // Only process if:
      // 1. It's a new message (not processed before)
      // 2. It's from the current user
      // 3. It has text content
      if (
        message?.id !== lastMessageIdRef.current &&
        message?.user?.id === client.userID &&
        message?.text
      ) {
        // Update reference to prevent processing the same message again
        lastMessageIdRef.current = message.id;

        // Check if this is a doctor sending a message
        const isDoctorMessage = message.user?.id?.startsWith('d_');

        if (isDoctorMessage && doctor?.accessID) {
          console.log('🔍 Doctor message detected, getting patient email...');

          // Use provided patientEmail prop first, fallback to extraction
          let targetPatientEmail: string | null = patientEmail || null;
          if (!targetPatientEmail) {
            console.log('🔍 No patientEmail prop provided, extracting from channel...');
            targetPatientEmail = extractPatientEmailFromChannel(channel);
          } else {
            console.log('✅ Using provided patientEmail prop:', targetPatientEmail);
          }

          console.log('🔍 Final patient email:', targetPatientEmail);

          if (targetPatientEmail) {
            try {
              console.log('🚀 Setting chat notification for:', { patientEmail: targetPatientEmail, doctorId: doctor.accessID });
              // Set chat notification for this patient
              await chatService.setChatNotificationForDoctorMessage(targetPatientEmail, doctor.accessID);
              console.log('✅ Chat notification set successfully');
            } catch (error) {
              console.error('❌ Failed to set chat notification:', error);
              // Don't break the chat flow if notification fails
            }
          } else {
            console.warn('⚠️ Could not get patient email (neither from prop nor channel)');
          }
        }

        // Call the custom handler with the message text (existing functionality)
        if (onMessageSend) {
          onMessageSend(message.text).catch(error => {
            console.error('Failed to update Zoho with message:', error);
          });
        }
      }
    };
    
    // Subscribe to new message events
    channel.on('message.new', handleNewMessage);

    // Cleanup
    return () => {
      channel.off('message.new', handleNewMessage);
    };
  }, [channel, client, onMessageSend]);

  // Add textarea auto-resize functionality
  useEffect(() => {
    // Find the textarea element and attach event listener
    const textarea = document.querySelector('.str-chat__textarea textarea') as HTMLTextAreaElement;
    if (textarea) {
      textareaRef.current = textarea;

      const adjustHeight = () => {
        if (!textarea) return;
        
        // Reset height to auto to get the correct scrollHeight
        textarea.style.height = 'auto';
        
        // Set the height to match content (with a maximum defined in CSS)
        const newHeight = Math.min(textarea.scrollHeight, 150);
        textarea.style.height = `${newHeight}px`;
      };

      // Initial adjustment
      adjustHeight();

      // Add event listeners for input changes
      textarea.addEventListener('input', adjustHeight);
      textarea.addEventListener('change', adjustHeight);
      
      // Clean up
      return () => {
        textarea.removeEventListener('input', adjustHeight);
        textarea.removeEventListener('change', adjustHeight);
      };
    }
  }, [channel]); // Re-run when channel changes to ensure we capture the new textarea

  // Render the standard MessageInput with placeholder if provided
  return (
    <MessageInput
      additionalTextareaProps={placeholder ? { placeholder } : undefined}
      {...props}
    />
  );
};

export default CustomMessageInput; 