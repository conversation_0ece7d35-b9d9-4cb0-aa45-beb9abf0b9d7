import { Button, Typo<PERSON> } from "@mui/material"
import Grid from "@mui/material/Grid2";
import React, { useEffect } from "react";
import { ApiClient } from "../../services";
import { calculateTimeLeft } from "../../utils";
import { useSearch, MakeGenerics } from "@tanstack/react-location";
import { useMeeting } from "../../hooks/meeting-provider";

type AppointmentTimerProps = {
    mobileConsultation?: boolean
}

type UrlProps = MakeGenerics<{
    Search: {
        token: string
    }
}>

const AppointmentTimer: React.FC<AppointmentTimerProps> = ({ mobileConsultation = false }) => {
    const {timeLeft, setTimeLeft, stopTimer} = useMeeting()
    const { token } = useSearch<UrlProps>();


    useEffect(() => {
        let timerId: NodeJS.Timeout
        const init = async () => {
            try {
                if (token) {
                    const patient = await ApiClient.getPatientById(token)
                    timerId = setInterval(() => {
                        const timeLeft = calculateTimeLeft(patient?.consultation?.consultationDateAndTime)
                        setTimeLeft(timeLeft)
                        if (timeLeft?.total && timeLeft?.total <= 0) clearInterval(timerId);

                    }, 1000);
                }
            }
            catch (error) {
                const incomingError: Error = error as unknown as any
                throw new Error(incomingError.message)
            }
        }
        init()

        return () => clearInterval(timerId);

    }, [])

    useEffect(()=> {
        if (stopTimer){
            setTimeLeft((prev)=>{
                if(prev){
                    return{
                        ...prev, 
                        total: -1000
                    }
                }
            })
        }
    }, [stopTimer])

    return (
        <>
            <Grid sx={{ border: '1px solid green', borderRadius: 2, p: 1, mt: { xs: mobileConsultation ? 1 : 3, lg: 2 } }} container direction={'column'} justifyContent={'center'} alignItems={'center'}>
                <Grid>
                    <Typography sx={{ fontSize: '0.90rem', fontWeight: 'bold' }}>
                        Your consultation will start in...
                    </Typography>
                </Grid>
                <Grid>
                    {!mobileConsultation && <Button variant="contained"
                        sx={{
                            backgroundColor: 'green', mt: 1,
                            height: '25px', textTransform: 'none',
                            fontSize: '12px'
                        }}>
                        Count Down
                    </Button>}
                </Grid>
                <Grid>
                    {stopTimer || (timeLeft?.total && timeLeft?.total <= 0) ?
                        <>
                            <Typography align="center" sx={{ fontSize: '12px', mt: 2 }}>
                                Reach out to our support team if you encounter any trouble.
                            </Typography>
                        </> :
                        <Typography sx={{ fontSize: { xs: '3rem', md: '4rem' } }}>
                            {timeLeft?.days || '00'}:{timeLeft?.hours || '00'}:{timeLeft?.minutes || '00'}:{timeLeft?.seconds || '00'}
                        </Typography>}
                </Grid>
                {
                    !mobileConsultation && <Grid sx={{ mt: 3 }}>
                        <Typography sx={{ fontSize: '0.75rem' }}>
                            Days : Hours : Minutes : Seconds
                        </Typography>
                    </Grid>
                }
            </Grid>
        </>
    )
}

export default AppointmentTimer