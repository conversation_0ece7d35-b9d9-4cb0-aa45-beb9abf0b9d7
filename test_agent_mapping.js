// Quick test to verify the agent name mapping works correctly
const { generateAgentReport, AGENTSLIST } = require('./apps/api/src/controllers/reports/helpers.ts');

// Mock call data with <PERSON> as the agent name (as it comes from softphone)
const mockCallData = [
  {
    Date: '2025-01-24',
    Agent: '<PERSON>', // This is what comes from the softphone system
    Phone: '1234567890',
    Type: 'Inbound',
    Status: 'Answered',
    Wait: 30,
    Talk: 120,
    ACW: 10,
    Tag: '',
    Sip: '',
    'Call finish initiator': '',
    'Hold counter': '',
    'Total hold time': '',
  },
  {
    Date: '2025-01-24',
    Agent: '<PERSON>',
    Phone: '0987654321',
    Type: 'Outbound',
    Status: 'Answered',
    Wait: 15,
    Talk: 90,
    ACW: 5,
    Tag: '',
    Sip: '',
    'Call finish initiator': '',
    'Hold counter': '',
    'Total hold time': '',
  }
];

// Mock conversion data
const mockConversions = [
  {
    id: '1',
    Booked_on_Call: 'Yes - Booked on Call RYAN'
  }
];

// Test the function
console.log('Testing agent name mapping...');
console.log('AGENTSLIST:', JSON.stringify(AGENTSLIST, null, 2));

const report = generateAgentReport(mockCallData, mockConversions, AGENTSLIST);
console.log('Generated report:', JSON.stringify(report, null, 2));

// Verify that Ryan Zenith's data is matched but displayed as "Ryan Sales"
const ryanReport = report.find(agent => agent.agent === 'Ryan Sales');
if (ryanReport) {
  console.log('✅ SUCCESS: Ryan Zenith data matched and displayed as "Ryan Sales"');
  console.log('Ryan Sales report:', ryanReport);
} else {
  console.log('❌ FAILED: Could not find Ryan Sales in the report');
}
