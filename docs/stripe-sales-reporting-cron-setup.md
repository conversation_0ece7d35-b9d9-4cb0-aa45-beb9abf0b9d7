# 🕐 Stripe Sales Reporting - Cron Job Configuration Guide

This guide provides complete documentation for setting up automated cron jobs for the Stripe Sales Reporting system.

## 📋 Overview

The system provides four automated reports:
- **Daily Reports** - Sales data for the previous day (text format)
- **Daily Image Reports** - Sales data as visual image (sent at midnight Sydney)
- **Weekly Reports** - Weekly summary sent every Monday
- **Monthly Reports** - Monthly summary sent on the 1st of each month

## 🔧 Prerequisites

### Server Requirements
- Linux/Unix server with cron daemon
- curl installed
- Network access to your API server

### API Server Requirements
- Sales reporting API running and accessible
- Proper environment variables configured
- Slack webhook configured

## 📅 Recommended Cron Schedule

| Report Type | Schedule | Purpose | Endpoint |
|-------------|----------|---------|----------|
| **Data Sync** | Daily 11:40 PM Sydney | Sync yesterday's Stripe data (20 min before image report) | `/api/sales/v1/sync-daily-payments` |
| **Daily Image Report** | Daily 12:00 AM Sydney | Visual report sent at midnight | `/api/sales/v1/trigger-daily-report-image` |
| **Daily Text Report** | Daily 9:00 AM Sydney | Text report previous day's sales | `/api/sales/v1/trigger-daily-report` |
| **Weekly Report** | Monday 9:30 AM Sydney | Report previous week's sales | `/api/sales/v1/trigger-weekly-report` |
| **Monthly Report** | 1st of month 10:00 AM Sydney | Report previous month's sales | `/api/sales/v1/trigger-monthly-report` |

## 🛠 Cron Job Configuration

### Step 1: Open Crontab
```bash
crontab -e
```

### Step 2: Basic Configuration
Add these lines to your crontab (using your domain `doctor.zenith.clinic`):

```bash
# Stripe Sales Reporting Automation
# Timezone: Australia/Sydney
# Domain: doctor.zenith.clinic

# Daily Stripe data sync - 11:40 PM Sydney time (13:40 UTC same day)
40 13 * * * curl -X POST -H "Content-Type: application/json" https://doctor.zenith.clinic/api/sales/v1/sync-daily-payments >/dev/null 2>&1

# Daily image report - 12:00 AM Sydney time (14:00 UTC same day)
0 14 * * * curl -X POST -H "Content-Type: application/json" https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report-image >/dev/null 2>&1

# Daily text report - 9:00 AM Sydney time (23:00 UTC previous day)
0 23 * * * curl -X POST -H "Content-Type: application/json" https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report >/dev/null 2>&1

# Weekly sales report - Monday 9:30 AM Sydney time (23:30 UTC Sunday)
30 23 * * 0 curl -X POST -H "Content-Type: application/json" https://doctor.zenith.clinic/api/sales/v1/trigger-weekly-report >/dev/null 2>&1

# Monthly sales report - 1st of month 10:00 AM Sydney time (00:00 UTC same day)
0 0 1 * * curl -X POST -H "Content-Type: application/json" https://doctor.zenith.clinic/api/sales/v1/trigger-monthly-report >/dev/null 2>&1
```

### Step 3: Enhanced Configuration with Logging
For better monitoring, use this version with logging (following your existing pattern):

```bash
# Stripe Sales Reporting with Logging
# Timezone: Australia/Sydney
# Domain: doctor.zenith.clinic
# Logs: /home/<USER>/ (following your existing pattern)

# Daily Stripe data sync - 11:40 PM Sydney time
40 13 * * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/sync-daily-payments >> /home/<USER>/stripe-sync.log 2>&1

# Daily image report - 12:00 AM Sydney time
0 14 * * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report-image >> /home/<USER>/stripe-daily-image-report.log 2>&1

# Daily text report - 9:00 AM Sydney time
0 23 * * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report >> /home/<USER>/stripe-daily-report.log 2>&1

# Weekly sales report - Monday 9:30 AM Sydney time
30 23 * * 0 curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-weekly-report >> /home/<USER>/stripe-weekly-report.log 2>&1

# Monthly sales report - 1st of month 10:00 AM Sydney time
0 0 1 * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-monthly-report >> /home/<USER>/stripe-monthly-report.log 2>&1

# Existing availability report (for reference)
# 0 23 * 5-9 0 curl -X POST https://doctor.zenith.clinic/api/availability/trigger-report >> /home/<USER>/availability-report.log 2>&1
```

## 📁 Setup Logging Directory

If using the logging version, ensure the log directory exists (following your existing pattern):

```bash
# Logs will be stored in /home/<USER>/ (your existing pattern)
# Ensure the directory is writable
ls -la /home/<USER>/

# Create log files if needed
touch /home/<USER>/stripe-sync.log
touch /home/<USER>/stripe-daily-image-report.log
touch /home/<USER>/stripe-daily-report.log
touch /home/<USER>/stripe-weekly-report.log
touch /home/<USER>/stripe-monthly-report.log

# Set permissions if needed
chmod 644 /home/<USER>/stripe-*.log
```

### Alternative: Centralized Logging Directory
If you prefer to organize all logs in a dedicated directory:

```bash
# Create dedicated stripe reports directory
mkdir -p /home/<USER>/logs/stripe-reports

# Create log files
touch /home/<USER>/logs/stripe-reports/sync.log
touch /home/<USER>/logs/stripe-reports/daily-image.log
touch /home/<USER>/logs/stripe-reports/daily.log
touch /home/<USER>/logs/stripe-reports/weekly.log
touch /home/<USER>/logs/stripe-reports/monthly.log
```

## 🌍 Timezone Considerations

### Important Notes
- **Server Timezone:** Cron times are in UTC, converted for Sydney time
- **API Timezone:** The API handles Australia/Sydney timezone internally
- **Daylight Saving:** You may need to adjust times during DST transitions

### UTC to Sydney Time Conversion
- **Sydney Standard Time (AEDT):** UTC + 11 hours
- **Sydney Daylight Time (AEDT):** UTC + 10 hours

### Current Cron Times (Sydney → UTC)
- **11:40 PM Sydney** → 13:40 UTC (same day)
- **12:00 AM Sydney** → 14:00 UTC (same day)
- **9:00 AM Sydney** → 23:00 UTC (previous day)
- **9:30 AM Sydney** → 23:30 UTC (previous day)
- **10:00 AM Sydney** → 00:00 UTC (same day)

## 🔍 Testing Cron Jobs

### Test Individual Endpoints
```bash
# Test daily sync
curl -X POST https://doctor.zenith.clinic/api/sales/v1/sync-daily-payments

# Test daily image report
curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report-image

# Test daily text report
curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report

# Test weekly report
curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-weekly-report

# Test monthly report
curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-monthly-report
```

### Test Cron Syntax
```bash
# Check cron syntax
crontab -l

# Test cron job manually
curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report-image

# View your existing cron jobs (including availability report)
crontab -l | grep -E "(availability|sales)"
```

## 📊 Monitoring & Troubleshooting

### Check Cron Job Status
```bash
# View cron logs (Ubuntu/Debian)
sudo tail -f /var/log/cron.log

# View cron logs (CentOS/RHEL)  
sudo tail -f /var/log/cron

# Check if cron daemon is running
sudo systemctl status cron
```

### Check API Health
```bash
# Check system health
curl https://doctor.zenith.clinic/api/sales/v1/sync-health

# Check sync history
curl https://doctor.zenith.clinic/api/sales/v1/sync-history

# Check monitoring metrics
curl https://doctor.zenith.clinic/api/sales/v1/monitoring-metrics
```

### View Application Logs
```bash
# View Stripe sales reporting logs (following your pattern)
tail -f /home/<USER>/stripe-daily-image-report.log
tail -f /home/<USER>/stripe-daily-report.log
tail -f /home/<USER>/stripe-weekly-report.log
tail -f /home/<USER>/stripe-monthly-report.log
tail -f /home/<USER>/stripe-sync.log

# View your existing availability report log
tail -f /home/<USER>/availability-report.log

# View all report logs at once
tail -f /home/<USER>/*-report.log
```

## 🚨 Error Handling

### Common Issues

1. **Network Connectivity:**
   ```bash
   # Test API connectivity
   curl -I https://doctor.zenith.clinic/api/sales/v1/sync-health
   ```

2. **Authentication Issues:**
   - Verify Stripe API keys in environment variables
   - Check Slack webhook configuration

3. **Cron Not Running:**
   ```bash
   # Start cron service
   sudo systemctl start cron
   sudo systemctl enable cron
   ```

### Notification Setup
Add email notifications for failures:
```bash
# Add to top of crontab
MAILTO=<EMAIL>

# Or add to individual jobs
0 14 * * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report-image || echo "Daily image report failed" | mail -s "Stripe Report Error" <EMAIL>
```

## 📋 Complete Example Configuration

Here's a complete crontab configuration:

```bash
# Stripe Sales Reporting System
# Server: Production (doctor.zenith.clinic)
# Timezone: Australia/Sydney (UTC+10/+11)
# Contact: <EMAIL>

MAILTO=<EMAIL>
PATH=/usr/local/bin:/usr/bin:/bin

# Daily Stripe data sync - 11:40 PM Sydney (13:40 UTC same day)
40 13 * * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/sync-daily-payments >> /home/<USER>/stripe-sync.log 2>&1

# Daily image report - 12:00 AM Sydney (14:00 UTC same day)
0 14 * * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report-image >> /home/<USER>/stripe-daily-image-report.log 2>&1

# Daily text report - 9:00 AM Sydney (23:00 UTC previous day)
0 23 * * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-daily-report >> /home/<USER>/stripe-daily-report.log 2>&1

# Weekly sales report - Monday 9:30 AM Sydney (23:30 UTC Sunday)
30 23 * * 0 curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-weekly-report >> /home/<USER>/stripe-weekly-report.log 2>&1

# Monthly sales report - 1st of month 10:00 AM Sydney (00:00 UTC same day)
0 0 1 * * curl -X POST https://doctor.zenith.clinic/api/sales/v1/trigger-monthly-report >> /home/<USER>/stripe-monthly-report.log 2>&1

# Existing availability report (for reference - May to September, Sundays)
# 0 23 * 5-9 0 curl -X POST https://doctor.zenith.clinic/api/availability/trigger-report >> /home/<USER>/availability-report.log 2>&1

# Log rotation cleanup - Weekly at 2:00 AM Sunday (16:00 UTC Saturday)
0 16 * * 6 find /home/<USER>"stripe-*.log" -mtime +30 -delete
```

## ✅ Verification Checklist

- [ ] Cron daemon is running
- [ ] Crontab entries are correct
- [ ] API endpoints are accessible
- [ ] Environment variables are configured
- [ ] Slack webhook is working
- [ ] Log directory exists and is writable
- [ ] Test runs complete successfully
- [ ] Email notifications are configured (optional)

## 🔄 Maintenance

### Regular Tasks
- **Weekly:** Check log files for errors
- **Monthly:** Verify reports are being sent to Slack
- **Quarterly:** Review and update cron schedules if needed

### Log Rotation
The example includes automatic log cleanup. Alternatively, use logrotate:

```bash
# Create logrotate config
sudo nano /etc/logrotate.d/stripe-reports

# Add content:
/home/<USER>/stripe-*.log {
    weekly
    rotate 4
    compress
    delaycompress
    missingok
    notifempty
}
```

## 🚀 Quick Start Commands

```bash
# 1. Ensure log directory is ready (using your existing pattern)
ls -la /home/<USER>/

# 2. Open crontab
crontab -e

# 3. Add the cron jobs (copy from Complete Example Configuration above)

# 4. Test the setup
curl -X POST https://doctor.zenith.clinic/api/sales/v1/sync-health

# 5. Monitor logs
tail -f /home/<USER>/stripe-daily-image-report.log

# 6. View all your report logs
ls -la /home/<USER>/*-report.log
```

---

**🎯 This configuration will ensure your Stripe Sales Reporting system runs automatically and reliably!**
