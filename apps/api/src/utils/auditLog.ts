import { Request } from 'express-serve-static-core';
import useragent from 'useragent';
import { logger } from '../config/logger';
import llogger from '../config/llogger';

export class LLog {
  appName: string = 'doc-ui';
  message: unknown = '';
  level: string = '';
  ip: string | undefined = '';
  os: string = '';
  device: string = '';
  agent: string = '';
  action: string | undefined = '';
  url: string | undefined = '';
  actor: string = '';

  constructor(req: Request, actor: string = 'unknown') {
    const agent = useragent.parse(req.headers['user-agent']);
    this.ip = req.ip;
    this.os = agent.os.toString();
    this.agent = agent.toString();
    this.device = agent.device.toString();
    this.url = req.url;
    this.action = req.method;
    this.actor = actor;
  }

  info(message: string, actor?: string) {
    if (actor) {
      this.actor = actor;
    }

    this.level = 'info';
    this.message = message;
    llogger.info(this.toJSON());
    return;
  }

  summary(message: string, actor?: string) {
    if (actor) {
      this.actor = actor;
    }
    
    this.level = 'info';
    this.message = JSON.parse(message);

    // Send "this" to file for AWS but only display the message
    llogger.info(this.toJSON());
    logger.info(message);
    return;
  }

  error(message: string, actor?: string) {
    if (actor) {
      this.actor = actor;
    }
    this.level = 'error';
    this.message = message;
    llogger.error(JSON.stringify(this.toJSON()));
    return;
  }

  debug(message: string, actor?: string) {
    if (actor) {
      this.actor = actor;
    }
    this.level = 'debug';
    this.message = message;
    llogger.debug(JSON.stringify(this.toJSON()));
    return;
  }

  toJSON() {
    return {
      appName: this.appName,
      message: this.message,
      level: this.level,
      ip: this.ip,
      os: this.os,
      device: this.device,
      agent: this.agent,
      action: this.action,
      url: this.url,
      actor: this.actor,
    };
  }
}
