import { logger } from '../config/logger';
import config from '../config';
import { db } from '../utils/db';

export class WordPressUtils {
  private static async getWPToken() {
    const loginData = {
      username: config.wpUsername,
      password: config.wpPassword,
    };

    const requestEndpoint = config.wpApiUrl + `/jwt-auth/v1/token`;

    const fetchOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData),
    };
    const response = await fetch(requestEndpoint, fetchOptions);
    const jsonResponse = await response.json();
    if (jsonResponse.token) {
      return jsonResponse.token;
    }
    throw new Error('Unable to get WP token');
  }

  public static async getWPUser(email: string) {
    const token = await this.getWPToken();

    const requestEndpoint = config.wpApiUrl + `/wp/v2/users?search=` + email;

    const fetchOptions = {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    const response = await fetch(requestEndpoint, fetchOptions);
    const jsonResponse = await response.json();
    if (jsonResponse) {
      if (jsonResponse.length > 0) {
        const matchingUser = jsonResponse.find(function (user) {
          return user.email === email;
        });
        return matchingUser;
      } else {
        return null;
      }
    }
    return null;
  }

  public static async getTPs() {
    const token = await this.getWPToken();
    const requestEndpoint = config.wpApiUrl + `/sync/v1/tpdata`;

    const fetchOptions = {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    const response = await fetch(requestEndpoint, fetchOptions);
    const res = await response.json();
    return res;
  }
  // https://letsroll.harvest.delivery/wp-json/sync/v1/orderdata?start_date=2025-03-15&end_date=2025-03-20

  public static async getOrders(start: string, end: string, status: string = 'completed') {
    const token = await this.getWPToken();
    const requestEndpoint = config.wpApiUrl + `/sync/v1/orderdata?start_date=${start}&end_date=${end}&status=${status}`;

    const fetchOptions = {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    };
    const response = await fetch(requestEndpoint, fetchOptions);
    const res = await response.json();
    return res;
  }

  public static async createWPUser(userData) {
    const token = await this.getWPToken();
    const requestEndpoint = config.wpApiUrl + `/custom/v2/create-wp-user`;
    const fetchOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(userData),
    };
    const response = await fetch(requestEndpoint, fetchOptions);
    const jsonResponse = await response.json();

    if (jsonResponse.message && jsonResponse.message == 'success') {
      return jsonResponse;
    } else {
      logger.info('Response : ', JSON.stringify(response));
      logger.info('Body : ', JSON.stringify(jsonResponse));
      throw new Error('Unable to create a WP user : ' + JSON.stringify(userData));
    }
  }

  public static async updateWPUser(userId,userData) {
    const token = await this.getWPToken();
    const requestEndpoint = config.wpApiUrl + `/wp/v2/users/` + userId;
    const fetchOptions = {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(userData),
    };
    const response = await fetch(requestEndpoint, fetchOptions);
    const jsonResponse = await response.json();
    if (jsonResponse.id) {
      return true;
    } else {
      logger.info('Response : '+ JSON.stringify(response));
      logger.info('Body : '+ JSON.stringify(jsonResponse));
      throw new Error('Unable to update a WP user : ' + JSON.stringify(userData));
    }
  }

  public static async getStockUpdates() {

    const products:{ name: string, code: string, balance: string }[] = [];
    let jsonResponse = {};

    const client = await db.connect();

    const query = `
          SELECT
            id,
            name,
            code,
            balance
          FROM productstock
        `;

    const result = await client.query(query);

    if (result.rows.length > 0) {

      result.rows.forEach(product => {

        const newCode = "[" + product.code.replaceAll("/", "|") + "]";

        products.push({
          name: product.name,
          code: newCode,
          balance: product.balance
        });
      });

      jsonResponse = {
        products: products
      }
        
    }

    client.release();

    return jsonResponse;

  }
}
