import { TextField, Typography } from "@mui/material"
import Grid from "@mui/material/Grid2";
import { usePatient } from "../../hooks/patient-provider";

interface DoctorNotesProps {
    onNoteChange?: (notes: string) => void;
    onTypingStart?: () => void;
    onTypingStop?: () => void;
    onFocusStart?: () => void;
    onFocusEnd?: () => void;
}

const DoctorNotes: React.FC<DoctorNotesProps> = ({ onNoteChange, onTypingStart, onFocusStart, onFocusEnd }) => {
    const { selectedPatient, setPatientTreatmentPlan, patientTreatmentPlan } = usePatient()

    const handleNoteChanges = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const newValue = e.target.value;

        onTypingStart?.()
        onNoteChange?.(newValue)

        if (selectedPatient) {
            setPatientTreatmentPlan((prev) => ({
                ...prev,
                drNotes: newValue
            }))
        }
    }

    return (
        <>
            <Grid sx={{ border: '1px solid green', borderRadius: 2, p: 1 }} container direction={'column'} justifyContent={'center'} alignItems={'center'}>
                <Grid sx={{ mb: 1 }}>
                    <Typography sx={{ fontSize: '0.70rem' }}>
                        Notes will be attached to patient's Treatment Plan.
                    </Typography>
                </Grid>

                <Grid container sx={{ width: '100%', mt: 1, pr: 1, pl: 1, mb: 2 }}>
                    <TextField
                        multiline
                        placeholder="Type your notes here"
                        fullWidth
                        rows={4}
                        value={patientTreatmentPlan?.drNotes}
                        onChange={handleNoteChanges}
                        onFocus={() => onFocusStart?.()}
                        onBlur={() => onFocusEnd?.()}
                        sx={{
                            width: '100%',
                            '& .MuiInputBase-root': {
                                borderRadius: '15px',
                                borderColor: 'transparent',
                                backgroundColor: '#F4F4F4',
                                '&:hover': {
                                    borderColor: 'red',
                                },
                            },
                            '& .MuiOutlinedInput-root': {
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: 'rgba(0, 0, 0, 0.05)',
                                },
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: 'rgba(0, 0, 0, 0.08)',
                                },
                                '&.Mui-focused fieldset': {
                                    borderColor: 'rgba(0, 0, 0, 0.05)',
                                }
                            },
                            '& textarea': {
                                resize: 'vertical', // Allow both vertical and horizontal resizing
                            },
                        }}
                    />
                </Grid>
            </Grid></>
    )
}

export default DoctorNotes