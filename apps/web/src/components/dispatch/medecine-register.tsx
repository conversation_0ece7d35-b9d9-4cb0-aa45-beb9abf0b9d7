import { useEffect, useState } from "react";
import Grid from '@mui/material/Grid2';
import { Button, TextField, Typography } from "@mui/material";
import DocsToPrint from "./print-doc";
import { ApiClient } from "../../services";
import { CircularProgress } from '@mui/material';

const MedecineRegister: React.FC = () => {
    const [otp, setOtp] = useState<string>("");

    // TODO: Change to true
    const [showOtpInput, setShowOtpInput] = useState<boolean>(true);
    const [loading, setLoading] = useState<boolean>(false)

    useEffect(() => {
        setLoading(true)
        const init = async () => {
            try {
                const otp = localStorage.getItem('mrp')
                if (otp) {
                    const result = await ApiClient.verifyPin(otp)
                    if (result.found) {
                        setShowOtpInput(false)
                    }
                }
            } finally {
                setLoading(false)

            }
        }

        init()
    }, [])
    // border: '1px solid #e0e0e0'

    const handleChange = (value: string) => {
        setOtp(value)
    }
    const handleOTpSubmit = async () => {
        setLoading(true)
        try {
            if (otp.length > 0) {
                const result = await ApiClient.verifyPin(otp)
                if (result.found) {
                    localStorage.setItem('mrp', otp)
                    setShowOtpInput(false)
                }
            }
        }
        finally {
            setLoading(false)

        }
    }
    return (
        <>
            {showOtpInput ? <Grid container spacing={2} sx={{ padding: 2, mt: 10 }} justifyContent={"center"} alignItems={"center"}>
                <Grid container sx={{ p: 5, border: '1px solid #e0e0e0' }} direction="column" justifyContent={"center"} alignItems={"center"}>
                    <Grid>
                        <Typography gutterBottom>
                            Enter the One Time Password shared with you by Admin
                        </Typography>
                    </Grid>
                    <Grid sx={{ width: '100%' }} container justifyContent={"center"} alignItems={"center"}>
                        <TextField
                            label="OTP"
                            onChange={(e) => handleChange(e.target.value)}
                            value={otp}
                            fullWidth
                        />
                    </Grid>
                    <Grid>
                        <Button onClick={handleOTpSubmit} sx={{ mt: 2 }}>
                            {loading ? <CircularProgress size="1rem" style={{ marginRight: '16px' }} /> : null}
                            Submit
                        </Button>
                    </Grid>
                </Grid>
            </Grid> : <DocsToPrint />}
        </>
    );
}

export default MedecineRegister;