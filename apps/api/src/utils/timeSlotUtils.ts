import { DateTime } from 'luxon';
import { Pool } from 'pg';
import { logger } from '../config/logger';

/**
 * Utility functions for handling consultation time slots and timezone conversions
 */

interface ConsultationTimeData {
  consultationDate: string;
  patientID: string;
}

interface ActiveSlotInfo {
  slotTime: DateTime;
  intervalMinutes: number;
}

/**
 * Checks if the current Sydney time falls within a patient's consultation time slot
 * @param consultationDateUTC - The consultation date in UTC format from database
 * @returns boolean - True if current time is within the consultation slot
 */
export const isConsultationSlotActive = (consultationDateUTC: string | Date): boolean => {
  try {
    // Parse the consultation date from UTC and convert to Sydney timezone
    const consultationDateTime = DateTime.fromJSDate(
      typeof consultationDateUTC === 'string' ? new Date(consultationDateUTC) : consultationDateUTC
    ).setZone('Australia/Sydney');

    // Get current time in Sydney timezone
    const currentSydneyTime = DateTime.now().setZone('Australia/Sydney');

    // Check if current time is within the 30-minute consultation slot
    // Slot is active if current time is between consultation time and 30 minutes after
    // For example: 16:30 slot is active from 16:30 to 17:00
    const slotStart = consultationDateTime;
    const slotEnd = consultationDateTime.plus({ minutes: 30 });

    const isActive = currentSydneyTime >= slotStart && currentSydneyTime < slotEnd;

    return isActive;
  } catch (error) {
    console.error('Error checking consultation slot activity:', error);
    return false;
  }
};

/**
 * Gets the current active consultation time slot that should be notified
 * This finds which 30-minute slot the current time falls into
 * @returns object with start and end times of the consultation slot that should be active now
 */
export const getCurrentActiveTimeWindow = (): { start: DateTime; end: DateTime } => {
  const currentSydneyTime = DateTime.now().setZone('Australia/Sydney');

  // Find the most recent 30-minute boundary that has passed
  // For example: at 16:40, we want the 16:30 slot (16:30-17:00)
  const minutes = currentSydneyTime.minute;
  const roundedMinutes = minutes < 30 ? 0 : 30;

  const windowStart = currentSydneyTime.set({
    minute: roundedMinutes,
    second: 0,
    millisecond: 0
  });

  const windowEnd = windowStart.plus({ minutes: 30 });

  return { start: windowStart, end: windowEnd };
};

/**
 * Formats a consultation time for logging purposes
 * @param consultationDateUTC - The consultation date in UTC format
 * @returns string - Formatted time in Sydney timezone
 */
export const formatConsultationTimeForLogging = (consultationDateUTC: string | Date): string => {
  try {
    const consultationDateTime = DateTime.fromJSDate(
      typeof consultationDateUTC === 'string' ? new Date(consultationDateUTC) : consultationDateUTC
    ).setZone('Australia/Sydney');

    return consultationDateTime.toFormat('h:mm a, dd MMM yyyy');
  } catch (error) {
    return 'Invalid date' + (error instanceof Error ? ': ' + error.message : '');
  }
};

/**
 * Gets the current Sydney time formatted for logging
 * @returns string - Current time in Sydney timezone
 */
export const getCurrentSydneyTimeForLogging = (): string => {
  return DateTime.now().setZone('Australia/Sydney').toFormat('h:mm a, dd MMM yyyy');
};

/**
 * Checks if a consultation time falls within a specific time range
 * @param consultationDateUTC - The consultation date in UTC format
 * @param startTime - Start of the time range in Sydney timezone
 * @param endTime - End of the time range in Sydney timezone
 * @returns boolean - True if consultation falls within the range
 */
export const isConsultationInTimeRange = (
  consultationDateUTC: string | Date,
  startTime: DateTime,
  endTime: DateTime
): boolean => {
  try {
    const consultationDateTime = DateTime.fromJSDate(
      typeof consultationDateUTC === 'string' ? new Date(consultationDateUTC) : consultationDateUTC
    ).setZone('Australia/Sydney');

    return consultationDateTime >= startTime && consultationDateTime <= endTime;
  } catch (error) {
    console.error('Error checking consultation time range:', error);
    return false;
  }
};

/**
 * Fetches all consultations for today from the database
 * @param db Database connection pool
 * @returns Array of consultation time data
 */
export const fetchTodaysConsultations = async (db: Pool): Promise<ConsultationTimeData[]> => {
  const client = await db.connect();

  try {
    // Fetch raw UTC timestamps and filter by Sydney timezone dates
    // Keep the filtering in PostgreSQL but fetch raw UTC for JavaScript conversion
    const query = `
      SELECT
        c."consultationDate",
        c."patientID"
      FROM Consultation c
      WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      AND c.completed = FALSE
      ORDER BY c."consultationDate" ASC
    `;

    const result = await client.query(query);
    return result.rows;
  } catch (error) {
    console.error('Error fetching today\'s consultations:', error);
    return [];
  } finally {
    client.release();
  }
};

/**
 * Clusters consultations by time proximity to identify separate appointment slots
 * @param consultations Array of consultation data
 * @returns Array of consultation clusters (appointment slots)
 */
 export const clusterConsultationsByTime = (consultations: ConsultationTimeData[]): ConsultationTimeData[][] => {
  if (consultations.length === 0) return [];

  // Convert and sort consultations by time
  const sortedConsultations = consultations
    .map(c => ({
      ...c,
      sydneyTime: DateTime.fromJSDate(new Date(c.consultationDate)).setZone('Australia/Sydney')
    }))
    .sort((a, b) => a.sydneyTime.toMillis() - b.sydneyTime.toMillis());

  const clusters: ConsultationTimeData[][] = [];
  let currentCluster: ConsultationTimeData[] = [sortedConsultations[0]];

  // Group consultations that are within 10 minutes of each other
  const CLUSTER_THRESHOLD_MINUTES = 10;

  for (let i = 1; i < sortedConsultations.length; i++) {
    const current = sortedConsultations[i];
    const lastInCluster = currentCluster[currentCluster.length - 1];

    const timeDiff = current.sydneyTime.diff(
      DateTime.fromJSDate(new Date(lastInCluster.consultationDate)).setZone('Australia/Sydney'),
      'minutes'
    ).minutes;

    if (timeDiff <= CLUSTER_THRESHOLD_MINUTES) {
      // Add to current cluster
      currentCluster.push(current);
    } else {
      // Start new cluster
      clusters.push(currentCluster);
      currentCluster = [current];
    }
  }

  // Don't forget the last cluster
  clusters.push(currentCluster);

  return clusters;
};

/**
 * Analyzes consultation clusters to detect appointment slot patterns
 * @param consultations Array of consultation data
 * @returns Detected slot duration in minutes based on business logic
 */
export const detectAppointmentSlot = (consultations: ConsultationTimeData[]): number => {
  if (consultations.length === 0) {
    logger.info(`🔍 APPOINTMENT SLOT DETECTION: No consultations found`);
    return 30; // Default fallback
  }

  // Cluster consultations by time proximity
  const clusters = clusterConsultationsByTime(consultations);

  // // Log detailed consultation analysis
  // const consultationTimes = consultations.map(c =>
  //   DateTime.fromJSDate(new Date(c.consultationDate)).setZone('Australia/Sydney')
  // ).sort((a, b) => a.toMillis() - b.toMillis());

//   const consultationDetails = consultationTimes.map((time, index) => ({
//     index,
//     time: time.toFormat('h:mm:ss a'),
//     patientID: consultations[index]?.patientID || 'unknown'
//   }));

//   console.log(`
// 🔍 DETAILED CONSULTATION ANALYSIS:
//    Total consultations: ${consultations.length}
//    First time: ${consultationTimes[0]?.toFormat('h:mm a') || 'N/A'}
//    Last time: ${consultationTimes[consultationTimes.length - 1]?.toFormat('h:mm a') || 'N/A'}
//    Total span: ${consultationTimes.length > 1 ? consultationTimes[consultationTimes.length - 1].diff(consultationTimes[0], 'minutes').minutes.toFixed(2) + ' minutes' : '0 minutes'}
//    Detected clusters: ${clusters.length}
//    First 10 consultations:
// ${consultationDetails.slice(0, 10).map(c => `      ${c.index}: ${c.time} - ${c.patientID}`).join('\n')}`);

  logger.info(`🔍 APPOINTMENT SLOT DETECTION DEBUG`, {
    totalConsultations: consultations.length,
    clustersDetected: clusters.length,
    clusterSizes: clusters.map(cluster => cluster.length),
    context: 'slot_detection_debug'
  });

  // Analyze clusters to determine slot duration
  if (clusters.length === 1) {
    // Single cluster - determine duration based on cluster characteristics
    const cluster = clusters[0];

    if (cluster.length === 1) {
      // Single consultation - use standard 30-minute slot
      logger.info(`🎯 SLOT DETECTION: Single consultation detected`, {
        detectedSlot: 30,
        logic: 'single_consultation',
        context: 'slot_detection_result'
      });
      return 30;
    } else {
      // Group consultation - use 30-minute slot (standard for group sessions)
      logger.info(`🎯 SLOT DETECTION: Group consultation detected`, {
        patientsInGroup: cluster.length,
        detectedSlot: 30,
        logic: 'group_consultation',
        context: 'slot_detection_result'
      });
      return 30;
    }
  } else {
    // Multiple clusters - analyze gaps between them
    const gaps: number[] = [];

    for (let i = 1; i < clusters.length; i++) {
      const prevClusterEnd = DateTime.fromJSDate(new Date(clusters[i-1][clusters[i-1].length - 1].consultationDate))
        .setZone('Australia/Sydney');
      const currentClusterStart = DateTime.fromJSDate(new Date(clusters[i][0].consultationDate))
        .setZone('Australia/Sydney');

      const gapMinutes = currentClusterStart.diff(prevClusterEnd, 'minutes').minutes;
      gaps.push(Math.round(gapMinutes));
    }

    // Use the most common gap as an indicator of slot duration
    if (gaps.length > 0) {
      const avgGap = gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length;

      // Determine slot duration based on average gap
      let detectedSlot: number;
      if (avgGap <= 20) detectedSlot = 15;
      else if (avgGap <= 40) detectedSlot = 30;
      else if (avgGap <= 70) detectedSlot = 60;
      else detectedSlot = 30; // Default fallback

      logger.info(`🎯 SLOT DETECTION: Multi-cluster analysis`, {
        clustersFound: clusters.length,
        gaps: gaps,
        averageGap: avgGap.toFixed(2),
        detectedSlot,
        logic: 'multi_cluster_gap_analysis',
        context: 'slot_detection_result'
      });

      return detectedSlot;
    }
  }

  // Default fallback
  logger.info(`🎯 SLOT DETECTION: Using default slot duration`, {
    detectedSlot: 30,
    logic: 'default_fallback',
    context: 'slot_detection_result'
  });

  return 30;
};

/**
 * Finds the currently active appointment slot that contains the current time
 * @param consultations Array of consultation data
 * @param slotDuration Detected slot duration in minutes
 * @returns Information about the active slot, or null if no active slot
 */
export const findCurrentActiveSlot = (
  consultations: ConsultationTimeData[],
  slotDuration: number
): ActiveSlotInfo | null => {
  if (consultations.length === 0) {
    return null;
  }

  const currentSydneyTime = DateTime.now().setZone('Australia/Sydney');

  // Convert all consultation times to Sydney timezone and sort
  const consultationTimes = consultations.map(c => ({
    patientID: c.patientID,
    time: DateTime.fromJSDate(new Date(c.consultationDate)).setZone('Australia/Sydney')
  })).sort((a, b) => a.time.toMillis() - b.time.toMillis());

  // Find the appointment slot that contains the current time
  // We'll look for a group of consultations that form an appointment slot

  // First, try to find if current time falls within any existing consultation cluster
  for (let i = 0; i < consultationTimes.length; i++) {
    const consultationTime = consultationTimes[i].time;

    // Check if we can form a slot starting from this consultation
    const potentialSlotStart = consultationTime;
    const potentialSlotEnd = consultationTime.plus({ minutes: slotDuration });

    // Count how many consultations fall within this potential slot
    const consultationsInSlot = consultationTimes.filter(c =>
      c.time >= potentialSlotStart && c.time < potentialSlotEnd
    );

    // If current time falls within this slot and there are consultations in it
    if (currentSydneyTime >= potentialSlotStart &&
        currentSydneyTime < potentialSlotEnd &&
        consultationsInSlot.length > 0) {
      return {
        slotTime: potentialSlotStart,
        intervalMinutes: slotDuration
      };
    }
  }

  // If no slot found containing current time, find the nearest upcoming slot
  const futureConsultations = consultationTimes.filter(c => c.time > currentSydneyTime);
  if (futureConsultations.length > 0) {
    const nextConsultation = futureConsultations[0];
    return {
      slotTime: nextConsultation.time,
      intervalMinutes: slotDuration
    };
  }

  return null; // No active slot found
};

/**
 * Gets consultations that fall within the currently active slot
 * @param consultations Array of all consultations
 * @param activeSlot Information about the active slot
 * @returns Array of patient IDs whose consultations are in the active slot
 */
export const getConsultationsInActiveSlot = (
  consultations: ConsultationTimeData[],
  activeSlot: ActiveSlotInfo
): string[] => {
  if (!activeSlot) {
    return [];
  }

  const slotStart = activeSlot.slotTime;
  const slotEnd = activeSlot.slotTime.plus({ minutes: activeSlot.intervalMinutes });

  return consultations
    .filter(consultation => {
      // Convert UTC timestamp from database to Sydney timezone
      const consultationTime = DateTime.fromJSDate(new Date(consultation.consultationDate))
        .setZone('Australia/Sydney');

      return consultationTime >= slotStart && consultationTime < slotEnd;
    })
    .map(consultation => consultation.patientID);
};

/**
 * Calculates the time remaining in the current active slot
 * @param activeSlotInfo Information about the active slot
 * @param detectedInterval The detected interval in minutes
 * @returns Minutes remaining in the slot
 */
export const calculateTimeRemainingInSlot = (
  activeSlotInfo: ActiveSlotInfo,
  detectedInterval: number
): number => {
  try {
    const now = DateTime.now().setZone('Australia/Sydney');
    const slotStart = activeSlotInfo.slotTime;
    const slotEnd = slotStart.plus({ minutes: detectedInterval });

    const remainingMinutes = slotEnd.diff(now, 'minutes').minutes;
    return Math.max(0, Math.round(remainingMinutes));
  } catch (error) {
    logger.error('Error calculating time remaining in slot:', error);
    return 30; // Default fallback
  }
};

/**
 * Finds the next upcoming consultation slot time
 * @param consultations Array of consultation data
 * @returns Formatted time string of next slot or null
 */
export const findNextSlotTime = (
  consultations: ConsultationTimeData[]
): string | null => {
  try {
    const currentSydneyTime = DateTime.now().setZone('Australia/Sydney');

    // Find the next consultation that starts after current time
    const futureConsultations = consultations
      .map(consultation => ({
        ...consultation,
        sydneyTime: DateTime.fromJSDate(new Date(consultation.consultationDate))
          .setZone('Australia/Sydney')
      }))
      .filter(consultation => consultation.sydneyTime > currentSydneyTime)
      .sort((a, b) => a.sydneyTime.toMillis() - b.sydneyTime.toMillis());

    if (futureConsultations.length > 0) {
      return futureConsultations[0].sydneyTime.toFormat('h:mm a');
    }

    return null;
  } catch (error) {
    logger.error('Error finding next slot time:', error);
    return null;
  }
};



/**
 * Main function that performs dynamic range detection and returns patients in active slot
 * @param db Database connection pool
 * @returns Array of patient IDs whose consultations are currently active
 */
export const getPatientsInCurrentActiveSlot = async (db: Pool): Promise<{
  patientIDs: string[];
  activeSlotInfo: ActiveSlotInfo | null;
  detectedInterval: number;
  totalConsultations: number;
}> => {
  const startTime = Date.now();



  try {
    // Step 1: Fetch all consultations for today
    const consultations = await fetchTodaysConsultations(db);

    

    // Step 2: Detect the appointment slot duration
    const detectedInterval = detectAppointmentSlot(consultations);

    // Step 3: Find the currently active slot
    const activeSlot = findCurrentActiveSlot(consultations, detectedInterval);

    // Step 4: Get patients in the active slot
    const patientIDs = activeSlot ? getConsultationsInActiveSlot(consultations, activeSlot) : [];

    // Log the results
    if (activeSlot) {
      const timeRemaining = calculateTimeRemainingInSlot(activeSlot, detectedInterval);
      const currentTime = getCurrentSydneyTimeForLogging();
      const slotStart = activeSlot.slotTime.toFormat('h:mm a');
      const slotEnd = activeSlot.slotTime.plus({ minutes: detectedInterval }).toFormat('h:mm a');

      logger.info(`🎯 ACTIVE SLOT IDENTIFIED`, {
        slotStart,
        slotEnd,
        intervalMinutes: detectedInterval,
        timeRemainingInSlot: timeRemaining,
        patientsInSlot: patientIDs.length,
        context: 'active_slot_detection'
      });

      // Enhanced visible slot information
      console.log(`
🎯 ACTIVE CONSULTATION SLOT DETECTED:
   📅 Slot time: ${slotStart} - ${slotEnd}
   ⏱️  Slot duration: ${detectedInterval} minutes
   🕐 Current time: ${currentTime}
   ⏳ Time remaining in slot: ${timeRemaining} minutes
   👥 Patients in this slot: ${patientIDs.length}
   📋 Patient IDs: [${patientIDs.slice(0, 5).join(', ')}${patientIDs.length > 5 ? '...' : ''}]`);
    } else {
      const currentTime = getCurrentSydneyTimeForLogging();
      const nextSlotTime = findNextSlotTime(consultations);

      logger.warn(`❌ NO ACTIVE SLOT FOUND`, {
        currentTime,
        totalConsultations: consultations.length,
        detectedInterval,
        nextSlotTime,
        context: 'active_slot_detection'
      });

      console.log(`
❌ NO ACTIVE CONSULTATION SLOT FOUND:
   🕐 Current time: ${currentTime}
   📊 Total consultations today: ${consultations.length}
   ⏱️  Detected interval: ${detectedInterval} minutes
   ⏭️  Next slot time: ${nextSlotTime || 'None found'}`);
    }

    logger.info(`⚡ RANGE DETECTION COMPLETED`, {
      executionTimeMs: Date.now() - startTime,
      result: activeSlot ? 'success' : 'no_active_slot',
      patientsFound: patientIDs.length,
      context: 'performance_monitoring'
    });

    return {
      patientIDs,
      activeSlotInfo: activeSlot,
      detectedInterval,
      totalConsultations: consultations.length
    };
  } catch (error) {
    logger.error(`🚨 RANGE DETECTION ERROR`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTimeMs: Date.now() - startTime,
      context: 'error_handling'
    });

    return {
      patientIDs: [],
      activeSlotInfo: null,
      detectedInterval: 30,
      totalConsultations: 0
    };
  }
};
