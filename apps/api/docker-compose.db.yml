version: '3.9'

services:
  docui-db:
    image: postgres
    restart: always
    networks:
      docui:
    shm_size: 128mb
    volumes:
      - type: tmpfs
        target: /dev/shm
        tmpfs:
          size: 134217728 # 128*2^20 bytes = 128Mb
    environment:
      POSTGRES_NAME: docui
      POSTGRES_USER: docui
      POSTGRES_PASSWORD: docui
    ports:
      - 5432:5432

  adminer:
    image: adminer
    restart: always
    networks:
      docui:
    ports:
      - 8081:8080

networks:
  docui:
    name: docui
