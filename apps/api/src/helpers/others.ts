import axios from 'axios';

export async function getStateFromIp(ip) {
  try {
    const response = await axios.get(`http://ip-api.com/json/${ip}`);
    const data = response.data;
    console.log('IP : ', ip);
    if (data && data.status === 'success') {
      console.log('Region name : ', data.regionName);
      return data.regionName;
    } else {
      console.error('IP lookup failed:', data ? data.message : 'Unknown error');
      return null;
    }
  } catch (error) {
    console.error('Error during IP lookup:', JSON.stringify(error));
    return null;
  }
}
