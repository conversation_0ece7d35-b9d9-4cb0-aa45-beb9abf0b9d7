import { Box, CircularProgress, Stack, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import HighlightOffIcon from "@mui/icons-material/HighlightOff";
import moment from "moment";
import { ReportData } from "../../types";
import ZenithLogo from "../../assets/Zenith.svg";
import highRiskImage from "../../assets/high_risk.png";
import mediumRiskImage from "../../assets/medium_risk.png";
import lowRiskImage from "../../assets/low_risk.png";

const PatientReportTemplate: React.FC<{ reportData: ReportData; loading: boolean }> = ({ reportData, loading }) => {
	const {
		pregnancy = "",
		condition = "",
		cardiovascular_diseases = "",
		psychotic_disorder = "",
		tried_medications = [],
		response = "",
		createdAt = "",
		explanation = "",
		patientName = "",
		risk_score = 0,
	} = reportData || {};

	const risk_assessment = risk_score < 2 ? "Low Risk" : risk_score < 4 ? "Medium Risk" : "High Risk";

	const getRiskScoreImage = () => {
		switch (risk_score) {
			case 0:
				return lowRiskImage; // Path to low risk image
			case 1:
				return lowRiskImage; // Path to medium risk image
			case 2:
				return mediumRiskImage; // Path to high risk image
			case 3:
				return mediumRiskImage; // Path to very high risk image
			case 4:
				return highRiskImage; // Path to extreme risk image
			case 5:
				return highRiskImage; // Path to critical risk image
			default:
				return "unknown-risk.png"; // Default image if risk score is not recognized
		}
	};

	const assessmentOutcome =
		response === "YES" ? "Suitable for medicinal cannabis flower" : "Not suitable for medicinal cannabis flower";

	const recommendation =
		response === "YES"
			? "The patient is a suitable candidate for medicinal cannabis flower."
			: "The patient is not a suitable candidate for medicinal cannabis flower at this time.";

	return (
		<Grid container spacing={2} sx={{ padding: { xs: 1, sm: 2 } }} direction="column" alignItems="center">
			{loading ? (
				<Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
					<CircularProgress />
				</Box>
			) : !reportData || Object.keys(reportData).length === 0 ? (
				<Box sx={{ display: "flex", flexDirection: "column", alignItems: "center", p: 4 }}>
					<Typography variant="h6" color="textSecondary" gutterBottom>
						No report data available.
					</Typography>
				</Box>
			) : (
				<Grid
					container
					spacing={2}
					sx={{ padding: 2, maxWidth: "800px" }}
					direction="column"
					alignItems="center"
				>
					<Grid size={12}>
						<Box
							sx={{
								width: "100%",
								backgroundColor: "green",
								height: "80px",
								pl: 4,
								pr: 4,
								borderRadius: 1,
							}}
						>
							<Grid
								container
								direction="row"
								justifyContent="space-between"
								alignItems="center"
								sx={{ height: "100%", width: "100%" }}
							>
								<Grid size={5}>
									<img src={ZenithLogo} width={"100%"} style={{ maxWidth: "150px" }} />
								</Grid>
								<Grid size={7} container justifyContent="flex-end" alignItems="center">
									<Typography variant="h5" color="white" gutterBottom>
										Patient Report
									</Typography>
								</Grid>
							</Grid>
						</Box>
					</Grid>
					<Grid
						size={12}
						pl={{ xs: 1, sm: 4 }}
						pr={{ xs: 1, sm: 4 }}
						pt={{ xs: 1, sm: 4 }}
						direction={"column"}
						alignItems="center"
						justifyContent="center"
						spacing={2}
						container
					>
						<Grid size={12}>
							<Grid container direction="row" justifyContent={"center"} alignItems="center">
								<Grid size={{ xs: 12, sm: 6 }}>
									<Stack
										direction="column"
										justifyContent="center"
										alignItems="flex-start"
										sx={{ width: "100%" }}
									>
										<Typography variant="h5" fontWeight={700} gutterBottom color="primary">
											Patient Assessment Summary
										</Typography>
										<Typography
											variant="body2"
											color="textSecondary"
											textTransform={"capitalize"}
											gutterBottom
										>
											<span style={{ fontWeight: 900 }}>Full Name:</span> {patientName}
										</Typography>
										<Typography variant="body2" color="textSecondary" gutterBottom>
											<span style={{ fontWeight: 900 }}>Consult Date:</span>{" "}
											{moment(createdAt).format("MMMM Do YYYY")}
										</Typography>
										<Typography
											variant="body2"
											color="textSecondary"
											textTransform={"capitalize"}
											gutterBottom
										>
											<span style={{ fontWeight: 900 }}>Condition Evaluated:</span> {condition}
										</Typography>
										<Typography variant="body2" color="textSecondary" gutterBottom>
											<span style={{ fontWeight: 900 }}>Assessment Outcome:</span>{" "}
											{assessmentOutcome}
										</Typography>
									</Stack>
								</Grid>
								<Grid size={{ xs: 12, sm: 6 }} container justifyContent="center" alignItems="center">
									<Stack
										alignItems="center"
										justifyContent="center"
										sx={{ width: "100%" }}
										spacing={2}
									>
										<img
											src={getRiskScoreImage()}
											alt="Risk Score"
											width={"100%"}
											style={{ maxWidth: "200px" }}
										/>
										<Typography variant="body1" color="textSecondary" fontWeight={700}>
											Risk Score{" "}
											<span
												style={{
													padding: "4px",
													borderRadius: "6px",
													color: "white",
													backgroundColor:
														risk_assessment === "Low Risk"
															? "green"
															: risk_assessment === "Medium Risk"
																? "#EAAF0A"
																: "red",
												}}
											>
												{risk_assessment}
											</span>
										</Typography>
									</Stack>
								</Grid>
							</Grid>
						</Grid>
						<Grid size={12} mt={2} mb={2}>
							<Typography variant="h6" gutterBottom color="green" fontWeight={800}>
								Patient Medical Profile
							</Typography>
						</Grid>
						<Grid size={12}>
							<Box sx={{ width: "100%", backgroundColor: "#EBEBEB", padding: 2, borderRadius: 4 }}>
								<Stack direction="row" justifyContent="space-between" alignItems="center">
									<Typography variant="body2" color="textSecondary" fontWeight={900}>
										Pregnancy Status:
									</Typography>
									<Stack direction="row" alignItems="center" spacing={1}>
										<Typography
											color={pregnancy === "yes" ? "red" : "green"}
											variant="body2"
											fontWeight={900}
										>
											{pregnancy === "yes" ? "Pregnant" : "Not Pregnant"}
										</Typography>
										{pregnancy === "no" ? (
											<CheckCircleOutlineIcon sx={{ color: "green" }} />
										) : (
											<HighlightOffIcon sx={{ color: "red" }} />
										)}
									</Stack>
								</Stack>
							</Box>
							<Box sx={{ width: "100%", backgroundColor: "#FFFFFF", padding: 2, borderRadius: 1 }}>
								<Stack direction="row" justifyContent="space-between" alignItems="center">
									<Typography variant="body2" color="textSecondary" fontWeight={900}>
										Condition:
									</Typography>
									<Stack direction="row" alignItems="center" spacing={1}>
										<Typography
											color={response === "YES" ? "green" : "red"}
											variant="body2"
											textTransform="capitalize"
											fontWeight={900}
										>
											{condition}
										</Typography>
										{response === "YES" ? (
											<CheckCircleOutlineIcon sx={{ color: "green" }} />
										) : (
											<HighlightOffIcon sx={{ color: "red" }} />
										)}
									</Stack>
								</Stack>
							</Box>
							<Box sx={{ width: "100%", backgroundColor: "#EBEBEB", padding: 2, borderRadius: 4 }}>
								<Stack direction="row" justifyContent="space-between" alignItems="center">
									<Typography variant="body2" color="textSecondary" fontWeight={900}>
										Cardiovascular Diseases:
									</Typography>
									<Stack direction="row" alignItems="center" spacing={1}>
										<Typography
											color={cardiovascular_diseases !== "no" ? "red" : "green"}
											variant="body2"
											fontWeight={900}
										>
											{cardiovascular_diseases !== "no" ? "Present(Uncontrolled)" : "None"}
										</Typography>
										{cardiovascular_diseases !== "no" ? (
											<HighlightOffIcon sx={{ color: "red" }} />
										) : (
											<CheckCircleOutlineIcon sx={{ color: "green" }} />
										)}
									</Stack>
								</Stack>
							</Box>
							<Box sx={{ width: "100%", backgroundColor: "#FFFFFF", padding: 2, borderRadius: 1 }}>
								<Stack direction="row" justifyContent="space-between" alignItems="center">
									<Typography variant="body2" color="textSecondary" fontWeight={900}>
										Psychotic Disorders:
									</Typography>
									<Stack direction="row" alignItems="center" spacing={1}>
										<Typography
											color={psychotic_disorder === "no" ? "green" : "red"}
											variant="body2"
											fontWeight={900}
										>
											{psychotic_disorder === "no" ? "None" : "Present"}
										</Typography>
										{psychotic_disorder === "no" ? (
											<CheckCircleOutlineIcon sx={{ color: "green" }} />
										) : (
											<HighlightOffIcon sx={{ color: "red" }} />
										)}
									</Stack>
								</Stack>
							</Box>
							<Box sx={{ width: "100%", backgroundColor: "#EBEBEB", padding: 2, borderRadius: 4 }}>
								<Stack direction="row" justifyContent="space-between" alignItems="center">
									<Typography variant="body2" color="textSecondary" fontWeight={900}>
										Prior Medications Tried:
									</Typography>
									<Stack direction="row" alignItems="center" spacing={1}>
										<Stack direction="column" spacing={0.5} alignItems="flex-start">
											{tried_medications?.map((medication, index) => (
												<Typography
													key={index}
													color={reportData.response === "YES" ? "green" : "red"}
													variant="body2"
													fontWeight={900}
												>
													{index + 1}. {medication}
												</Typography>
											))}
										</Stack>
										{response === "YES" ? (
											<CheckCircleOutlineIcon sx={{ color: "green" }} />
										) : (
											<HighlightOffIcon sx={{ color: "red" }} />
										)}
									</Stack>
								</Stack>
							</Box>
						</Grid>
						<Grid size={12} mt={4} mb={3}>
							<Typography color="green" variant="h6" fontWeight={800} gutterBottom>
								Eligibility Evaluation
							</Typography>
						</Grid>
						<Grid size={12}>
							<Stack direction="column" spacing={2} sx={{ width: "100%" }}>
								<Typography variant="body1" color="textSecondary" gutterBottom>
									<Typography component="span" fontWeight="bold">
										Recommendation:{" "}
									</Typography>
									{recommendation}
								</Typography>
								<Typography variant="body1" color="textSecondary" gutterBottom>
									<Typography component="span" fontWeight="bold">
										Rationale:{" "}
									</Typography>
									{explanation}
								</Typography>
							</Stack>
						</Grid>
					</Grid>
					<Grid size={12} mt={2}>
						<Box
							sx={{
								width: "100%",
								backgroundColor: "green",
								height: "50px",
								pl: 2,
								pr: 2,
								borderRadius: 1,
							}}
						>
							<Stack
								direction="row"
								justifyContent="center"
								alignItems="center"
								spacing={1}
								sx={{ height: "100%", width: "100%" }}
							>
								<Typography variant="body2" color="white" gutterBottom>
									Provided by
								</Typography>
								<img src={ZenithLogo} width={"100%"} style={{ maxWidth: "90px" }} />
								<Typography variant="body2" color="white" gutterBottom>
									Pty Ltd
								</Typography>
							</Stack>
						</Box>
					</Grid>
				</Grid>
			)}
		</Grid>
	);
};

export default PatientReportTemplate;