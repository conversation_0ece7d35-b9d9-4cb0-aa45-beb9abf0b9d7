import { Box, Typography } from "@mui/material";
import { Questionnaire, OrderHistory, TreatmentPlanHistory, HealthCheckShape } from "../../../types"
import Grid from "@mui/material/Grid2";
import Order from "./orders/order";
import QuestionnaireForm from "./questionnaire";
import TreatmentPlan from "./treatmentPlan";
import HealthCheck from "./healthCheck";

type HistoryDetailsProps = {
    selectedHistory: Questionnaire | OrderHistory | TreatmentPlanHistory | HealthCheckShape | undefined
}

type History = {
    [key: string]: {
        name: string
        element: React.ReactElement
    }
}

const HistoryTypes: History = {
    order: {
        name: 'Order',
        element: <Order />
    },
    questionnaire: {
        name: "Questionnaire",
        element: <QuestionnaireForm />
    },
    treatmentPlan: {
        name: 'Treatment Plan',
        element: <TreatmentPlan />
    },
    healthCheck: {
        name: 'Health Check - 2 weeks',
        element: <HealthCheck />
    }
}

const HistoryDetails: React.FC<HistoryDetailsProps> = ({ selectedHistory }) => {
    return (
        <>
            <Box sx={{ pl: { lg: 5 } }}>
                <Box>
                    <Grid container justifyContent={'center'} alignItems='center'>
                        <Grid sx={{ flexGrow: 1 }}>
                            <Typography sx={{ fontSize: '24px', fontWeight: 'bold', color: 'green' }}>
                                {selectedHistory?.type ? HistoryTypes[selectedHistory.type]?.name : 'General'}
                            </Typography>
                        </Grid>
                    </Grid>
                </Box>
                <Grid sx={{ mt: 2 }}>
                    {selectedHistory?.type ? HistoryTypes[selectedHistory.type]?.element : null}
                </Grid>
            </Box>
        </>
    )
}

export default HistoryDetails