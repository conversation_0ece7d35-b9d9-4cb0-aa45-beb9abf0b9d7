import config from "../config";

class QueryManager {
  timeZone = `AT TIME ZONE 'Australia/Sydney'`;
  // TODO: Is_Stored_In_DB is null and must be put back in the condition to avoid fetching twice.

  selectPatient(from: string) {
    const data = {
      select_query: `SELECT
                id,
                Auto_Number_Member_ID,
                Dr_<PERSON><PERSON>,
                Consult_Date_Time,
                WP_User_ID,
                Full_Name,
                Email,
                Mobile,
                Dr_Rejection_Date_Time,
                Dr_Approve_Date_Time,
                Date_of_Birth_2,
                Existing_Patient,
                Have_you_used_Alternative_Medicines_before_whethe,
                Doctor_Notes,
                Is_Stored_In_DB,
                State,
                Consult_Range_Start,
                Risk_Rating,
                Consult_Range_End,
                Condition,
                Queue_Tags,
                What_condition_or_symptom_are_you_having_issues_wi,
                Are_you_planning_to_have_children_in_the_near_futu,
                Do_you_suffer_from_any_cardiovascular_diseases_in,
                Do_you_have_an_addiction_to_any_psychoactive_subst,
                What_was_your_gender_at_birth,
                Please_add_the_first_medication_treatment_or_ther,
                Please_add_the_second_medication_treatment_or_the,
                Have_you_discussed_other_treatment_options_with_yo,
                Knowing_the_alternative_management_options_do_you,
                Do_you_suffer_from_psychosis_bipolar_disorder_or
                FROM Leads
                WHERE Consult_Date_Time >= '${from}'
                and ((Dr_Approve_Date_Time is null AND Dr_Rejection_Date_Time is null) and (${config.devMode ? `Full_Name like '%TESTX%'` : `Full_Name not like '%TESTX%'`}) )
                ORDER BY Consult_Date_Time`
    };
    return data;
  }

  // Lightweight version without patient history for main patient list
  fetchPatientDataLightweight() {
    const query = `
    WITH TodaysConsultations AS (
          -- ✅ Step 1: Get consultations scheduled for today only
          SELECT c.*
          FROM Consultation c
          WHERE
              c."consultationDate" ${this.timeZone} >= (NOW() ${this.timeZone})::DATE
              AND c."consultationDate" ${this.timeZone} < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND c.completed = FALSE
      ),
      LatestConsultationPerPatient AS (
          -- ✅ Step 2: Get the most recent consultation per patient for today
          SELECT DISTINCT ON (tc."patientID") tc.*
          FROM TodaysConsultations tc
          ORDER BY tc."patientID", tc."createdAt" DESC
      ),
      MatchingPatientSlots AS (
          -- ✅ Step 3: Get PatientSlots that match the consultation date
          SELECT DISTINCT ON (ps.patient_id, r.date)
              ps.patient_id,
              ps.range_id,
              r."doctorID",
              r.date as range_date
          FROM PatientSlot ps
          JOIN Range r ON ps.range_id = r.id
          WHERE
              r."doctorID" IS NOT NULL
              AND r.date::DATE = (NOW() ${this.timeZone})::DATE  -- Only today's ranges
          ORDER BY ps.patient_id, r.date, ps."createdAt" DESC  -- Get most recent slot per patient per date
      ),
      PatientConsultation AS (
          -- ✅ Step 4: Combine patient data with consultation and doctor assignment
          -- Only return patients who have doctor assignments
          SELECT DISTINCT ON (p."patientID")  -- Ensure one record per patient
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              d."accessID" AS "assignedDoctorID", -- Get doctor assignment
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date,
              -- ✅ Add PatientQueue fields for business rule validation
              pq."noShow",
              pq.attempt,
              pq.status AS queue_status,
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone}
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultationPerPatient lc ON p."patientID" = lc."patientID"
          INNER JOIN MatchingPatientSlots mps ON p."zohoID" = mps.patient_id  -- INNER JOIN ensures only patients with doctor assignments
          LEFT JOIN Dr d ON mps."doctorID" = d.id
          LEFT JOIN PatientQueue pq ON p."patientID" = pq."patientID" AND lc.id = pq."consultationId"  -- Join PatientQueue for business rule fields
          ORDER BY p."patientID", mps."doctorID"  -- Prioritize by doctor ID for consistent results
      )
      -- Return patient data without history for better performance
      SELECT
          patient_id,
          "fullName",
          email,
          "returningPatient",
          "patientID",
          locked,
          "zohoID",
          "drLocked",
          "assignedDoctorID",
          state,
          dob,
          "usedCannabisBefore",
          mobile,
          "riskRating",
          "createdAt",
          "updatedAt",
          consultation,
          consultation_date,
          -- ✅ Include PatientQueue fields for business rule validation
          "noShow",
          attempt,
          queue_status,
          NULL as history  -- Set history to null for lightweight query
      FROM PatientConsultation
      WHERE "fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
          consultation_date ASC NULLS LAST,
          "returningPatient" DESC,
          "riskRating" ASC NULLS LAST,
          "fullName" ASC;`;

    return query;
  }

  // Query to fetch patients with consultations today but no doctor assignment
  fetchUnassignedPatientsLightweight() {
    const query = `
    WITH TodaysConsultations AS (
          -- ✅ Step 1: Get consultations scheduled for today only
          SELECT c.*
          FROM Consultation c
          WHERE
              c."consultationDate" ${this.timeZone} >= (NOW() ${this.timeZone})::DATE
              AND c."consultationDate" ${this.timeZone} < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND c.completed = FALSE
      ),
      LatestConsultationPerPatient AS (
          -- ✅ Step 2: Get the most recent consultation per patient for today
          SELECT DISTINCT ON (tc."patientID") tc.*
          FROM TodaysConsultations tc
          ORDER BY tc."patientID", tc."createdAt" DESC
      ),
      UnassignedPatients AS (
          -- ✅ Step 3: Find patients with consultations but no doctor assignment
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              'UNASSIGNED' AS "assignedDoctorID",
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date,
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone}
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultationPerPatient lc ON p."patientID" = lc."patientID"
          WHERE NOT EXISTS (
              -- Exclude patients who have doctor assignments for today
              SELECT 1
              FROM PatientSlot ps
              JOIN Range r ON ps.range_id = r.id
              WHERE ps.patient_id = p."zohoID"
                AND r."doctorID" IS NOT NULL
                AND r.date::DATE = (NOW() ${this.timeZone})::DATE
          )
      )
      -- Return unassigned patient data
      SELECT
          patient_id,
          "fullName",
          email,
          "returningPatient",
          "patientID",
          locked,
          "zohoID",
          "drLocked",
          "assignedDoctorID",
          state,
          dob,
          "usedCannabisBefore",
          mobile,
          "riskRating",
          "createdAt",
          "updatedAt",
          consultation,
          consultation_date,
          NULL as history  -- Set history to null for lightweight query
      FROM UnassignedPatients
      WHERE "fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
          consultation_date ASC NULLS LAST,
          "returningPatient" DESC,
          "riskRating" ASC NULLS LAST,
          "fullName" ASC;`;

    return query;
  }

  // Query to get all slots for a specific doctor today (regardless of availability)
  getDoctorSlotsToday() {
    const query = `
    SELECT
        s.id as slot_id,
        s.range_id,
        s.slot as slot_time,
        s.remaining,
        s."noShowRemaining",
        r.date as range_date,
        r.start as range_start,
        r."end" as range_end,
        r."doctorID",
        d."accessID" as doctor_access_id,
        d.username as doctor_name
    FROM Slot s
    JOIN Range r ON s.range_id = r.id
    JOIN Dr d ON r."doctorID" = d.id
    WHERE
        d.email = $1
        AND r.date::DATE = (NOW() ${this.timeZone})::DATE
        AND r.status = 'active'
    ORDER BY s.slot ASC;`;

    return query;
  }

  // todo: NEW DEV - Fix this query to return TP and Questionnaire independently
  fetchPatientData() {
    const query = `
    WITH LatestConsultation AS (
          -- ✅ Step 1: Get the latest consultation per patient, regardless of date
          SELECT DISTINCT ON (c."createdAt",c."consultationDate", c."patientID") c.*
          FROM Consultation c
          JOIN Patient p ON c."patientID" = p."patientID"
          JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
          JOIN Range r ON ps.range_id = r.id
          WHERE r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
          ORDER BY c."patientID", c."createdAt" DESC  -- Get the most recent consultation for each patient
      ),
      PatientConsultation AS (
          -- ✅ Step 2: Now filter to only include consultations scheduled for today
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              d."accessID" AS "assignedDoctorID", -- Get doctor assignment from patientslot → range → doctor relationship
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date, -- Include consultationDate separately
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone}
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultation lc ON p."patientID" = lc."patientID" -- Get latest consultation
          LEFT JOIN PatientSlot ps ON p."zohoID" = ps.patient_id -- Join with PatientSlot using zohoID
          LEFT JOIN Range r ON ps.range_id = r.id -- Join with Range to get doctor assignment
          LEFT JOIN Dr d ON r."doctorID" = d.id -- Join with Dr table to get doctor info
          WHERE
              lc."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
              AND lc."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
              AND lc.completed = FALSE -- ✅ Ensures only today's consultations are considered
      ),
      -- Separate CTEs for each type of data to avoid duplications
      TreatmentPlans AS (
          SELECT 
              email,
              ARRAY_AGG(
                  JSON_BUILD_OBJECT(
                      'id', id,
                      'drName', "drName",
                      'treatmentPlan', outcome,
                      'date', "createdAt" ${this.timeZone},
                      'outcome', outcome,
                      'updatedAt', "updatedAt" ${this.timeZone},
                      'createdAt', "createdAt" ${this.timeZone},
                      'strengthAndConcentration22', "strengthAndConcentration22",
                      'maxDosePerDay22', "maxDose22",
                      'drNotes', "drNotes",
                      'totalQuantity22', "totalQuantity22",
                      'numberOfRepeat22', "numberOfRepeat22",
                      'supplyInterval22', "supplyInterval22",
                      'dosePerDay22', "dosePerDay22",
                      'mentalHealthSupprtingDocument', "mentalHealthSupprtingDocument",
                      'strengthAndConcentration29', "strengthAndConcentration29",
                      'maxDosePerDay29', "maxDose29",
                      'totalQuantity29', "totalQuantity29",
                      'numberOfRepeat29', "numberOfRepeat29",
                      'supplyInterval29', "supplyInterval29",
                      'dosePerDay29', "dosePerDay29"
                  )
              ) AS treatment_plans
          FROM TreatmentPlan
          GROUP BY email
      ),
      PatientOrders AS (
          SELECT
              email,
              ARRAY_AGG(
                  JSON_BUILD_OBJECT(
                      'id', id,
                      'orderNumber', "orderNumber",
                      'date', date ${this.timeZone},
                      'strength', strength,
                      'quantity', quantity,
                      'updatedAt', "updatedAt" ${this.timeZone},
                      'createdAt', "createdAt" ${this.timeZone},
                      'remainingQuantity', "remainingQuantity",
                      'initialQuantity', "initialQuantity",
                      'remainingRepeat', "remainingRepeat",
                      'initialRepeat', "initialRepeat",
                      'createdAt', "createdAt" ${this.timeZone},
                      'updatedAt', "updatedAt" ${this.timeZone}
                  )
              ) AS orders
          FROM Orders
          GROUP BY email
      ),
      PatientQuestionnaires AS (
          SELECT
              "patientID",
              ARRAY_AGG(
                  JSON_BUILD_OBJECT(
                      'id', id,
                      'question', question,
                      'answers', answers,
                      'createdAt', "createdAt" ${this.timeZone},
                      'updatedAt', "updatedAt" ${this.timeZone}
                  )
              ) AS questionnaires
          FROM Questionnaire
          GROUP BY "patientID"
      ),
      HealthChecks AS (
          SELECT
              email,
              ARRAY_AGG(
                  JSON_BUILD_OBJECT(
                      'id', id,
                      'question', question,
                      'answers', answers,
                      'createdAt', "createdAt" ${this.timeZone},
                      'updatedAt', "updatedAt" ${this.timeZone}
                  )
              ) AS health_checks
          FROM HealthCheck
          GROUP BY email
      ),
      PatientHistory AS (
          -- Create patient history using the separate CTEs instead of direct joins
          SELECT
              p."patientID",
              p.email,
              JSON_BUILD_OBJECT(
                  'treatmentPlan', COALESCE(tp.treatment_plans, NULL),
                  'orders', COALESCE(po.orders, NULL),
                  'questionnaire', COALESCE(pq.questionnaires, NULL),
                  'healthCheck', COALESCE(hc.health_checks, NULL)
              ) AS history
          FROM Patient p
          INNER JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
          LEFT JOIN TreatmentPlans tp ON p.email = tp.email
          LEFT JOIN PatientOrders po ON p.email = po.email
          LEFT JOIN PatientQuestionnaires pq ON p."patientID" = pq."patientID"
          LEFT JOIN HealthChecks hc ON p.email = hc.email
      ),
      DedupedPatients AS (
          -- First de-duplicate patients  
          SELECT DISTINCT ON (pc."patientID")
              pc.patient_id,
              pc."fullName",
              pc.email,
              pc."returningPatient",
              pc."patientID",
              pc.locked,
              pc."zohoID",
              pc."drLocked",
              pc."assignedDoctorID", 
              pc.state,
              pc.dob,
              pc."usedCannabisBefore",
              pc.mobile,
              pc."riskRating",
              pc."createdAt",
              pc."updatedAt",
              pc.consultation,
              pc.consultation_date,
              ph.history
          FROM PatientConsultation pc
          LEFT JOIN PatientHistory ph ON pc."email" = ph."email"
          WHERE pc."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
          ORDER BY pc."patientID"
      )
      -- Then apply the desired sort order on the deduplicated result
      SELECT 
          patient_id,
          "fullName",
          email,
          "returningPatient",
          "patientID",
          locked,
          "zohoID",
          "drLocked",
          "assignedDoctorID",
          state,
          dob,
          "usedCannabisBefore",
          mobile,
          "riskRating",
          "createdAt",
          "updatedAt",
          consultation,
          history
      FROM DedupedPatients
      ORDER BY
          consultation_date ASC NULLS LAST, -- First priority: consultation date
          "returningPatient" DESC,          -- Second priority
          "riskRating" ASC NULLS LAST,      -- Third priority  
          "fullName" ASC;                   -- Fourth priority
          `;

    return query;
  }

  fetchNextPatient(limit: string) {
    const query = `    WITH LatestConsultation AS (
          -- ✅ Step 1: Get the latest consultation per patient, regardless of date
          SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.*
          FROM Consultation c
          JOIN Patient p ON c."patientID" = p."patientID"
          JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
          JOIN Range r ON ps.range_id = r.id
          WHERE r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
          ORDER BY c."createdAt",c."consultationDate",c."patientID" DESC  -- Get the most recent consultation for each patient
      ),
      PatientBookings AS (
          -- Get today's bookings with their queue type
          SELECT
              ps."patient_id",
              ps."queueType",
              r.date
          FROM PatientSlot ps
          JOIN Range r ON ps.range_id = r.id
          WHERE
              r.date::DATE >= (NOW() ${this.timeZone})::DATE
              AND r.date::DATE < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
      ),
      PatientConsultation AS (
          -- ✅ Step 2: Now filter to only include consultations scheduled for today
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date, -- Include consultationDate separately
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone},
                  'queueType', COALESCE(pb."queueType", 'regular') -- Include queueType in the consultation object with default 'regular'
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultation lc ON p."patientID" = lc."patientID" -- Get latest consultation
          LEFT JOIN PatientBookings pb ON p."zohoID" = pb."patient_id" -- Left join using zohoID which is stored in patient_id column
          WHERE
              (lc."notificationSent" = false OR lc."notificationSent" IS NULL)
              AND lc."consultationDate" ${this.timeZone} >= (NOW() ${this.timeZone})::DATE
              AND lc."consultationDate" ${this.timeZone} < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND lc.completed = FALSE -- ✅ Ensures only today's consultations are considered
              AND (pb."queueType" = 'regular' OR pb."queueType" IS NULL) -- Include patients booked in the regular queue or without a booking
      )
      SELECT
        pc.patient_id,
        pc."fullName",
        pc.email,
        pc."returningPatient",
        pc."patientID",
        pc.locked,
        pc."zohoID",
        pc."drLocked",
        pc.state,
        pc.dob,
        pc."usedCannabisBefore",
        pc.mobile,
        pc."riskRating",
        pc."createdAt",
        pc."updatedAt",
        pc.consultation
      FROM PatientConsultation pc
      WHERE pc."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
        pc.consultation_date ASC NULLS LAST, -- Order by consultation_date to get the earliest patient
        pc."returningPatient" DESC,
         pc."riskRating" ASC NULLS LAST,
         pc."fullName" ASC
      LIMIT ${limit}; -- Only return the first result;`;

    return query;
  }

  getDoctorByAccessID() {
    const query = `SELECT * FROM Dr WHERE "accessID" = $1;`;
    return query;
  }

  fetchNextNoShowPatient() {
    const query = `    WITH LatestConsultation AS (
          -- ✅ Step 1: Get the latest consultation per patient, regardless of date
          SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.*
          FROM Consultation c
          JOIN Patient p ON c."patientID" = p."patientID"
          JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
          JOIN Range r ON ps.range_id = r.id
          WHERE r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
          ORDER BY c."createdAt",c."consultationDate",c."patientID" DESC  -- Get the most recent consultation for each patient
      ),
      PatientBookings AS (
          -- Get today's bookings with their queue type
          SELECT
              ps."patient_id",
              ps."queueType",
              r.date
          FROM PatientSlot ps
          JOIN Range r ON ps.range_id = r.id
          WHERE
              r.date::DATE >= (NOW() ${this.timeZone})::DATE
              AND r.date::DATE < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
      ),
      PatientConsultation AS (
          -- ✅ Step 2: Now filter to only include consultations scheduled for today
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date, -- Include consultationDate separately
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone},
                  'queueType', COALESCE(pb."queueType", 'regular') -- Include queueType in the consultation object with default 'regular'
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultation lc ON p."patientID" = lc."patientID" -- Get latest consultation
          INNER JOIN PatientBookings pb ON p."zohoID" = pb."patient_id" -- Join with today's bookings for no-show patients using zohoID
          WHERE
              (lc."notificationSent" = false OR lc."notificationSent" IS NULL)
              AND lc."consultationDate" ${this.timeZone} >= (NOW() ${this.timeZone})::DATE
              AND lc."consultationDate" ${this.timeZone} < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND lc.completed = FALSE -- ✅ Ensures only today's consultations are considered
              AND pb."queueType" = 'noShow' -- Only include patients booked in the noShow queue
      )
      SELECT
        pc.patient_id,
        pc."fullName",
        pc.email,
        pc."returningPatient",
        pc."patientID",
        pc.locked,
        pc."zohoID",
        pc."drLocked",
        pc.state,
        pc.dob,
        pc."usedCannabisBefore",
        pc.mobile,
        pc."riskRating",
        pc."createdAt",
        pc."updatedAt",
        pc.consultation
      FROM PatientConsultation pc
      WHERE pc."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
        pc.consultation_date ASC NULLS LAST, -- Order by consultation_date to get the earliest patient
        pc."returningPatient" DESC,
         pc."riskRating" ASC NULLS LAST,
         pc."fullName" ASC
      LIMIT 1; -- Only return one no-show patient`;

    return query;
  }

  fetchNextNoShowPatientForDoctor() {
    const query = `    WITH LatestConsultation AS (
          -- ✅ Step 1: Get the latest consultation per patient, regardless of date
          SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.*
          FROM Consultation c
          JOIN Patient p ON c."patientID" = p."patientID"
          JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
          JOIN Range r ON ps.range_id = r.id
          WHERE r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
          ORDER BY c."createdAt",c."consultationDate",c."patientID" DESC  -- Get the most recent consultation for each patient
      ),
      PatientBookings AS (
          -- Get today's bookings with their queue type for the specific doctor
          SELECT
              ps."patient_id",
              ps."queueType",
              r.date
          FROM PatientSlot ps
          JOIN Range r ON ps.range_id = r.id
          WHERE
              r.date::DATE >= (NOW() ${this.timeZone})::DATE
              AND r.date::DATE < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND r."doctorID" = $1  -- Filter by specific doctor
      ),
      PatientConsultation AS (
          -- ✅ Step 2: Now filter to only include consultations scheduled for today and assigned to the specific doctor
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date, -- Include consultationDate separately
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone},
                  'queueType', COALESCE(pb."queueType", 'regular') -- Include queueType in the consultation object with default 'regular'
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultation lc ON p."patientID" = lc."patientID" -- Get latest consultation
          INNER JOIN PatientBookings pb ON p."zohoID" = pb."patient_id" -- Join with today's bookings for no-show patients using zohoID
          JOIN PatientSlot ps ON p."zohoID" = ps.patient_id  -- Additional join to ensure doctor assignment
          JOIN Range r ON ps.range_id = r.id
          WHERE
              (lc."notificationSent" = false OR lc."notificationSent" IS NULL)
              AND lc."consultationDate" ${this.timeZone} >= (NOW() ${this.timeZone})::DATE
              AND lc."consultationDate" ${this.timeZone} < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND lc.completed = FALSE -- ✅ Ensures only today's consultations are considered
              AND pb."queueType" = 'noShow' -- Only include patients booked in the noShow queue
              AND r."doctorID" = $1  -- Filter by specific doctor
      )
      SELECT
        pc.patient_id,
        pc."fullName",
        pc.email,
        pc."returningPatient",
        pc."patientID",
        pc.locked,
        pc."zohoID",
        pc."drLocked",
        pc.state,
        pc.dob,
        pc."usedCannabisBefore",
        pc.mobile,
        pc."riskRating",
        pc."createdAt",
        pc."updatedAt",
        pc.consultation
      FROM PatientConsultation pc
      WHERE pc."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
        pc.consultation_date ASC NULLS LAST, -- Order by consultation_date to get the earliest patient
        pc."returningPatient" DESC,
         pc."riskRating" ASC NULLS LAST,
         pc."fullName" ASC
      LIMIT 1; -- Only return one no-show patient`;

    return query;
  }

  fetchNextPatientByPatientID() {
    const query = `    WITH LatestConsultation AS (
          -- ✅ Step 1: Get the latest consultation per patient, regardless of date
          SELECT DISTINCT ON (c."createdAt",c."consultationDate", c."patientID") c.*
          FROM Consultation c
          JOIN Patient p ON c."patientID" = p."patientID"
          JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
          JOIN Range r ON ps.range_id = r.id
          WHERE c."patientID" = $1
          AND r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
          ORDER BY c."createdAt",c."consultationDate", c."patientID" DESC  -- Get the most recent consultation for each patient
      ),
      PatientBookings AS (
          -- Get today's bookings with their queue type
          SELECT
              ps."patient_id",
              ps."queueType",
              r.date
          FROM PatientSlot ps
          JOIN Range r ON ps.range_id = r.id
          WHERE
              r.date::DATE >= (NOW() ${this.timeZone})::DATE
              AND r.date::DATE < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              -- Note: $1 is patientID, but patient_id in PatientSlot is zohoID, so we need to join differently
      ),
      PatientConsultation AS (
          -- ✅ Step 2: Now filter to only include consultations scheduled for today
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date, -- Include consultationDate separately
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone},
                  'queueType', COALESCE(pb."queueType", 'regular') -- Include queueType in the consultation object
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultation lc ON p."patientID" = lc."patientID" -- Get latest consultation
          LEFT JOIN PatientBookings pb ON p."zohoID" = pb."patient_id" -- Left join using zohoID which is stored in patient_id column
          WHERE
              (lc."notificationSent" = false OR lc."notificationSent" IS NULL)
              AND lc."consultationDate" ${this.timeZone} >= (NOW() ${this.timeZone})::DATE
              AND lc."consultationDate" ${this.timeZone} < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND lc.completed = FALSE -- ✅ Ensures only today's consultations are considered
      )
      SELECT
        pc.patient_id,
        pc."fullName",
        pc.email,
        pc."returningPatient",
        pc."patientID",
        pc.locked,
        pc."zohoID",
        pc."drLocked",
        pc.state,
        pc.dob,
        pc."usedCannabisBefore",
        pc.mobile,
        pc."riskRating",
        pc."createdAt",
        pc."updatedAt",
        pc.consultation
      FROM PatientConsultation pc
      WHERE pc."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
        pc.consultation_date ASC NULLS LAST, -- Order by consultation_date to get the earliest patient
        pc."returningPatient" DESC,
         pc."riskRating" ASC NULLS LAST,
         pc."fullName" ASC
      LIMIT 1; -- Only return the first result;`;

    return query;
  }

  fetchNextPatientToWarn(limit: string) {
    const query = `    WITH LatestConsultation AS (
          -- ✅ Step 1: Get the latest consultation per patient, regardless of date
          SELECT DISTINCT ON (c."createdAt",c."consultationDate", c."patientID") c.*
          FROM Consultation c
          JOIN Patient p ON c."patientID" = p."patientID"
          JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
          JOIN Range r ON ps.range_id = r.id
          WHERE r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
          ORDER BY c."createdAt",c."consultationDate", c."patientID" DESC  -- Get the most recent consultation for each patient
      ),
      PatientBookings AS (
          -- Get today's bookings with their queue type
          SELECT
              ps."patient_id",
              ps."queueType",
              r.date
          FROM PatientSlot ps
          JOIN Range r ON ps.range_id = r.id
          WHERE
              r.date::DATE >= (NOW() ${this.timeZone})::DATE
              AND r.date::DATE < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
      ),
      PatientConsultation AS (
          -- ✅ Step 2: Now filter to only include consultations scheduled for today
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p."drLocked",
              p.dob,
              p."riskRating",
              p."usedCannabisBefore",
              p.mobile,
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt",
              lc."consultationDate" ${this.timeZone} AS consultation_date, -- Include consultationDate separately
              JSON_BUILD_OBJECT(
                  'joinedAt', lc."joinedAt" ${this.timeZone},
                  'consultationDate', lc."consultationDate" ${this.timeZone},
                  'meetingOngoing', lc."meetingOngoing",
                  'drJoined', lc."drJoined",
                  'id', lc.id,
                  'consultationStart', lc."consultationStart" ${this.timeZone},
                  'consultationEnd', lc."consultationEnd" ${this.timeZone},
                  'notificationSent', lc."notificationSent",
                  'notificationSentDateTime', lc."notificationSentDateTime" ${this.timeZone},
                  'createdAt', lc."createdAt" ${this.timeZone},
                  'updatedAt', lc."updatedAt" ${this.timeZone},
                  'queueType', COALESCE(pb."queueType", 'regular') -- Include queueType in the consultation object with default 'regular'
              ) AS consultation
          FROM Patient p
          INNER JOIN LatestConsultation lc ON p."patientID" = lc."patientID" -- Get latest consultation
          LEFT JOIN PatientBookings pb ON p."zohoID" = pb."patient_id" -- Left join using zohoID which is stored in patient_id column
          WHERE
              lc."consultationDate" ${this.timeZone} >= (NOW() ${this.timeZone})::DATE
              AND lc."consultationDate" ${this.timeZone} < ((NOW() ${this.timeZone})::DATE + INTERVAL '1 day')
              AND lc."warningSent" = FALSE
              AND lc."notificationSent" = TRUE
              AND lc."queueTag" = 'pre-consult'
      )
      SELECT
        pc.patient_id,
        pc."fullName",
        pc.email,
        pc."returningPatient",
        pc."patientID",
        pc.locked,
        pc."zohoID",
        pc."drLocked",
        pc.state,
        pc.dob,
        pc."usedCannabisBefore",
        pc.mobile,
        pc."riskRating",
        pc."createdAt",
        pc."updatedAt",
        pc.consultation
      FROM PatientConsultation pc
      WHERE pc."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
        pc.consultation_date ASC NULLS LAST, -- Order by consultation_date to get the earliest patient
        pc."returningPatient" DESC,
         pc."riskRating" ASC NULLS LAST,
         pc."fullName" ASC
      LIMIT ${limit}; -- Only return the first 3 result;`;

    return query;
  }

  patientHistory() {
    const query = `
      WITH AllConsultations AS (
          SELECT
              c.*
          FROM Consultation c
          ORDER BY c."createdAt" DESC
      ),
      PatientDetails AS (
          SELECT
              p.id AS patient_id,
              p."fullName",
              p.email,
              p."returningPatient",
              p."patientID",
              p.locked,
              p."zohoID",
              p.dob,
              p."usedCannabisBefore",
              p.mobile,
              p."drLocked",
              p.state,
              p."createdAt" ${this.timeZone} AS "createdAt",
              p."updatedAt" ${this.timeZone} AS "updatedAt"
          FROM Patient p
          WHERE p."fullName" ILIKE '%' || $1 || '%' -- Case-sensitive matching
      ),
      ConsultationsForPatient AS (
          SELECT
              p.patient_id,
              JSON_AGG(
                  JSON_BUILD_OBJECT(
                      'id', c.id,
                      'joinedAt', c."joinedAt" ${this.timeZone},
                      'consultationDate', c."consultationDate" ${this.timeZone},
                      'meetingOngoing', c."meetingOngoing",
                      'drJoined', c."drJoined",
                      'completed', c.completed,
                      'consultationStart', c."consultationStart" ${this.timeZone},
                      'consultationEnd', c."consultationEnd" ${this.timeZone},
                      'notificationSent', c."notificationSent",
                      'notificationSentDateTime', c."notificationSentDateTime" ${this.timeZone},
                      'createdAt', c."createdAt" ${this.timeZone},
                      'updatedAt', c."updatedAt" ${this.timeZone}
                  )
              ) AS consultations
          FROM AllConsultations c
          INNER JOIN PatientDetails p ON p."patientID" = c."patientID"
          GROUP BY p.patient_id
      ),
      PatientHistory AS (
          SELECT
              p.patient_id,
              p.email,
              JSON_BUILD_OBJECT(
                  'treatmentPlan', ARRAY_AGG(
                      JSON_BUILD_OBJECT(
                          'id', tp.id,
                          'drName', tp."drName",
                          'treatmentPlan', tp.outcome,
                          'date', tp."createdAt" ${this.timeZone},
                          'dosePerDay', tp."dosePerDay",
                          'outcome', tp.outcome,
                          'updatedAt', tp."updatedAt" ${this.timeZone},
                          'createdAt', tp."createdAt" ${this.timeZone},
                          'maxDosePerDay', tp."maxDose",
                          'drNotes', tp."drNotes",
                          'totalQuantity', tp."totalQuantity",
                          'numberOfRepeat', tp."numberOfRepeat",
                          'supplyInterval', tp."supplyInterval"

                          'strengthAndConcentration22', tp."strengthAndConcentration22",
                          'maxDosePerDay22', tp."maxDose22",
                          'drNotes', tp."drNotes",
                          'totalQuantity22', tp."totalQuantity22",
                          'numberOfRepeat22', tp."numberOfRepeat22",
                          'supplyInterval22', tp."supplyInterval22",
                          'mentalHealthSupprtingDocument', tp."mentalHealthSupprtingDocument",
                          'strengthAndConcentration29', tp."strengthAndConcentration29",
                          'maxDosePerDay29', tp."maxDose29",
                          'totalQuantity29', tp."totalQuantity29",
                          'numberOfRepeat29', tp."numberOfRepeat29",
                          'supplyInterval29', tp."supplyInterval29"
                      )
                  ) FILTER (WHERE tp.id IS NOT NULL),
                  'orders', ARRAY_AGG(
                      JSON_BUILD_OBJECT(
                          'id', o.id,
                          'orderNumber', o."orderNumber",
                          'date', o.date ${this.timeZone},
                          'strength', o.strength,
                          'quantity', o.quantity,
                          'updatedAt', o."updatedAt" ${this.timeZone},
                          'createdAt', o."createdAt" ${this.timeZone},
                          'remainingQuantity', o."remainingQuantity",
                          'initialQuantity', o."initialQuantity",
                          'remainingRepeat', o."remainingRepeat",
                          'initialRepeat', o."initialRepeat"
                      )
                  ) FILTER (WHERE o.id IS NOT NULL),
                  'questionnaire', ARRAY_AGG(
                      JSON_BUILD_OBJECT(
                          'id', q.id,
                          'question', q.question,
                          'answers', q.answers,
                          'created_at', q."createdAt" ${this.timeZone},
                          'updated_at', q."updatedAt" ${this.timeZone}
                      )
                  ) FILTER (WHERE q.id IS NOT NULL),
                  'healthCheck', ARRAY_AGG(
                      JSON_BUILD_OBJECT(
                          'id', hc.id,
                          'question', hc.question,
                          'answers', hc.answers,
                          'created_at', hc."createdAt" ${this.timeZone},
                          'updated_at', hc."updatedAt" ${this.timeZone}
                      )
                  ) FILTER (WHERE hc.id IS NOT NULL)
              ) AS history
          FROM PatientDetails p
          LEFT JOIN TreatmentPlan tp ON p."email" = tp."email"
          LEFT JOIN Orders o ON p."email" = o."email"
          LEFT JOIN Questionnaire q ON p."email" = q."email"
          LEFT JOIN HealthCheck hc ON p."email" = hc."email"
          GROUP BY p."email", p.patient_id
      )
      SELECT
          pd.patient_id,
          pd."fullName",
          pd.email,
          pd."returningPatient",
          pd."patientID",
          pd.locked,
          pd."zohoID",
          pd.dob,
          pd."usedCannabisBefore",
          pd.mobile,
          pd."drLocked",
          pd.state,
          pd."createdAt",
          pd."updatedAt",
          cf.consultations,
          ph.history
      FROM PatientDetails pd
      LEFT JOIN ConsultationsForPatient cf ON pd.patient_id = cf.patient_id
      LEFT JOIN PatientHistory ph ON pd.email = ph.email
      WHERE pd."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
      ORDER BY
          pd."fullName" ASC;
    `;

    return query;
  }

  // todod: NEW DEV: Add questionnaire once you know how to handle it.

  InboxData() {
    const query = `WITH combined_data AS (
    SELECT
        'treatmentplan' AS type,
        tp.id,
        tp."patientID",
        tp."drId",
        tp."consultationId",
        tp."createdAt" ${this.timeZone} AS "createdAt",
        tp."updatedAt" ${this.timeZone} AS "updatedAt",
        tp.outcome,
        tp."email",
        tp."drNotes",
        tp.date,
        tp."drName",
        tp."type",
        tp."source",
        tp."dosePerDay22",
        tp."strengthAndConcentration22",
        tp."maxDose22",
        tp."totalQuantity22",
        tp."numberOfRepeat22",
        tp."supplyInterval22",
        tp."dosePerDay29",
        tp."strengthAndConcentration29",
        tp."maxDose29",
        tp."totalQuantity29",
        tp."numberOfRepeat29",
        tp."supplyInterval29",
        tp."mentalHealthSupprtingDocument",
        p."fullName"
    FROM TreatmentPlan tp
    JOIN Patient p ON tp."email" = p.email
    WHERE p."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
    UNION ALL

    SELECT
        'healthcheck' AS type,
        hc.id,
        hc."patientID",
        NULL AS "drId",
        NULL AS "consultationId",
        hc."createdAt" ${this.timeZone} AS "createdAt",
        hc."updatedAt" ${this.timeZone} AS "updatedAt",
        NULL AS outcome,
        hc."email",
        hc."type",
        NULL AS "source",
        NULL AS "drNotes",
        NULL AS date,
        NULL AS "drName",
        NULL AS "dosePerDay22",
        NULL AS "strengthAndConcentration22",
        NULL AS "maxDose22",
        NULL AS "totalQuantity22",
        NULL AS "numberOfRepeat22",
        NULL AS "supplyInterval22",
        NULL AS "dosePerDay29",
        NULL AS "strengthAndConcentration29",
        NULL AS "maxDose29",
        NULL AS "totalQuantity29",
        NULL AS "numberOfRepeat29",
        NULL AS "supplyInterval29",
        NULL AS "mentalHealthSupprtingDocument",
        p."fullName"
    FROM HealthCheck hc
    JOIN Patient p ON hc."email" = p.email
    WHERE p."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
    UNION ALL

    SELECT
        'order' AS type,
        o.id,
        o."patientID",
        NULL AS "drId",
        NULL AS "consultationId",
        o."createdAt" ${this.timeZone} AS "createdAt",
        o."updatedAt" ${this.timeZone} AS "updatedAt",
        NULL AS outcome,
        o."email",
        NULL AS "drNotes",
        o.date,
        NULL AS "drName",
        o."type",
        NULL AS "source",
        NULL AS "dosePerDay22",
        NULL AS "strengthAndConcentration22",
        NULL AS "maxDose22",
        NULL AS "totalQuantity22",
        NULL AS "numberOfRepeat22",
        NULL AS "supplyInterval22",
        NULL AS "dosePerDay29",
        NULL AS "strengthAndConcentration29",
        NULL AS "maxDose29",
        NULL AS "totalQuantity29",
        NULL AS "numberOfRepeat29",
        NULL AS "supplyInterval29",
        NULL AS "mentalHealthSupprtingDocument",
        p."fullName"
    FROM Orders o
    JOIN Patient p ON o."email" = p.email
    WHERE p."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}
),
paginated_data AS (
    SELECT *,
           COUNT(*) OVER() AS "totalRows"
    FROM combined_data
    ORDER BY "createdAt" DESC
    LIMIT $1 OFFSET $2
)
SELECT * FROM paginated_data;
`;

    return query;
  }

  InboxDataByEmail() {
    const query = `WITH combined_data AS (
      SELECT
          'treatmentplan' AS type,
          tp.id,
          tp."patientID",
          tp."drId",
          tp."consultationId",
          tp."createdAt" ${this.timeZone} AS "createdAt",
          tp."updatedAt" ${this.timeZone} AS "updatedAt",
          tp.outcome,
          tp."email",
          tp."drNotes",
          tp.date,
          tp."drName",
          tp."type",
          tp."source",
          tp."dosePerDay22",
          tp."strengthAndConcentration22",
          tp."maxDose22",
          tp."totalQuantity22",
          tp."numberOfRepeat22",
          tp."supplyInterval22",
          tp."dosePerDay29",
          tp."strengthAndConcentration29",
          tp."maxDose29",
          tp."totalQuantity29",
          tp."numberOfRepeat29",
          tp."supplyInterval29",
          tp."mentalHealthSupprtingDocument",
          p."fullName"
      FROM TreatmentPlan tp
      JOIN Patient p ON tp."email" = p.email
      WHERE tp."email" = $1

      UNION ALL

      SELECT
          'healthcheck' AS type,
          hc.id,
          hc."patientID",
          NULL AS "drId",
          NULL AS "consultationId",
          hc."createdAt" ${this.timeZone} AS "createdAt",
          hc."updatedAt" ${this.timeZone} AS "updatedAt",
          NULL AS outcome,
          hc."email",
          hc."type",
          NULL AS "source",
          NULL AS "drNotes",
          NULL AS date,
          NULL AS "drName",
          NULL AS "dosePerDay22",
          NULL AS "strengthAndConcentration22",
          NULL AS "maxDose22",
          NULL AS "totalQuantity22",
          NULL AS "numberOfRepeat22",
          NULL AS "supplyInterval22",
          NULL AS "dosePerDay29",
          NULL AS "strengthAndConcentration29",
          NULL AS "maxDose29",
          NULL AS "totalQuantity29",
          NULL AS "numberOfRepeat29",
          NULL AS "supplyInterval29",
          NULL AS "mentalHealthSupprtingDocument",
          p."fullName"
      FROM HealthCheck hc
      JOIN Patient p ON hc."email" = p.email
      WHERE hc."email" = $1

      UNION ALL

      SELECT
          'order' AS type,
          o.id,
          o."patientID",
          NULL AS "drId",
          NULL AS "consultationId",
          o."createdAt" ${this.timeZone} AS "createdAt",
          o."updatedAt" ${this.timeZone} AS "updatedAt",
          NULL AS outcome,
          o."email",
          NULL AS "drNotes",
          o.date,
          NULL AS "drName",
          o."type",
          NULL AS "source",
          NULL AS "dosePerDay22",
          NULL AS "strengthAndConcentration22",
          NULL AS "maxDose22",
          NULL AS "totalQuantity22",
          NULL AS "numberOfRepeat22",
          NULL AS "supplyInterval22",
          NULL AS "dosePerDay29",
          NULL AS "strengthAndConcentration29",
          NULL AS "maxDose29",
          NULL AS "totalQuantity29",
          NULL AS "numberOfRepeat29",
          NULL AS "supplyInterval29",
          NULL AS "mentalHealthSupprtingDocument",
          p."fullName"
      FROM Orders o
      JOIN Patient p ON o."email" = p.email
      WHERE o."email" = $1
  ),
  paginated_data AS (
      SELECT *,
             COUNT(*) OVER() AS "totalRows"
      FROM combined_data
      ORDER BY "createdAt"
      LIMIT $2 OFFSET $3
  )
  SELECT * FROM paginated_data;
  `;

    return query;
  }
}

export const Queries = new QueryManager();
