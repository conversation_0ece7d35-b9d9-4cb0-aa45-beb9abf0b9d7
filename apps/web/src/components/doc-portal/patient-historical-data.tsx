import { <PERSON>u<PERSON><PERSON>, <PERSON>uList, Typography, ListItemIcon, ListItemText, Button, TextField, Divider } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useEffect, useState } from "react";
import MoveToInboxIcon from '@mui/icons-material/MoveToInbox';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import Inbox from "./inbox/inbox";
import ConsultationCalendar from "./inbox/consultation-calendar";
import { usePatient } from "../../hooks/patient-provider";
import { ApiClient } from "../../services";
import LoadingScreen from "../../utils/loading-screen";
import { AxiosError } from "axios";
import { useHistory } from "../../hooks/history-provider";

const inputStyle = {
    '& .MuiInputBase-root': {
        borderRadius: '5px',
    },
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

const PatientHistoricalData: React.FC = () => {
    const { searchTerm, setSearchTerm, selectedMenu, setSelectedMenu } = useHistory()
    const [localError, setLocalError] = useState<string | undefined>(undefined)
    const [isLoading, setIsLoading] = useState(true)
    const {
        setSearchedPatients,
        setSelectedSearchedPatient,
        setSelectedInboxItem,
        selectedSearchedPatient,
        searchedpatients,
        currentSearchPage,
        setCurrentSearchPage,
        inboxData,
        setError,
        updateInboxData,
    } = usePatient()

    const handleMenuItemClicked = async (value: string) => {
        setSelectedMenu(value)
        setSelectedSearchedPatient(undefined)
        if (value === 'inbox') {
            setSelectedInboxItem(null)
            const result = await ApiClient.fetchAllInbox(currentSearchPage)
            updateInboxData(result)
        }
    }

    const handleNext = async () => {
        if (selectedMenu === 'inbox') {
            setSelectedInboxItem(null)
            const result = selectedSearchedPatient && selectedSearchedPatient?.email ? await ApiClient.fetchInboxByEmail(selectedSearchedPatient?.email, currentSearchPage + 1) : await ApiClient.fetchAllInbox(currentSearchPage + 1)
            updateInboxData(result)
            setCurrentSearchPage(currentSearchPage + 1)
        }
    }

    const handlePrev = async () => {
        if (selectedMenu === 'inbox' && currentSearchPage > 0) {
            setSelectedInboxItem(null)
            const result = selectedSearchedPatient && selectedSearchedPatient?.email ? await ApiClient.fetchInboxByEmail(selectedSearchedPatient?.email, currentSearchPage - 1) : await ApiClient.fetchAllInbox(currentSearchPage - 1)
            updateInboxData(result)
            setCurrentSearchPage(currentSearchPage - 1)
        }
    }

    const handleSearchTermChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setLocalError(undefined)
        setSearchTerm(e.target.value)
    }

    const handleClickedSearch = async () => {
        try {
            if (searchTerm) {
                const trimmedString = searchTerm.replace(/\s+/g, '');
                if (trimmedString !== '') {
                    setIsLoading(true)
                    const result = await ApiClient.getPatientsBySearchTerm(trimmedString)
                    if (result) {
                        setSearchedPatients(() => {
                            return [...result]
                        })
                        setSelectedSearchedPatient(undefined)
                        setIsLoading(false)
                    }
                }
                else {
                    setSearchedPatients(null)
                }
            }
            else {
                setSearchedPatients(null)
            }
        }
        catch (error) {
            setIsLoading(false)
            setError(error as AxiosError<unknown, any>)
        }
    }


    const checkNextPage = () => {
        const limit = 60
        const offset = (Number(currentSearchPage) - 1) * Number(limit)
        const hasNextPage = inboxData?.length > 0 && offset + limit < Number(inboxData?.[0].totalRows);
        if (hasNextPage) {
            return false
        }
        return true

    }

    useEffect(() => {
        const init = async () => {
            setIsLoading(true)
            try {
                setSelectedInboxItem(null)
                const result = await ApiClient.fetchAllInbox(currentSearchPage)
                updateInboxData(result)
                setIsLoading(false)
            }
            catch (error) {
                setIsLoading(false)
                setError(error as AxiosError<unknown, any>)

            }
        }

        init()
    }, [])

    return (
        <>
            {isLoading && <LoadingScreen />}
            <Grid container sx={{ width: '100%' }}>
                <Grid size={{ md: 2 }} container direction='column'>
                    <Grid>
                        <Typography sx={{ fontSize: '28px', fontWeight: 'bold', color: 'green' }}>
                            Patient History
                        </Typography>
                    </Grid>

                    <Grid sx={{ mt: 2 }}>
                        <MenuList>
                            <MenuItem
                                selected={selectedMenu === 'inbox'}
                                onClick={() => handleMenuItemClicked('inbox')}
                                sx={{
                                    '&:hover': {
                                        backgroundColor: '#f0f0f0',
                                        borderRadius: 5
                                    },
                                    '&.Mui-selected, &.Mui-selected:hover': {
                                        backgroundColor: '#e4fade',
                                        borderRadius: 5
                                    }
                                }}
                            >
                                <ListItemIcon>
                                    <MoveToInboxIcon fontSize="small" sx={{ color: 'black' }} />
                                </ListItemIcon>
                                <ListItemText
                                    primary={
                                        <Typography fontWeight={selectedMenu === 'inbox' ? 500 : ''}>
                                            Inbox
                                        </Typography>
                                    }
                                />
                            </MenuItem>
                            <MenuItem selected={selectedMenu === 'consultation_calendar'} onClick={() => handleMenuItemClicked('consultation_calendar')} sx={{
                                '&:hover': {
                                    backgroundColor: '#f0f0f0',
                                    borderRadius: 5
                                },
                                '&.Mui-selected, &.Mui-selected:hover': {
                                    backgroundColor: '#e4fade',
                                    borderRadius: 5
                                }
                            }}>
                                <ListItemIcon>
                                    <EventAvailableIcon fontSize="small" sx={{ color: 'black' }} />
                                </ListItemIcon>
                                <ListItemText
                                    primary={
                                        <Typography fontWeight={selectedMenu === 'consultation_calendar' ? 500 : ''}>
                                            Consultation Calendar
                                        </Typography>
                                    }
                                />
                            </MenuItem>
                        </MenuList>
                    </Grid>
                    <Divider />

                    <Grid container direction='column'>
                        <Grid container alignItems='center' justifyContent={'center'} sx={{ mt: 1, mb: 1 }}>
                            <Typography>
                                Search Result
                            </Typography>
                        </Grid>
                        {
                            searchedpatients && searchedpatients.length <= 0 && <Grid container alignItems='center' justifyContent={'center'} sx={{ mt: 1 }}>
                                <Typography>
                                    No Patient Found
                                </Typography>
                            </Grid>
                        }

                        <Grid sx={{ overflow: 'auto', maxHeight: 'calc(70vh - 80px)' }} >
                            {searchedpatients && searchedpatients.map((entry, index) => {
                                return (
                                    <div key={`${index}`} onClick={async () => {
                                        setSelectedSearchedPatient(entry)
                                        setSelectedInboxItem(null)
                                        if (entry?.email) {
                                            const result = await ApiClient.fetchInboxByEmail(entry?.email, currentSearchPage)
                                            updateInboxData(result)
                                        }

                                    }} style={{
                                        cursor: 'pointer',
                                        width: '100%',
                                    }}>
                                        <Grid container
                                            sx={{
                                                mr: '4px',
                                                ml: 1,
                                                p: 0.5,
                                                backgroundColor: entry?.patientID === selectedSearchedPatient?.patientID ? '#e4fade' : 'white',
                                                "&:hover": {
                                                    // boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.5)" ,
                                                    backgroundColor: '#f0f0f0'
                                                }

                                            }}
                                            direction={"column"}
                                            key={`${index}`}
                                        >
                                            <Grid container sx={{ width: '100%' }}>
                                                <Grid sx={{ width: '100%' }} container direction={'column'}>
                                                    <Grid container alignItems={'center'}>
                                                        <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                                            {`${entry?.fullName || "No Name"}`}
                                                        </Typography>
                                                    </Grid>
                                                    <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem' }}>
                                                        Email: {entry?.email}
                                                    </Typography>

                                                </Grid>
                                            </Grid>
                                        </Grid>
                                        <Divider style={{ marginBottom: "8px", marginLeft: '12px' }} />
                                    </div>
                                )
                            })}
                        </Grid>

                    </Grid>
                </Grid>

                <Grid size={{ md: 10 }} sx={{ pl: 10, mt: 1, zIndex: 0 }} container direction={'column'}>
                    <Grid container sx={{ width: { lg: '70%', xs: '100%' }, mb: 3 }} spacing={2}>
                        <Grid sx={{ width: '70%' }}>
                            <TextField
                                label='Patient Name'
                                size="small"
                                value={searchTerm}
                                name='name'
                                fullWidth={true}
                                sx={{
                                    ...inputStyle,
                                    zIndex: 0
                                }}
                                onChange={handleSearchTermChange}
                                helperText={localError ? localError : undefined}
                                error={localError ? true : false}
                            />
                        </Grid>
                        <Grid>
                            <Button variant='contained' sx={{ backgroundColor: 'green', textTransform: 'none' }} onClick={() => handleClickedSearch()}>
                                Search
                            </Button>
                        </Grid>
                    </Grid>

                    {selectedMenu === 'inbox' && <Grid container alignItems={'center'}>
                        <Grid>
                            <Typography sx={{ fontSize: '12px', whiteSpace: 'nowrap' }}>
                                {`Showing results for: ${selectedSearchedPatient ? selectedSearchedPatient?.fullName : 'All'}`}
                            </Typography>
                        </Grid>
                        <Grid container sx={{ ml: 'auto' }}>
                            <Grid>
                                <Button
                                    variant='text'
                                    disabled={currentSearchPage <= 1}
                                    sx={{ fontSize: '10px', fontWeight: 'bold', color: 'green', }}
                                    onClick={handlePrev}
                                >
                                    PREV
                                </Button>
                            </Grid>
                            <Grid>
                                <Button disabled={checkNextPage()} sx={{ fontSize: '10px', fontWeight: 'bold', color: 'green', }} onClick={handleNext}>
                                    NEXT
                                </Button>
                            </Grid>
                        </Grid>
                    </Grid>}


                    {
                        selectedMenu === 'inbox' && inboxData?.length <= 0 &&

                        <Grid container sx={{ width: '100%' }} justifyContent={'center'} alignItems='center'>
                            <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                YOUR INBOX IS EMPTY - NO DATA FOUND
                            </Typography>
                        </Grid>

                    }

                    <Grid container sx={{ overflow: 'auto', p: 0.5, width: '100%', zIndex: 0, maxHeight: 'calc(100vh - 200px)', }} >
                        <>
                            {
                                selectedMenu === 'inbox' && <Inbox />
                            }
                            {
                                selectedMenu === 'consultation_calendar' && <ConsultationCalendar />
                            }
                        </>
                    </Grid>

                </Grid>
            </Grid>
        </>
    )
}

export default PatientHistoricalData
