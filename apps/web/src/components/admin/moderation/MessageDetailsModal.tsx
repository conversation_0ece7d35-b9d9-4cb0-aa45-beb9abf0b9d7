import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Chip,
  Divider,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Close as CloseIcon,
  AttachFile as AttachFileIcon,
  ZoomIn as ZoomInIcon
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import { ModerationMessage } from '../../../services/moderation.service';

interface MessageDetailsModalProps {
  open: boolean;
  message: ModerationMessage | null;
  onClose: () => void;
  onModerate?: (messageId: string, action: 'approve' | 'reject') => void;
  processing?: boolean;
}

const MessageDetailsModal: React.FC<MessageDetailsModalProps> = ({
  open,
  message,
  onClose,
  onModerate,
  processing = false
}) => {
  const [imageModal, setImageModal] = useState<{
    open: boolean;
    imageUrl: string;
    alt: string;
  }>({
    open: false,
    imageUrl: '',
    alt: ''
  });

  if (!message) return null;

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('h:mm a, dd MMM yyyy');
  };

  const handleModerate = (action: 'approve' | 'reject') => {
    if (onModerate) {
      onModerate(message.messageId, action);
    }
  };

  // Check if message can be reversed (for moderated messages)
  const canReverse = (message: ModerationMessage): boolean => {
    if (message.moderationStatus === 'pending' || !message.moderatedAt) return false;

    const moderatedTime = new Date(message.moderatedAt);
    const currentTime = new Date();
    const diffMinutes = (currentTime.getTime() - moderatedTime.getTime()) / (1000 * 60);

    return diffMinutes <= 30;
  };

  const renderAttachments = () => {
    if (!message.hasAttachments || !message.attachmentData) return null;

    const attachments = JSON.parse(
      typeof message.attachmentData === 'string'
        ? message.attachmentData
        : JSON.stringify(message.attachmentData)
    );

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Attachments ({message.attachmentCount})
        </Typography>
        {attachments.map((attachment: any, index: number) => (
          <Card key={index} sx={{ mb: 2 }}>
            <CardContent>
              {attachment.type === 'image' && attachment.image_url ? (
                <Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="subtitle2" color="primary">
                      📷 Image Attachment
                    </Typography>
                    <Tooltip title="View full size">
                      <IconButton
                        size="small"
                        onClick={() => setImageModal({
                          open: true,
                          imageUrl: attachment.image_url,
                          alt: 'Patient attachment'
                        })}
                      >
                        <ZoomInIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                    <img
                      src={attachment.image_url}
                      alt="Patient attachment"
                      style={{
                        maxWidth: '100%',
                        maxHeight: '400px',
                        objectFit: 'contain',
                        border: '1px solid #ddd',
                        borderRadius: '8px',
                        cursor: 'pointer'
                      }}
                      onClick={() => setImageModal({
                        open: true,
                        imageUrl: attachment.image_url,
                        alt: 'Patient attachment'
                      })}
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'block';
                      }}
                    />
                    <Box sx={{ display: 'none', textAlign: 'center', p: 2 }}>
                      <AttachFileIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        Image failed to load
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <AttachFileIcon sx={{ fontSize: 32, color: 'primary.main' }} />
                  <Box>
                    <Typography variant="subtitle2">
                      {attachment.title || `${attachment.type || 'File'} attachment`}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Type: {attachment.type || 'Unknown'}
                      {attachment.file_size && ` • Size: ${Math.round(attachment.file_size / 1024)}KB`}
                    </Typography>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        ))}
      </Box>
    );
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Message Details</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent>
          {/* Patient Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                👤 Patient Information
              </Typography>
              <Box sx={{ display: 'flex', gap: 4 }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Name</Typography>
                  <Typography variant="body1" fontWeight="bold">{message.patientName}</Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">Patient ID</Typography>
                  <Typography variant="body1">{message.patientId}</Typography>
                </Box>
                <Box>
                  <Typography variant="body2" color="text.secondary">Message ID</Typography>
                  <Typography variant="body1" sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                    {message.messageId}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* Message Content */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                💬 Message Content
              </Typography>

              {message.messageText && message.messageText.trim() ? (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" gutterBottom>Text Message</Typography>
                  <Box sx={{
                    p: 2,
                    bgcolor: 'grey.50',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'grey.200'
                  }}>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                      {message.messageText}
                    </Typography>
                  </Box>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary" fontStyle="italic" sx={{ mb: 2 }}>
                  No text content
                </Typography>
              )}

              {renderAttachments()}
            </CardContent>
          </Card>

          {/* Timestamps */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom color="primary">
                ⏰ Timeline
              </Typography>
              <Box sx={{ display: 'flex', gap: 4 }}>
                <Box>
                  <Typography variant="body2" color="text.secondary">Received</Typography>
                  <Typography variant="body1">{formatTimestamp(message.createdAt)}</Typography>
                </Box>
                {message.moderatedAt && (
                  <Box>
                    <Typography variant="body2" color="text.secondary">Moderated</Typography>
                    <Typography variant="body1">{formatTimestamp(message.moderatedAt)}</Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>

          {/* Moderation Status */}
          {message.moderationStatus !== 'pending' && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  🛡️ Moderation Status
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Chip
                    icon={message.moderationStatus === 'approved' ? <ApproveIcon /> : <RejectIcon />}
                    label={message.moderationStatus.toUpperCase()}
                    size="medium"
                    sx={{
                      backgroundColor: message.moderationStatus === 'approved' ? '#008000' : undefined,
                      color: message.moderationStatus === 'approved' ? 'white' : undefined,
                      '& .MuiChip-icon': {
                        color: message.moderationStatus === 'approved' ? 'white' : undefined
                      }
                    }}
                    color={message.moderationStatus === 'approved' ? undefined : 'error'}
                  />
                </Box>
                <Box sx={{ display: 'flex', gap: 4 }}>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Moderator</Typography>
                    <Typography variant="body1">{message.moderatorName || message.moderatedBy || 'N/A'}</Typography>
                  </Box>
                  {message.moderationReason && (
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body2" color="text.secondary">Reason</Typography>
                      <Typography variant="body1">{message.moderationReason}</Typography>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>
          )}
        </DialogContent>

        <Divider />

        <DialogActions sx={{ p: 3 }}>
          <Button onClick={onClose} variant="outlined">
            Close
          </Button>

          {/* Show moderation buttons for pending messages */}
          {message.moderationStatus === 'pending' && onModerate && (
            <>
              <Button
                onClick={() => handleModerate('reject')}
                variant="contained"
                color="error"
                startIcon={processing ? <CircularProgress size={16} /> : <RejectIcon />}
                disabled={processing}
              >
                {processing ? 'Processing...' : 'Reject'}
              </Button>
              <Button
                onClick={() => handleModerate('approve')}
                variant="contained"
                startIcon={processing ? <CircularProgress size={16} /> : <ApproveIcon />}
                disabled={processing}
                sx={{
                  backgroundColor: '#008000',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#006600'
                  }
                }}
              >
                {processing ? 'Processing...' : 'Approve'}
              </Button>
            </>
          )}

          {/* Show reverse buttons for moderated messages (within 30 minutes) */}
          {message.moderationStatus !== 'pending' && onModerate && canReverse(message) && (
            <>
              <Button
                onClick={() => handleModerate(message.moderationStatus === 'approved' ? 'reject' : 'approve')}
                variant="contained"
                color={message.moderationStatus === 'approved' ? 'error' : 'primary'}
                startIcon={processing ? <CircularProgress size={16} /> :
                  (message.moderationStatus === 'approved' ? <RejectIcon /> : <ApproveIcon />)}
                disabled={processing}
                sx={message.moderationStatus === 'rejected' ? {
                  backgroundColor: '#008000',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#006600'
                  }
                } : undefined}
              >
                {processing ? 'Processing...' :
                  `Reverse to ${message.moderationStatus === 'approved' ? 'Rejected' : 'Approved'}`}
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>

      {/* Full-size Image Modal */}
      <Dialog
        open={imageModal.open}
        onClose={() => setImageModal({ open: false, imageUrl: '', alt: '' })}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Full Size Image
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <img
              src={imageModal.imageUrl}
              alt={imageModal.alt}
              style={{
                maxWidth: '100%',
                maxHeight: '80vh',
                objectFit: 'contain'
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImageModal({ open: false, imageUrl: '', alt: '' })}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default MessageDetailsModal;
