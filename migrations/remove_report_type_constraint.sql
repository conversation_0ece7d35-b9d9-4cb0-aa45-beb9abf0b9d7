-- Migration: Remove report_type CHECK constraint for flexibility
-- Description: Remove the CHECK constraint on report_type to allow dynamic report types
-- Date: 2025-01-24

-- Remove the CHECK constraint on report_type column
-- This allows for dynamic report types without requiring database schema changes

-- First, check if the constraint exists and get its name
-- Note: The constraint name might be auto-generated, so we need to find it first

-- For PostgreSQL, we can drop the constraint by finding its name
DO $$
DECLARE
    constraint_name TEXT;
BEGIN
    -- Find the constraint name for the report_type CHECK constraint
    SELECT conname INTO constraint_name
    FROM pg_constraint 
    WHERE conrelid = 'sales_report_files'::regclass 
    AND contype = 'c' 
    AND pg_get_constraintdef(oid) LIKE '%report_type%';
    
    -- If constraint exists, drop it
    IF constraint_name IS NOT NULL THEN
        EXECUTE 'ALTER TABLE sales_report_files DROP CONSTRAINT ' || constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_name;
    ELSE
        RAISE NOTICE 'No report_type CHECK constraint found to drop';
    END IF;
END $$;

-- Verify the change
-- You can run this to confirm the constraint is removed:
-- SELECT conname, pg_get_constraintdef(oid) 
-- FROM pg_constraint 
-- WHERE conrelid = 'sales_report_files'::regclass AND contype = 'c';

-- Add a comment to document the change
COMMENT ON COLUMN sales_report_files.report_type IS 'Type of report (no constraints - allows dynamic report types like daily, weekly, monthly, custom, sales-agent, etc.)';

-- Optional: Add some example data to show the flexibility
-- INSERT INTO sales_report_files (
--   report_date, 
--   report_type, 
--   report_format,
--   s3_bucket, 
--   s3_key, 
--   file_url, 
--   file_name,
--   content_type,
--   report_title,
--   status
-- ) VALUES 
-- (CURRENT_DATE, 'sales-agent', 'png', 'test-bucket', 'test-key', 'test-url', 'test-file.png', 'image/png', 'Test Sales Agent Report', 'generated'),
-- (CURRENT_DATE, 'custom-analytics', 'pdf', 'test-bucket', 'test-key-2', 'test-url-2', 'test-file-2.pdf', 'application/pdf', 'Test Custom Analytics Report', 'generated');

RAISE NOTICE 'Migration completed: report_type column now accepts any string value for maximum flexibility';
