import Grid from "@mui/material/Grid2";
import { Typography, FormControl, Box, TextField, Divider, useMediaQuery } from "@mui/material";
import { convertTimeStampToReadableFormat } from "../../../../utils";
import { useTheme } from "@mui/material/styles"
import { usePatient } from "../../../../hooks/patient-provider";
import { PatientOrder } from "../../../../types";

const inputStyle = {
    '& .MuiInputBase-root': {
        borderRadius: '5px',
        fontSize: '0.8rem',
        height: '30px',
        padding: '4px 8px',
    },
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
        fontSize: '0.75rem'
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

type RequiredFields = 'quantity' | 'strength' | 'trade_name'

const formFields = {
    quantity: 'Quantity Ordered (g)',
    strength: 'Strength (%)',
    trade_name: 'Product'
}

const Order: React.FC = () => {
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
    const { selectedPatientHistory } = usePatient()
    const orderSelected = selectedPatientHistory as PatientOrder

    return (
        <>
            <Grid container direction={'column'} sx={{ mt: 0 }} spacing={2}>

                <Grid container sx={{ width: '100%' }}>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Order Number :
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {orderSelected?.order_id}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Date :
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {convertTimeStampToReadableFormat(orderSelected?.createdAt)}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Divider sx={{ mt: 1, mb: 1, width: '100%' }} />
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Allowance Left 22:
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {orderSelected?.allowanceLeft22}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Repeat Left 22:
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {orderSelected?.repeatLeft22}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Divider sx={{ mt: 1, mb: 1, width: '100%' }} />
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Allowance Left 29:
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {orderSelected?.allowanceLeft29}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container spacing={2} sx={{ width: '100%' }}>
                        <Grid size={{ lg: 2 }} container>
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                Repeat Left 29:
                            </Typography>
                        </Grid>
                        <Grid size={{ lg: 2 }} container >
                            <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px' }}>
                                {orderSelected?.repeatLeft29}
                            </Typography>
                        </Grid>
                    </Grid>
                </Grid>

                <Grid container direction={'column'}>
                    <Box>
                        <Divider sx={{ mt: 1, mb: 2 }} />
                        <Grid container spacing={2} sx={{ width: '100%', mb: 2 }}>
                            <Grid size={{ lg: 2 }} container>
                                <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: 'bold' }}>
                                    Order Items
                                </Typography>
                            </Grid>
                            <Grid size={{ lg: 2 }} container >
                                <Typography sx={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: 'bold' }}>
                                    {orderSelected.items.length}
                                </Typography>
                            </Grid>
                        </Grid>
                        <Grid container alignItems={'start'} justifyContent={'start'} spacing={1} sx={{ height: '30px', mb: 10 }}>
                            {orderSelected.items.map((item, index) => {
                                return (
                                    Object.keys(item).map((key) => (
                                        <Grid key={`${index}-${key}`} sx={{ width: isDesktop ? null : '100%' }}>
                                            <Typography sx={{ fontSize: '14px' }}>
                                                {formFields[key as RequiredFields] || key} {/* Map key to form field name */}
                                            </Typography>
                                            <FormControl fullWidth={!isDesktop} sx={{ ...inputStyle }} size="small">
                                                <TextField value={item[key as RequiredFields]} />
                                            </FormControl>
                                        </Grid>
                                    ))
                                )}
                            )}

                        </Grid>
                    </Box>
                </Grid>
            </Grid>
        </>
    )
}

export default Order
