import { Pool, PoolClient } from 'pg';
import { v4 as uuid } from 'uuid';
import { logger } from '../config/logger';
import { 
  fetchTodaysConsultations, 
  detectAppointmentSlot
} from '../utils/timeSlotUtils';
import {
  IncompleteConsultation,
  DoctorAssignment,
  ConsultationAnalysis,
  CreatedRange,
  CreatedSlot,
  CreatedPatientSlot,
  FixResult,
  TargetDoctor,
  ZohoConsultationManagerConfig,
  ExistingRange,
  QueryResult,
  ExistingPatientSlot,
  SlotDbRow
} from '../types/zohoConsultation';

/**
 * Service class for fixing Zoho consultations missing proper scheduling structure
 * Leverages existing timeSlotUtils for consultation analysis and pattern detection
 */
export class ZohoConsultationManager {
  private db: Pool;
  private config: ZohoConsultationManagerConfig;

  constructor(db: Pool, config?: Partial<ZohoConsultationManagerConfig>) {
    this.db = db;
    // Default configuration
    this.config = {
      targetDoctors: {
        anjum: 'd8f83cc3-5a44-4a85-a99b-b1594c2de48f', // Dr Anjum ID
        lavett: 'f4ff8ea5-f65e-403d-8af8-d17d0e45e1f5' // Dr Lavett ID
      },
      defaultSlotDuration: 15, // 15 minute slots
      defaultAvailability: 1, // Only 1 patient per slot
      rangeStartTime: '09:00', // Default start time (Sydney time)
      rangeEndTime: '18:00',   // Default end time (Sydney time)
      defaultSlotAvailability: 1, // Default slot availability
      defaultNoShowRemaining: 0  // Default no-show remaining
    };

    // Override defaults with provided config
    if (config) {
      this.config = { ...this.config, ...config };
    }
  }

  /**
   * Main orchestration method to fix Zoho consultations
   */
  async fixZohoConsultations(): Promise<FixResult> {
    const startTime = Date.now();
    const client = await this.db.connect();

    try {
      await client.query('BEGIN');
      logger.info('🚀 Starting Zoho consultation scheduling fix');

      // Step 1: Analyze current consultation patterns using existing utilities
      const analysis = await this.analyzeConsultationPatterns();
      logger.info('📊 Consultation analysis completed', {
        totalIncomplete: analysis.totalIncomplete,
        detectedInterval: analysis.detectedInterval,
        clusters: analysis.consultationClusters.length
      });

      if (analysis.totalIncomplete === 0) {
        await client.query('COMMIT');
        return this.createSuccessResult(startTime, analysis, {
          rangesCreated: 0,
          slotsCreated: 0,
          patientSlotsCreated: 0,
          doctorAssignments: {}
        });
      }

      // Step 2: Create target doctors if they don't exist
      await this.createTargetDoctors(client);

      // Step 3: Verify target doctors exist
      const targetDoctors = await this.verifyTargetDoctors(client);
      logger.info('👥 Target doctors verified', {
        anjumExists: targetDoctors.anjum.exists,
        lavettExists: targetDoctors.lavett.exists
      });

      // Step 4: Find incomplete consultations
      const incompleteConsultations = await this.findIncompleteConsultations(client);
      
      // Debug: Check zohoIDs in incomplete consultations
      const consultationsWithZohoID = incompleteConsultations.filter(c => c.zohoID);
      const consultationsWithoutZohoID = incompleteConsultations.filter(c => !c.zohoID);
      
      logger.info('🔍 Found incomplete consultations', {
        count: incompleteConsultations.length,
        withZohoID: consultationsWithZohoID.length,
        withoutZohoID: consultationsWithoutZohoID.length,
        zohoIDSamples: consultationsWithZohoID.slice(0, 3).map(c => ({
          consultationId: c.consultation_id,
          patientID: c.patientID,
          zohoID: c.zohoID
        })),
        missingZohoIDSamples: consultationsWithoutZohoID.slice(0, 3).map(c => ({
          consultationId: c.consultation_id, 
          patientID: c.patientID,
          fullName: c.fullName
        }))
      });

      // Step 5: Distribute consultations between doctors, taking into account existing workload
      const doctorAssignments = await this.distributeConsultationsEqually(
        incompleteConsultations,
        targetDoctors
      );
      logger.info('⚖️ Consultations distributed with workload balancing', {
        totalIncompleteConsultations: incompleteConsultations.length,
        anjumAssigned: doctorAssignments.find(d => d.doctorId === this.config.targetDoctors.anjum)?.consultations.length || 0,
        lavettAssigned: doctorAssignments.find(d => d.doctorId === this.config.targetDoctors.lavett)?.consultations.length || 0,
        // Show the assignments per doctor for clarity
        assignmentDetails: doctorAssignments.map(a => ({
          doctorName: a.doctorName,
          consultationsCount: a.consultations.length,
          consultationDates: a.consultations.map(c => new Date(c.consultationDate).toISOString().split('T')[0])
            .reduce((acc, date) => {
              acc[date] = (acc[date] || 0) + 1;
              return acc;
            }, {} as Record<string, number>)
        }))
      });

      // Initialize metrics for result
      let totalRangesCreated = 0;
      let totalSlotsCreated = 0;
      let totalPatientSlotsCreated = 0;

      // Step 6: Create ranges for assigned consultations (if any incomplete consultations exist)
      if (incompleteConsultations.length > 0) {
        const createdRanges = await this.createRangesForConsultations(client, doctorAssignments, analysis.detectedInterval);
        totalRangesCreated = createdRanges.length;
        logger.info('📅 Ranges created', {
          rangesCreated: totalRangesCreated
        });

        // Step 7: Create slots within ranges
        const createdSlots = await this.createSlotsForRanges(client, createdRanges);
        totalSlotsCreated = createdSlots.length;
        logger.info('🕐 Slots created', {
          slotsCreated: totalSlotsCreated,
          rangesProcessed: createdRanges.length
        });

        // Debug: Check slots created
        logger.info('🔍 Slots detail', {
          slotSamples: createdSlots.slice(0, 5).map(s => ({
            id: s.id,
            rangeId: s.range_id,
            slot: s.slot
          }))
        });

        // Step 8: Create patient slots (with proper doctor assignment)
        const createdPatientSlots = await this.createPatientSlots(client, doctorAssignments, createdSlots);
        totalPatientSlotsCreated = createdPatientSlots.length;
        logger.info('👥 Patient slots created', {
          patientSlotsCreated: totalPatientSlotsCreated,
          patientSlotSamples: createdPatientSlots.slice(0, 3).map(ps => ({
            patientId: ps.patient_id,
            rangeId: ps.range_id,
            slotId: ps.slot_id
          }))
        });

        // Step 9: Update consultation records with doctor assignments
        await this.updateConsultationRecords(client, doctorAssignments);
        logger.info('📝 Consultation records updated');
      }

      await client.query('COMMIT');
      logger.info('✅ Zoho consultation fix completed successfully');

      // Format doctor assignments for result
      const formattedDoctorAssignments = this.formatDoctorAssignments(doctorAssignments);
      
      // Calculate ranges created per doctor
      doctorAssignments.forEach(assignment => {
        if (formattedDoctorAssignments[assignment.doctorId]) {
          const dateGroups = new Set<string>();
          assignment.consultations.forEach(consultation => {
            const date = new Date(consultation.consultationDate).toISOString().split('T')[0];
            dateGroups.add(date);
          });
          formattedDoctorAssignments[assignment.doctorId].rangesCreated = dateGroups.size;
        }
      });

      return this.createSuccessResult(startTime, analysis, {
        rangesCreated: totalRangesCreated,
        slotsCreated: totalSlotsCreated,
        patientSlotsCreated: totalPatientSlotsCreated,
        doctorAssignments: formattedDoctorAssignments
      });

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('❌ Zoho consultation fix failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTimeMs: Date.now() - startTime
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Analyzes consultation patterns using existing timeSlotUtils
   */
  async analyzeConsultationPatterns(): Promise<ConsultationAnalysis> {
    // Use existing utility to fetch today's consultations
    const consultations = await fetchTodaysConsultations(this.db);
    
    // Use existing utility to detect appointment slot duration
    const detectedInterval = detectAppointmentSlot(consultations);
    
    // Get incomplete consultations for analysis
    const client = await this.db.connect();
    try {
      const incompleteConsultations = await this.findIncompleteConsultations(client);
      
      // Analyze issue breakdown
      const issueBreakdown = {
        missingZohoID: incompleteConsultations.filter(c => c.issue_type === 'Missing zohoID').length,
        missingPatientSlot: incompleteConsultations.filter(c => c.issue_type === 'Missing PatientSlot').length,
        missingRange: incompleteConsultations.filter(c => c.issue_type === 'Missing Range for date').length,
        missingDoctor: incompleteConsultations.filter(c => c.issue_type === 'Range missing doctor').length
      };

      // TODO: Implement clustering logic for incomplete consultations
      const consultationClusters: IncompleteConsultation[][] = [incompleteConsultations];

      return {
        totalIncomplete: incompleteConsultations.length,
        detectedInterval,
        consultationClusters,
        issueBreakdown
      };
    } finally {
      client.release();
    }
  }

  /**
   * Finds consultations that are missing proper scheduling structure
   */
  async findIncompleteConsultations(client: PoolClient): Promise<IncompleteConsultation[]> {
    const query = `
      WITH ConsultationRangeCheck AS (
        SELECT DISTINCT
          c.id as consultation_id,
          c."patientID",
          c."consultationDate",
          c."drId",
          c."queueTag",
          p."zohoID",
          p."fullName",
          p.email,
          -- Check if patient has any range for the consultation date
          EXISTS(
            SELECT 1 FROM patientslot ps2
            JOIN range r2 ON ps2.range_id = r2.id
            WHERE ps2.patient_id = p."zohoID"
            AND r2.date::DATE = c."consultationDate"::DATE
          ) as has_range_for_date,
          -- Check if patient has any patient slots at all
          EXISTS(
            SELECT 1 FROM patientslot ps3
            WHERE ps3.patient_id = p."zohoID"
          ) as has_patient_slots
        FROM Consultation c
        JOIN Patient p ON c."patientID" = p."patientID"
        WHERE c."consultationDate"::DATE = (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND (c.completed IS NULL OR c.completed = false)
      )
      SELECT
        consultation_id,
        "patientID",
        "consultationDate",
        "drId",
        "queueTag",
        "zohoID",
        "fullName",
        email,
        CASE
          WHEN "zohoID" IS NULL THEN 'Missing zohoID'
          WHEN "drId" IS NULL THEN 'Missing doctor assignment'
          WHEN NOT has_patient_slots THEN 'Missing PatientSlot'
          WHEN NOT has_range_for_date THEN 'Missing Range for date'
          ELSE 'Complete'
        END as issue_type
      FROM ConsultationRangeCheck
      WHERE "zohoID" IS NULL
         OR "drId" IS NULL
         OR NOT has_patient_slots
         OR NOT has_range_for_date
      ORDER BY "consultationDate" DESC;
    `;

    const result = await client.query(query);
    return result.rows;
  }

  /**
   * Creates target doctors if they don't exist
   */
  async createTargetDoctors(client: PoolClient): Promise<void> {
    const createDoctorQuery = `
      INSERT INTO Dr (id, "accessID", name, username, email, status, role, "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      ON CONFLICT (id) DO NOTHING
      RETURNING id;
    `;

    // Create Dr. Anjum
    await client.query(createDoctorQuery, [
      this.config.targetDoctors.anjum,
      this.config.targetDoctors.anjum, // Use ID as accessID
      'Dr. Anjum',
      'Dr. Anjum',
      '<EMAIL>',
      'active',
      'doctor'
    ]);

    // Create Dr. Lavett
    await client.query(createDoctorQuery, [
      this.config.targetDoctors.lavett,
      this.config.targetDoctors.lavett, // Use ID as accessID
      'Dr. Lavett',
      'Dr. Lavett',
      '<EMAIL>',
      'active',
      'doctor'
    ]);

    logger.info('✅ Target doctors created/verified', {
      anjumId: this.config.targetDoctors.anjum,
      lavettId: this.config.targetDoctors.lavett
    });
  }

  /**
   * Verifies that target doctors exist in the database
   */
  async verifyTargetDoctors(client: PoolClient): Promise<{ anjum: TargetDoctor; lavett: TargetDoctor }> {
    const query = `
      SELECT id, "accessID", name, username, email
      FROM Dr 
      WHERE id IN ($1, $2);
    `;

    const result = await client.query(query, [
      this.config.targetDoctors.anjum,
      this.config.targetDoctors.lavett
    ]);

    const doctors = result.rows;
    
    const anjumDoctor = doctors.find(d => d.id === this.config.targetDoctors.anjum);
    const lavettDoctor = doctors.find(d => d.id === this.config.targetDoctors.lavett);

    return {
      anjum: {
        id: this.config.targetDoctors.anjum,
        accessID: anjumDoctor?.accessID || '',
        name: anjumDoctor?.name || 'Dr. Anjum',
        username: anjumDoctor?.username || 'Dr. Anjum',
        email: anjumDoctor?.email || '',
        exists: !!anjumDoctor
      },
      lavett: {
        id: this.config.targetDoctors.lavett,
        accessID: lavettDoctor?.accessID || '',
        name: lavettDoctor?.name || 'Dr. Lavett',
        username: lavettDoctor?.username || 'Dr. Lavett',
        email: lavettDoctor?.email || '',
        exists: !!lavettDoctor
      }
    };
  }

  /**
   * Distributes consultations between doctors, taking into account existing workload
   */
  async distributeConsultationsEqually(
    consultations: IncompleteConsultation[],
    targetDoctors: { anjum: TargetDoctor; lavett: TargetDoctor }
  ): Promise<DoctorAssignment[]> {
    const assignments: DoctorAssignment[] = [];
    const doctorWorkloads = new Map<string, number>();
    
    // Only create assignments for existing doctors
    if (targetDoctors.anjum.exists) {
      assignments.push({
        doctorId: targetDoctors.anjum.id,
        doctorName: targetDoctors.anjum.name,
        accessID: targetDoctors.anjum.accessID,
        consultations: []
      });
      doctorWorkloads.set(targetDoctors.anjum.id, 0);
    }
    
    if (targetDoctors.lavett.exists) {
      assignments.push({
        doctorId: targetDoctors.lavett.id,
        doctorName: targetDoctors.lavett.name,
        accessID: targetDoctors.lavett.accessID,
        consultations: []
      });
      doctorWorkloads.set(targetDoctors.lavett.id, 0);
    }

    // If no doctors exist, return empty assignments
    if (assignments.length === 0) {
      logger.warn('⚠️ No target doctors exist');
      return assignments;
    }

    // Count existing patient slots for each doctor to use as a starting point for distribution
    const client = await this.db.connect();
    try {
      // Get the date range from consultations (to count relevant appointments)
      let minDate = new Date();
      let maxDate = new Date();
      
      if (consultations.length > 0) {
        // Get min and max dates from the consultations being distributed
        const dates = consultations.map(c => new Date(c.consultationDate));
        minDate = new Date(Math.min(...dates.map(d => d.getTime())));
        maxDate = new Date(Math.max(...dates.map(d => d.getTime())));
        
        // Format dates for SQL query
        const minDateStr = minDate.toISOString().split('T')[0];
        const maxDateStr = maxDate.toISOString().split('T')[0];
        
        // Query to count existing patient slots per doctor within the relevant date range
        const existingWorkloadQuery = `
          SELECT r."doctorID", COUNT(ps.id) as patient_count
          FROM patientslot ps
          JOIN range r ON ps.range_id = r.id
          WHERE r.date BETWEEN $1 AND $2
          GROUP BY r."doctorID"
        `;
        
        const workloadResult = await client.query(existingWorkloadQuery, [minDateStr, maxDateStr]);
        
        // Update workload counts with existing patients
        workloadResult.rows.forEach(row => {
          if (doctorWorkloads.has(row.doctorID)) {
            doctorWorkloads.set(row.doctorID, parseInt(row.patient_count));
          }
        });
        
        logger.info('📊 Existing doctor workloads', {
          dateRange: `${minDateStr} to ${maxDateStr}`,
          existingWorkloads: Object.fromEntries(doctorWorkloads.entries())
        });
      }
    } catch (error) {
      logger.warn('⚠️ Error retrieving existing workloads, proceeding with equal distribution', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      client.release();
    }

    // Sort consultations by date
    consultations.sort((a, b) => {
      return new Date(a.consultationDate).getTime() - new Date(b.consultationDate).getTime();
    });

    // Distribute consultations to the doctor with the lower workload
    for (const consultation of consultations) {
      // Find doctor with lowest workload
      let lowestWorkloadDoctorId = assignments[0].doctorId;
      let lowestWorkload = doctorWorkloads.get(lowestWorkloadDoctorId) || 0;
      
      for (const [doctorId, workload] of doctorWorkloads.entries()) {
        if (workload < lowestWorkload) {
          lowestWorkload = workload;
          lowestWorkloadDoctorId = doctorId;
        }
      }
      
      // Assign consultation to doctor with lowest workload
      const doctorAssignment = assignments.find(a => a.doctorId === lowestWorkloadDoctorId);
      if (doctorAssignment) {
        doctorAssignment.consultations.push(consultation);
        // Increment workload count
        doctorWorkloads.set(lowestWorkloadDoctorId, (doctorWorkloads.get(lowestWorkloadDoctorId) || 0) + 1);
      }
    }

    logger.info('⚖️ Consultations distributed with workload balancing', {
      totalConsultations: consultations.length,
      finalWorkloads: Object.fromEntries(doctorWorkloads.entries()),
      assignments: assignments.map(a => ({
        doctorName: a.doctorName,
        consultationsCount: a.consultations.length
      }))
    });

    return assignments;
  }

  /**
   * Creates a success result object
   */
  private createSuccessResult(
    startTime: number,
    analysis: ConsultationAnalysis,
    summary: {
      rangesCreated: number;
      slotsCreated: number;
      patientSlotsCreated: number;
      doctorAssignments: Record<string, {
        doctorName: string;
        consultationsAssigned: number;
        rangesCreated: number;
      }>;
    }
  ): FixResult {
    return {
      success: true,
      message: `Successfully processed ${analysis.totalIncomplete} incomplete consultations`,
      summary: {
        totalConsultationsProcessed: analysis.totalIncomplete,
        consultationsFixed: analysis.totalIncomplete,
        ...summary
      },
      executionTimeMs: Date.now() - startTime
    };
  }

  /**
   * Creates ranges for assigned consultations, reusing existing ranges when available
   */
  async createRangesForConsultations(
    client: PoolClient,
    doctorAssignments: DoctorAssignment[],
    detectedInterval: number
  ): Promise<CreatedRange[]> {
    const createdRanges: CreatedRange[] = [];
    const existingRanges = new Map<string, ExistingRange>(); // Map to store existing ranges

    // First, analyze all consultations to find the global time boundaries
    const allConsultations: IncompleteConsultation[] = doctorAssignments.flatMap(
      assignment => assignment.consultations
    );
    
    if (allConsultations.length === 0) {
      logger.warn('⚠️ No consultations to process');
      return createdRanges;
    }
    
    // Log raw consultation data for debugging
    logger.info('📊 Raw consultation data sample', {
      sample: allConsultations.slice(0, 3).map(c => ({
        consultationId: c.consultation_id?.substring(0, 8) || 'unknown',
        patientName: c.fullName || 'Unknown',
        consultationDate: c.consultationDate,
        dbFormat: typeof c.consultationDate,
        originalDate: new Date(c.consultationDate).toISOString()
      }))
    });
    
    // Define interface for date analysis results
    interface DateAnalysis {
      originalString: string;
      isoString?: string;
      utcTime?: string;
      localTime?: string;
      sydneyTimeString?: string;
      sydneyHour: number;
      sydneyMinute: number;
      sydneyTotalMinutes: number;
      localToSydneyHourDiff?: number;
      error?: string;
    }
    
    // Diagnostic function to fully analyze a date
    const analyzeDateTimezone = (dateStr: string): DateAnalysis => {
      try {
        // Original date object
        const originalDate = new Date(dateStr);
        
        // ISO representation
        const isoString = originalDate.toISOString();
        
        // UTC time components
        const utcHours = originalDate.getUTCHours();
        const utcMinutes = originalDate.getUTCMinutes();
        
        // Local time components
        const localHours = originalDate.getHours();
        const localMinutes = originalDate.getMinutes();
        
        // Direct Sydney conversion using toLocaleString
        const sydneyTimeStr = originalDate.toLocaleString("en-AU", {
          timeZone: "Australia/Sydney",
          hour12: false
        });
        
        // Get Sydney date components using a different method
        const sydneyTimeParts = originalDate.toLocaleString("en-AU", {
          timeZone: "Australia/Sydney",
          hour12: false,
          hour: 'numeric',
          minute: 'numeric'
        }).split(':');
        
        const sydneyHour = parseInt(sydneyTimeParts[0]);
        const sydneyMinute = parseInt(sydneyTimeParts[1]);
        
        // Return comprehensive analysis
        return {
          originalString: dateStr,
          isoString,
          utcTime: `${utcHours}:${utcMinutes}`,
          localTime: `${localHours}:${localMinutes}`,
          sydneyTimeString: sydneyTimeStr,
          sydneyHour: isNaN(sydneyHour) ? 0 : sydneyHour,
          sydneyMinute: isNaN(sydneyMinute) ? 0 : sydneyMinute,
          sydneyTotalMinutes: (isNaN(sydneyHour) ? 0 : sydneyHour) * 60 + (isNaN(sydneyMinute) ? 0 : sydneyMinute),
          localToSydneyHourDiff: isNaN(sydneyHour) ? 0 : sydneyHour - localHours
        };
      } catch (error) {
        return {
          originalString: dateStr,
          error: error instanceof Error ? error.message : 'Unknown error',
          sydneyHour: 0,
          sydneyMinute: 0,
          sydneyTotalMinutes: 0
        };
      }
    };
    
    // Analyze all consultations with comprehensive timezone info
    const consultationAnalysis = allConsultations.map(c => {
      const dateAnalysis = analyzeDateTimezone(c.consultationDate);
      return {
        consultationId: c.consultation_id?.substring(0, 8) || 'unknown',
        patientName: c.fullName || 'Unknown',
        patientID: c.patientID,
        ...dateAnalysis
      };
    });
    
    // Log analysis of ALL consultations to identify potential outliers
    logger.info('🔍 Full consultation time analysis', {
      totalConsultations: consultationAnalysis.length,
      timezoneDiffs: consultationAnalysis.map(c => ({
        id: c.consultationId,
        sydneyTime: c.sydneyTimeString,
        sydneyHour: c.sydneyHour,
        localToSydneyHourDiff: c.localToSydneyHourDiff
      }))
    });
    
    // Find consultations with unusually late times (potential timezone issues)
    const suspiciousConsultations = consultationAnalysis.filter(c => 
      typeof c.sydneyHour === 'number' && (c.sydneyHour >= 24 || c.sydneyHour < 0)
    );
    
    if (suspiciousConsultations.length > 0) {
      logger.warn('⚠️ Found consultations with suspicious times', {
        count: suspiciousConsultations.length,
        examples: suspiciousConsultations.map(c => ({
          id: c.consultationId,
          patientName: c.patientName,
          originalString: c.originalString,
          sydneyTimeString: c.sydneyTimeString,
          sydneyHour: c.sydneyHour,
          localToSydneyHourDiff: c.localToSydneyHourDiff
        }))
      });
    }
    
    // Sort by Sydney time for correct ordering
    consultationAnalysis.sort((a, b) => 
      (a.sydneyTotalMinutes || 0) - (b.sydneyTotalMinutes || 0)
    );
    
    // Filter out consultations with obviously invalid times
    const validConsultations = consultationAnalysis.filter(c => 
      typeof c.sydneyHour === 'number' && c.sydneyHour >= 0 && c.sydneyHour < 24
    );
    
    if (validConsultations.length !== consultationAnalysis.length) {
      logger.warn('⚠️ Filtered out invalid consultations', {
        totalBefore: consultationAnalysis.length,
        totalAfter: validConsultations.length,
        filtered: consultationAnalysis.length - validConsultations.length
      });
    }
    
    // Use filtered list or fall back to all if none are valid
    const consultationsForRangeCalculation = validConsultations.length > 0 
      ? validConsultations 
      : consultationAnalysis;
    
    // Find earliest and latest valid consultation times
    const earliestConsultation = consultationsForRangeCalculation[0];
    const latestConsultation = consultationsForRangeCalculation[consultationsForRangeCalculation.length - 1];
    
    logger.info('🔄 Range boundary consultations', {
      earliest: {
        id: earliestConsultation.consultationId,
        patientName: earliestConsultation.patientName,
        originalTime: earliestConsultation.originalString,
        sydneyTime: earliestConsultation.sydneyTimeString,
        sydneyHour: earliestConsultation.sydneyHour,
        sydneyMinute: earliestConsultation.sydneyMinute
      },
      latest: {
        id: latestConsultation.consultationId,
        patientName: latestConsultation.patientName,
        originalTime: latestConsultation.originalString, 
        sydneyTime: latestConsultation.sydneyTimeString,
        sydneyHour: latestConsultation.sydneyHour,
        sydneyMinute: latestConsultation.sydneyMinute
      }
    });
    
    // Use exact earliest consultation time for range start
    const rangeStartTime = 
      String(earliestConsultation.sydneyHour).padStart(2, '0') + ':' + 
      String(earliestConsultation.sydneyMinute).padStart(2, '0');
    
    // Calculate the expected end time (latest consultation + one interval)
    const endTimeMinutes = (latestConsultation.sydneyTotalMinutes || 0) + detectedInterval;
    const rangeEndHour = Math.floor(endTimeMinutes / 60);
    const rangeEndMinute = endTimeMinutes % 60;
    
    const rangeEndTime = 
      String(rangeEndHour).padStart(2, '0') + ':' + 
      String(rangeEndMinute).padStart(2, '0');
    
    // Additional debug info
    logger.info('⏰ Range time calculation details', {
      earliestConsultationTime: `${earliestConsultation.sydneyHour}:${String(earliestConsultation.sydneyMinute).padStart(2, '0')}`,
      latestConsultationTime: `${latestConsultation.sydneyHour}:${String(latestConsultation.sydneyMinute).padStart(2, '0')}`,
      latestConsultationTotalMinutes: latestConsultation.sydneyTotalMinutes || 0,
      detectedInterval,
      endTimeMinutesCalc: `${latestConsultation.sydneyTotalMinutes || 0} + ${detectedInterval} = ${endTimeMinutes}`,
      endTimeHourCalc: `${endTimeMinutes} / 60 = ${rangeEndHour}`,
      endTimeMinuteCalc: `${endTimeMinutes} % 60 = ${rangeEndMinute}`,
      calculatedRangeStart: rangeStartTime,
      calculatedRangeEnd: rangeEndTime
    });
    
    // Get all unique dates from the consultations to retrieve all possible existing ranges
    const consultationDates = new Set<string>();
    const doctorIds = new Set<string>();
    
    doctorAssignments.forEach(assignment => {
      doctorIds.add(assignment.doctorId);
      assignment.consultations.forEach(consultation => {
        const date = new Date(consultation.consultationDate).toISOString().split('T')[0];
        consultationDates.add(date);
      });
    });
    
    // Get all ranges for these dates and doctors
    if (consultationDates.size > 0 && doctorIds.size > 0) {
      // Create placeholders for SQL query
      const datePlaceholders = Array.from(consultationDates).map((_, i) => `$${i + 1}`).join(', ');
      const doctorPlaceholders = Array.from(doctorIds).map((_, i) => 
        `$${consultationDates.size + i + 1}`
      ).join(', ');
      
      const existingRangeQuery = `
        SELECT * FROM range 
        WHERE date IN (${datePlaceholders})
        AND "doctorID" IN (${doctorPlaceholders})
        AND status = 'active'
        ORDER BY date, start;
      `;
      
      // Combine all parameters
      const params = [
        ...Array.from(consultationDates),
        ...Array.from(doctorIds)
      ];
      
      const existingRangeResult = await client.query(existingRangeQuery, params);
      
      logger.info('🔍 Checking for existing ranges', {
        uniqueDates: Array.from(consultationDates),
        uniqueDoctors: Array.from(doctorIds),
        existingRangesFound: existingRangeResult.rows.length
      });
      
      // Store all existing ranges
      existingRangeResult.rows.forEach(range => {
        // Convert range times to minutes for easier comparison
        const [startHour, startMinute] = range.start.split(':').map(Number);
        const [endHour, endMinute] = range.end.split(':').map(Number);
        
        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;
        
        // Store range with time details for easy lookup
        existingRanges.set(range.id, {
          ...range,
          startMinutes,
          endMinutes
        });
      });
    }
    
    // Helper function to find a suitable existing range for a doctor and date
    const findSuitableRange = (
      doctorId: string,
      date: string
    ) => {
      // Look through all existing ranges to find any range for this doctor and date
      for (const range of existingRanges.values()) {
        // Check if this range is for the right doctor and date
        if (range.doctorID === doctorId && range.date === date) {
          // Return the first matching range for this doctor and date
          // We'll reuse this range for all consultations for this doctor on this date
          return range;
        }
      }

      return null; // No suitable range found
    };

    for (const assignment of doctorAssignments) {
      if (assignment.consultations.length === 0) continue;

      // Group consultations by date
      const consultationsByDate = assignment.consultations.reduce((groups, consultation) => {
        const consultationDate = new Date(consultation.consultationDate);
        const date = consultationDate.toISOString().split('T')[0]; // Get YYYY-MM-DD
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(consultation);
        return groups;
      }, {} as Record<string, IncompleteConsultation[]>);

      // Process each date group
      for (const [date, consultations] of Object.entries(consultationsByDate)) {
        // Check if a suitable existing range already exists for this doctor and date
        const suitableRange = findSuitableRange(
          assignment.doctorId,
          date
        );

        if (suitableRange) {
          logger.info('♻️ Reusing existing range for all consultations', {
            rangeId: suitableRange.id,
            doctorId: assignment.doctorId,
            doctorName: assignment.doctorName,
            date,
            rangeTime: `${suitableRange.start} - ${suitableRange.end}`,
            consultationsCount: consultations.length
          });

          // Add to our created ranges with the existing range data
          createdRanges.push({
            ...suitableRange,
            consultations: consultations
          });

          continue; // Skip range creation
        }

        // If no suitable range found, create one comprehensive range for all consultations
        const rangeId = uuid();

        // Find the earliest and latest consultation times for this doctor on this date
        let earliestTotalMinutes = 24 * 60; // Start with end of day
        let latestTotalMinutes = 0; // Start with beginning of day

        consultations.forEach(consultation => {
          const analysis = analyzeDateTimezone(consultation.consultationDate);
          const totalMinutes = analysis.sydneyHour * 60 + analysis.sydneyMinute;

          earliestTotalMinutes = Math.min(earliestTotalMinutes, totalMinutes);
          latestTotalMinutes = Math.max(latestTotalMinutes, totalMinutes);
        });

        // Calculate comprehensive start and end times
        const startHour = Math.floor(earliestTotalMinutes / 60);
        const startMinute = earliestTotalMinutes % 60;
        const comprehensiveStartTime = `${String(startHour).padStart(2, '0')}:${String(startMinute).padStart(2, '0')}`;

        // End time should be after the latest consultation plus one interval
        const endTotalMinutes = latestTotalMinutes + detectedInterval;
        const endHour = Math.floor(endTotalMinutes / 60);
        const endMinute = endTotalMinutes % 60;
        const comprehensiveEndTime = `${String(endHour).padStart(2, '0')}:${String(endMinute).padStart(2, '0')}`;

        logger.info('📅 Creating comprehensive range for all consultations', {
          date,
          doctorId: assignment.doctorId,
          doctorName: assignment.doctorName,
          consultationsCount: consultations.length,
          rangeStart: comprehensiveStartTime,
          rangeEnd: comprehensiveEndTime,
          earliestConsultation: `${Math.floor(earliestTotalMinutes / 60)}:${String(earliestTotalMinutes % 60).padStart(2, '0')}`,
          latestConsultation: `${Math.floor(latestTotalMinutes / 60)}:${String(latestTotalMinutes % 60).padStart(2, '0')}`
        });

        const createRangeQuery = `
          INSERT INTO range (
            id, day, date, start, "end", interval, availability, status, "doctorID", "createdAt", "updatedAt"
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING *;
        `;

        const rangeResult = await client.query(createRangeQuery, [
          rangeId,
          new Date(date).toLocaleDateString('en-AU', {
            weekday: 'long',
            timeZone: 'Australia/Sydney'
          }), // e.g., "Monday"
          date, // YYYY-MM-DD format
          comprehensiveStartTime,
          comprehensiveEndTime,
          detectedInterval,
          this.config.defaultAvailability,
          'active',
          assignment.doctorId
        ]);

        const newRange = rangeResult.rows[0];

        // Add the new range to our existingRanges map for future consultations
        const [rangeStartHour, rangeStartMinute] = newRange.start.split(':').map(Number);
        const [rangeEndHour, rangeEndMinute] = newRange.end.split(':').map(Number);

        existingRanges.set(newRange.id, {
          ...newRange,
          startMinutes: rangeStartHour * 60 + rangeStartMinute,
          endMinutes: rangeEndHour * 60 + rangeEndMinute
        });

        createdRanges.push({
          ...newRange,
          consultations: consultations
        });

        logger.info('📅 Comprehensive range created', {
          rangeId,
          doctorId: assignment.doctorId,
          doctorName: assignment.doctorName,
          date,
          startTime: comprehensiveStartTime,
          endTime: comprehensiveEndTime,
          consultationsCount: consultations.length,
          interval: detectedInterval
        });
      }
    }

    return createdRanges;
  }

  /**
   * Creates slots for ranges, reusing existing slots when available
   */
  async createSlotsForRanges(
    client: PoolClient,
    createdRanges: CreatedRange[]
  ): Promise<CreatedSlot[]> {
    const createdSlots: CreatedSlot[] = [];
    
    for (const range of createdRanges) {
      // Check if slots already exist for this range
      const existingSlotsQuery = `
        SELECT * FROM slot
        WHERE range_id = $1
        ORDER BY slot;
      `;
      
      const existingSlotsResult = await client.query(existingSlotsQuery, [range.id]);
      const existingSlots = existingSlotsResult.rows;
      
      if (existingSlots.length > 0) {
        logger.info('♻️ Reusing existing slots for range', {
          rangeId: range.id,
          existingSlotsCount: existingSlots.length
        });
        
        // Add existing slots to our created slots collection
        for (const slot of existingSlots) {
          createdSlots.push({
            ...slot,
            consultations: range.consultations.filter(c => {
              // Match consultations to slots based on time range
              const consultationDate = new Date(c.consultationDate);
              const slotTime = slot.slot;

              // Extract start and end times from slot.slot (format: HH:MM - HH:MM)
              const [slotStart, slotEnd] = slotTime.split(' - ');
              if (!slotStart || !slotEnd) {
                return false; // Skip malformed slots
              }

              const [slotStartHour, slotStartMinute] = slotStart.split(':').map(Number);
              const [slotEndHour, slotEndMinute] = slotEnd.split(':').map(Number);

              const slotStartMinutes = slotStartHour * 60 + slotStartMinute;
              const slotEndMinutes = slotEndHour * 60 + slotEndMinute;

              // Get Sydney time components for the consultation
              const sydneyHour = parseInt(consultationDate.toLocaleString('en-AU', {
                timeZone: 'Australia/Sydney',
                hour: 'numeric',
                hour12: false
              }));

              const sydneyMinute = parseInt(consultationDate.toLocaleString('en-AU', {
                timeZone: 'Australia/Sydney',
                minute: 'numeric'
              }));

              const consultationTotalMinutes = sydneyHour * 60 + sydneyMinute;

              // Check if the consultation time falls within this slot's time range
              return consultationTotalMinutes >= slotStartMinutes && consultationTotalMinutes < slotEndMinutes;
            })
          });
        }
        
        continue; // Skip slot creation for this range
      }
      
      // If no existing slots, create new ones
      const rangeStartStr = range.start;
      const rangeEndStr = range.end;
      
      // Split HH:MM format
      const [startHour, startMinute] = rangeStartStr.split(':').map(Number);
      const [endHour, endMinute] = rangeEndStr.split(':').map(Number);
      
      // Convert to minutes for easier calculation
      const startTotalMinutes = startHour * 60 + startMinute;
      const endTotalMinutes = endHour * 60 + endMinute;
      
      // Calculate the number of slots in the range
      const totalMinutes = endTotalMinutes - startTotalMinutes;
      const numberOfSlots = Math.ceil(totalMinutes / range.interval);
      
      logger.info('🔢 Slot calculation details', {
        rangeId: range.id,
        start: rangeStartStr,
        end: rangeEndStr,
        startTotalMinutes,
        endTotalMinutes,
        totalMinutes,
        interval: range.interval,
        calculatedNumberOfSlots: numberOfSlots
      });
      
      // Create each slot
      for (let i = 0; i < numberOfSlots; i++) {
        const slotTimeMinutes = startTotalMinutes + (i * range.interval);
        const slotHour = Math.floor(slotTimeMinutes / 60);
        const slotMinute = slotTimeMinutes % 60;
        
        // Calculate end time for this slot
        const slotEndTimeMinutes = slotTimeMinutes + range.interval;
        const slotEndHour = Math.floor(slotEndTimeMinutes / 60);
        const slotEndMinute = slotEndTimeMinutes % 60;

        // Format as HH:MM - HH:MM (start - end)
        const slotTime =
          String(slotHour).padStart(2, '0') + ':' +
          String(slotMinute).padStart(2, '0') + ' - ' +
          String(slotEndHour).padStart(2, '0') + ':' +
          String(slotEndMinute).padStart(2, '0');
        
        // Skip creating slots that exceed the end time
        if (slotTimeMinutes >= endTotalMinutes) {
          logger.info('⏭️ Skipping slot creation - exceeds range end time', {
            slotTime,
            slotTimeMinutes,
            endTotalMinutes
          });
          continue;
        }
        
        // Filter consultations that match this slot's time range
        const matchingConsultations = range.consultations.filter(c => {
          const consultationDate = new Date(c.consultationDate);

          // Get Sydney time components
          const sydneyHour = parseInt(consultationDate.toLocaleString('en-AU', {
            timeZone: 'Australia/Sydney',
            hour: 'numeric',
            hour12: false
          }));

          const sydneyMinute = parseInt(consultationDate.toLocaleString('en-AU', {
            timeZone: 'Australia/Sydney',
            minute: 'numeric'
          }));

          const consultationTotalMinutes = sydneyHour * 60 + sydneyMinute;

          // Check if the consultation time falls within this slot's time range
          return consultationTotalMinutes >= slotTimeMinutes && consultationTotalMinutes < slotEndTimeMinutes;
        });

        // Check if a slot with this time already exists for this range
        const existingSlotCheck = `
          SELECT id, slot, remaining, "noShowRemaining", range_id, "createdAt", "updatedAt"
          FROM slot
          WHERE range_id = $1 AND slot = $2
          LIMIT 1;
        `;

        const existingSlotResult = await client.query(existingSlotCheck, [range.id, slotTime]);

        let result: QueryResult;
        if (existingSlotResult.rows.length > 0) {
          // Slot already exists, reuse it
          result = existingSlotResult;
          logger.info('♻️ Reusing existing slot', {
            slotId: existingSlotResult.rows[0].id,
            rangeId: range.id,
            time: slotTime,
            message: 'Slot already exists for this time and range'
          });
        } else {
          // Create new slot with conflict resolution (let database auto-generate ID)
          const createSlotQuery = `
            INSERT INTO slot (
              slot, remaining, "noShowRemaining", range_id, "createdAt", "updatedAt"
            ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON CONFLICT (range_id, slot) DO UPDATE SET
              "updatedAt" = CURRENT_TIMESTAMP
            RETURNING *;
          `;

          // Default remaining slots count from configuration
          result = await client.query(createSlotQuery, [
            slotTime,
            this.config.defaultSlotAvailability,
            this.config.defaultNoShowRemaining,
            range.id
          ]);
        }
        
        const slotDbRow = result.rows[0] as unknown as SlotDbRow;
        const createdSlot: CreatedSlot = {
          ...slotDbRow,
          consultations: matchingConsultations
        };
        
        createdSlots.push(createdSlot);
        
        logger.info('⏰ Slot created', {
          slotId: createdSlot.id,
          rangeId: range.id,
          time: slotTime,
          matchingConsultationsCount: matchingConsultations.length,
          matchingConsultationPatients: matchingConsultations.map(c => c.fullName).join(', ')
        });
      }
    }
    
    return createdSlots;
  }

  /**
   * Creates patient slots linking patients to specific slots based on doctor assignments
   */
  async createPatientSlots(
    client: PoolClient,
    doctorAssignments: DoctorAssignment[],
    createdSlots: CreatedSlot[]
  ): Promise<CreatedPatientSlot[]> {
    const createdPatientSlots: CreatedPatientSlot[] = [];

    // Create a map of range_id to doctor for quick lookup
    const rangeToDoctorMap = new Map<string, string>();
    const rangeToDateMap = new Map<string, string>();

    if (createdSlots.length === 0) {
      logger.warn('⚠️ No slots available to create patient slots');
      return createdPatientSlots;
    }

    // Get range information to map ranges to doctors and dates
    const rangeIds = createdSlots.map(s => `'${s.range_id}'`).join(',');
    const rangeQuery = `SELECT id, "doctorID", date FROM range WHERE id IN (${rangeIds})`;
    const rangeResult = await client.query(rangeQuery);

    rangeResult.rows.forEach(range => {
      rangeToDoctorMap.set(range.id, range.doctorID);
      rangeToDateMap.set(range.id, range.date);
    });

    logger.info('🔍 Patient slot creation debug', {
      totalSlots: createdSlots.length,
      doctorAssignments: doctorAssignments.length,
      uniqueRanges: rangeResult.rows.length,
      rangeIds: rangeResult.rows.map(r => r.id)
    });
    
    // Collect all zoho IDs to check for existing patient slots
    const patientIDs = doctorAssignments
      .flatMap(assignment => assignment.consultations)
      .filter(c => c.patientID && c.zohoID)
      .map(c => c.zohoID);
    
    // Create a map to store existing patient slots by patientID
    const existingPatientSlots = new Map<string, ExistingPatientSlot>();
    
    if (patientIDs.length > 0) {
      // Query for existing patient slots for these patients
      const placeholders = patientIDs.map((_, i) => `$${i + 1}`).join(', ');
      const existingPatientSlotsQuery = `
        SELECT ps.*, s.slot as slot_time, s.range_id
        FROM patientslot ps
        JOIN slot s ON ps.slot_id = s.id
        WHERE ps.patient_id IN (${placeholders})
        AND ps."createdAt" >= NOW() - INTERVAL '24 hours'
      `;
      
      const existingPatientSlotsResult = await client.query(existingPatientSlotsQuery, patientIDs);
      
      logger.info('🔍 Checking for existing patient slots', {
        patientsToCheck: patientIDs.length,
        existingPatientsFound: existingPatientSlotsResult.rows.length
      });
      
      // Map existing patient slots by patient_id for easy lookup
      existingPatientSlotsResult.rows.forEach(patientSlot => {
        existingPatientSlots.set(patientSlot.patient_id, patientSlot);
      });
    }

    // Process each doctor's assignments
    for (const assignment of doctorAssignments) {
      // Skip if no consultations
      if (assignment.consultations.length === 0) {
        continue;
      }

      logger.info('👨‍⚕️ Processing doctor assignments', {
        doctorId: assignment.doctorId,
        doctorName: assignment.doctorName,
        consultationsCount: assignment.consultations.length
      });

      for (const consultation of assignment.consultations) {
        // Skip consultations without patientID
        if (!consultation.patientID) {
          logger.warn('⚠️ Missing patientID for consultation', {
            consultationId: consultation.consultation_id,
            skippingPatientSlot: true
          });
          continue;
        }
        
        // Check if patient already has a patient slot (using zohoID as the key)
        if (consultation.zohoID && existingPatientSlots.has(consultation.zohoID)) {
          const existingPatientSlot = existingPatientSlots.get(consultation.zohoID);

          if (!existingPatientSlot) {
            logger.warn('⚠️ Existing patient slot not found despite map check', {
              consultationId: consultation.consultation_id,
              zohoID: consultation.zohoID
            });
            continue;
          }

          logger.info('♻️ Reusing existing patient slot', {
            consultationId: consultation.consultation_id,
            patientID: consultation.patientID,
            patientName: consultation.fullName,
            existingPatientSlotId: existingPatientSlot.id,
            slotTime: existingPatientSlot.slot_time
          });

          // Add to our created patient slots collection
          createdPatientSlots.push({
            patient_id: consultation.zohoID || '',
            range_id: existingPatientSlot.range_id,
            slot_id: existingPatientSlot.slot_id,
            patientID: consultation.patientID
          });

          // Update the consultation record to make sure it's linked to the right doctor
          const doctorId = rangeToDoctorMap.get(existingPatientSlot.range_id);
          if (doctorId) {
            const updateConsultationQuery = `
              UPDATE Consultation 
              SET 
                "drId" = $1,
                "updatedAt" = CURRENT_TIMESTAMP
              WHERE id = $2
              RETURNING *;
            `;
  
            await client.query(updateConsultationQuery, [
              doctorId,
              consultation.consultation_id
            ]);
            
            logger.info('✅ Updated consultation with existing patient slot doctor', {
              consultationId: consultation.consultation_id,
              doctorId
            });
          }
          
          continue; // Skip creating a new patient slot
        }

        // Debug the consultation data
        logger.info('🔍 Processing consultation for new patient slot', {
          consultationId: consultation.consultation_id,
          patientName: consultation.fullName,
          patientID: consultation.patientID,
          consultationDate: consultation.consultationDate,
          zohoID: consultation.zohoID || 'Missing'
        });

        // Check for missing zohoID and try to handle
        if (!consultation.zohoID) {
          logger.warn('⚠️ Missing zohoID for consultation, attempting to find it', {
            consultationId: consultation.consultation_id,
            patientName: consultation.fullName,
            patientID: consultation.patientID
          });
          
          // Try to find zohoID using patientID
          try {
            const zohoQuery = `
              SELECT "zohoID" FROM Patient WHERE "patientID" = $1 AND "zohoID" IS NOT NULL
              LIMIT 1;
            `;
            const zohoResult = await client.query(zohoQuery, [consultation.patientID]);
            
            if (zohoResult.rows.length > 0 && zohoResult.rows[0].zohoID) {
              // Found a zohoID, update the consultation object
              consultation.zohoID = zohoResult.rows[0].zohoID;
              logger.info('✅ Found zohoID for patient via fallback', {
                consultationId: consultation.consultation_id,
                patientID: consultation.patientID,
                zohoID: consultation.zohoID
              });
            } else {
              // Second attempt - try to find by email if available
              if (consultation.email) {
                const emailZohoQuery = `
                  SELECT "zohoID" FROM Patient WHERE email = $1 AND "zohoID" IS NOT NULL
                  LIMIT 1;
                `;
                const emailZohoResult = await client.query(emailZohoQuery, [consultation.email]);
                
                if (emailZohoResult.rows.length > 0 && emailZohoResult.rows[0].zohoID) {
                  consultation.zohoID = emailZohoResult.rows[0].zohoID;
                  logger.info('✅ Found zohoID for patient via email', {
                    consultationId: consultation.consultation_id,
                    patientID: consultation.patientID,
                    email: consultation.email,
                    zohoID: consultation.zohoID
                  });
                } else {
                  logger.error('❌ Could not find zohoID for patient', {
                    consultationId: consultation.consultation_id,
                    patientID: consultation.patientID,
                    email: consultation.email || 'No email',
                    skippingPatientSlot: true
                  });
                  continue; // Skip this consultation
                }
              } else {
                logger.error('❌ Could not find zohoID for patient', {
                  consultationId: consultation.consultation_id,
                  patientID: consultation.patientID,
                  skippingPatientSlot: true
                });
                continue; // Skip this consultation
              }
            }
          } catch (error) {
            logger.error('❌ Error finding zohoID for patient', {
              error: error instanceof Error ? error.message : 'Unknown error',
              consultationId: consultation.consultation_id,
              patientID: consultation.patientID,
              skippingPatientSlot: true
            });
            continue; // Skip this consultation
          }
        }

        // Get consultation date in YYYY-MM-DD format for matching with range date
        const consultationDate = new Date(consultation.consultationDate)
          .toISOString().split('T')[0];

        // Get consultation time in Sydney timezone as HH:MM format
        const consultationTimeSydney = new Date(consultation.consultationDate)
          .toLocaleTimeString("en-AU", {
            timeZone: "Australia/Sydney",
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
          });

        // Find ranges for this doctor on this date
        const doctorRangesForDate: string[] = [];
        for (const [rangeId, date] of rangeToDateMap.entries()) {
          const doctorId = rangeToDoctorMap.get(rangeId);
          if (date === consultationDate && doctorId === assignment.doctorId) {
            doctorRangesForDate.push(rangeId);
          }
        }
          
        if (doctorRangesForDate.length === 0) {
          logger.warn('⚠️ No range found for consultation date', {
            consultationId: consultation.consultation_id,
            patientName: consultation.fullName,
            date: consultationDate,
            doctorId: assignment.doctorId,
            doctorName: assignment.doctorName,
            availableRanges: Array.from(rangeToDateMap.entries()).map(([id, date]) => ({
              rangeId: id, 
              date, 
              doctorId: rangeToDoctorMap.get(id)
            }))
          });
          continue;
        }

        logger.info('🔍 Finding slot for consultation', {
          consultationId: consultation.consultation_id,
          patientName: consultation.fullName,
          consultationDate,
          consultationTime: consultationTimeSydney,
          doctorId: assignment.doctorId,
          availableRanges: doctorRangesForDate
        });

        // Parse consultation time for comparison
        const [consultHour, consultMinute] = consultationTimeSydney.split(':').map(Number);
        const consultTotalMinutes = consultHour * 60 + consultMinute;

        // Find slots for this range
        const slotsInRange = createdSlots.filter(slot => doctorRangesForDate.includes(slot.range_id));
        
        if (slotsInRange.length === 0) {
          logger.warn('⚠️ No slots found for ranges', {
            consultationId: consultation.consultation_id,
            ranges: doctorRangesForDate,
            totalCreatedSlots: createdSlots.length
          });
          continue;
        }

        // Find a suitable slot that covers the consultation time
        let suitableSlot: CreatedSlot | null = null;
        for (const slot of slotsInRange) {
          // Parse slot time in "HH:MM - HH:MM" format
          const [slotStart, slotEnd] = slot.slot.split(' - ');
          
          if (!slotStart || !slotEnd) {
            continue; // Skip malformed slots
          }
          
          // Parse start and end times
          const [startHour, startMinute] = slotStart.split(':').map(Number);
          const [endHour, endMinute] = slotEnd.split(':').map(Number);
          
          const slotStartMinutes = startHour * 60 + startMinute;
          const slotEndMinutes = endHour * 60 + endMinute;
          
          // Check if consultation time falls within slot time range
          if (consultTotalMinutes >= slotStartMinutes && consultTotalMinutes < slotEndMinutes) {
            suitableSlot = slot;
            break;
          }
        }

        // If no suitable slot found, use the closest one
        if (!suitableSlot && slotsInRange.length > 0) {
          // Find closest slot by time difference
          let closestSlot = slotsInRange[0];
          let smallestDiff = Infinity;
          
          for (const slot of slotsInRange) {
            const [slotStart] = slot.slot.split(' - ');
            if (!slotStart) continue;
            
            const [startHour, startMinute] = slotStart.split(':').map(Number);
            const slotStartMinutes = startHour * 60 + startMinute;
            const diff = Math.abs(consultTotalMinutes - slotStartMinutes);
            
            if (diff < smallestDiff) {
              smallestDiff = diff;
              closestSlot = slot;
            }
          }
          
          suitableSlot = closestSlot;
          logger.info('🔄 Using closest slot as fallback', {
            consultationId: consultation.consultation_id,
            consultationTime: consultationTimeSydney,
            closestSlot: suitableSlot.slot,
            timeDifference: smallestDiff
          });
        }

        if (!suitableSlot) {
          logger.error('❌ Could not find any suitable slot', {
            consultationId: consultation.consultation_id,
            consultationTime: consultationTimeSydney,
            availableSlots: slotsInRange.map(s => s.slot).join(', ')
          });
          continue;
        }

        try {
          const createPatientSlotQuery = `
            INSERT INTO patientslot (patient_id, range_id, slot_id, "createdAt", "updatedAt")
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ON CONFLICT (patient_id, range_id) DO NOTHING
            RETURNING *;
          `;

          // Log the values we're trying to insert
          logger.info('📝 Attempting to create patient slot', {
            zohoID: consultation.zohoID,
            rangeId: suitableSlot.range_id,
            slotId: suitableSlot.id
          });

          const patientSlotResult = await client.query(createPatientSlotQuery, [
            consultation.zohoID, // CRITICAL: Use zohoID as patient_id
            suitableSlot.range_id,
            suitableSlot.id
          ]);

          if (patientSlotResult.rows.length > 0) {
            createdPatientSlots.push({
              ...patientSlotResult.rows[0],
              consultationId: consultation.consultation_id,
              patientName: consultation.fullName
            });

            logger.info('👥 Patient slot created successfully', {
              consultationId: consultation.consultation_id,
              patientID: consultation.patientID,
              zohoID: consultation.zohoID,
              patientName: consultation.fullName,
              slotId: suitableSlot.id,
              rangeId: suitableSlot.range_id,
              slotTime: suitableSlot.slot
            });
          } else {
            logger.warn('⚠️ Patient slot creation returned no rows', {
              consultationId: consultation.consultation_id,
              patientID: consultation.patientID,
              zohoID: consultation.zohoID
            });
          }
        } catch (error) {
          logger.error('❌ Error creating patient slot', {
            consultationId: consultation.consultation_id,
            patientID: consultation.patientID,
            zohoID: consultation.zohoID,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    }

    return createdPatientSlots;
  }

  /**
   * Updates consultation records with doctor assignments
   */
  async updateConsultationRecords(client: PoolClient, doctorAssignments: DoctorAssignment[]): Promise<void> {
    for (const assignment of doctorAssignments) {
      for (const consultation of assignment.consultations) {
        const updateConsultationQuery = `
          UPDATE Consultation
          SET "drId" = $1, "updatedAt" = CURRENT_TIMESTAMP
          WHERE id = $2;
        `;

        await client.query(updateConsultationQuery, [
          assignment.accessID, // Use accessID for drId field
          consultation.consultation_id
        ]);

        logger.info('📝 Consultation updated', {
          consultationId: consultation.consultation_id,
          patientName: consultation.fullName,
          assignedDoctorId: assignment.doctorId,
          assignedDoctorName: assignment.doctorName,
          drId: assignment.accessID
        });
      }
    }
  }

  /**
   * Formats doctor assignments for result summary
   */
  private formatDoctorAssignments(assignments: DoctorAssignment[]): Record<string, {
    doctorName: string;
    consultationsAssigned: number;
    rangesCreated: number;
  }> {
    const result: Record<string, {
      doctorName: string;
      consultationsAssigned: number;
      rangesCreated: number;
    }> = {};
    assignments.forEach(assignment => {
      result[assignment.doctorId] = {
        doctorName: assignment.doctorName,
        consultationsAssigned: assignment.consultations.length,
        rangesCreated: 0 // TODO: Update when ranges are created
      };
    });
    return result;
  }
}
