import React, { useEffect } from "react";
import { ApiClient } from "../services";
import { DateTime } from "luxon";
import { addEvent, getAllEvents, clearEvents } from "../utils/eventCache";
import { UserEvent } from "../types";

interface ActivityTrackerProps {
	children: React.ReactNode;
}

interface TrackerContextValue {
	trackActivity: (
		actor: "Doctor" | "Patient",
		target: string,
		action: string,
		message: string,
		actorId: string,
		targetId?: string
	) => Promise<void>;
	disableTracking: () => void;
	flushEventsToBackend: () => Promise<void>;
	setDocId: (id: string | null) => void;
}

const TrackerContext = React.createContext<TrackerContextValue | null>(null);

export const ActivityTracker: React.FC<ActivityTrackerProps> = ({ children }) => {
	const [trackingEnabled, setTrackingEnabled] = React.useState<boolean>(true);
	const [drId, setDocId] = React.useState<string | null>(null);
	const flushEventsToBackend = async () => {
		const events = await getAllEvents();
		if (events.length > 0) {
			// Send all events to the backend
			try {
				console.log("Flushing events to backend:", events);
				const response = await ApiClient.postTrackingEvent(events);
				if (response.success === true) {
					console.log("Events sent successfully:");
					await clearEvents();
				}
			} catch (e) {
				console.error("Error sending events to backend:", e);
			}
		}
	};

	const trackActivity = async (
		actor: "Doctor" | "Patient",
		target: string,
		action: string,
		message: string,
		actorId: string,
		targetId?: string
	) => {
		// Timestam is new. Must be added to the backend as well.
		// Store all these activity tracking. When Doctor complete the consultation pull them, sort by timestamp and send to slack.
		if (!trackingEnabled) return;
		const timeStamp = DateTime.utc().setZone("Australia/Sydney").toISO();

		addEvent({
			actor,
			target,
			action,
			message,
			timestamp: timeStamp,
			actor_id: actorId || drId || "",
			target_id: targetId,
		} as UserEvent);
	};

	const disableTracking = () => {
		console.log("Disabling activity tracking");
		setTrackingEnabled(false);
	};

	useEffect(() => {
		const timer = setInterval(flushEventsToBackend, 1 * 60 * 1000); // Every 1 min
		return () => clearInterval(timer);
	}, []);

	return (
		<TrackerContext.Provider value={{ trackActivity, disableTracking, flushEventsToBackend, setDocId }}>
			{children}
		</TrackerContext.Provider>
	);
};

export const useTracker = () => {
	const context = React.useContext(TrackerContext);
	if (!context) {
		throw new Error(`useTracker must be used within an ActivityTracker`);
	}
	return context;
};
