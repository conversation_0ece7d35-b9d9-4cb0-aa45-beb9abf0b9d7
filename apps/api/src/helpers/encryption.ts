import crypto from 'crypto';
interface CryptoOptions {
  algorithm: string;
  key: string;
  iv: string;
}
class CryptoHelper {
  private algorithm: string;
  private key: Buffer;
  private iv: Buffer;

  constructor(options: CryptoOptions) {
    this.algorithm = options.algorithm;
    this.key = Buffer.from(options.key, 'hex');
    this.iv = Buffer.from(options.iv, 'hex');
  }

  encrypt(text: string): string {
    const iv = crypto.randomBytes(16)

    const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);
    const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()]);
    return encrypted.toString('hex');
  }

  decrypt(encryptedText: string): string {
    const decipher = crypto.createDecipheriv(this.algorithm, this.key, this.iv);
    const decrypted = Buffer.concat([decipher.update(Buffer.from(encryptedText, 'hex')), decipher.final()]);
    return decrypted.toString('utf8');
  }

  encryptLeadId(text: string): string {
     // Generate a random IV
     const iv = crypto.randomBytes(16);

     // Create the cipher
     const cipher = crypto.createCipheriv(this.algorithm, this.key, iv);
 
     // Encrypt the text
     const encrypted = Buffer.concat([cipher.update(text, 'utf8'), cipher.final()]);
 
     // Combine IV and encrypted text
     const combined = Buffer.concat([iv, encrypted]);
 
     // Return as hex string
     return combined.toString('hex');
  }

  decryptLeadId(encryptedId: string): string {
        // Decode the hex-encoded data
        const data = Buffer.from(encryptedId, 'hex');

        // Extract the IV (first 16 bytes)
        const iv = data.subarray(0, 16);

        // Extract the encrypted text (remaining bytes)
        const encryptedText = data.subarray(16);

        // Create the decipher
        const decipher = crypto.createDecipheriv(this.algorithm, this.key, iv);

        // Decrypt the text
        const decrypted = Buffer.concat([decipher.update(encryptedText), decipher.final()]);

        return decrypted.toString('utf8');
  }

}

export const encryptionHelper = new CryptoHelper({
  algorithm: 'aes-256-cbc',
  key: 'b15d67cc0ffb721df28ea4c282dbe48cdd1bab6d6296787f881b0da1dcb87b38',
  iv: '8ad4c7d04aa80e14f3a93c7344358b60',
});


export const UrlEncryptionHelper = new CryptoHelper({
  algorithm: 'aes-256-cbc',
  key: '52a1f3c6bfbdf7c4a832e6a45d5674a9b398c4a1d7f1e1c2b9c6d4e38fda93e5',
  iv: 'e9f8c7d1a5b4c6f3d4a1b9e873c2f4d6',
})