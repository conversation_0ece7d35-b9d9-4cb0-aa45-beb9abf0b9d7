export interface ServerResponse {
    message: string
    status: number 
    data: unknown
    error: boolean | undefined
    success: boolean | undefined
}

export class ApiResponse {
    message: string
    status: number 
    data: unknown
    error: boolean | undefined
    success: boolean | undefined

    constructor(status: number, message: string, data: unknown, success?: boolean, error?: boolean){
        this.message = message
        this.status = status 
        this.data = data
        this.error = error
        this.success = success 
    }

}