import { Re<PERSON><PERSON><PERSON><PERSON> } from 'express-serve-static-core';
import { catchAll } from '../utils/catchAll';
import { getCookie } from '../utils/cookie';
import { db } from '../utils/db';
import { logger } from '../config/logger';
import { AxiosError } from 'axios';
import { ApiError } from '../utils/ApiError';
import httpStatus from 'http-status';

export const validateDr: RequestHandler = catchAll(async (req, res, next) => {
  const client = await db.connect();
  const cookie = req.headers.cookie;
  const drID = getCookie(cookie);
  try {
    if (drID) {
      const checkDrQuery = `SELECT * from Dr WHERE "accessID"=$1`;
      const existingDr = await client.query(checkDrQuery, [drID]);
      if (existingDr.rows && existingDr.rows.length > 0) {
        next();
      } else {
        logger.info(`Unauthorized: ${drID}`);
        res.status(401).send();
        return;
      }
    } else {
      logger.info(`Unauthorized: ${drID}`);
      res.status(401).send();
    }
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});
