import { useState, useEffect, useRef } from 'react';
import { StreamChat, Channel as StreamChannel, UserResponse } from 'stream-chat';
import { DefaultStreamChatGenerics } from 'stream-chat-react';
import chatService, { formatStreamChatId } from '../services/chat.service';
import { useAuth } from './auth-provider';
import { usePatient } from './patient-provider';
import { config } from '../config';
import { Dr } from '../types';

// API key for Stream Chat - get from config
const API_KEY = config.streamChat.apiKey;

/**
 * Helper function to get the doctor's display name
 * Prefers username (e.g., "<PERSON> <PERSON>") over name, with fallback to "Doctor"
 */
const getDoctorDisplayName = (doctor: Dr | undefined): string => {
  if (!doctor) return 'Doctor';
  return doctor.username || doctor.name || 'Doctor';
};

// Keep track of active clients globally
const activeClients = new Map<string, StreamChat<DefaultStreamChatGenerics>>();

// Add a global beforeunload handler to clean up all active clients
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    // Disconnect all active clients when the window is closed
    activeClients.forEach((client, userId) => {
      try {
        client.disconnectUser().catch(e => {
          console.error(`Error disconnecting user ${userId} on unload:`, e);
        });
      } catch (e) {
        console.error(`Error during disconnect for user ${userId}:`, e);
      }
    });

    // Clear the map
    activeClients.clear();
  });
}

interface UseStreamChatResult {
  loading: boolean;
  error: string | null;
  client: StreamChat<DefaultStreamChatGenerics> | null;
  userId: string | null;
  originalUserId: string | null;
  isDisconnected: boolean;
  createChannel: (
    userId1: string,
    userId2: string,
    channelName?: string,
    userType1?: 'doctor' | 'patient',
    userType2?: 'doctor' | 'patient'
  ) => Promise<StreamChannel<DefaultStreamChatGenerics> | null>;
  addMemberToChannel: (channelId: string, userId: string) => Promise<boolean>;
  removeMemberFromChannel: (channelId: string, userId: string) => Promise<boolean>;
}

/**
 * Custom hook to handle Stream Chat initialization and common operations
 * @param forceUserId - Optional user ID to force the use of a specific ID (overrides auth)
 * @param enabled - Whether to initialize the chat client (default: true)
 */
export function useStreamChat(forceUserId?: string, enabled: boolean = true): UseStreamChatResult {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [client, setClient] = useState<StreamChat<DefaultStreamChatGenerics> | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [originalUserId, setOriginalUserId] = useState<string | null>(null);
  const [isDisconnected, setIsDisconnected] = useState(false);

  const { doctor, user } = useAuth();
  const patientContext = usePatient();

  const clientRef = useRef<StreamChat<DefaultStreamChatGenerics> | null>(null);
  const isConnectingRef = useRef(false);
  const mountedRef = useRef(true);

  // Initialize Stream Chat client
  useEffect(() => {
    mountedRef.current = true;

    // Skip if not enabled
    if (!enabled) {
      setLoading(false);
      setError(null);
      return;
    }

    // Skip if already connecting or connected
    if (isConnectingRef.current || clientRef.current) {
      return;
    }

    isConnectingRef.current = true;

    const initializeChat = async () => {
      try {
        if (!mountedRef.current) return;
        setLoading(true);
        setError(null);

        // Determine user ID based on user type
        let currentUserId = forceUserId;
        let userData: UserResponse<DefaultStreamChatGenerics> | null = null;

        if (!currentUserId) {
          if (doctor && doctor.accessID) {
            currentUserId = "harvest";
            userData = {
              id: formatStreamChatId(currentUserId, 'd'),
              name: getDoctorDisplayName(doctor),
              role: 'doctor',
              originalId: doctor.accessID,
              status: 'online'
            };
          } else if (patientContext && patientContext.selectedPatient) {
            currentUserId = patientContext.selectedPatient.patientID;
            userData = {
              id: formatStreamChatId(currentUserId, 'p'),
              name: patientContext.selectedPatient.fullName || 'Patient',
              patientId: currentUserId,
              role: 'patient'
            };
          } else if (user) {
            currentUserId = user.id;
            userData = {
              id: formatStreamChatId(currentUserId, 'u'),
              name: user.email || currentUserId,
              originalId: currentUserId
            };
          } else {
            throw new Error("No user found. Please log in.");
          }
        } else {
          userData = {
            id: formatStreamChatId(forceUserId || 'guest'),
            name: forceUserId || 'Guest',
            originalId: forceUserId || 'guest'
          };
        }

        if (!currentUserId || !userData) {
          throw new Error("Failed to determine user ID");
        }

        setOriginalUserId(currentUserId);

        // Check if we already have an active client for this user
        const existingClient = activeClients.get(userData.id);
        if (existingClient) {
          // Check if the existing client is actually connected
          if (existingClient.userID) {
            clientRef.current = existingClient;
            setClient(existingClient);
            setUserId(userData.id);
            setLoading(false);
            isConnectingRef.current = false;
            return;
          } else {
            // Remove the disconnected client from the map
            activeClients.delete(userData.id);
            // Continue to create a new client
          }
        }

        // Create a new Stream Chat client if one doesn't exist
        const newClient = new StreamChat<DefaultStreamChatGenerics>(API_KEY);

        // Request token from backend using the formatted ID
        const tokenResponse = await chatService.generateToken(userData.id);

        if (tokenResponse.token) {
          // Connect the user to Stream Chat
          await newClient.connectUser(userData, tokenResponse.token);

          // If this is a doctor, explicitly set their status to online
          if (userData.role === 'doctor') {
            try {
              // Use upsertUsers instead of updateUser (which is deprecated)
              await newClient.upsertUsers([{
                id: userData.id,
                name: getDoctorDisplayName(doctor),
                role: 'doctor',
                status: 'online'
              }]);
            } catch (statusError) {
              console.error('Error setting doctor status to online:', statusError);
            }
          }

          // Store the client in our refs and map
          clientRef.current = newClient;
          activeClients.set(userData.id, newClient);

          if (mountedRef.current) {
            setClient(newClient);
            setUserId(userData.id);
          }
        } else {
          throw new Error("No token received from server");
        }
      } catch (err) {
        console.error("Error initializing Stream Chat:", err);

        // Check if the error is related to connection
        const errorMessage = err instanceof Error ? err.message : String(err);
        const isConnectionError = errorMessage.includes('connect') ||
                                 errorMessage.includes('token') ||
                                 errorMessage.includes('network');

        // Clean up any partially initialized client
        if (clientRef.current) {
          try {
            await clientRef.current.disconnectUser().catch(e => {
              console.error('Error disconnecting partial client:', e);
            });
          } catch (disconnectErr) {
            console.error('Exception during disconnect of partial client:', disconnectErr);
          }
          clientRef.current = null;
        }

        if (mountedRef.current) {
          if (isConnectionError) {
            setError("Failed to connect to chat service. Please check your connection and try again.");
          } else {
            setError("Failed to initialize chat. Please try again later.");
          }
        }
      } finally {
        if (mountedRef.current) {
          setLoading(false);
        }
        isConnectingRef.current = false;
      }
    };

    initializeChat();

    // Cleanup function
    return () => {
      mountedRef.current = false;

      // Only disconnect if this instance created the client
      if (clientRef.current && !isConnectingRef.current) {
        const currentClient = clientRef.current;
        const currentUserId = userId;

        // Check if there are other components using this client
        // by checking if it's still in the activeClients map
        const isClientStillActive = currentUserId && activeClients.has(currentUserId);

        // Only remove from active clients map if this is the component that created it
        if (currentUserId) {
          // Instead of immediately removing, check if other components might be using it
          // We'll set a flag to indicate this component is done with it
          const client = activeClients.get(currentUserId);
          if (client === currentClient) {
            // Keep the client in the map to allow reuse
            // activeClients.delete(currentUserId);
            //console.log(`Keeping Stream Chat client for ${currentUserId} in active clients map for potential reuse`);
          }
        }

        // Only disconnect if the client is no longer in the active clients map
        // This prevents disconnecting a client that other components are still using
        if (!isClientStillActive) {
          // Schedule disconnect after any pending operations
          setTimeout(() => {
            // Double-check that the client is still not in active use before disconnecting
            if (currentUserId && !activeClients.has(currentUserId)) {
              //console.log(`Disconnecting Stream Chat client for ${currentUserId}`);
              currentClient.disconnectUser()
                .then(() => {
                  if (mountedRef.current) {
                    setIsDisconnected(true);
                  }
                })
                .catch(err => {
                  console.error('Error disconnecting from Stream Chat:', err);
                  if (mountedRef.current) {
                    setIsDisconnected(true);
                  }
                });
            }
          }, 100); // Slightly longer timeout to ensure all components have had a chance to clean up
        }
      }

      // Don't set clientRef to null, as this might be needed for reconnection
      // clientRef.current = null;
    };
  }, [enabled, forceUserId, doctor, user, patientContext?.selectedPatient]);

  // Additional effect to periodically update the doctor's online status
  useEffect(() => {
    // Only run this effect for doctors
    if (!client || !userId || !doctor) return;

    let presenceInterval: NodeJS.Timeout;

    // Set up interval to periodically update presence for doctors
    if (doctor && doctor.accessID) {

      // Immediately set status to online using upsertUsers instead of updateUser
      client.upsertUsers([{
        id: userId,
        name: getDoctorDisplayName(doctor),
        role: 'doctor',
        status: 'online'
      }]).catch(err => console.error('Error updating doctor status:', err));

      // Set up interval to refresh online status every 30 seconds
      presenceInterval = setInterval(() => {
        if (client) {
          // Use upsertUsers instead of updateUser (which is deprecated)
          client.upsertUsers([{
            id: userId,
            name: getDoctorDisplayName(doctor),
            role: 'doctor',
            status: 'online'
          }]).catch(err => console.error('Error refreshing doctor status:', err));
        }
      }, 30000); // Update every 30 seconds
    }

    // Clean up interval on unmount
    return () => {
      if (presenceInterval) {
        clearInterval(presenceInterval);
      }
    };
  }, [client, userId, doctor]);

  /**
   * Create a new channel between a patient and doctor
   */
  const createChannel = async (
    userId1: string,
    userId2: string,
    channelName?: string,
    userType1?: 'doctor' | 'patient',
    userType2?: 'doctor' | 'patient'
  ): Promise<StreamChannel<DefaultStreamChatGenerics> | null> => {
    try {
      if (!client) {
        throw new Error("Chat client not initialized");
      }

      // Use original IDs when calling the backend
      const response = await chatService.createChannel(userId1, userId2, channelName, userType1, userType2);

      // Find the channel in the client
      const channel = client.channel('messaging', response.channelId);

      // Watch the channel
      await channel.watch();

      return channel;
    } catch (err) {
      console.error("Error creating channel:", err);
      setError("Failed to create channel");
      return null;
    }
  };

  /**
   * Add a member to a channel
   */
  const addMemberToChannel = async (channelId: string, memberId: string): Promise<boolean> => {
    try {
      if (!client) {
        throw new Error("Chat client not initialized");
      }

      // Format the member ID before sending to the backend
      const formattedMemberId = formatStreamChatId(memberId);
      await chatService.addChannelMember(channelId, formattedMemberId);
      return true;
    } catch (err) {
      console.error("Error adding member to channel:", err);
      setError("Failed to add member to channel");
      return false;
    }
  };

  /**
   * Remove a member from a channel
   */
  const removeMemberFromChannel = async (channelId: string, memberId: string): Promise<boolean> => {
    try {
      if (!client) {
        throw new Error("Chat client not initialized");
      }

      // Format the member ID before sending to the backend
      const formattedMemberId = formatStreamChatId(memberId);
      await chatService.removeChannelMember(channelId, formattedMemberId);
      return true;
    } catch (err) {
      console.error("Error removing member from channel:", err);
      setError("Failed to remove member from channel");
      return false;
    }
  };

  return {
    loading,
    error,
    client,
    userId,
    originalUserId,
    isDisconnected,
    createChannel,
    addMemberToChannel,
    removeMemberFromChannel,
  };
}