import { But<PERSON>, Typo<PERSON> } from "@mui/material"
import { useMemo, useEffect, useState, useRef } from "react"
import {  ChannelPreviewUIComponentProps, DefaultStreamChatGenerics, Avatar, useChatContext } from "stream-chat-react"
import type { Channel as StreamChannel } from 'stream-chat';
import type { UserResponse } from 'stream-chat';
import Grid from "@mui/material/Grid2";
import { DateTime } from 'luxon';

interface CustomChannelPreviewProps extends ChannelPreviewUIComponentProps {
  setSelectedChannel: (channel: StreamChannel<DefaultStreamChatGenerics>) => void;
  user?: UserResponse<DefaultStreamChatGenerics>;
  hiddenMessageIds?: Set<string>;
}

const CustomChannelPreview = (props: CustomChannelPreviewProps) => {
  const {
    channel,
    activeChannel,
    displayTitle,
    latestMessagePreview,
    setActiveChannel,
    setSelectedChannel,
    user,
    hiddenMessageIds = new Set()
  } = props;

  const [channelUnread, setChannelUnread] = useState<number>(0);
  const unreadRef = useRef<number>(0);
  const lastMessageIdRef = useRef<string | null>(null);
  const wasManuallySelectedRef = useRef<boolean>(false);

  const { client } = useChatContext();
  const latestMessageAt = channel.state.last_message_at;
  const isSelected = channel.id === activeChannel?.id;
  //const { userLanguage } = useTranslationContext();

  // Simply use displayTitle directly as it already contains the correct values
  const channelName = displayTitle;

  // Calculate unread messages without relying on Stream's unread count
  const calculateUnreadCount = () => {
    if (!client?.user?.id) return 0;
    const currentUserId = client.user.id; // Store user ID to avoid repeated null checks

    try {
      const messages = channel.state.messages || [];
      const lastReadMessageId = channel.state.read[currentUserId]?.last_read_message_id;

      // Filter function to exclude hidden patient messages
      const isVisibleMessage = (msg: any) => {
        // Always count doctor messages
        if (!msg.user?.id?.startsWith('p_')) {
          return true;
        }
        // For patient messages, exclude if they're hidden (unapproved)
        return !msg.id || !hiddenMessageIds.has(msg.id);
      };

      // If this is the active channel and we've sent a message, consider it read
      if (isSelected && messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        if (lastMessage && lastMessage.user?.id === currentUserId) {
          return 0;
        }
      }

      // If no messages have been read, count only visible messages not from current user
      if (!lastReadMessageId) {
        return messages.filter(msg =>
          msg.user?.id !== currentUserId && isVisibleMessage(msg)
        ).length;
      }

      // Find the index of the last read message
      const lastReadIndex = messages.findIndex(msg => msg.id === lastReadMessageId);

      // If the last read message is found, count unread visible messages not from current user
      if (lastReadIndex !== -1) {
        const unreadMessages = messages.slice(lastReadIndex + 1);
        return unreadMessages.filter(msg =>
          msg.user?.id !== currentUserId && isVisibleMessage(msg)
        ).length;
      }

      // If last read message not found, count all visible messages not from current user
      return messages.filter(msg =>
        msg.user?.id !== currentUserId && isVisibleMessage(msg)
      ).length;
    } catch (err) {
      console.error('Error calculating unread count:', err);
      return 0;
    }
  };

  // Check for new messages and update unread count
  const updateUnreadCount = () => {
    if (!client?.user?.id) return;
    const currentUserId = client.user.id;

    // Clear unread count if:
    // 1. Channel is selected and manually clicked
    // 2. Channel is selected and we've sent a message
    // 3. All messages are from current user
    if (isSelected) {
      const messages = channel.state.messages || [];
      const lastMessage = messages[messages.length - 1];
      if (wasManuallySelectedRef.current ||
          (lastMessage && lastMessage.user?.id === currentUserId) ||
          messages.every(msg => msg.user?.id === currentUserId)) {
        unreadRef.current = 0;
        setChannelUnread(0);
        return;
      }
    }

    // Get latest message ID
    const messages = channel.state.messages || [];
    const latestMessageId = messages.length > 0 ? messages[messages.length - 1]?.id : null;

    // Only recalculate if there's a new message not from current user
    if (latestMessageId && latestMessageId !== lastMessageIdRef.current) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && lastMessage.user?.id !== currentUserId) {
        const count = calculateUnreadCount();
        lastMessageIdRef.current = latestMessageId;
        unreadRef.current = count;
        setChannelUnread(count);
      } else {
        // If last message is from current user, don't count it as unread
        lastMessageIdRef.current = latestMessageId;
      }
    }
  };

  // Update when channel state changes
  useEffect(() => {
    if (!channel || !client) return;

    const handleNewMessage = () => {
      // If the new message is from the current user, mark channel as read
      const messages = channel.state.messages || [];
      const lastMessage = messages[messages.length - 1];
      if (lastMessage && lastMessage.user?.id === client.user?.id) {
        wasManuallySelectedRef.current = true;
      }

      // For patient messages, delay the unread count update to allow visibility check to complete
      if (lastMessage && lastMessage.user?.id?.startsWith('p_')) {
        setTimeout(() => {
          updateUnreadCount();
        }, 1000); // Wait 1 second for visibility check to complete
      } else {
        updateUnreadCount();
      }
    };

    const handleChannelUpdated = () => {
      updateUnreadCount();
    };

    // Handle read events more aggressively
    const handleReadEvent = () => {
      // Always update unread count when a read event occurs
      setTimeout(updateUnreadCount, 0);

      // If this is the selected channel, immediately set unread to 0
      if (isSelected) {
        unreadRef.current = 0;
        setChannelUnread(0);
      }
    };

    // Calculate initial unread count
    updateUnreadCount();

    // Listen for channel events
    channel.on('message.new', handleNewMessage);
    channel.on('channel.updated', handleChannelUpdated);
    channel.on('message.read', handleReadEvent);

    return () => {
      channel.off('message.new', handleNewMessage);
      channel.off('channel.updated', handleChannelUpdated);
      channel.off('message.read', handleReadEvent);
    };
  }, [channel, client, isSelected, hiddenMessageIds]);

  // Update unread count when selection changes
  useEffect(() => {
    updateUnreadCount();

    // If selected, immediately clear unread count
    if (isSelected) {
      unreadRef.current = 0;
      setChannelUnread(0);

      // Also mark as read if not already done
      if (wasManuallySelectedRef.current) {
        channel.markRead().catch(err => {
          console.error('Error marking channel as read on selection:', err);
        });
      }
    }
  }, [isSelected, hiddenMessageIds]);

  // Update unread count when hiddenMessageIds changes
  useEffect(() => {
    updateUnreadCount();
  }, [hiddenMessageIds]);

  const timestamp = useMemo(() => {
    if (!latestMessageAt) {
      return "";
    }
    // Use Luxon to format in Australia/Sydney timezone
    const dt = DateTime.fromJSDate(latestMessageAt, { zone: 'utc' }).setZone('Australia/Sydney');
    const now = DateTime.now().setZone('Australia/Sydney');
    const diffInHours = now.diff(dt, 'hours').hours;
    if (diffInHours < 24) {
      return dt.toFormat('HH:mm');
    }
    return dt.toFormat('HH:mm, dd MMM yyyy');
  }, [latestMessageAt]);

  // Handle click - explicitly mark as read only when clicked
  const handleClick = () => {
    if (setActiveChannel) {
      // Mark this as a manual selection
      wasManuallySelectedRef.current = true;

      // Update UI state immediately
      setActiveChannel(channel);
      setSelectedChannel(channel);
      unreadRef.current = 0;
      setChannelUnread(0);

      // Save the channel ID to localStorage to indicate it was explicitly selected
      localStorage.setItem('lastSelectedChannel', channel.id || '');

      // Mark as read in Stream Chat and force an immediate update
      try {
        channel.markRead().then(() => {
          // Force another update after marking as read
          setTimeout(() => {
            unreadRef.current = 0;
            setChannelUnread(0);
          }, 100);
        }).catch(err => {
          console.error('Error marking channel as read:', err);
        });
      } catch (err) {
        console.error('Error in handleClick:', err);
      }
    }
  };

  // For debugging - log unread changes
  // useEffect(() => {
  //   const messages = channel.state.messages || [];
  //   const lastMessage = messages[messages.length - 1];
  //   const lastMessageId = lastMessage?.id;
  //   const isLastMessageHidden = lastMessageId && hiddenMessageIds.has(lastMessageId);

  //   if (channelUnread > 0) {
  //     console.log(`Channel ${channel.id} UI shows ${channelUnread} unread messages (hiddenMessageIds size: ${hiddenMessageIds.size}, last message hidden: ${isLastMessageHidden})`);
  //   } else {
  //     console.log(`Channel ${channel.id} UI shows 0 unread messages (hiddenMessageIds size: ${hiddenMessageIds.size}, last message hidden: ${isLastMessageHidden})`);
  //   }
  // }, [channel.id, channelUnread, hiddenMessageIds.size]);

  // Determine if we should show the unread badge (red dot) for unanswered channels
  //const messages = channel.state.messages || [];
  //const lastMessage = messages[messages.length - 1];
  //const showUnread = lastMessage?.user?.id?.startsWith('p_') && !isSelected;

  return (
    <Button
      variant={isSelected ? 'contained' : 'text'}
      sx={{
        width: '99%',
        textTransform: 'none',
        p: 1,
        backgroundColor: isSelected ? '#E8E8E8' : 'none',
        '&:hover': {
          backgroundColor: isSelected ? '#E8E8E8' : '#f5f5f5',
        },
        position: 'relative'
      }}
      onClick={handleClick}
      disableRipple
      disableElevation
    >
      <Grid container alignItems={'flex-start'} sx={{ width: '100%' }}>
        {/* Top row: avatar, name, timestamp */}
        <Grid container alignItems="center" justifyContent="space-between" sx={{ width: '100%' }}>
          <span style={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
            <span style={{ display: 'inline-flex', alignItems: 'center', justifyContent: 'center', background: '#1976d2', color: '#fff', borderRadius: '50%', width: 32, height: 32, marginRight: 10 }}>
              <Avatar user={user} name={channelName} />
            </span>
            <Typography
              align="left"
              sx={{
                fontWeight: channelUnread > 0 ? 'bold' : 'normal',
                color: 'black',
                fontSize: "14px",
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: 180
              }}
            >
              {channelName}
            </Typography>
          </span>
          <Typography sx={{ color: 'grey', fontSize: '11px' }} aria-label={latestMessageAt ? DateTime.fromJSDate(latestMessageAt, { zone: 'utc' }).setZone('Australia/Sydney').toFormat('HH:mm, dd MMM yyyy') : undefined}>
            {timestamp}
          </Typography>
        </Grid>
        {/* Second row: message preview */}
        <Grid sx={{ width: '100%' }}>
          <Typography
            component="span"
            align="left"
            sx={{
              color: channelUnread > 0 ? 'text.primary' : 'text.secondary',
              fontSize: '12px',
              fontWeight: channelUnread > 0 ? 'medium' : 'normal',
              maxWidth: '100%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              textAlign: 'left',
              mt: 0.5,
              display: 'block',
              ml: 6
            }}
          >
            {latestMessagePreview}
          </Typography>
        </Grid>
      </Grid>
    </Button>
  );
};

export default CustomChannelPreview;
