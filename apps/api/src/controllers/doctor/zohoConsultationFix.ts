import { Request<PERSON>and<PERSON> } from 'express';
import { catchAll } from '../../utils/catchAll';
import { db } from '../../utils/db';
import { logger } from '../../config/logger';
import { ZohoConsultationManager } from '../../services/zohoConsultationManager';

/**
 * Production endpoint to fix Zoho consultations missing proper scheduling structure
 * This endpoint processes real incomplete consultations and creates the missing
 * ranges, slots, and patient slots while distributing consultations equally
 * between Dr<PERSON> and Dr<PERSON>.
 * 
 * Usage: POST /api/doc/v1.0/fix-zoho-consultations
 * 
 * This endpoint should be called when:
 * - Consultations are imported from Zoho but missing scheduling structure
 * - Manual intervention is needed to fix incomplete consultation data
 * - System detects consultations without proper ranges/slots/patient slots
 * 
 * 
 */
export const fixZohoConsultations: RequestHandler = catchAll(async (_req, res) => {
  const startTime = Date.now();
  logger.info('🚀 Starting production Zoho consultation fix', {
    timestamp: new Date().toISOString()
  });

  try {
    // Initialize the Zoho consultation manager
    const zohoManager = new ZohoConsultationManager(db);
    
    // Run the fix process
    const fixResult = await zohoManager.fixZohoConsultations();
    
    // Log the results
    logger.info('✅ Zoho consultation fix completed', {
      success: fixResult.success,
      consultationsProcessed: fixResult.summary.totalConsultationsProcessed,
      consultationsFixed: fixResult.summary.consultationsFixed,
      rangesCreated: fixResult.summary.rangesCreated,
      slotsCreated: fixResult.summary.slotsCreated,
      patientSlotsCreated: fixResult.summary.patientSlotsCreated,
      executionTimeMs: fixResult.executionTimeMs,
    });

    // Return success response
    res.status(200).json({
      success: true,
      message: 'Zoho consultation fix completed successfully',
      result: fixResult,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const executionTimeMs = Date.now() - startTime;
    
    logger.error('❌ Zoho consultation fix failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      executionTimeMs,
    });

    res.status(500).json({
      success: false,
      message: 'Zoho consultation fix failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTimeMs,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Endpoint to get analysis of current consultation state without making changes
 * This is useful for checking what would be fixed before running the actual fix
 * 
 * Usage: GET /api/doc/v1.0/analyze-zoho-consultations
 */
export const analyzeZohoConsultations: RequestHandler = catchAll(async (_req, res) => {
  const startTime = Date.now();
  logger.info('📊 Starting Zoho consultation analysis', {
  });

  try {
    const zohoManager = new ZohoConsultationManager(db);
    
    // Run analysis only (no fixes)
    const analysis = await zohoManager.analyzeConsultationPatterns();
    
    logger.info('📋 Zoho consultation analysis completed', {
      totalIncomplete: analysis.totalIncomplete,
      issueBreakdown: analysis.issueBreakdown,
      executionTimeMs: Date.now() - startTime,
    });

    res.status(200).json({
      success: true,
      message: 'Zoho consultation analysis completed',
      analysis,
      executionTimeMs: Date.now() - startTime,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    const executionTimeMs = Date.now() - startTime;
    
    logger.error('❌ Zoho consultation analysis failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTimeMs,
    });

    res.status(500).json({
      success: false,
      message: 'Zoho consultation analysis failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTimeMs,
      timestamp: new Date().toISOString()
    });
  }
});
