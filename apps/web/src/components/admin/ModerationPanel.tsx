// import React, { useState, useEffect } from 'react';
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge';
// import { Textarea } from '@/components/ui/textarea';
// import { CheckCircle, XCircle, Clock, User, MessageSquare } from 'lucide-react';
// import { toast } from 'sonner';

// interface ModerationMessage {
//   id: string;
//   messageId: string;
//   channelId: string;
//   patientId: string;
//   messageText: string;
//   patientName?: string;
//   moderationStatus: 'pending' | 'approved' | 'rejected';
//   createdAt: string;
//   moderatedBy?: string;
//   moderatedAt?: string;
//   moderationReason?: string;
// }

// interface ModerationStats {
//   pending: number;
//   approved: number;
//   rejected: number;
//   todayTotal: number;
// }

// const ModerationPanel: React.FC = () => {
//   const [pendingMessages, setPendingMessages] = useState<ModerationMessage[]>([]);
//   const [stats, setStats] = useState<ModerationStats>({ pending: 0, approved: 0, rejected: 0, todayTotal: 0 });
//   const [loading, setLoading] = useState(true);
//   const [moderatingId, setModeratingId] = useState<string | null>(null);
//   const [rejectionReason, setRejectionReason] = useState('');

//   useEffect(() => {
//     fetchPendingMessages();
//     fetchStats();
//   }, []);

//   const fetchPendingMessages = async () => {
//     try {
//       const response = await fetch('/api/moderation/v1.0/pending');
//       const data = await response.json();
//       if (data.success) {
//         setPendingMessages(data.data);
//       }
//     } catch (error) {
//       console.error('Error fetching pending messages:', error);
//       toast.error('Failed to fetch pending messages');
//     }
//   };

//   const fetchStats = async () => {
//     try {
//       const response = await fetch('/api/moderation/v1.0/stats');
//       const data = await response.json();
//       if (data.success) {
//         setStats(data.data);
//       }
//     } catch (error) {
//       console.error('Error fetching stats:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const moderateMessage = async (messageId: string, action: 'approve' | 'reject', reason?: string) => {
//     setModeratingId(messageId);
//     try {
//       const response = await fetch('/api/moderation/v1.0/moderate', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           messageId,
//           action,
//           reason,
//         }),
//       });

//       const data = await response.json();
//       if (data.success) {
//         toast.success(`Message ${action}d successfully`);
//         // Remove the message from pending list
//         setPendingMessages(prev => prev.filter(msg => msg.messageId !== messageId));
//         // Update stats
//         fetchStats();
//         setRejectionReason('');
//       } else {
//         toast.error(`Failed to ${action} message`);
//       }
//     } catch (error) {
//       console.error(`Error ${action}ing message:`, error);
//       toast.error(`Failed to ${action} message`);
//     } finally {
//       setModeratingId(null);
//     }
//   };

//   const sendTestNotification = async () => {
//     try {
//       const response = await fetch('/api/moderation/v1.0/test-notification', {
//         method: 'POST',
//       });
//       const data = await response.json();
//       if (data.success) {
//         toast.success('Test notification sent to Slack');
//       } else {
//         toast.error('Failed to send test notification');
//       }
//     } catch (error) {
//       console.error('Error sending test notification:', error);
//       toast.error('Failed to send test notification');
//     }
//   };

//   const formatDate = (dateString: string) => {
//     return new Date(dateString).toLocaleString('en-AU', {
//       timeZone: 'Australia/Sydney',
//       year: 'numeric',
//       month: 'short',
//       day: 'numeric',
//       hour: '2-digit',
//       minute: '2-digit',
//     });
//   };

//   if (loading) {
//     return (
//       <div className="flex items-center justify-center h-64">
//         <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
//       </div>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       {/* Header */}
//       <div className="flex justify-between items-center">
//         <h1 className="text-2xl font-bold">Chat Message Moderation</h1>
//         <Button onClick={sendTestNotification} variant="outline">
//           Send Test Notification
//         </Button>
//       </div>

//       {/* Stats Cards */}
//       <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
//         <Card>
//           <CardContent className="p-4">
//             <div className="flex items-center space-x-2">
//               <Clock className="h-5 w-5 text-yellow-500" />
//               <div>
//                 <p className="text-sm text-gray-600">Pending</p>
//                 <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
//               </div>
//             </div>
//           </CardContent>
//         </Card>

//         <Card>
//           <CardContent className="p-4">
//             <div className="flex items-center space-x-2">
//               <CheckCircle className="h-5 w-5 text-green-500" />
//               <div>
//                 <p className="text-sm text-gray-600">Approved</p>
//                 <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
//               </div>
//             </div>
//           </CardContent>
//         </Card>

//         <Card>
//           <CardContent className="p-4">
//             <div className="flex items-center space-x-2">
//               <XCircle className="h-5 w-5 text-red-500" />
//               <div>
//                 <p className="text-sm text-gray-600">Rejected</p>
//                 <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
//               </div>
//             </div>
//           </CardContent>
//         </Card>

//         <Card>
//           <CardContent className="p-4">
//             <div className="flex items-center space-x-2">
//               <MessageSquare className="h-5 w-5 text-blue-500" />
//               <div>
//                 <p className="text-sm text-gray-600">Today Total</p>
//                 <p className="text-2xl font-bold text-blue-600">{stats.todayTotal}</p>
//               </div>
//             </div>
//           </CardContent>
//         </Card>
//       </div>

//       {/* Pending Messages */}
//       <Card>
//         <CardHeader>
//           <CardTitle>Pending Messages ({pendingMessages.length})</CardTitle>
//         </CardHeader>
//         <CardContent>
//           {pendingMessages.length === 0 ? (
//             <div className="text-center py-8 text-gray-500">
//               <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
//               <p>No pending messages to moderate</p>
//             </div>
//           ) : (
//             <div className="space-y-4">
//               {pendingMessages.map((message) => (
//                 <div key={message.id} className="border rounded-lg p-4 space-y-3">
//                   <div className="flex justify-between items-start">
//                     <div className="flex items-center space-x-2">
//                       <User className="h-4 w-4 text-gray-500" />
//                       <span className="font-medium">{message.patientName || 'Unknown Patient'}</span>
//                       <Badge variant="secondary">ID: {message.patientId}</Badge>
//                     </div>
//                     <span className="text-sm text-gray-500">
//                       {formatDate(message.createdAt)}
//                     </span>
//                   </div>

//                   <div className="bg-gray-50 rounded p-3">
//                     <p className="text-gray-800">{message.messageText}</p>
//                   </div>

//                   <div className="flex justify-between items-center">
//                     <div className="flex space-x-2">
//                       <Button
//                         onClick={() => moderateMessage(message.messageId, 'approve')}
//                         disabled={moderatingId === message.messageId}
//                         className="bg-green-600 hover:bg-green-700"
//                       >
//                         <CheckCircle className="h-4 w-4 mr-1" />
//                         Approve
//                       </Button>
//                       <Button
//                         onClick={() => moderateMessage(message.messageId, 'reject', rejectionReason || 'Inappropriate content')}
//                         disabled={moderatingId === message.messageId}
//                         variant="destructive"
//                       >
//                         <XCircle className="h-4 w-4 mr-1" />
//                         Reject
//                       </Button>
//                     </div>
//                   </div>

//                   <div className="mt-2">
//                     <Textarea
//                       placeholder="Rejection reason (optional)"
//                       value={rejectionReason}
//                       onChange={(e) => setRejectionReason(e.target.value)}
//                       className="text-sm"
//                       rows={2}
//                     />
//                   </div>
//                 </div>
//               ))}
//             </div>
//           )}
//         </CardContent>
//       </Card>
//     </div>
//   );
// };

// export default ModerationPanel;
