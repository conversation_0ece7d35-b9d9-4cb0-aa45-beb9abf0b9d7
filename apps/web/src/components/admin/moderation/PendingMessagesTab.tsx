import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Pagination
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  AttachFile as AttachFileIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import moderationService, { ModerationMessage, PaginationInfo } from '../../../services/moderation.service';
import MessageDetailsModal from './MessageDetailsModal';

interface PendingMessagesTabProps {
  onStatsUpdate: () => void;
  onShowSnackbar: (message: string, severity: 'success' | 'error' | 'info') => void;
}

const PendingMessagesTab: React.FC<PendingMessagesTabProps> = ({ onStatsUpdate, onShowSnackbar }) => {
  const [messages, setMessages] = useState<ModerationMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 5,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  const [moderationDialog, setModerationDialog] = useState<{
    open: boolean;
    messageId: string | null;
    action: 'approve' | 'reject' | null;
    isBulk: boolean;
  }>({
    open: false,
    messageId: null,
    action: null,
    isBulk: false
  });
  const [reason, setReason] = useState('');
  const [processing, setProcessing] = useState(false);
  const [imageModal, setImageModal] = useState<{
    open: boolean;
    imageUrl: string;
    alt: string;
  }>({
    open: false,
    imageUrl: '',
    alt: ''
  });
  const [detailsModal, setDetailsModal] = useState<{
    open: boolean;
    message: ModerationMessage | null;
  }>({
    open: false,
    message: null
  });

  useEffect(() => {
    loadPendingMessages();
  }, []);

  const loadPendingMessages = async (page: number = pagination.page) => {
    try {
      setLoading(true);
      const response = await moderationService.getPendingMessages(pagination.limit, page);
      setMessages(response.data);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Error loading pending messages:', error);
      onShowSnackbar('Failed to load pending messages', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    loadPendingMessages(page);
  };

  const handleSelectMessage = (messageId: string) => {
    const newSelected = new Set(selectedMessages);
    if (newSelected.has(messageId)) {
      newSelected.delete(messageId);
    } else {
      newSelected.add(messageId);
    }
    setSelectedMessages(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedMessages.size === messages.length) {
      setSelectedMessages(new Set());
    } else {
      setSelectedMessages(new Set(messages.map(m => m.messageId)));
    }
  };

  const openModerationDialog = (action: 'approve' | 'reject', messageId?: string) => {
    setModerationDialog({
      open: true,
      messageId: messageId || null,
      action,
      isBulk: !messageId
    });
    setReason('');
  };

  const closeModerationDialog = () => {
    setModerationDialog({
      open: false,
      messageId: null,
      action: null,
      isBulk: false
    });
    setReason('');
  };

  const handleDetailsModeration = (messageId: string, action: 'approve' | 'reject') => {
    // Close details modal first
    setDetailsModal({ open: false, message: null });

    // Open moderation dialog with reason collection
    openModerationDialog(action, messageId);
  };

  const handleModeration = async () => {
    if (!moderationDialog.action) return;

    try {
      setProcessing(true);

      if (moderationDialog.isBulk) {
        // Bulk moderation
        const messageIds = Array.from(selectedMessages);
        if (messageIds.length === 0) {
          onShowSnackbar('No messages selected', 'error');
          return;
        }

        const result = await moderationService.bulkModerateMessages({
          messageIds,
          action: moderationDialog.action,
          reason: reason || undefined
        });

        onShowSnackbar(result.message, 'success');
        setSelectedMessages(new Set());
      } else {
        // Single message moderation
        if (!moderationDialog.messageId) return;

        const result = await moderationService.moderateMessage({
          messageId: moderationDialog.messageId,
          action: moderationDialog.action,
          reason: reason || undefined
        });

        onShowSnackbar(result.message, 'success');
      }

      // Refresh data
      await loadPendingMessages();
      onStatsUpdate();

      closeModerationDialog();
    } catch (error) {
      console.error('Error moderating message:', error);
      onShowSnackbar('Failed to moderate message', 'error');
    } finally {
      setProcessing(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('dd/MM/yyyy HH:mm');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (messages.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="h6" color="textSecondary">
          No pending messages
        </Typography>
        <Typography variant="body2" color="textSecondary">
          All messages have been moderated
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Bulk Actions */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={selectedMessages.size === messages.length && messages.length > 0}
                indeterminate={selectedMessages.size > 0 && selectedMessages.size < messages.length}
                onChange={handleSelectAll}
              />
            }
            label={`Select All (${selectedMessages.size} selected)`}
          />
        </Box>

        {selectedMessages.size > 0 && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<ApproveIcon />}
              onClick={() => openModerationDialog('approve')}
              sx={{
                backgroundColor: '#008000',
                color: 'white',
                '&:hover': {
                  backgroundColor: '#006600'
                }
              }}
            >
              Approve Selected ({selectedMessages.size})
            </Button>
            <Button
              variant="contained"
              color="error"
              startIcon={<RejectIcon />}
              onClick={() => openModerationDialog('reject')}
            >
              Reject Selected ({selectedMessages.size})
            </Button>
          </Box>
        )}
      </Box>

      {/* Messages Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">Select</TableCell>
              <TableCell>Patient</TableCell>
              <TableCell>Message</TableCell>
              <TableCell>Received</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {messages.map((message) => (
              <TableRow key={message.messageId} hover>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedMessages.has(message.messageId)}
                    onChange={() => handleSelectMessage(message.messageId)}
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="bold">
                      {message.patientName}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      ID: {message.patientId}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ maxWidth: 250 }}>
                    {/* Show truncated text message */}
                    {message.messageText && message.messageText.trim() && (
                      <Typography
                        variant="body2"
                        sx={{
                          wordBreak: 'break-word',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          mb: 1
                        }}
                      >
                        {message.messageText}
                      </Typography>
                    )}

                    {/* Show attachment summary */}
                    {message.hasAttachments && message.attachmentCount && message.attachmentCount > 0 && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <AttachFileIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                        <Typography variant="body2" color="primary.main">
                          {message.attachmentCount} {message.attachmentTypes?.join(', ') || 'attachment'}
                          {message.attachmentCount > 1 ? 's' : ''}
                        </Typography>
                      </Box>
                    )}

                    {/* Show placeholder if no content */}
                    {!message.messageText?.trim() && !message.hasAttachments && (
                      <Typography variant="body2" color="textSecondary" fontStyle="italic">
                        [No content]
                      </Typography>
                    )}

                    {/* View Details Button */}
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => setDetailsModal({ open: true, message })}
                      sx={{ mt: 1 }}
                    >
                      View Details
                    </Button>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatTimestamp(message.createdAt)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Approve">
                      <IconButton
                        onClick={() => openModerationDialog('approve', message.messageId)}
                        sx={{
                          color: '#008000',
                          '&:hover': {
                            backgroundColor: 'rgba(0, 128, 0, 0.1)'
                          }
                        }}
                      >
                        <ApproveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reject">
                      <IconButton
                        color="error"
                        onClick={() => openModerationDialog('reject', message.messageId)}
                      >
                        <RejectIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination Controls */}
      {!loading && messages.length > 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
          <Typography variant="body2" color="textSecondary">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} messages
          </Typography>
          <Pagination
            count={Math.max(pagination.totalPages, 1)} // Always show at least 1 page
            page={pagination.page}
            onChange={handlePageChange}
            color="primary"
            showFirstButton
            showLastButton
            size="medium"
            disabled={pagination.totalPages <= 1} // Disable when only one page
          />
        </Box>
      )}

      {/* Moderation Dialog */}
      <Dialog open={moderationDialog.open} onClose={closeModerationDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {moderationDialog.action === 'approve' ? 'Approve' : 'Reject'} Message
          {moderationDialog.isBulk ? 's' : ''}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            {moderationDialog.isBulk
              ? `Are you sure you want to ${moderationDialog.action} ${selectedMessages.size} selected messages?`
              : `Are you sure you want to ${moderationDialog.action} this message?`
            }
          </Typography>
          <TextField
            fullWidth
            label="Reason (optional)"
            multiline
            rows={3}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter a reason for this moderation action..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={closeModerationDialog} disabled={processing}>
            Cancel
          </Button>
          <Button
            onClick={handleModeration}
            variant="contained"
            disabled={processing}
            startIcon={processing ? <CircularProgress size={20} /> : null}
            sx={{
              backgroundColor: moderationDialog.action === 'approve' ? '#008000' : undefined,
              color: moderationDialog.action === 'approve' ? 'white' : undefined,
              '&:hover': {
                backgroundColor: moderationDialog.action === 'approve' ? '#006600' : undefined
              }
            }}
            color={moderationDialog.action === 'approve' ? undefined : 'error'}
          >
            {processing ? 'Processing...' : `${moderationDialog.action === 'approve' ? 'Approve' : 'Reject'}`}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Modal */}
      <Dialog
        open={imageModal.open}
        onClose={() => setImageModal({ open: false, imageUrl: '', alt: '' })}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Patient Image Attachment
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <img
              src={imageModal.imageUrl}
              alt={imageModal.alt}
              style={{
                maxWidth: '100%',
                maxHeight: '80vh',
                objectFit: 'contain'
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImageModal({ open: false, imageUrl: '', alt: '' })}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Message Details Modal */}
      <MessageDetailsModal
        open={detailsModal.open}
        message={detailsModal.message}
        onClose={() => setDetailsModal({ open: false, message: null })}
        onModerate={handleDetailsModeration}
      />
    </Box>
  );
};

export default PendingMessagesTab;
