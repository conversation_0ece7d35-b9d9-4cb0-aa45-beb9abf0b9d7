-- Drop existing tables
DROP TABLE IF EXISTS PatientSlot CASCADE;
DROP TABLE IF EXISTS Slot CASCADE;
DROP TABLE IF EXISTS Range CASCADE;

-- Create the Range table
CREATE TABLE Range (
id TEXT PRIMARY KEY, -- Externally provided, can be a string
day TEXT NOT NULL, -- Day of the week as a string
date TEXT NOT NULL,
start TEXT NOT NULL, -- Start time as a string
"end" TEXT NOT NULL, -- End time as a string (quoted because it's a reserved keyword)
interval INTEGER NOT NULL, -- Numeric availability
availability INTEGER NOT NULL, -- Numeric availability
status TEXT NOT NULL,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create the Slot table
CREATE TABLE Slot (
id SERIAL PRIMARY KEY, -- id is now a serial number (auto-incrementing integer)
range_id TEXT NOT NULL, -- Foreign key referencing Range (string ID)
slot TEXT NOT NULL, -- Time slot as a string
remaining INTEGER NOT NULL, -- Numeric remaining availability
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
CONSTRAINT fk_range
FOREIGN KEY (range_id) REFERENCES Range (id)
ON DELETE CASCADE, -- Cascades delete if a Range is deleted
CONSTRAINT slot_range_slot_unique UNIQUE (range_id, slot) -- Unique constraint for ON CONFLICT
);

-- Create the PatientSlot table
CREATE TABLE PatientSlot (
patient_id TEXT NOT NULL, -- Patient ID as a string (sent by you)
range_id TEXT NOT NULL, -- Foreign key referencing Range (string ID)
slot_id SERIAL NOT NULL, -- Foreign key referencing Slot (auto-incrementing integer)
"sameDayRebook" BOOLEAN DEFAULT FALSE,
"createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
"updatedAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
PRIMARY KEY (patient_id, range_id, slot_id), -- Composite primary key
CONSTRAINT fk_range
FOREIGN KEY (range_id) REFERENCES Range (id) -- Foreign key referencing Range
ON DELETE CASCADE, -- Cascade delete if a Range is deleted
CONSTRAINT fk_slot
FOREIGN KEY (slot_id) REFERENCES Slot (id) -- Foreign key referencing Slot
ON DELETE CASCADE, -- Cascade delete if a Slot is deleted
CONSTRAINT unique_patient_range UNIQUE (patient_id, range_id)
);

CREATE INDEX idx_range_date ON Range(date);
