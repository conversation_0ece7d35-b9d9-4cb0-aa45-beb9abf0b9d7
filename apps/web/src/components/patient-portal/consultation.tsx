import { useSearch, MakeGenerics } from "@tanstack/react-location";
import HelpIcon from '@mui/icons-material/Help';
import Grid from "@mui/material/Grid2";
import React, { useEffect, useRef, useState } from "react";
import { Box, Typography, useMediaQuery, CircularProgress } from "@mui/material";
import { useTheme } from "@mui/material/styles"
import SalesIQ from "./support-window";
import Daily, { DailyCall } from '@daily-co/daily-js'
import { ApiClient } from "../../services";
import { useSnackbar } from "notistack";
import { UserActions } from "../../utils";

type UrlProps = MakeGenerics<{
    Search: {
        token: string
    }
}>

const DoctorWaitingPopup: React.FC = () => (
    <Box
        sx={{
            position: 'absolute',
            left: '50%',
            top: '35%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 100,
            borderRadius: '12px',
            padding: '24px 32px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            maxWidth: '400px',
            width: 'calc(100% - 64px)', // Responsive width with margins
        }}
    >
        <Typography variant="h5" sx={{ mb: 2, textAlign: 'center' }}>
            The doctor is finishing up with another patient and will be with you shortly. 
        </Typography>
        <CircularProgress color="success" sx={{ my: 2 }} />
        <Typography variant="body1" sx={{ textAlign: 'center' }}>
            Please stay on this page.
        </Typography>
    </Box>
);

const Consultation: React.FC = () => {
    const { token } = useSearch<UrlProps>();
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
    const componentRef = useRef<HTMLElement | null>(null);
    const frameRef = useRef<DailyCall | null>(null);
    const { enqueueSnackbar } = useSnackbar();
    const [showWaitingPopup, setShowWaitingPopup] = useState(false);
    const [_isDoctorPresent, setIsDoctorPresent] = useState(false);

    // Helper function to check if a participant is a doctor
    const isDoctorUser = (username: string = '') => {
        // Check various prefixes and patterns for doctor usernames
        const doctorPatterns = [
            'Dr.', 
            'Dr ', 
            'Doctor',
            'doctor',
            '(Zenith)',
            'dr.',
            'DR.'
        ];
        
        // Check if any pattern matches
        return doctorPatterns.some(pattern => username.includes(pattern));
    };

    // Helper function to check if a participant is the patient (local user)
    // const isPatient = (participant: any, frame: DailyCall) => {
    //     try {
    //         // Various ways to check if participant is local
    //         const localParticipant = frame.participants().local;
            
    //         if (!participant || !localParticipant) return false;
            
    //         return (
    //             participant.session_id === localParticipant.session_id ||
    //             participant.local === true ||
    //             participant.user_name === 'Patient'
    //         );
    //     } catch (error) {
    //         console.error("Error checking if participant is patient:", error);
    //         return false;
    //     }
    // };

    // Local - Patient 
    const handleLocalJoinedMeeting = async () => {
        if (token) {
            await ApiClient.joinedCallPatientWaitingQueue(token)
                .catch((e) => {
                    enqueueSnackbar(e.message, {
                        variant: 'error'
                    })
                })

            await ApiClient.userActions('Patient', token || '', UserActions.JOINED, 'Patient Auto Joined')
                .catch((e) => {
                    enqueueSnackbar(e.message, {
                        variant: 'error'
                    })
                })
        }
    }

    const handleLeave = async () => {
        await frameRef?.current?.leave()

        if (token) {
            await ApiClient.updateMeetingStatus(token, false)
                .catch((e) => {
                    enqueueSnackbar(e.message, {
                        variant: 'error'
                    })
                })

            ApiClient.userActions('Patient', token || '', UserActions.ENDED, 'Patient Left the call')
                .catch((e) => {
                    enqueueSnackbar(e.message, {
                        variant: 'error'
                    })
                })
        }
    }

    useEffect(() => {
        const init = () => {
            if (componentRef.current) {
                let newFrame = Daily.getCallInstance()
                if (!newFrame) {
                    newFrame = Daily.createFrame(componentRef?.current as HTMLElement, {
                        iframeStyle: {
                            position: "relative",
                            width: "100%",
                            height: isDesktop ? '100%' : "80%",
                            border: "0",
                        },
                        startVideoOff: true,
                        showLeaveButton: true,
                        showFullscreenButton: true,
                        userName: 'Patient'
                    })
                    frameRef.current = newFrame

                    newFrame.setTheme({
                        colors: {
                            accent: '#008000',
                            accentText: '#FFFFFF'
                        },
                    });

                    newFrame.updateInputSettings({
                        video: {
                            processor: {
                                type: "background-blur",
                                config: {
                                    strength: 1,
                                },
                            },
                        },
                    })

                    newFrame.join({ url: `https://harvest-australia.daily.co/${token}` })
                    
                    // Handle patient leaving
                    newFrame.on('left-meeting', async () => {
                        await handleLeave();

                        //remove the waiting popup when patient leavees
                        setShowWaitingPopup(false);
                    })
                    
                    // Handle when patient joins and check if doctor is present
                    newFrame.on("joined-meeting", async () => {
                        await handleLocalJoinedMeeting();
                        
                        // Check if doctor is already in the call
                        try {
                            if (!newFrame) return;
                            
                            const participants = newFrame.participants();
                            
                            // Filter participants to find doctor
                            const doctorExists = Object.values(participants).some(
                                p => !p.local && isDoctorUser(p.user_name)
                            );
                            
                            setIsDoctorPresent(doctorExists);
                            setShowWaitingPopup(!doctorExists);
                            
                        } catch (error) {
                            console.error("Error checking for doctor presence:", error);
                        }
                    })
                    
                    // When a new participant joins
                    newFrame.on("participant-joined", (event) => {
                        // Only proceed if it's a doctor (not the patient)
                        if (!event.participant.local && isDoctorUser(event.participant.user_name)) {
                            console.log('Doctor joined the call');
                            setIsDoctorPresent(true);
                            setShowWaitingPopup(false);
                        }
                    })
                    
                    // When a participant leaves
                    newFrame.on("participant-left", (event) => {
                        // Only proceed if it's a doctor (not the patient)
                        if (!event.participant.local && isDoctorUser(event.participant.user_name)) {
                            console.log('Doctor left the call');
                            setIsDoctorPresent(false);
                            setShowWaitingPopup(true);
                        }
                    })
                }

                return newFrame
            }
        }

        ApiClient.userActions('Patient', token || '', UserActions.REACHED, 'Patient reached consultation video page.')
        if (token) {
            ApiClient.updateMeetingStatus(token, false)
        }
        
        const newCreatedFrame = init()
        return () => {
            if (newCreatedFrame) {
                newCreatedFrame.leave();
            }
        }
    }, [])

    useEffect(() => {
        const handleVisibilityChange = async () => {
            if (document.visibilityState === 'visible') {
                await ApiClient.userActions('Patient', token || '', UserActions.RETURNED, 'Patient returned to video URL screen')
            }
            else if (document.visibilityState === 'hidden') {
                await ApiClient.userActions('Patient', token || '', UserActions.AWAY, 'Patient left video URL screen')
            }
        }

        document.addEventListener('visibilitychange', handleVisibilityChange);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        }

    }, [])

    return (
        <>
            <SalesIQ />
            <Grid container sx={{ height: '100vh' }}>
                {isDesktop &&
                    <Grid size={{ lg: 4 }} container justifyContent={'start'} alignItems={'center'} direction={'column'} sx={{ pt: 5 }}>
                        {/* <AppointmentTimer /> */}
                        <Grid container justifyContent={'start'} alignItems={'center'} sx={{ mt: 4 }} >
                            <Typography display={'inline'} sx={{ fontSize: '30px', mr: 2 }} align="center">
                                Join your consultation call
                            </Typography>
                        </Grid>
                        <Grid>
                            <Typography align='center' sx={{ fontSize: '14px' }} display={'inline'}>
                                Contact our Support Team via the chatbox if you experience any issues.  <HelpIcon sx={{ fontSize: '15px' }} />
                            </Typography>
                        </Grid>
                    </Grid>
                }
                <Grid size={{ xs: 12, lg: 8 }} container justifyContent={'center'} alignItems={'center'} sx={{ height: '100%', width: '100%' }}>
                    <Box sx={{ position: 'relative', width: '100%', height: '100%', mt: { xs: 2, lg: 0 } }}>
                        <Box ref={componentRef} sx={{ width: '100%', height: '100%' }} />
                        {showWaitingPopup && <DoctorWaitingPopup />}
                    </Box>
                </Grid>
            </Grid>
        </>
    )
}
export default Consultation
