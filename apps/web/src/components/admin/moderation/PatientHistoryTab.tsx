import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  TextField,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Pending as PendingIcon,
  TrendingUp as IncreaseIcon,
  Schedule as ExtendIcon
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import adminRequestsService, { AdminRequest } from '../../../services/admin-requests.service';

interface PatientRequestHistoryTabProps {
  onShowSnackbar: (message: string, severity: 'success' | 'error' | 'info') => void;
}

const PatientRequestHistoryTab: React.FC<PatientRequestHistoryTabProps> = ({ onShowSnackbar }) => {
  const [patientId, setPatientId] = useState('');
  const [requests, setRequests] = useState<AdminRequest[]>([]);
  const [loading, setLoading] = useState(false);
  const [searched, setSearched] = useState(false);

  const handleSearch = async () => {
    if (!patientId.trim()) {
      onShowSnackbar('Please enter a patient ID', 'error');
      return;
    }

    try {
      setLoading(true);
      setSearched(true);
      const response = await adminRequestsService.getPatientRequestHistory(patientId.trim(), 50);
      setRequests(response.requests || []);

      if (response.requests?.length === 0) {
        onShowSnackbar('No request history found for this patient', 'info');
      } else {
        onShowSnackbar(`Found ${response.requests?.length || 0} requests for this patient`, 'success');
      }
    } catch (error) {
      console.error('Error loading patient request history:', error);
      onShowSnackbar('Failed to load patient request history', 'error');
      setRequests([]);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('dd/MM/yyyy HH:mm');
  };

  const getRequestTypeIcon = (type: string) => {
    return type === 'thc_increase' ? <IncreaseIcon /> : <ExtendIcon />;
  };

  const getRequestTypeLabel = (type: string) => {
    return type === 'thc_increase' ? 'THC Increase' : 'Extend TP';
  };

  const getRiskScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 70) return '#4caf50'; // Green
    if (percentage >= 40) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'approved':
        return (
          <Chip
            icon={<ApproveIcon />}
            label="Approved"
            size="small"
            sx={{
              backgroundColor: '#008000',
              color: 'white',
              '& .MuiChip-icon': {
                color: 'white'
              }
            }}
          />
        );
      case 'rejected':
        return (
          <Chip
            icon={<RejectIcon />}
            label="Rejected"
            color="error"
            size="small"
          />
        );
      case 'pending':
        return (
          <Chip
            icon={<PendingIcon />}
            label="Pending"
            color="warning"
            size="small"
          />
        );
      default:
        return (
          <Chip
            label={status}
            color="default"
            size="small"
          />
        );
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Patient Request History
      </Typography>

      <Typography variant="body2" color="textSecondary" sx={{ mb: 3 }}>
        Search for a specific patient's treatment plan request history
      </Typography>

      {/* Search Section */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'flex-end' }}>
            <TextField
              fullWidth
              label="Patient ID"
              value={patientId}
              onChange={(e) => setPatientId(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter patient ID to search..."
              disabled={loading}
            />
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
              onClick={handleSearch}
              disabled={loading || !patientId.trim()}
              sx={{ minWidth: 120 }}
            >
              {loading ? 'Searching...' : 'Search'}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Results Section */}
      {searched && (
        <>
          {requests.length === 0 ? (
            <Alert severity="info">
              No request history found for patient ID: {patientId}
            </Alert>
          ) : (
            <>
              <Typography variant="subtitle1" gutterBottom>
                Found {requests.length} requests for patient: {patientId}
              </Typography>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Request Type</TableCell>
                      <TableCell>Risk Score</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Submitted</TableCell>
                      <TableCell>Processed</TableCell>
                      <TableCell>Doctor</TableCell>
                      <TableCell>Notes</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {requests.map((request) => (
                      <TableRow key={request.id} hover>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            {getRequestTypeIcon(request.type)}
                            <Typography variant="body2">
                              {getRequestTypeLabel(request.type)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={`${request.total_score}/${request.max_score}`}
                            size="small"
                            sx={{
                              backgroundColor: getRiskScoreColor(request.total_score, request.max_score),
                              color: 'white',
                              fontWeight: 'bold'
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          {getStatusChip(request.status)}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatTimestamp(request.created_at)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {request.reviewed_at ? (
                            <Typography variant="body2">
                              {formatTimestamp(request.reviewed_at)}
                            </Typography>
                          ) : (
                            <Typography variant="body2" color="textSecondary">
                              -
                            </Typography>
                          )}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {request.doctor_name || '-'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ maxWidth: 200, wordBreak: 'break-word' }}>
                            {request.review_notes || '-'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}
        </>
      )}
    </Box>
  );
};

export default PatientRequestHistoryTab;
