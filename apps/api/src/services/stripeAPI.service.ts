import Stripe from 'stripe';
import { DateTime } from 'luxon';
import config from '../config/index';
import { logger } from '../config/logger';

interface StripePaymentData {
  stripePaymentIntentId: string;
  customerName: string;
  customerEmail: string;
  amountCents: number;
  currency: string;
  status: string;
  description: string;
  metadata: Record<string, unknown>;
  createdAt: Date;
}

interface FetchPaymentsOptions {
  startDate: DateTime;
  endDate: DateTime;
  limit?: number;
  expand?: string[];
}

interface FetchPaymentsResult {
  payments: StripePaymentData[];
  totalCount: number;
  hasMore: boolean;
  lastPaymentId?: string;
}

class StripeAPIService {
  private stripe: Stripe;

  constructor() {
    // Use production key by default, test key for development
    const apiKey = config.stripeApiKey;

    if (!apiKey) {
      throw new Error('Stripe API key not configured');
    }

    this.stripe = new Stripe(apiKey, {
      apiVersion: config.stripeSync.apiVersion as Stripe.LatestApiVersion,
      maxNetworkRetries: config.stripeSync.maxRetries,
      timeout: config.stripeSync.timeout,
    });

    logger.info(`StripeAPIService initialized with ${config.env} environment`);
    logger.info(`Stripe API config: version=${config.stripeSync.apiVersion}, retries=${config.stripeSync.maxRetries}, timeout=${config.stripeSync.timeout}ms`);
  }

  /**
   * Fetch payments for a specific date range
   */
  async fetchPaymentsForDateRange(options: FetchPaymentsOptions): Promise<FetchPaymentsResult> {
    const { startDate, endDate, limit = 100, expand = ['data.charges.data.payment_method_details'] } = options;

    try {
      logger.info(`Fetching payments from ${startDate.toISO()} to ${endDate.toISO()}`);

      const params: Stripe.PaymentIntentListParams = {
        created: {
          gte: startDate.toUnixInteger(),
          lte: endDate.toUnixInteger(),
        },
        limit,
        expand,
      };

      const response = await this.stripe.paymentIntents.list(params);

      const payments = response.data.map(intent => this.transformPaymentIntent(intent));

      logger.info(`Fetched ${payments.length} payments from Stripe API`);

      return {
        payments,
        totalCount: response.data.length,
        hasMore: response.has_more,
        lastPaymentId: response.data.length > 0 ? response.data[response.data.length - 1].id : undefined,
      };
    } catch (error) {
      logger.error('Error fetching payments from Stripe API:', error);
      throw this.handleStripeError(error);
    }
  }

  /**
   * Fetch all payments for a date range with automatic pagination
   */
  async fetchAllPaymentsForDateRange(options: FetchPaymentsOptions): Promise<StripePaymentData[]> {
    const allPayments: StripePaymentData[] = [];
    let hasMore = true;
    let startingAfter: string | undefined;

    while (hasMore) {
      try {
        const params: Stripe.PaymentIntentListParams = {
          created: {
            gte: options.startDate.toUnixInteger(),
            lte: options.endDate.toUnixInteger(),
          },
          limit: options.limit || 100,
          expand: options.expand || ['data.charges.data.payment_method_details'],
          ...(startingAfter && { starting_after: startingAfter }),
        };

        const response = await this.stripe.paymentIntents.list(params);

        const payments = response.data.map(intent => this.transformPaymentIntent(intent));
        allPayments.push(...payments);

        hasMore = response.has_more;
        if (hasMore && response.data.length > 0) {
          startingAfter = response.data[response.data.length - 1].id;
        }

        // Add configurable delay to respect rate limits
        if (hasMore) {
          await this.delay(config.stripeSync.rateLimitDelay);
        }

        logger.info(`Fetched batch of ${payments.length} payments. Total so far: ${allPayments.length}`);
      } catch (error) {
        logger.error('Error during paginated fetch:', error);
        throw this.handleStripeError(error);
      }
    }

    logger.info(`Completed fetching all payments. Total: ${allPayments.length}`);
    return allPayments;
  }

  /**
   * Fetch recent payments (last 24 hours)
   */
  async fetchRecentPayments(): Promise<StripePaymentData[]> {
    const now = DateTime.now().setZone('Australia/Sydney');
    const yesterday = now.minus({ days: 1 });

    return this.fetchAllPaymentsForDateRange({
      startDate: yesterday,
      endDate: now,
    });
  }

  /**
   * Fetch payments for a specific date (day)
   */
  async fetchPaymentsForDate(date: string): Promise<StripePaymentData[]> {
    const targetDate = DateTime.fromISO(date).setZone('Australia/Sydney');
    const startOfDay = targetDate.startOf('day');
    const endOfDay = targetDate.endOf('day');

    return this.fetchAllPaymentsForDateRange({
      startDate: startOfDay,
      endDate: endOfDay,
    });
  }

  /**
   * Fetch payments with advanced filtering options
   */
  async fetchPaymentsWithFilters(options: {
    startDate: DateTime;
    endDate: DateTime;
    status?: string[];
    currency?: string;
    minAmount?: number;
    maxAmount?: number;
    limit?: number;
  }): Promise<StripePaymentData[]> {
    const { startDate, endDate, status, currency, minAmount, maxAmount, limit } = options;

    try {
      logger.info(`Fetching payments with filters: status=${status?.join(',')}, currency=${currency}, amount=${minAmount}-${maxAmount}`);

      const params: Stripe.PaymentIntentListParams = {
        created: {
          gte: startDate.toUnixInteger(),
          lte: endDate.toUnixInteger(),
        },
        limit: limit || config.stripeSync.batchSize,
        expand: ['data.charges.data.payment_method_details'],
      };

      const response = await this.stripe.paymentIntents.list(params);

      let payments = response.data.map(intent => this.transformPaymentIntent(intent));

      // Apply additional filters (Stripe API doesn't support all filters natively)
      if (status && status.length > 0) {
        payments = payments.filter(payment => status.includes(payment.status));
      }

      if (currency) {
        payments = payments.filter(payment => payment.currency.toLowerCase() === currency.toLowerCase());
      }

      if (minAmount !== undefined) {
        payments = payments.filter(payment => payment.amountCents >= minAmount);
      }

      if (maxAmount !== undefined) {
        payments = payments.filter(payment => payment.amountCents <= maxAmount);
      }

      logger.info(`Filtered results: ${payments.length} payments match criteria`);
      return payments;

    } catch (error) {
      logger.error('Error fetching payments with filters:', error);
      throw this.handleStripeError(error);
    }
  }

  /**
   * Fetch payments by customer email
   */
  async fetchPaymentsByCustomer(customerEmail: string, daysBack: number = 30): Promise<StripePaymentData[]> {
    const now = DateTime.now().setZone('Australia/Sydney');
    const startDate = now.minus({ days: daysBack });

    const allPayments = await this.fetchAllPaymentsForDateRange({
      startDate,
      endDate: now,
    });

    return allPayments.filter(payment =>
      payment.customerEmail.toLowerCase() === customerEmail.toLowerCase()
    );
  }

  /**
   * Get payment statistics for a date range
   */
  async getPaymentStatistics(startDate: DateTime, endDate: DateTime): Promise<{
    totalPayments: number;
    successfulPayments: number;
    failedPayments: number;
    totalAmountCents: number;
    averageAmountCents: number;
    paymentMethodBreakdown: Record<string, number>;
    currencyBreakdown: Record<string, number>;
  }> {
    const payments = await this.fetchAllPaymentsForDateRange({ startDate, endDate });

    const successful = payments.filter(p => p.status === 'succeeded');
    const failed = payments.filter(p => p.status !== 'succeeded');
    const totalAmount = successful.reduce((sum, p) => sum + p.amountCents, 0);

    // Payment method breakdown - we'll skip this since we don't store payment method type
    const paymentMethodBreakdown: Record<string, number> = {
      'not_tracked': payments.length
    };

    // Currency breakdown
    const currencyBreakdown: Record<string, number> = {};
    payments.forEach(payment => {
      currencyBreakdown[payment.currency] = (currencyBreakdown[payment.currency] || 0) + 1;
    });

    return {
      totalPayments: payments.length,
      successfulPayments: successful.length,
      failedPayments: failed.length,
      totalAmountCents: totalAmount,
      averageAmountCents: successful.length > 0 ? Math.round(totalAmount / successful.length) : 0,
      paymentMethodBreakdown,
      currencyBreakdown,
    };
  }

  /**
   * Transform Stripe PaymentIntent to our internal format
   */
  private transformPaymentIntent(intent: Stripe.PaymentIntent): StripePaymentData {
    try {
      // Extract customer information with validation
      const { customerName, customerEmail } = this.extractCustomerInfo(intent);

      // Validate and transform status
      const status = this.normalizePaymentStatus(intent.status);

      // Validate amount
      if (typeof intent.amount !== 'number' || intent.amount < 0) {
        logger.warn(`Invalid amount for payment ${intent.id}: ${intent.amount}`);
      }

      return {
        stripePaymentIntentId: intent.id,
        customerName,
        customerEmail,
        amountCents: intent.amount || 0,
        currency: (intent.currency || 'AUD').toUpperCase(),
        status,
        description: this.sanitizeDescription(intent.description),
        metadata: this.sanitizeMetadata(intent.metadata),
        createdAt: new Date(intent.created * 1000), // Convert Unix timestamp to Date
      };
    } catch (error) {
      logger.error(`Error transforming payment intent ${intent.id}:`, error);
      // Return a safe fallback object
      return this.createFallbackPaymentData(intent);
    }
  }





  /**
   * Extract and validate customer information (original method)
   */
  private extractCustomerInfo(intent: Stripe.PaymentIntent): { customerName: string; customerEmail: string } {
    // Try metadata first (our preferred source)
    let customerName: string | undefined = intent.metadata?.Name || intent.metadata?.customer_name;
    let customerEmail: string | undefined = intent.metadata?.email;

    // Fallback to receipt email
    if (!customerEmail && intent.receipt_email) {
      customerEmail = intent.receipt_email;
    }

    // Fallback to customer object if available
    if ((!customerName || !customerEmail) && intent.customer && typeof intent.customer === 'object') {
      const customer = intent.customer as Stripe.Customer;
      if (!customerName && customer.name) {
        customerName = customer.name;
      }
      if (!customerEmail && customer.email) {
        customerEmail = customer.email;
      }
    }

    // Validate email format
    if (customerEmail && !this.isValidEmail(customerEmail)) {
      logger.warn(`Invalid email format for payment ${intent.id}: ${customerEmail}`);
      customerEmail = '<EMAIL>';
    }

    return {
      customerName: customerName || 'Unknown',
      customerEmail: customerEmail || '<EMAIL>',
    };
  }

  /**
   * Normalize payment status to our expected values
   */
  private normalizePaymentStatus(status: string): string {
    const statusMap: Record<string, string> = {
      'succeeded': 'payment_intent.succeeded',
      'processing': 'payment_intent.processing',
      'requires_payment_method': 'payment_intent.requires_payment_method',
      'requires_confirmation': 'payment_intent.requires_confirmation',
      'requires_action': 'payment_intent.requires_action',
      'canceled': 'payment_intent.canceled',
      'failed': 'payment_intent.failed',
    };

    return statusMap[status] || `payment_intent.${status}`;
  }

  /**
   * Sanitize description field
   */
  private sanitizeDescription(description: string | null): string {
    if (!description) return '';

    // Remove any potentially harmful content and limit length
    return description
      .replace(/[<>]/g, '') // Remove HTML-like characters
      .substring(0, 500) // Limit length
      .trim();
  }

  /**
   * Sanitize metadata object
   */
  private sanitizeMetadata(metadata: Stripe.MetadataParam | null): Record<string, unknown> {
    if (!metadata) return {};

    const sanitized: Record<string, unknown> = {};

    Object.entries(metadata).forEach(([key, value]) => {
      // Only include safe keys and values
      if (typeof key === 'string' && key.length <= 100) {
        if (typeof value === 'string' && value.length <= 1000) {
          sanitized[key] = value;
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          sanitized[key] = value;
        }
      }
    });

    return sanitized;
  }

  /**
   * Create fallback payment data for error cases
   */
  private createFallbackPaymentData(intent: Stripe.PaymentIntent): StripePaymentData {
    return {
      stripePaymentIntentId: intent.id,
      customerName: 'Unknown',
      customerEmail: '<EMAIL>',
      amountCents: intent.amount || 0,
      currency: 'AUD',
      status: 'payment_intent.unknown',
      description: 'Error processing payment data',
      metadata: { error: 'transformation_failed' },
      createdAt: new Date(intent.created * 1000),
    };
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Handle Stripe API errors with proper error types
   */
  private handleStripeError(error: unknown): Error {
    if (error instanceof Stripe.errors.StripeError) {
      const message = `Stripe API Error: ${error.type} - ${error.message}`;
      logger.error(message, { 
        type: error.type, 
        code: error.code, 
        statusCode: error.statusCode 
      });
      return new Error(message);
    }
    
    if (error instanceof Error) {
      return error;
    }
    
    return new Error('Unknown Stripe API error');
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get API usage information
   */
  async getAPIUsage(): Promise<{ requestsRemaining?: number; resetTime?: Date }> {
    try {
      // Make a minimal API call to get rate limit headers
      await this.stripe.paymentIntents.list({ limit: 1 });

      // Note: Stripe doesn't expose rate limit info in the SDK
      // This is a placeholder for future rate limit monitoring
      return {
        requestsRemaining: undefined,
        resetTime: undefined,
      };
    } catch (error) {
      logger.error('Error getting API usage:', error);
      return {};
    }
  }

  /**
   * Test API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.stripe.paymentIntents.list({ limit: 1 });
      logger.info('Stripe API connection test successful');
      return true;
    } catch (error) {
      logger.error('Stripe API connection test failed:', error);
      return false;
    }
  }
}

export default StripeAPIService;
export { StripePaymentData, FetchPaymentsOptions, FetchPaymentsResult };
