import twilio from 'twilio';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { catchAll } from '../../utils/catchAll';
import { ZohoAuth, zohoLeadURL } from '../../helpers/zoho';
import axios from 'axios';
import { db } from '../../utils/db';
// ==================== CONFIGURATION ====================
interface AppConfig {
  SERVER_URL: string;
  ACCOUNT_SID: string;
  AUTH_TOKEN: string;
  TWILIO_NUMBERS: [string, string];
  AGENT_SIP: string;
  CALL_TIMEOUT: number;
  MACHINE_DETECTION_SENSITIVITY: number;
  VOICEMAIL_MESSAGE: string;
}

interface LeadData {
  leadId: string;
  name: string;
  mobile: string | null;
  phone: string | null;
}

const config: AppConfig = {
  CALL_TIMEOUT: 45,
  MACHINE_DETECTION_SENSITIVITY: 5000,
  VOICEMAIL_MESSAGE: "please call us back at your earliest convenience.",
  SERVER_URL: 'https://zenith.ad/api/zoho',
  ACCOUNT_SID: '**********************************',
  AUTH_TOKEN: '5846f60055bbc9436837f5b6d9f866ea',
  TWILIO_NUMBERS: ['+***********', '+***********'],
  AGENT_SIP: 'sip:+<EMAIL>',
};

const client = twilio(config.ACCOUNT_SID, config.AUTH_TOKEN);
const leadsDictionary: Record<string, LeadData> = {};

// ==================== STATE MANAGEMENT ====================
type CallStatus = 'dialing' | 'dialing-agent' | 'answered' | 'voicemail' | 'completed';
type AnsweredBy = 'human' | 'machine' | 'no-answer';
type AgentStatus = 'available' | 'busy' | 'wrapping_up';

const dialerState = {
  customerQueue: [] as string[],
  activeCalls: new Map<string, {
    twilioNumber: string;
    customerNumber: string;
    status: CallStatus;
    answeredBy?: AnsweredBy;
    leadId?: string;
  }>(),
  availableNumbers: [...config.TWILIO_NUMBERS],
  agentStatus: 'available' as AgentStatus,
  currentAgentCallSid: null as string | null,

  canTakeCall: () => {
    return dialerState.agentStatus === 'available' &&
      dialerState.currentAgentCallSid === null;
  },

  getAvailableNumber: () => {
    return dialerState.availableNumbers[0];
  }
};

// Helper function to safely return numbers to pool
function returnNumberToPool(number: string) {
  if (number && !dialerState.availableNumbers.includes(number)) {
    dialerState.availableNumbers.push(number);
  }
}

// ==================== CONTROL ENDPOINTS ====================
export const startAutodialer: RequestHandler = catchAll(async (req, res) => {
  await refreshLeadsDictionary();
  const mobileNumbers = Object.values(leadsDictionary)
    .map(lead => lead.mobile)
    .filter((mobile): mobile is string => mobile !== null);

  const numbers = req.body.numbers || mobileNumbers;

  if (numbers.length === 0) {
    res.status(400).json({ error: 'No numbers provided' });
    return;
  }

  dialerState.customerQueue = [...numbers];
  dialerState.availableNumbers = [...config.TWILIO_NUMBERS];
  dialerState.activeCalls.clear();
  dialerState.agentStatus = 'available';
  dialerState.currentAgentCallSid = null;


  res.json({
    success: true,
    message: `Dialer started with ${numbers.length} numbers`,
    queueLength: dialerState.customerQueue.length
  });

  processQueue();
});

//export const getStatus: RequestHandler = catchAll(async (req, res) => {
export const getStatus: RequestHandler = catchAll(async (_req, res) => {
  const activeCalls = Array.from(dialerState.activeCalls.values()).map(call => ({
    ...call,
    leadName: call.leadId ? leadsDictionary[call.leadId]?.name : 'unknown'
  }));

  res.json({
    queue: dialerState.customerQueue,
    activeCalls,
    availableNumbers: dialerState.availableNumbers,
    agentStatus: dialerState.agentStatus,
    currentAgentCallSid: dialerState.currentAgentCallSid
  });
});

// ==================== LEAD MANAGEMENT ====================

export const createLeads: RequestHandler = catchAll(async (req, res) => {
  const { leadId, mobile, phone, fullName, agent } = req.body;

  console.log('==================== LEAD MANAGEMENT ====================');
  console.log(JSON.stringify(req.body));
  const client = await db.connect();

  try {
    // 1. First check if lead exists and get current values
    const existingLead = await client.query(
      `SELECT lead_id, mobile, phone, full_name, agent 
       FROM leads WHERE lead_id = $1 LIMIT 1`,
      [leadId]
    );

    // Enhanced helper function to format and validate Australian numbers
    const validateAndFormat = (number?: string): string | null => {
      if (!number) return null;
      
      // Remove all non-digit characters
      const cleaned = number.replace(/\D/g, '');
      
      // Check if already in international format
      if (cleaned.startsWith('61') && cleaned.length === 11) {
        return `+${cleaned}`;
      }
      if (cleaned.startsWith('+61') && cleaned.length === 12) {
        return number; // Already properly formatted
      }

      // Validate Australian mobile number patterns
      const isValid = (
        (/^04\d{8}$/.test(cleaned) && cleaned.length === 10) || // 0***********
        (/^4\d{8}$/.test(cleaned) && cleaned.length === 9) || // ***********
        (/^614\d{8}$/.test(cleaned) && cleaned.length === 11) // 61***********
      );

      if (!isValid) {
        throw new Error(`Invalid Australian number format: ${number}`);
      }

      // Convert to international format
      if (cleaned.startsWith('04')) return `+61${cleaned.substring(1)}`; // 0400 -> +61400
      if (cleaned.startsWith('4')) return `+61${cleaned}`; // 400 -> +61400
      if (cleaned.startsWith('614')) return `+${cleaned}`; // 61400 -> +61400
      
      return null;
    };

    const formattedMobile = validateAndFormat(mobile);
    const formattedPhone = validateAndFormat(phone);

    if (!formattedMobile && !formattedPhone) {
      throw new Error('At least one valid contact number required');
    }

    // 2. If lead exists, check for changes
    if (existingLead.rows.length > 0) {
      const current = existingLead.rows[0];
      const changes = {
        mobile: formattedMobile !== current.mobile,
        phone: formattedPhone !== current.phone,
        fullName: fullName !== current.full_name,
        agent: agent !== current.agent
      };

      // 3. Only update if there are changes
      if (Object.values(changes).some(Boolean)) {
        await client.query(
          `UPDATE leads SET
            mobile = COALESCE($2, mobile),
            phone = COALESCE($3, phone),
            full_name = COALESCE($4, full_name),
            agent = COALESCE($5, agent),
            updated_at = NOW()
           WHERE lead_id = $1
           RETURNING *`,
          [leadId, formattedMobile, formattedPhone, fullName, agent]
        );

        await refreshLeadsDictionary();
        client.release();
        res.status(200).json({ message: 'Lead updated successfully' });
        return;
      }

      client.release();
      res.status(200).json({ message: 'Lead exists with identical data' });
      return;
    }

    // 4. Create new lead if doesn't exist
    const result = await client.query(
      `INSERT INTO leads (lead_id, mobile, phone, full_name, agent, status_id)
       VALUES ($1, $2, $3, $4, $5, 
         (SELECT status_id FROM lead_status WHERE status_name = 'new'))
       RETURNING *`,
      [leadId, formattedMobile, formattedPhone, fullName, agent]
    );

    await refreshLeadsDictionary();
    await initialCall();

    client.release();
    res.status(201).json(result.rows[0]);

  } catch (err: any) {
    client.release();

    if (err.code === '23505') {
      res.status(200).json({ message: 'Lead already exists' });
      return;
    }

    if (err.message.includes('Invalid Australian number format') ||
      err.message.includes('valid contact number')) {
      res.status(400).json({ error: err.message });
      return;
    }

    console.error('Lead creation failed:', err);
    res.status(500).json({ error: 'Server error' });
  }
});

// ==================== HELPER FUNCTIONS ====================
export async function initialCall(): Promise<void> {
  try {
    const mobileNumbers = Object.values(leadsDictionary)
      .map(lead => lead.mobile)
      .filter((mobile): mobile is string => mobile !== null);

    if (mobileNumbers.length === 0) {
      console.warn('No valid mobile numbers available');
      return;
    }

    dialerState.customerQueue = [...mobileNumbers];
    dialerState.availableNumbers = [...config.TWILIO_NUMBERS];
    dialerState.activeCalls.clear();
    dialerState.agentStatus = 'available';
    dialerState.currentAgentCallSid = null;

    console.log('Autodialer initialized with:', mobileNumbers.length, 'numbers');
    await processQueue();

  } catch (error) {
    console.error('Failed to initialize autodialer:', error);
    dialerState.customerQueue = [];
    dialerState.availableNumbers = [...config.TWILIO_NUMBERS];
    throw error;
  }
}

export async function refreshLeadsDictionary() {
  const client = await db.connect();

  try {
    const leadsRes = await client.query<{
      lead_id: string;
      full_name: string;
      mobile: string | null;
      phone: string | null;
    }>(
      `SELECT l.lead_id, l.full_name, l.mobile, l.phone
       FROM leads l
       WHERE l.status_id = (
         SELECT status_id 
         FROM lead_status 
         WHERE status_name = 'new'
         LIMIT 1
       )
       AND NOT EXISTS (
         SELECT 1 
         FROM leads l2
         JOIN lead_status ls ON l2.status_id = ls.status_id
         WHERE l2.lead_id = l.lead_id
         AND ls.status_name IN ('success')
       )`
    );

    // Clear existing dictionary
    Object.keys(leadsDictionary).forEach(key => delete leadsDictionary[key]);

    // Populate with new leads
    leadsRes.rows.forEach(lead => {
      leadsDictionary[lead.lead_id] = {
        leadId: lead.lead_id,
        name: lead.full_name,
        mobile: lead.mobile,
        phone: lead.phone
      };
    });

    console.log(`Refreshed leads dictionary with ${leadsRes.rowCount} NEW leads`);

  } catch (error) {
    console.error('Dictionary refresh error:', error);
    throw error;
  } finally {
    client.release();
  }
}

// ==================== CALL HANDLERS ====================
export const callCompletedHandler: RequestHandler = catchAll(async (req, res) => {
  const { callSid } = req.query;
  //const { CallStatus } = req.body;

  if (callSid && typeof callSid === 'string') {
    const call = dialerState.activeCalls.get(callSid);
    if (call) {
      // Return number to pool
      if (!dialerState.availableNumbers.includes(call.twilioNumber)) {
        dialerState.availableNumbers.push(call.twilioNumber);
      }
      dialerState.activeCalls.delete(callSid);

      // Reset agent status if this was their current call
      if (dialerState.currentAgentCallSid === callSid) {
        dialerState.agentStatus = 'available';
        dialerState.currentAgentCallSid = null;
      }
    }
  }

  // Process next call immediately
  processQueue();

  const twiml = new twilio.twiml.VoiceResponse();
  res.type('text/xml').send(twiml.toString());
});


export const agentCallHandler: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();

  // Check if agent is already on a call or has an incoming call
  const hasIncomingCall = Array.from(dialerState.activeCalls.values()).some(call =>
    call.status === 'dialing-agent' &&
    call.twilioNumber !== req.query.twilioNumber
  );

  if (!dialerState.canTakeCall() || hasIncomingCall) {
    console.log('[AGENT] Rejected new call - agent already has an incoming call');
    twiml.say('Agent is currently receiving another call');
    twiml.hangup();
    res.type('text/xml').send(twiml.toString());
    return
  }

  // Safely extract parameters
  const customerNumber = typeof req.query.customerNumber === 'string' ? req.query.customerNumber : undefined;
  const twilioNumber = typeof req.query.twilioNumber === 'string' ? req.query.twilioNumber : undefined;
  const leadId = typeof req.query.leadId === 'string' ? req.query.leadId : undefined;

  try {
    // Validate required parameters
    if (!customerNumber || !twilioNumber) {
      throw new Error('Missing customerNumber or twilioNumber');
    }

    // Mark agent as busy
    dialerState.agentStatus = 'busy';
    dialerState.currentAgentCallSid = req.body.CallSid;

    // Get lead info
    const leadInfo = leadId ? leadsDictionary[leadId] : null;
    const callerName = leadInfo?.name || 'Customer';

    // Notify agent
    twiml.say(`Connecting you to ${callerName}, please wait.`);

    // Create dial with basic attributes
    const dial = twiml.dial({
      callerId: twilioNumber,
      action: `${config.SERVER_URL}/handle-customer-dial-status?callSid=${req.body.CallSid}&customerName=${encodeURIComponent(callerName)}&leadId=${leadId || ''}`,
      timeout: config.CALL_TIMEOUT,
      record: 'record-from-answer'
    });

    // Add number with machine detection parameters
    dial.number({
      machineDetection: 'Enable',
      machineDetectionTimeout: 30,
      machineDetectionSpeechThreshold: 2400,
      machineDetectionSpeechEndThreshold: 1200,
      amdStatusCallback: `${config.SERVER_URL}/amd-status`
    }, customerNumber);

  } catch (error) {
    console.error('[AGENT CALL HANDLER] Error:', error);
    twiml.say('An error occurred connecting the call');
    twiml.hangup();

    // Reset agent status on error
    dialerState.agentStatus = 'available';
    dialerState.currentAgentCallSid = null;
  }

  res.type('text/xml').send(twiml.toString());
});

export const handleCustomerDialStatus: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();
  const { callSid, customerName, leadId } = req.query;
  const { CallStatus, AnsweredBy, RecordingUrl, RecordingDuration } = req.body;

  console.log('==================== CUSTOMER STATUS HANDLER ====================');
  //console.log('STATUS DATA:', JSON.stringify(req.body));

  const decodedCustomerName = typeof customerName === 'string' ?
    decodeURIComponent(customerName) : 'The customer';

  // Check for voicemail (either via AMD or recording)
  const isVoicemail = (
    (AnsweredBy === 'machine_start' ||
      AnsweredBy === 'machine' ||
      AnsweredBy === 'machine_end_beep' ||
      AnsweredBy === 'machine_end_silence' ||
      AnsweredBy === 'machine_end_other') ||
    (RecordingUrl && parseInt(RecordingDuration) > 0)
  );

  if (isVoicemail) {
    // Get personalized message
    console.log('==================== Playing voicemail message on line ====================');
    let customMessage = config.VOICEMAIL_MESSAGE;
    if (leadId && typeof leadId === 'string') {
      const leadInfo = leadsDictionary[leadId];
      if (leadInfo?.name) {
        customMessage = `Hello ${leadInfo.name}, ${config.VOICEMAIL_MESSAGE}`;
      }
    }

    // Wait for beep (5 seconds is typical)
    twiml.pause({ length: 5 });

    // Play the voicemail message
    twiml.say({
      voice: 'woman',
      language: 'en-US'
    }, customMessage);

    // Add brief silence before hangup
    twiml.pause({ length: 1 });
  } else {
    // Handle other call statuses
    switch (CallStatus) {
      case 'answered':
        twiml.say(`The call with ${decodedCustomerName} was disconnected.`);
        break;
      case 'no-answer':
        twiml.say(`${decodedCustomerName} is not answering their phone.`);
        break;
      case 'failed':
        twiml.say(`${decodedCustomerName}'s phone appears to be disconnected.`);
        break;
      case 'busy':
        twiml.say(`${decodedCustomerName}'s line is busy.`);
        break;
      default:
        twiml.say(`The call to ${decodedCustomerName} could not be completed.`);
    }

    twiml.pause({ length: 2 });
  }

  twiml.hangup();

  // Clean up resources
  if (callSid && typeof callSid === 'string') {
    const call = dialerState.activeCalls.get(callSid);
    if (call) {
      if (!dialerState.availableNumbers.includes(call.twilioNumber)) {
        dialerState.availableNumbers.push(call.twilioNumber);
      }
      dialerState.activeCalls.delete(callSid);
    }
  }

  // Mark agent as available and process next call
  dialerState.agentStatus = 'available';
  dialerState.currentAgentCallSid = null;
  processQueue();

  res.type('text/xml').send(twiml.toString());
});

export const amdStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();

  // Safely extract parameters with type checking
  const callSid = typeof req.query.CallSid === 'string' ? req.query.CallSid : undefined;

  //const customerName = typeof req.query.customerName === 'string' ? req.query.customerName : undefined;

  const leadId = typeof req.query.leadId === 'string' ? req.query.leadId : undefined;

  //const { CallStatus, AnsweredBy, RecordingUrl, RecordingDuration } = req.body;
  const { AnsweredBy, RecordingUrl, RecordingDuration } = req.body;

  console.log('==================== AMD STATUS HANDLER ====================');
  console.log('STATUS DATA:', JSON.stringify(req.body));

  //const decodedCustomerName = customerName ? decodeURIComponent(customerName) : 'The customer';


  // Check for voicemail
  const isVoicemail = (
    (AnsweredBy === 'machine_start' ||
      AnsweredBy === 'machine' ||
      AnsweredBy === 'machine_end_beep' ||
      AnsweredBy === 'machine_end_silence' ||
      AnsweredBy === 'machine_end_other') ||
    (RecordingUrl && parseInt(RecordingDuration) > 0)
  );

  if (isVoicemail) {
    console.log('==================== Playing voicemail message ====================');

    // Get personalized message
    let customMessage = config.VOICEMAIL_MESSAGE;
    if (leadId && leadsDictionary[leadId]?.name) {
      customMessage = `Hello ${leadsDictionary[leadId].name}, ${config.VOICEMAIL_MESSAGE}`;
    }

    // Wait for beep and leave message
    twiml.pause({ length: 5 });
    twiml.say({
      voice: 'woman',
      language: 'en-US'
    }, customMessage);
    twiml.pause({ length: 1 });
    twiml.hangup();

    // Update call state if we have a valid CallSid
    if (callSid) {
      const call = dialerState.activeCalls.get(callSid);
      if (call) {
        call.status = 'voicemail';
        call.answeredBy = 'machine';
        returnNumberToPool(call.twilioNumber);
      }
    }
  }

  res.type('text/xml').send(twiml.toString());
});

export const recordingStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const { CallSid, RecordingUrl, RecordingDuration } = req.body;

  console.log(`[RECORDING] Status for ${CallSid}:`, RecordingUrl ? 'Recording available' : 'No recording');

  // Update call state if recording exists (definitive voicemail)
  if (RecordingUrl && parseInt(RecordingDuration) > 0) {
    const call = dialerState.activeCalls.get(CallSid);
    if (call) {
      call.answeredBy = 'machine';
      call.status = 'voicemail';
    }
  }

  res.sendStatus(200);
});


const processQueue = async () => {

  console.log('[DIALER] ####### PROCESS QUEUE ########');
  if (!dialerState.canTakeCall()) {
    console.log('[DIALER] Agent is busy, pausing queue processing');
    return;
  }

  while (dialerState.availableNumbers.length > 0 &&
    dialerState.customerQueue.length > 0 &&
    dialerState.canTakeCall()) {

    const customerNumber = dialerState.customerQueue.shift();
    const twilioNumber = dialerState.availableNumbers.shift();

    if (!customerNumber || !twilioNumber) {
      console.error('[QUEUE] Missing number - customer:', customerNumber, 'twilio:', twilioNumber);
      continue;
    }

    const leadEntry = Object.values(leadsDictionary).find(
      lead => lead.mobile === customerNumber || lead.phone === customerNumber
    );

    try {
      // Call customer first
      const customerCall = await client.calls.create({
        url: `${config.SERVER_URL}/customer-call-handler?twilioNumber=${encodeURIComponent(twilioNumber)}&leadId=${leadEntry?.leadId || ''}&name=${encodeURIComponent(leadEntry?.name || '')}`,
        to: customerNumber,
        from: twilioNumber,
        timeout: config.CALL_TIMEOUT,
        machineDetection: 'DetectMessageEnd',
        machineDetectionTimeout: config.MACHINE_DETECTION_SENSITIVITY,
        statusCallback: `${config.SERVER_URL}/call-status?leadId=${encodeURIComponent(leadEntry?.leadId ||'')}`,
        statusCallbackEvent: ['initiated', 'ringing', 'answered', 'busy', 'completed']
      });

      dialerState.activeCalls.set(customerCall.sid, {
        twilioNumber,
        customerNumber,
        status: 'dialing',
        leadId: leadEntry?.leadId
      });

      console.log(`[DIALER] Calling customer :  ${customerNumber} first From Twilio Number : ${twilioNumber}`);

    } catch (error) {
      console.error('[DIALER] Customer call initiation failed:', error);
      dialerState.availableNumbers.push(twilioNumber);
    }
  }
};

export const customerCallHandler: RequestHandler = catchAll(async (req, res) => {
  const twiml = new twilio.twiml.VoiceResponse();
  const { twilioNumber, leadId, name } = req.query;
  const { AnsweredBy, CallSid, CallStatus, To } = req.body;




  console.log('########## CUSTOMER CALL HANDLER ##########');
  console.log('Request body:', JSON.stringify(req.body));
  console.log('Request query:', JSON.stringify(req.query));

  console.log('****Call status:*****', CallStatus);
  console.log('****Call AnsweredBy:*****', AnsweredBy);



  try {
    if (!twilioNumber || typeof twilioNumber !== 'string' || !leadId || typeof leadId !== 'string') {
      throw new Error('Missing twilioNumber or leadId parameter');
    }

    // Mark agent as busy
    dialerState.agentStatus = 'busy';
    dialerState.currentAgentCallSid = CallSid;

     

    if (!AnsweredBy) {
      res.type('text/xml').send(twiml.toString());
      return
    }
    // Second request - definitive handling


    if (AnsweredBy === 'machine_start' ||
      AnsweredBy === 'machine' ||
      AnsweredBy === 'machine_end_beep' ||
      AnsweredBy === 'machine_end_silence' ||
      AnsweredBy === 'machine_end_other') {

      console.log('[CUSTOMER CALL] Leaving voicemail to the agent');
      // Handle machines AND unknown (conservative)
      twiml.pause({ length: 3 }); // Wait for voicemail beep
      twiml.say(config.VOICEMAIL_MESSAGE);
      twiml.hangup();

      updateZohoLeadStatus(leadId, "voicemail");
      updateDBLeadStatus(leadId,'voicemail');
    }
    

    else {

      if (AnsweredBy === "unknown" || AnsweredBy === 'human') {
        console.log('########## CONNECTING TO THE AGENT ##########');
      
        const suffix = '@'+To;  
        const rawName = name; // Could be string, array, or ParsedQs
        const callerName = typeof rawName === 'string' 
          ? rawName.replace(/\s+/g, '-') // "Yannick Ngoie" → "Yannick-Ngoie"
          : 'Default-Caller';         
      
        console.log(`********************** Customer name ${callerName.replace(/\s+/g, "-") + suffix }`)

        const dial = twiml.dial({
          callerId: callerName.replace(/\s+/g, "-")// This will show in Zoiper
        });

        dial.sip(config.AGENT_SIP);
        // Only connect confirmed humans
        //twiml.dial().sip(config.AGENT_SIP);
        updateZohoLeadStatus(leadId,"success");

        updateDBLeadStatus(leadId,'success');
      }


    }

    //console.log('[CUSTOMER CALL] Connecting customer to agent');

    res.type('text/xml').send(twiml.toString());
    return;

  } catch (error) {
    console.error('[CUSTOMER CALL HANDLER] Error:', error);

    // Create new TwiML for error case
    const errorTwiml = new twilio.twiml.VoiceResponse();
    errorTwiml.say('An error occurred connecting your call');
    errorTwiml.hangup();

    // Reset agent status
    dialerState.agentStatus = 'available';
    dialerState.currentAgentCallSid = null;

    res.type('text/xml').send(errorTwiml.toString());
    return
  }
});
export const callStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const { CallSid, CallStatus, To, From, Direction, AnsweredBy } = req.body;
  const { leadId } = req.query;

  console.log(`##################CALL STATUS HANDLER #################################`);
  console.log(`[CALL STATUS] Update for ${CallSid}: ${CallStatus}  To ${To}`);
  console.log('CALL STATUS Request body ' + JSON.stringify(req.body));
  console.log('CALL STATUS Lead  ID :' + leadId);

  const call = dialerState.activeCalls.get(CallSid);
  if (!call) {
    console.warn(`Call ${CallSid} not found in active calls`);
    res.sendStatus(200);
    return
  }

  call.status = CallStatus.toLowerCase() as CallStatus;

  switch (CallStatus.toLowerCase()) {
    case 'in-progress':
      if (Direction === 'outbound-api' && To !== config.AGENT_SIP) {
        console.log(`Customer ${From} answered the call`);
        call.answeredBy = AnsweredBy === 'human' ? 'human' : 'machine';
        
      }
      break;

    case 'no-answer':
    case 'failed':
    case 'busy':
      console.log(`Call to ${From} failed with status: ${CallStatus}`);
      returnNumberToPool(call.twilioNumber);
      dialerState.activeCalls.delete(CallSid);
      console.log(`Updating Zoho Call to ${From} failed with status: ${CallStatus}`);
      updateZohoLeadStatus(leadId, CallStatus);
      updateDBLeadStatus(leadId,'no-answer')
      break;

    case 'completed':
      console.log(`Call ${CallSid} completed`);
      returnNumberToPool(call.twilioNumber);
      dialerState.activeCalls.delete(CallSid);

      if (To === config.AGENT_SIP || dialerState.currentAgentCallSid === CallSid) {
        dialerState.agentStatus = 'available';
        dialerState.currentAgentCallSid = null;
      }
      break;
  }

  if (dialerState.canTakeCall()) {
    processQueue();
  }

  res.sendStatus(200);
});


export const updateZohoLeadStatusHandler: RequestHandler = catchAll(async (req, res) => {
  const { leadId, lead_status } = req.body;

  console.log(` updateZohoLeadStatusHandler [ZOHO] Updating lead ${leadId} status to:`, lead_status);

  // Validate required fields
  if (!leadId || !lead_status) {
    console.warn('[ZOHO] Missing required fields for lead update');
    res.status(400).json({
      success: false,
      message: 'Missing required fields: leadId and lead_status'
    });
    return;
  }

  try {

    updateZohoLeadStatus(leadId, lead_status)
    console.log(`[ZOHO] Successfully updated lead ${leadId} status`);
    res.json({
      success: true,
      message: 'Lead status updated successfully'
    });
  } catch (error) {
    console.error('[ZOHO] Error updating lead status:', error);
    throw error; // Let the catchAll handler deal with it
  }
});

const updateZohoLeadStatus = async (leadId, lead_status) => {

  console.log(`updating Zoho Lead Field **Call_Outcome_or_Conversion** to : ${lead_status} with Lead_ID ${leadId}`);

  const headers = await ZohoAuth.getHeaders();

  switch (lead_status) {

    case 'no-answer':
      lead_status = "No Answer - Auto Dialler"
      break;
    case 'failed':
      lead_status = "Failed Call - Auto Dialler"
      break;
    case 'success':
      lead_status = "Successful Call - Auto Dialler"
      break;
    case 'voicemail':
      lead_status = "No Answer VM Left - Auto Dialler"
      break;
    default:

  }

  const dataZoho = {
    data: [
      {
        Call_Outcome_or_Conversion: lead_status
      },
    ],
  };

  await axios.put(`${zohoLeadURL}/${leadId}`, dataZoho, { headers });
}




export async function updateDBLeadStatus(leadId, newStatus) {
  // Validate input status
  // Valid status values - adjust according to your business requirements
const VALID_STATUSES = ['new', 'no-answer', 'voicemail', 'success'];

  if (!VALID_STATUSES.includes(newStatus)) {
    return {
      success: false,
      error: `Invalid status. Valid values are: ${VALID_STATUSES.join(', ')}`
    };
  }

  const client = await db.connect();
  
  try {
    await client.query('BEGIN');

    // 1. Verify lead exists and get current status (with row lock)
    const leadCheck = await client.query(
      `SELECT lead_id, status 
       FROM leads 
       WHERE lead_id = $1 
       FOR UPDATE LIMIT 1`,
      [leadId]
    );

    if (leadCheck.rowCount === 0) {
      await client.query('ROLLBACK');
      return { 
        success: false, 
        error: `Lead ${leadId} not found` 
      };
    }

    const currentStatus = leadCheck.rows[0].status;

    // 2. Skip update if status isn't changing
    if (currentStatus === newStatus) {
      await client.query('ROLLBACK');
      return {
        success: false,
        error: `Lead already has status ${newStatus}`,
        lead: leadCheck.rows[0]
      };
    }

    // 3. Update the lead
    const updateResult = await client.query(
      `UPDATE leads 
       SET status = $1,
           status_id = (SELECT status_id FROM lead_status WHERE status_name = $1 LIMIT 1),
           updated_at = NOW()
       WHERE lead_id = $2
       RETURNING lead_id, full_name, mobile, phone, status, status_id, updated_at`,
      [newStatus, leadId]
    );

   
    await client.query('COMMIT');

    return {
      success: true,
      lead: updateResult.rows[0]
    };

  } catch (error) {
    await client.query('ROLLBACK');
    console.error(`Database error updating lead ${leadId}:`, error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Database operation failed'
    };
  } finally {
    client.release();
  }
}