-- Migration: Add index on consultation.createdAt for cache invalidation
-- This index will optimize queries that check for new consultation records
-- based on timestamp for the cache invalidation mechanism

CREATE INDEX IF NOT EXISTS idx_consultation_createdat ON consultation ("createdAt");

-- Verify the index was created
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'consultation' 
AND indexname = 'idx_consultation_createdat';
