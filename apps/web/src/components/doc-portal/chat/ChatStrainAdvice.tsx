import React, { useState, useEffect, useCallback } from 'react';
import {
    Box,
    Typography,
    Paper,
    TextField,
    Button,
    Checkbox,
    Card,
    CardContent,
    CardActionArea,
    Chip,
    Tooltip
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

import { useChatEmail } from '../../../hooks/chat-email-provider';
import { useEmailContext } from '../../../hooks/email-provider';
import { usePatient } from '../../../hooks/patient-provider';
import { ApiClient } from '../../../services';
import chatService from '../../../services/chat.service';
import { config } from '../../../config';
import Snackbar from '@mui/material/Snackbar';
import Alert from '@mui/material/Alert';

interface StrainAdviceMap {
    [key: string]: string;
}

const ChatStrainAdvice: React.FC<{ onComplete?: () => void }> = ({ onComplete }) => {
    const { setStrainAdvice, treatmentPlan } = useChatEmail();
    const { listValuesSativa, listValuesIndica, listValuesHybrid } = useEmailContext();
    const { selectedPatient } = usePatient();
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');


    const strainTypes: Record<string, StrainAdviceMap> = {
        sativa: {
            'Sativa Strain 1': listValuesSativa.option1,
            'Sativa Strain 2': listValuesSativa.option2,
            'Sativa Strain 3': listValuesSativa.option3,
            'Sativa Strain 4': listValuesSativa.option4,
            'Sativa Strain 5': listValuesSativa.option5,
        },
        indica: {
            'Indica Strain 1': listValuesIndica.option1,
            'Indica Strain 2': listValuesIndica.option2,
            'Indica Strain 3': listValuesIndica.option3,
            'Indica Strain 4': listValuesIndica.option4,
            'Indica Strain 5': listValuesIndica.option5,
        },
        hybrid: {
            'Hybrid Strain 1': listValuesHybrid.option1,
            'Hybrid Strain 2': listValuesHybrid.option2,
            'Hybrid Strain 3': listValuesHybrid.option3,
            'Hybrid Strain 4': listValuesHybrid.option4,
            'Hybrid Strain 5': listValuesHybrid.option5,
        }
    };

    // Create lookup maps outside of useEffect (they're constant)
    const sativaLookup = Object.entries(strainTypes.sativa).reduce((acc, [name, value]) => {
        acc[value] = name;
        return acc;
    }, {} as Record<string, string>);

    const indicaLookup = Object.entries(strainTypes.indica).reduce((acc, [name, value]) => {
        acc[value] = name;
        return acc;
    }, {} as Record<string, string>);

    const hybridLookup = Object.entries(strainTypes.hybrid).reduce((acc, [name, value]) => {
        acc[value] = name;
        return acc;
    }, {} as Record<string, string>);

    const [selectedStrains, setSelectedStrains] = useState({
        sativa: [] as string[],
        indica: [] as string[],
        hybrid: [] as string[]
    });
    const [customAdvice, setCustomAdvice] = useState('');
    const [submitting, setSubmitting] = useState(false);

    // Add ref to track initialization
    const initialized = React.useRef(false);

    // Initialize selected strains from existing treatment plan if available
    useEffect(() => {
        // Only set initial selections once
        if (initialized.current) return;
        
        if (treatmentPlan?.treatmentPlan?.strainAdvice) {
            const existingAdvice = treatmentPlan.treatmentPlan.strainAdvice;
            const selectedSativa: string[] = [];
            const selectedIndica: string[] = [];
            const selectedHybrid: string[] = [];

            // Map Zoho strain advice to our strain types
            Object.values(existingAdvice).forEach((value) => {
                if (typeof value === 'string') {
                    // Check if this value exists in our lookup maps
                    if (sativaLookup[value]) {
                        selectedSativa.push(sativaLookup[value]);
                    } else if (indicaLookup[value]) {
                        selectedIndica.push(indicaLookup[value]);
                    } else if (hybridLookup[value]) {
                        selectedHybrid.push(hybridLookup[value]);
                    }
                }
            });

            // Update selected strains
            setSelectedStrains({
                sativa: selectedSativa,
                indica: selectedIndica,
                hybrid: selectedHybrid
            });

            // Mark as initialized
            initialized.current = true;
        }
    }, [treatmentPlan]); // Remove strainTypes from dependencies

    // Update handleStrainSelection to ensure it can toggle properly
    const handleStrainSelection = (type: 'sativa' | 'indica' | 'hybrid', strain: string, event?: React.MouseEvent | React.ChangeEvent) => {
        // Stop propagation if event is provided
        if (event) {
            event.stopPropagation();
        }
        
        // Create a completely new object to ensure React detects the change
        const newStrains = {
            sativa: [...selectedStrains.sativa],
            indica: [...selectedStrains.indica],
            hybrid: [...selectedStrains.hybrid]
        };
        
        const index = newStrains[type].indexOf(strain);
        
        if (index !== -1) {
            // Remove the strain if it's already selected
            newStrains[type].splice(index, 1);
        } else {
            // Add the strain if it's not selected
            newStrains[type].push(strain);
        }
        
        setSelectedStrains(newStrains);
    };

    // Modify handleSilentSave to prevent unnecessary updates
    const handleSilentSave = useCallback(() => {
        handleSave(false);
    }, []);

    // Update useEffect for strain changes to use callback
    useEffect(() => {
        if (initialized.current) {
            handleSilentSave();
        }
    }, [selectedStrains, handleSilentSave]);

    const handleSave = (showSnackbar = true) => {
        // Format the selected strains into text
        const selectedStrainsText = Object.entries(selectedStrains)
            .filter(([_, strains]) => strains.length > 0)
            .map(([type, strains]) => {
                // Make sure we include the full strain name (e.g., "Sativa Strain 1") in the heading
                // This is important for the parser to correctly identify sections
                const typeLabel = type.charAt(0).toUpperCase() + type.slice(1);
                
                // Format each strain with bullet points for parsing
                const adviceTexts = strains.map(strain => {
                    // Get the advice text for this strain
                    const adviceText = strainTypes[type][strain];
                    // Return with dash prefix for proper parsing
                    return `- ${adviceText}`;
                });
                
                // Format the section with proper heading for parsing
                return `${typeLabel} Strains:\n${adviceTexts.join('\n')}`;
            })
            .join('\n\n');

        // Include custom advice if provided
        const finalAdvice = [
            selectedStrainsText,
            customAdvice ? `\nAdditional Advice:\n${customAdvice}` : ''
        ].filter(Boolean).join('\n');

        // Save to context
        setStrainAdvice(finalAdvice);

        // Show snackbar if requested
        if (showSnackbar) {
            setSnackbarMessage('Strain advice saved successfully');
            setSnackbarSeverity('success');
            setSnackbarOpen(true);
        }


    };

    // Add a function to quickly select common strains
    const handleQuickSelect = (type: 'sativa' | 'indica' | 'hybrid') => {
        // Select the first strain of the specified type if available
        const strain = Object.keys(strainTypes[type])[0];
        if (strain) {
            // Create a completely new object to ensure React detects the change
            const newStrains = {
                sativa: [...selectedStrains.sativa],
                indica: [...selectedStrains.indica],
                hybrid: [...selectedStrains.hybrid]
            };
            
            // Always add the first strain, whether it was selected before or not
            if (!newStrains[type].includes(strain)) {
                newStrains[type].push(strain);
            }
            
            setSelectedStrains(newStrains);
            
            // Force silent save after selection
            setTimeout(() => {
                handleSilentSave();
            }, 100);
        }
    };

    // Split strains into categories for the mobile view
    const sativaStrains = Object.keys(strainTypes.sativa);
    const indicaStrains = Object.keys(strainTypes.indica);
    const hybridStrains = Object.keys(strainTypes.hybrid);

    const handleFinish = async () => {
        setSubmitting(true);
        try {
            // First save the current strain advice
            handleSave(false); // Don't show snackbar for this save
            
            // Get the saved treatment plan from localStorage
            const savedPlan = localStorage.getItem('TpPlan');
            if (!savedPlan) throw new Error('No treatment plan found');
            
            // Parse the treatment plan
            const parsedPlan = JSON.parse(savedPlan);
            
            // Ensure string conversions for treatments
            if (parsedPlan["22"]) {
                parsedPlan["22"].numberOfRepeat = String(parsedPlan["22"].numberOfRepeat);
                parsedPlan["22"].supplyInterval = String(parsedPlan["22"].supplyInterval);
            }
            if (parsedPlan["29"]) {
                parsedPlan["29"].numberOfRepeat = String(parsedPlan["29"].numberOfRepeat);
                parsedPlan["29"].supplyInterval = String(parsedPlan["29"].supplyInterval);
            }
            
            const treatmentPlan = parsedPlan;
            
            // CRITICAL FIX: Instead of parsing the text advice, use the actual selected strain values
            // This ensures we're sending the correct strain advice values to Zoho
            const checkedSativa = selectedStrains.sativa.map(strain => strainTypes.sativa[strain]);
            const checkedIndica = selectedStrains.indica.map(strain => strainTypes.indica[strain]);
            const checkedHybrid = selectedStrains.hybrid.map(strain => strainTypes.hybrid[strain]);
            
            // Zoho ID handling
            const zohoID = treatmentPlan.zohoID;
            const drIdLocal = localStorage.getItem('xdr');
            const doctorID = treatmentPlan.drId || drIdLocal || '';
            let finalZohoID = zohoID || treatmentPlan.patient?.zohoID;
            
            // Fallback ID handling
            if (!finalZohoID && treatmentPlan.patient?.patientID) {
                if (treatmentPlan.patient.patientID.startsWith('p')) {
                    finalZohoID = treatmentPlan.patient.patientID.substring(1);
                } else {
                    finalZohoID = treatmentPlan.patient.patientID;
                }
            }
            
            // Validate Zoho ID
            if (!finalZohoID) throw new Error('No zohoID found in treatment plan. The prescription lead should have been created during form submission.');
            
            // Prepare the treatment plan object for submission
            const planWithDoctor = {
                patient: {
                    patientID: treatmentPlan.patient?.patientID,
                    email: treatmentPlan.patient?.email || '',
                    fullName: treatmentPlan.patient?.fullName || `Patient ${treatmentPlan.patient?.patientID}`,
                    returningPatient: true,
                    consultation: treatmentPlan.patient?.consultation?.id ? { id: treatmentPlan.patient.consultation.id } : undefined,
                    zohoID: treatmentPlan.patient?.zohoID || finalZohoID
                },
                outcome: treatmentPlan.outcome || 'Approve Unrestricted',
                drNotes: treatmentPlan.drNotes !== undefined ? treatmentPlan.drNotes : 'Treatment plan approved via chat',
                mentalHealthSupportingDocumentation: treatmentPlan.mentalHealthSupportingDocumentation || 'No',
                date: treatmentPlan.date || new Date().toISOString(),
                drId: doctorID,
                drName: treatmentPlan.drName || '',
                drAphraNumber: treatmentPlan.drAphraNumber,
                zohoID: finalZohoID,
                ...(treatmentPlan["22"] ? {
                    "22": {
                        dosePerDay: String(treatmentPlan["22"].dosePerDay || '1.0'),
                        maxDosePerDay: String(treatmentPlan["22"].maxDosePerDay || '2.0'),
                        totalQuantity: String(treatmentPlan["22"].totalQuantity || '28'),
                        numberOfRepeat: String(treatmentPlan["22"].numberOfRepeat || '3'),
                        supplyInterval: String(treatmentPlan["22"].supplyInterval || '28')
                    }
                } : {}),
                ...(treatmentPlan["29"] ? {
                    "29": {
                        dosePerDay: String(treatmentPlan["29"].dosePerDay || '0.1'),
                        maxDosePerDay: String(treatmentPlan["29"].maxDosePerDay || '1.0'),
                        totalQuantity: String(treatmentPlan["29"].totalQuantity || '28'),
                        numberOfRepeat: String(treatmentPlan["29"].numberOfRepeat || '3'),
                        supplyInterval: String(treatmentPlan["29"].supplyInterval || '28')
                    }
                } : {}),
                email: {
                    introMessage: {
                        intro: 'As discussed, your Treatment Plan has now been approved. Below, you\'ll find all the necessary details, including the steps to obtain your treatment',
                        conclusion: 'You now have access to the Harvest Members Only shop, where you can purchase the therapeutic products outlined in your Treatment Plan. Please note that only the products included in your plan will be available for purchase.'
                    },
                    listTitle: {
                        title1: 'Possible Side Effects',
                        title2: 'Contraindications',
                        title3: 'Important Information',
                    },
                    listItemText: {
                        item1: 'Drowsiness, dizziness, dry mouth, euphoria, anxiety, or paranoia',
                        item2: 'Not recommended for pregnant or breastfeeding women, individuals with psychotic disorders, heart conditions, or those operating heavy machinery',
                        item3: 'Start with a low dose and gradually increase as needed. Avoid alcohol and consult with your healthcare provider about potential drug interactions',
                    },
                    // Pass the direct strain advice values
                    checkedSativa,
                    checkedIndica,
                    checkedHybrid,
                    otherTreatment: {
                        otreat1: 'Lifestyle changes (exercise, diet, sleep hygiene, stress management)',
                        otreat2: 'Physical therapy',
                        otreat3: 'Cognitive Behavioral Therapy (CBT)',
                        otreat4: 'Over-the-counter medications (paracetamol, ibuprofen)',
                        otreat5: 'Prescription medications (antidepressants, anti-anxiety meds, pain relievers)',
                        otreat6: 'Acupuncture',
                        otreat7: 'Herbal supplements (turmeric, valerian root)',
                        otreat8: 'Specialist care (pain management, rheumatologist)'
                    }
                }
            };
            
            // Submit the plan to the API
            await ApiClient.postChatTreatmentPlan(planWithDoctor);

            // ONLY AFTER successful treatment plan submission, send the chat termination signal
            try {
                // Get patient ID from selected patient or treatment plan
                const patientId = selectedPatient?.patientID || treatmentPlan?.patient?.patientID;

                if (!patientId) {
                    console.warn("No patient ID available for chat termination signal");
                    return;
                }

                // First check if the patient has a chat channel
                const patientChannels = await chatService.getPatientChannels(patientId);

                if (patientChannels && patientChannels.length > 0) {
                    const channelId = patientChannels[0];

                    // Create custom event data instead of a visible message
                    const eventData = {
                        type: "treatment.plan.submitted",
                        patientId: patientId,
                        timestamp: new Date().toISOString()
                    };

                    // Send a hidden message to signal treatment plan submission
                    try {
                        // Send a hidden message via the event endpoint (which now sends a hidden message)
                        await fetch(`${config.apiUrl}/chat/v1.0/channel/${channelId}/event`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            credentials: 'include',
                            body: JSON.stringify({
                                event: {
                                    type: eventData.type,
                                    patient_id: eventData.patientId,
                                    timestamp: eventData.timestamp
                                }
                            })
                        });
                        console.log("Treatment plan termination signal sent after strain advice");
                    } catch (error) {
                        console.error("Error sending treatment plan termination signal:", error);
                    }
                } else {
                    console.log("No chat channels found for patient");
                }
            } catch (messageError) {
                console.error("Error in chat termination process:", messageError);
                // Continue with submission even if this fails
            }

            // Set a flag to bypass email confirmation
            localStorage.removeItem('directSubmission'); // Clear any existing flag first
            localStorage.setItem('bypassed_email', 'true');

            // Complete workflow after success - this should completely reset to form view
            setTimeout(() => {
                if (onComplete) onComplete();
            }, 1000);
        } catch (err) {
            console.error('Failed to submit treatment plan:', err);
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Box sx={{ width: '100%' }}>
            <Typography variant="h6" sx={{
                color: '#2d3748',
                fontWeight: 'bold',
                mb: 2,
                fontSize: { xs: '20px', md: '24px' }
            }}>
                Strain Advice
            </Typography>

            {/* Quick Select Options */}
            <Box sx={{ mb: 3, p: 2, bgcolor: '#f8f9fa', borderRadius: '12px', boxShadow: 1 }}>
                <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold', color: '#2d3748' }}>
                    Quick Select Common Strains
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                    <Card sx={{ width: { xs: '100%', sm: 'auto' }, flexGrow: { sm: 1 }, minWidth: { sm: '150px' } }}>
                        <CardActionArea onClick={() => handleQuickSelect('sativa')}>
                            <Box sx={{
                                p: 2,
                                display: 'flex',
                                alignItems: 'center',
                                borderLeft: '4px solid #4caf50' // Green for Sativa
                            }}>
                                <Chip
                                    label="Sativa"
                                    size="small"
                                    sx={{
                                        bgcolor: '#e8f5e9',
                                        color: '#2e7d32',
                                        mr: 1
                                    }}
                                />
                                <Typography variant="body2">
                                    {selectedStrains.sativa.length > 0 ?
                                        `Selected (${selectedStrains.sativa.length})` :
                                        'Select First Strain'}
                                </Typography>
                            </Box>
                        </CardActionArea>
                    </Card>

                    <Card sx={{ width: { xs: '100%', sm: 'auto' }, flexGrow: { sm: 1 }, minWidth: { sm: '150px' } }}>
                        <CardActionArea onClick={() => handleQuickSelect('indica')}>
                            <Box sx={{
                                p: 2,
                                display: 'flex',
                                alignItems: 'center',
                                borderLeft: '4px solid #7e57c2' // Purple for Indica
                            }}>
                                <Chip
                                    label="Indica"
                                    size="small"
                                    sx={{
                                        bgcolor: '#ede7f6',
                                        color: '#5e35b1',
                                        mr: 1
                                    }}
                                />
                                <Typography variant="body2">
                                    {selectedStrains.indica.length > 0 ?
                                        `Selected (${selectedStrains.indica.length})` :
                                        'Select First Strain'}
                                </Typography>
                            </Box>
                        </CardActionArea>
                    </Card>

                    <Card sx={{ width: { xs: '100%', sm: 'auto' }, flexGrow: { sm: 1 }, minWidth: { sm: '150px' } }}>
                        <CardActionArea onClick={() => handleQuickSelect('hybrid')}>
                            <Box sx={{
                                p: 2,
                                display: 'flex',
                                alignItems: 'center',
                                borderLeft: '4px solid #ff9800' // Orange for Hybrid
                            }}>
                                <Chip
                                    label="Hybrid"
                                    size="small"
                                    sx={{
                                        bgcolor: '#fff3e0',
                                        color: '#ef6c00',
                                        mr: 1
                                    }}
                                />
                                <Typography variant="body2">
                                    {selectedStrains.hybrid.length > 0 ?
                                        `Selected (${selectedStrains.hybrid.length})` :
                                        'Select First Strain'}
                                </Typography>
                            </Box>
                        </CardActionArea>
                    </Card>
                </Box>
                <Typography variant="caption" sx={{ display: 'block', mt: 2, color: 'text.secondary' }}>
                    Click on a card to quickly select the first strain of that type, or select individual strains below.
                </Typography>
            </Box>

            {/* Simple strain list layout */}
            <Paper
                elevation={0}
                sx={{
                    p: { xs: 2, md: 3 },
                    bgcolor: '#f8f9fa',
                    borderRadius: '12px',
                    width: '100%',
                    mb: 3
                }}
            >
                <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                    {/* Two-column layout for strain types */}
                    <Box sx={{
                        display: 'grid',
                        gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                        gap: 2,
                        mb: 3
                    }}>
                        {/* Sativa Strains Column */}
                        <Box>
                            <Typography variant="subtitle1" sx={{
                                color: '#2d3748',
                                fontWeight: 'bold',
                                mb: 2,
                                fontSize: { xs: '16px', sm: '18px' },
                                borderBottom: '1px solid #e2e8f0',
                                pb: 1
                            }}>
                                Sativa Strains
                            </Typography>

                            {sativaStrains.map((strain) => (
                                <Box
                                    key={strain}
                                    sx={{
                                        mb: 2,
                                        pb: 2,
                                        borderBottom: '1px solid #f0f2f5',
                                        borderRadius: '4px',
                                        transition: 'background-color 0.2s',
                                        position: 'relative'
                                    }}
                                >
                                    <Box sx={{
                                        display: 'flex',
                                        alignItems: 'flex-start',
                                        mb: 0.5
                                    }}>
                                        <Checkbox
                                            checked={selectedStrains.sativa.includes(strain)}
                                            onChange={(e) => handleStrainSelection('sativa', strain, e)}
                                            size="small"
                                            sx={{
                                                color: '#4a5568',
                                                '&.Mui-checked': {
                                                    color: 'green',
                                                },
                                                p: 0.5,
                                                mr: 1,
                                                cursor: 'pointer'
                                            }}
                                            slotProps={{ input: { 'aria-label': `Select ${strain}` } }}
                                        />
                                        <Typography
                                            variant="body1"
                                            onClick={(e) => handleStrainSelection('sativa', strain, e)}
                                            sx={{
                                                fontWeight: 'medium',
                                                color: '#2d3748',
                                                fontSize: { xs: '0.95rem', sm: '1rem' },
                                                cursor: 'pointer'
                                            }}
                                        >
                                            {strain}
                                        </Typography>
                                    </Box>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{
                                            ml: 5,
                                            fontSize: '0.85rem',
                                            display: 'flex',
                                            alignItems: 'flex-start'
                                        }}
                                    >
                                        <Box component="span" sx={{
                                                maxHeight: { xs: '40px', sm: '60px' },
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                WebkitLineClamp: { xs: 2, sm: 3 },
                                                WebkitBoxOrient: 'vertical',
                                                display: '-webkit-box',
                                                width: '100%',
                                                position: 'relative',
                                                paddingRight: '24px'
                                            }}>
                                                {strainTypes.sativa[strain]}
                                                <Tooltip
                                                    title={strainTypes.sativa[strain]}
                                                    arrow
                                                    placement="top"
                                                >
                                                    <Box
                                                        component="span"
                                                        onClick={(e) => e.stopPropagation()}
                                                        sx={{
                                                            display: 'inline-flex',
                                                            position: 'absolute',
                                                            right: 0,
                                                            top: 0,
                                                            color: '#4caf50',
                                                            cursor: 'help'
                                                        }}
                                                    >
                                                        <InfoOutlinedIcon fontSize="small" />
                                                    </Box>
                                                </Tooltip>
                                            </Box>
                                    </Typography>
                                </Box>
                            ))}
                        </Box>

                        {/* Indica Strains Column */}
                        <Box>
                            <Typography variant="subtitle1" sx={{
                                color: '#2d3748',
                                fontWeight: 'bold',
                                mb: 2,
                                fontSize: { xs: '16px', sm: '18px' },
                                borderBottom: '1px solid #e2e8f0',
                                pb: 1
                            }}>
                                Indica Strains
                            </Typography>

                            {indicaStrains.map((strain) => (
                                <Box
                                    key={strain}
                                    sx={{
                                        mb: 2,
                                        pb: 2,
                                        borderBottom: '1px solid #f0f2f5',
                                        borderRadius: '4px',
                                        transition: 'background-color 0.2s',
                                        position: 'relative'
                                    }}
                                >
                                    <Box sx={{
                                        display: 'flex',
                                        alignItems: 'flex-start',
                                        mb: 0.5
                                    }}>
                                        <Checkbox
                                            checked={selectedStrains.indica.includes(strain)}
                                            onChange={(e) => handleStrainSelection('indica', strain, e)}
                                            size="small"
                                            sx={{
                                                color: '#4a5568',
                                                '&.Mui-checked': {
                                                    color: 'green',
                                                },
                                                p: 0.5,
                                                mr: 1,
                                                cursor: 'pointer'
                                            }}
                                            slotProps={{ input: { 'aria-label': `Select ${strain}` } }}
                                        />
                                        <Typography
                                            variant="body1"
                                            onClick={(e) => handleStrainSelection('indica', strain, e)}
                                            sx={{
                                                fontWeight: 'medium',
                                                color: '#2d3748',
                                                fontSize: { xs: '0.95rem', sm: '1rem' },
                                                cursor: 'pointer'
                                            }}
                                        >
                                            {strain}
                                        </Typography>
                                    </Box>
                                    <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{
                                            ml: 5,
                                            fontSize: '0.85rem',
                                            display: 'flex',
                                            alignItems: 'flex-start'
                                        }}
                                    >
                                        <Box component="span" sx={{
                                                maxHeight: { xs: '40px', sm: '60px' },
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                WebkitLineClamp: { xs: 2, sm: 3 },
                                                WebkitBoxOrient: 'vertical',
                                                display: '-webkit-box',
                                                width: '100%',
                                                position: 'relative',
                                                paddingRight: '24px'
                                            }}>
                                                {strainTypes.indica[strain]}
                                                <Tooltip
                                                    title={strainTypes.indica[strain]}
                                                    arrow
                                                    placement="top"
                                                >
                                                    <Box
                                                        component="span"
                                                        onClick={(e) => e.stopPropagation()}
                                                        sx={{
                                                            display: 'inline-flex',
                                                            position: 'absolute',
                                                            right: 0,
                                                            top: 0,
                                                            color: '#7e57c2',
                                                            cursor: 'help'
                                                        }}
                                                    >
                                                        <InfoOutlinedIcon fontSize="small" />
                                                    </Box>
                                                </Tooltip>
                                            </Box>
                                    </Typography>
                                </Box>
                            ))}
                        </Box>
                    </Box>

                    {/* Hybrid Strains */}
                    <Typography variant="subtitle1" sx={{
                        color: '#2d3748',
                        fontWeight: 'bold',
                        mb: 2,
                        fontSize: { xs: '16px', sm: '18px' },
                        borderBottom: '1px solid #e2e8f0',
                        pb: 1
                    }}>
                        Hybrid Strains
                    </Typography>

                    <Box sx={{
                        display: 'grid',
                        gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                        gap: 2,
                        mb: 3
                    }}>
                        {hybridStrains.map((strain) => (
                            <Box
                                key={strain}
                                sx={{
                                    mb: 2,
                                    pb: 2,
                                    borderBottom: '1px solid #f0f2f5',
                                    borderRadius: '4px',
                                    transition: 'background-color 0.2s',
                                    position: 'relative'
                                }}
                            >
                                <Box sx={{
                                    display: 'flex',
                                    alignItems: 'flex-start',
                                    mb: 0.5
                                }}>
                                    <Checkbox
                                        checked={selectedStrains.hybrid.includes(strain)}
                                        onChange={(e) => handleStrainSelection('hybrid', strain, e)}
                                        size="small"
                                        sx={{
                                            color: '#4a5568',
                                            '&.Mui-checked': {
                                                color: 'green',
                                            },
                                            p: 0.5,
                                            mr: 1,
                                            cursor: 'pointer'
                                        }}
                                        slotProps={{ input: { 'aria-label': `Select ${strain}` } }}
                                    />
                                    <Typography
                                        variant="body1"
                                        onClick={(e) => handleStrainSelection('hybrid', strain, e)}
                                        sx={{
                                            fontWeight: 'medium',
                                            color: '#2d3748',
                                            fontSize: { xs: '0.95rem', sm: '1rem' },
                                            cursor: 'pointer'
                                        }}
                                    >
                                        {strain}
                                    </Typography>
                                </Box>
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{
                                        ml: 5,
                                        fontSize: '0.85rem',
                                        display: 'flex',
                                        alignItems: 'flex-start'
                                    }}
                                >
                                    <Box component="span" sx={{
                                            maxHeight: { xs: '40px', sm: '60px' },
                                            overflow: 'hidden',
                                            textOverflow: 'ellipsis',
                                            WebkitLineClamp: { xs: 2, sm: 3 },
                                            WebkitBoxOrient: 'vertical',
                                            display: '-webkit-box',
                                            width: '100%',
                                            position: 'relative',
                                            paddingRight: '24px'
                                        }}>
                                            {strainTypes.hybrid[strain]}
                                            <Tooltip
                                                title={strainTypes.hybrid[strain]}
                                                arrow
                                                placement="top"
                                            >
                                                <Box
                                                    component="span"
                                                    onClick={(e) => e.stopPropagation()}
                                                    sx={{
                                                        display: 'inline-flex',
                                                        position: 'absolute',
                                                        right: 0,
                                                        top: 0,
                                                        color: '#ff9800',
                                                        cursor: 'help'
                                                    }}
                                                >
                                                    <InfoOutlinedIcon fontSize="small" />
                                                </Box>
                                            </Tooltip>
                                        </Box>
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                </Box>
            </Paper>

            <Paper
                elevation={0}
                sx={{
                    p: { xs: 2, md: 3 },
                    bgcolor: '#f8f9fa',
                    borderRadius: '12px',
                    width: '100%',
                    mb: 3
                }}
            >
                <Typography variant="subtitle1" sx={{
                    color: '#2d3748',
                    fontWeight: 'bold',
                    mb: { xs: 1, sm: 2 },
                    fontSize: { xs: '16px', sm: '18px' }
                }}>
                    Additional Advice
                </Typography>
                <Card sx={{ mb: 2 }}>
                    <CardContent sx={{ pb: '16px !important' }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            Add any custom advice or notes about the selected strains:
                        </Typography>
                        <TextField
                            fullWidth
                            multiline
                            rows={3}
                            value={customAdvice}
                            onChange={(e) => setCustomAdvice(e.target.value)}
                            placeholder="Enter any additional strain advice here..."
                            size="small"
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    backgroundColor: '#ffffff',
                                    '& fieldset': {
                                        borderColor: '#e2e8f0',
                                    },
                                    '&:hover fieldset': {
                                        borderColor: '#cbd5e0',
                                    },
                                    '&.Mui-focused fieldset': {
                                        borderColor: '#4a5568',
                                    },
                                },
                            }}
                        />
                    </CardContent>
                </Card>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                    <Card sx={{ flexGrow: 1, minWidth: { xs: '100%', sm: '200px' } }}>
                        <CardContent>
                            <Typography variant="subtitle2" color="text.primary" sx={{ mb: 1, fontWeight: 'bold' }}>
                                Selected Strains Summary
                            </Typography>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                                {selectedStrains.sativa.length > 0 && (
                                    <Chip
                                        label={`${selectedStrains.sativa.length} Sativa`}
                                        size="small"
                                        sx={{ bgcolor: '#e8f5e9', color: '#2e7d32' }}
                                    />
                                )}
                                {selectedStrains.indica.length > 0 && (
                                    <Chip
                                        label={`${selectedStrains.indica.length} Indica`}
                                        size="small"
                                        sx={{ bgcolor: '#ede7f6', color: '#5e35b1' }}
                                    />
                                )}
                                {selectedStrains.hybrid.length > 0 && (
                                    <Chip
                                        label={`${selectedStrains.hybrid.length} Hybrid`}
                                        size="small"
                                        sx={{ bgcolor: '#fff3e0', color: '#ef6c00' }}
                                    />
                                )}
                                {selectedStrains.sativa.length === 0 && selectedStrains.indica.length === 0 && selectedStrains.hybrid.length === 0 && (
                                    <Typography variant="body2" color="text.secondary">
                                        No strains selected yet
                                    </Typography>
                                )}
                            </Box>
                            <Typography variant="caption" color="text.secondary">
                                Click on strain cards above to select or deselect them
                            </Typography>
                        </CardContent>
                    </Card>
                </Box>
            </Paper>

            <Card elevation={1} sx={{ mb: 3, borderRadius: '12px', overflow: 'visible' }}>
                <CardContent sx={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    flexWrap: 'wrap',
                    gap: 2,
                    p: { xs: 2, sm: 3 },
                    '&:last-child': { pb: { xs: 2, sm: 3 } }
                }}>
                    <Button
                        variant="outlined"
                        onClick={() => handleSave(true)}
                        sx={{
                            color: 'green',
                            borderColor: 'green',
                            '&:hover': {
                                borderColor: 'darkgreen',
                                bgcolor: 'rgba(0, 128, 0, 0.04)'
                            },
                            textTransform: 'none',
                            fontSize: { xs: '14px', sm: '16px' },
                            py: 1,
                            px: 3,
                            borderRadius: '8px',
                            minWidth: { xs: '100%', sm: 'auto' }
                        }}
                        disabled={submitting}
                    >
                        Save Advice
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleFinish}
                        sx={{
                            bgcolor: 'green',
                            '&:hover': {
                                bgcolor: 'darkgreen',
                            },
                            textTransform: 'none',
                            fontSize: { xs: '14px', sm: '16px' },
                            py: 1,
                            px: 3,
                            borderRadius: '8px',
                            minWidth: { xs: '100%', sm: 'auto' }
                        }}
                        disabled={submitting}
                    >
                        {submitting ? 'Submitting...' : 'Finish & Submit Treatment Plan'}
                    </Button>
                </CardContent>
            </Card>
            <Snackbar
                open={snackbarOpen}
                autoHideDuration={4000}
                onClose={() => setSnackbarOpen(false)}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
                <Alert
                    onClose={() => setSnackbarOpen(false)}
                    severity={snackbarSeverity}
                    sx={{ width: '100%' }}
                >
                    {snackbarMessage}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default ChatStrainAdvice;