-- Migration: Update chat_notifications table to use email instead of patient_id
-- Date: 2025-07-10

-- Step 1: Add new email column
ALTER TABLE chat_notifications 
ADD COLUMN patient_email VARCHAR(255);

-- Step 2: Drop the existing unique constraint on patient_id
ALTER TABLE chat_notifications 
DROP CONSTRAINT IF EXISTS chat_notifications_patient_id_key;

-- Step 3: Create unique constraint on patient_email
ALTER TABLE chat_notifications 
ADD CONSTRAINT chat_notifications_patient_email_key UNIQUE (patient_email);

-- Step 4: Create index on patient_email for better query performance
CREATE INDEX IF NOT EXISTS idx_chat_notifications_patient_email 
ON chat_notifications (patient_email);

-- Step 5: Update existing records (if any) - this will need to be done manually
-- based on your patient data mapping from patient_id to email
-- Example: UPDATE chat_notifications SET patient_email = '<EMAIL>' WHERE patient_id = '12345';

-- Step 6: After data migration is complete, you can drop the old patient_id column
-- (Commented out for safety - run this manually after confirming data migration)
-- ALTER TABLE chat_notifications DROP COLUMN patient_id;

-- Note: The patient_id column is kept for now to allow for data migration
-- Once all existing records have been updated with email addresses,
-- you can uncomment and run the DROP COLUMN statement above
