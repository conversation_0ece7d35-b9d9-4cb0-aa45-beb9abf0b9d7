import { errorConverter, errorHandler } from '../middlewares/errorMiddleware';
import routes from '../routes';
import config from './index';
import cors from 'cors';
import express from 'express';
import httpStatus from 'http-status';

export const createServer = () => {
  const allowedOrigins = config.allowedHostList
    ? config.allowedHostList.split(',').map((host) => host.trim().replace(/^'|'$/g, ''))
    : [
        'http://localhost:5173',
        'http://localhost:5174',
        'https://doctor.zenith.clinic',
        'https://care.zenith.clinic',
        'https://funnel.zenith.ad',
        'https://www.zohoapis.com.au',
        'https://one.zoho.com.au/',
        'https://zenith.ad',
        'https://pay.harvest.delivery',
        'https://medicine-register.simdoar.net',
      ];

      const corsOptions = {
    origin: (origin: string | undefined, callback: (err: Error | null, allowed?: boolean) => void) => {
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    credentials: true,
  };

  const app = express();

  // Note: Static file serving for /api/assets/ removed as reports now use S3 storage
  // All report images are now stored in Amazon S3 bucket instead of local filesystem

  app.use(cors(corsOptions));
  app.use(express.json());
  app.use(express.text({ type: 'text/html' }));
  app.use(express.text({ type: 'text/plain' }));
  app.use(express.urlencoded({ extended: true }));
  app.disable('x-powered-by');
  // function timeTracker(req, res, next) {
  //   const startTime = Date.now();
  //   console.log(`[${startTime}] Middleware started : ${req.method} ${req.url}`);

  //   res.on('finish', () => {
  //     const endTime = Date.now();
  //     const elapsedTime = endTime - startTime;
  //     console.log(`[${endTime}] Middleware terminated : ${req.method} ${req.url} (${elapsedTime} ms)`);
  //   });

  //   next(); // Passer au middleware suivant ou à la route
  // }
  // app.use(timeTracker);
  app.use('/api', routes);
  app.get('/health', (_req, res) => {
    res.status(httpStatus.OK).send('UP');
    return;
  });
  app.get('/test', (_req, res) => {
    res.sendStatus(200).send('UP');
    return;
  });

  app.use((req, res) => {
    console.log('Unkown API :: ', req.url);
    res.status(200).send({});
    return;
  });

  app.use(errorConverter);

  app.use(errorHandler);

  return app;
};
