import openai from 'openai';
import config from '../../config';
import { AICheckerResponse } from '../../types/funnel';

const client = new openai.OpenAI({
  apiKey: config.openAiKey,
});

const aiChecker = async (
  condition: string,
  med1: string,
  med2: string,
  pregnancy: string,
  disorder: string,
  diseases: string,
) => {
  if (!condition || !med1 || !med2) {
    throw new Error('Condition and medications are required');
  }

  const prompt = `You are an AHPRA-registered medical doctor and an authorised TGA prescriber of medicinal cannabis in Australia. Your role is to assess whether a patient is a clinically appropriate candidate for medicinal cannabis flower, based on their medical condition and two prior treatment attempts.
  Use Australian clinical guidance and best prescribing practices. Your decision should favour eligibility when it is clinically safe to do so.
  Assessment criteria:
  1.	Determine if the condition (e.g. chronic pain, anxiety, insomnia) is commonly and appropriately treated with cannabis flower.
  2.	Confirm that the two medications tried are reasonable first- or second-line treatments for the condition.
  3.	Do not approve cases where a cannabis product was used as a first- or second-line treatment.
  4.	Assume that failure to respond or intolerance to both medications justifies consideration of medicinal cannabis.
  5.	Only decline if there are clear clinical red flags, such as:
      o	Personal or family history of psychosis
      o	Pregnancy or breastfeeding
      o	Uncontrolled cardiovascular disease
      o	Recent or active substance use disorder
  6.	Do not infer medication names or classes. For example, if the patient says “tablets” without clarification, treat this as insufficient information.
  7.	Do not consider non-pharmacological treatments (e.g. physiotherapy, CBT, massage) as valid prior treatments.
  Be generous in your assessment when the condition is common and no major risks are present.
  Ignore spelling and grammar. Focus only on clinical logic. Return your decision using the exact JSON format below:



  {
    "condition": "${condition}",
    "pregnancy": "${pregnancy}",
    "psychotic_disorder": "${disorder}",
    "cardiovascular_diseases": "${diseases}",
    "tried_medications": ["${med1}", "${med2}"],
    "response": "YES" or "NO",
    "risk_score": 1-5,
    "explanation": "Brief clinical justification focusing on treatment history, condition suitability, and safety."
  }

  You must use the exact patient inputs provided above. Do not invent or assume other conditions or medications.

  `;

  const response = await client.chat.completions.create({
    model: 'gpt-4.1',
    messages: [{ role: 'user', content: prompt }],
    temperature: 0,
  });
  const rawContent = response.choices[0].message.content;
  if (!rawContent) {
    throw new Error('No content returned from OpenAI API');
  }
  // Extract the JSON content from the response
  let jsonContent: AICheckerResponse;
  try {
    jsonContent = JSON.parse(rawContent);
  } catch (error) {
    const errMsg = error instanceof Error ? error.message : String(error);
    throw new Error('Failed to parse JSON from OpenAI response: ' + errMsg);
  }
  return jsonContent;
};

export default aiChecker;
