/// <reference types="vite-plugin-svgr/client" />

import { Button, Table, TableCell, TableContainer, TableBody, TableHead, TableRow, Toolbar, Typography } from "@mui/material";
import Grid from '@mui/material/Grid2';
import ZenithLogo from '../../assets/ZenithGreen.svg?react';
import LocalPrintshopIcon from '@mui/icons-material/LocalPrintshop';
import { useReactToPrint } from 'react-to-print';
import { Fragment, useEffect, useRef, useState } from "react";
import { doctorSignatureMapping, supplyLetterTable } from "../../utils";
import { DateTime } from "luxon";
import { Product, OutputDoc, OrderPatient } from "../../types";
import { ApiClient } from "../../services";
import { CircularProgress } from '@mui/material';
import { customAlphabet } from 'nanoid';

const DocsToPrint: React.FC = () => {
    const medicineRegisterDoc = useRef<HTMLDivElement>(null);
    const supplyDoc = useRef<HTMLDivElement>(null);
    const patientListDoc = useRef<HTMLDivElement>(null);

    const [productCentricData, setProductCentricData] = useState<Product | undefined>(undefined)
    const [orderPatient, setOrderPatient] = useState<OrderPatient[] | []>([])
    const [output, setOutput] = useState<OutputDoc | undefined>(undefined)
    const [loading, setLoading] = useState<boolean>(true)
    const [uniqueReportId, setUniqueReportId] = useState('')
    const [count, setCount] = useState(0)
    const [oldData, setOldData] = useState<boolean>(false)
    const [lastPull, setLastPull] = useState('')
    const printMedicineRegister = useReactToPrint({ contentRef: medicineRegisterDoc })
    const printSupplyDoc = useReactToPrint({ contentRef: supplyDoc })
    const printPatientList = useReactToPrint({ contentRef: patientListDoc })

    const today = DateTime.now().setZone('Australia/Sydney').toFormat('dd-MM-yyyy HH:mm:ss')
    const todayDate = DateTime.now().setZone('Australia/Sydney').toFormat('dd/MM')

    useEffect(() => {
        const init = async () => {
            try {
                const result = await ApiClient.getmedicineRegisterData()
                setProductCentricData(result.productCentricData)
                setOutput(result.output)
                setOldData(result.oldData ? result.oldData : false)
                setLastPull(result.lastPullDate)
                setOrderPatient(result.orders.data)
                const nanoid = customAlphabet('**********', 6); // Only numbers, 6 digits
                setUniqueReportId(nanoid())
                const getCount = localStorage.getItem('regC')
                if (getCount) {
                    setCount(Number(getCount))
                }
            }
            finally {
                setLoading(false)
            }
        }
        init()
    }, [])

    const refreshWithNewData = async () => {
        try {
            setLoading(true)
            const update = true
            const result = await ApiClient.getmedicineRegisterData(update)
            setProductCentricData(result.productCentricData)
            setOutput(result.output)
            setOldData(result.oldData ? result.oldData : false)
            setLastPull(result.lastPullDate)
            setOrderPatient(result.orders.data)
        }
        finally {
            setLoading(false)
        }

    }
    // #FFD580
    return (
        <>
            {oldData && <Grid container sx={{ width: '100%', backgroundColor: '#FFD580', p: 2 }} justifyContent={'center'}>
                <Typography fontWeight={700}>
                    You are looking at old data. You need to wait 4 hours for the nextRegister. Last pull {lastPull}
                </Typography>
            </Grid>}
            <Grid container sx={{ mt: 2, p: 2 }}>
                <Grid>
                    <Button variant='contained' onClick={refreshWithNewData} startIcon={loading ? <CircularProgress size="1rem" style={{ marginRight: '16px', color: 'white', fontWeight: 'bold' }} /> : undefined}>
                        Refresh Data
                    </Button>
                </Grid>
            </Grid>

            <Grid container sx={{ width: '100%', pr: 5 }} justifyContent={"end"} alignItems={"center"}>
                <Grid>
                    <Button variant="outlined" color="primary" sx={{ mt: 2, mr: 2 }} onClick={printMedicineRegister}>
                        <LocalPrintshopIcon style={{ marginRight: '16px' }} />
                        Print Medicine Register
                    </Button>
                </Grid>
            </Grid>

            <div ref={medicineRegisterDoc}>
                {/* MEDICINE REGISTER */}
                <Grid container sx={{ pl: 5, pr: 5, pt: 3, pb: 5, pageBreakAfter: 'always' }} >

                    <Grid container sx={{ width: '100%', mb: 2 }}>
                        <Grid container direction='column'>
                            <Typography>
                                MEDICINES REGISTER <br />
                                {today}
                            </Typography>
                            <Typography sx={{ mt: 2 }}>
                                Unique ID: {uniqueReportId}
                            </Typography>
                        </Grid>
                        <Toolbar sx={{ flexGrow: 1 }} />
                        <Grid container direction='column' alignItems={'end'}>
                            <Typography fontSize={'20px'}>
                                Medicinal Cannabis Wholesale Australia Pty Ltd
                            </Typography>
                            <Grid>
                                114B/545-553 Pacific Highway, St Leonards NSW 2065
                            </Grid>
                        </Grid>
                    </Grid>
                    <TableContainer>
                        <Table sx={{
                            minWidth: 650, border: '1px solid rgba(224, 224, 224, 1)', // outer border
                            '& .MuiTableCell-root': {
                                border: '1px solid rgba(224, 224, 224, 1)', // cell borders
                            },
                        }} aria-label="simple table">
                            <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
                                <TableRow>
                                    <TableCell align="center">Date</TableCell>
                                    <TableCell align="center">Dealing</TableCell>
                                    <TableCell align="center">Address</TableCell>
                                    <TableCell align="center">Code</TableCell>
                                    <TableCell align="center">Prescriber</TableCell>
                                    <TableCell align="center">Quantity out</TableCell>
                                    <TableCell align="center">Balance</TableCell>
                                    <TableCell align="center">Identifier</TableCell>
                                    <TableCell align="center">Sign</TableCell>
                                    <TableCell align="center">Notes</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {loading ? <CircularProgress size="1rem" style={{ marginRight: '16px' }} /> : null}
                                {productCentricData && Object.entries(productCentricData).map(([productName, productData], index) => {
                                    const newCount = count + (index + 1) * 10
                                    localStorage.setItem('regC', `${newCount}`)

                                    return (<Fragment key={productName}>
                                        {/* Main product row */}
                                        <TableRow>
                                            <TableCell sx={{ width: '50px' }}>{todayDate}</TableCell>
                                            <TableCell sx={{ width: '50px' }}>{'Zenith'}</TableCell>
                                            <TableCell sx={{ width: '250px' }}>{'114A/545-553 Pacific Highway'}</TableCell>
                                            <TableCell sx={{ width: '250px' }}>{`${productData?.tradeName}\n(${productName})`}</TableCell>
                                            <TableCell sx={{ width: '250px' }}>
                                                {productData.requests.map((request, index) => (
                                                    <div key={index}>{`${request.drName && request.drName !== 'null' ? request.drName : 'Unknown'} (${request.budQuantity})`}</div>
                                                ))}
                                            </TableCell>
                                            <TableCell align="center" sx={{ width: '100px' }}>{productData.totalBudQuantity}</TableCell>
                                            <TableCell align="center" sx={{ width: '150px' }}>{productData.balance}</TableCell>
                                            <TableCell align="center" sx={{ width: '100px' }}>{newCount}</TableCell>
                                            <TableCell align="center"></TableCell>
                                            <TableCell align="center"></TableCell>
                                        </TableRow>
                                    </Fragment>)
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Grid>
            </div>

            <Grid container sx={{ width: '100%', pr: 2 }} justifyContent={"end"} alignItems={"center"}>
                <Grid>
                    <Button variant="outlined" color="primary" sx={{ mt: 2 }} onClick={printSupplyDoc}>
                        <LocalPrintshopIcon style={{ marginRight: '16px' }} />
                        PRINT ALL SUPPLY DOCUMENTS
                    </Button>
                </Grid>
            </Grid>
            <div ref={supplyDoc} >
                {/*SUPPLY REQUEST */}

                <Grid container>
                    <Grid container sx={{ width: '100%' }} justifyContent={'center'}>
                        {loading ? <CircularProgress size="1rem" style={{ marginRight: '16px' }} /> : null}
                    </Grid>

                    {output && Object.entries(output).map(([doctorName, { products }], index) => {

                        const productList = Object.entries(products).map(([_productName, product]) => {
                            return product
                        })
                        return (
                            <Grid key={`${doctorName}${index}`} container size={{ lg: 6 }} sx={{ p: 2, pageBreakAfter: 'always' }} direction="column">
                                <Grid container alignItems={"end"} direction="column">
                                    <Grid>
                                        <ZenithLogo width={100} />
                                    </Grid>
                                    <Grid>
                                        <Typography align="right">
                                            Zenith Clinics Pty Ltd <br />
                                            114A/545-553 Pacific Highway, St Leonards NSW 2065 <br />
                                            (02) 7228 8399
                                        </Typography>
                                    </Grid>
                                </Grid>
                                <Grid>
                                    <Typography>
                                        Date: {today} <br />
                                        {`Unique ID: ${uniqueReportId}`}<br /><br />
                                        Medicinal Cannabis Wholesale Australia Pty Ltd <br />
                                        114A/545-553 Pacific Highway <br />
                                        St Leonards  NSW  2065 <br /><br />
                                        Re: Supply Request for Medicinal Cannabis Flower <br /><br />
                                        I am writing to formally request the supply of Medicinal Cannabis Flower for our patients as part of the Authorised Prescriber Scheme. Our clinic, Zenith Clinics Pty Ltd, is authorised under the Therapeutic Goods Administration (TGA) guidelines to prescribe and supply unapproved therapeutic goods directly to patients in our immediate care.
                                        <br /><br />
                                        Details of Medicinal Cannabis to be Collected:<br /><br />
                                    </Typography>
                                </Grid>


                                <TableContainer sx={{ overflow: 'hidden' }}>
                                    <Table sx={{
                                        border: '1px solid rgba(224, 224, 224, 1)', // outer border
                                        '& .MuiTableCell-root': {
                                            border: '1px solid rgba(224, 224, 224, 1)', // cell borders
                                        },
                                    }} aria-label="simple table">
                                        <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
                                            <TableRow>
                                                <TableCell align="center">Tradename</TableCell>
                                                <TableCell align="center">Quantity</TableCell>
                                                <TableCell align="center">Strength</TableCell>
                                            </TableRow>
                                        </TableHead>
                                        <TableBody>
                                            {productList.map((item) => {
                                                const details = supplyLetterTable.find((p) => p.tradeName.trim() === item.tradeName.trim())
                                                return (
                                                    <Fragment key={`${item.tradeName}${doctorName}`}>
                                                        {/* Main product row */}
                                                        <TableRow>
                                                            <TableCell align="center" sx={{ width: '250px' }}>{item.tradeName}</TableCell>
                                                            <TableCell align="center">{item.totalBudQuantity}</TableCell>
                                                            <TableCell align="center">{details?.strength}</TableCell>
                                                        </TableRow>
                                                    </Fragment>
                                                )
                                            })}
                                        </TableBody>
                                    </Table>
                                </TableContainer>

                                <Grid>
                                    <Typography>
                                        <br /><br />NOTE: Only provide supply if the Quantity field contains an amount.<br /><br />
                                        Purpose of the Request: These products are essential for the treatment plans of patients who have been pre-approved and meet the clinical criteria established by our practice.
                                        The use of these medicinal cannabis flowers is in strict compliance with the protocols outlined by the TGA and our internal clinical guidelines. <br /><br />
                                        We assure you that all supplied goods will be used strictly in accordance with the Therapeutic Goods Act 1989 and the Therapeutic Goods Regulations 1990.
                                        Each patient will receive the product with the appropriate dispensing label and monitoring protocols to ensure their safety and the efficacy of the treatment.<br /><br />
                                        Contact Information: Should you require any further information or have any queries regarding this request,
                                        please do not hesitate to contact me directly at 02 7228 8399 or via <NAME_EMAIL> <br /><br />
                                        Thank you for your prompt attention to this matter.<br /><br />
                                        Yours sincerely,<br />
                                    </Typography>
                                </Grid>
                                <Typography sx={{ fontStyle: 'italic', fontSize: '28px', color: 'grey' }}>
                                    {doctorName} <br /><br />
                                </Typography>
                                <img src={doctorSignatureMapping[doctorName as keyof typeof doctorSignatureMapping]} width="200" height="100" alt={doctorName} />
                                <Grid>
                                    <Typography>
                                        {doctorName} <br />
                                        Zenith Clinics Pty Ltd <br />
                                        114A/545-553 Pacific Highway
                                        St Leonards  NSW  2065
                                    </Typography>
                                </Grid>
                                <Grid>
                                    <Typography sx={{ fontSize: '12px', color: 'grey' }}>
                                        <br /><br />Disclaimer:
                                        This email and any attachments are intended solely for the designated recipient and may contain confidential or privileged information. If you are not the intended recipient:<br />
                                        (a) Please notify us immediately by phone and ensure the permanent deletion of this email from your system;<br />
                                        (b) Be advised that any unauthorized use, sharing, distribution, or reproduction of this email’s content is strictly forbidden.<br />
                                        Opinions expressed herein are those of the author and do not necessarily represent the views of Zenith Clinics unless explicitly stated. Zenith Clinics and its directors disclaim any liability for potential harm from computer viruses transmitted through this email.
                                    </Typography>
                                </Grid>
                            </Grid>
                        )
                    })}
                </Grid>
            </div>


            <Grid container sx={{ width: '100%', pr: 2 }} justifyContent={"end"} alignItems={"center"}>
                <Grid>
                    <Button variant="outlined" color="primary" sx={{ mt: 2 }} onClick={printPatientList}>
                        <LocalPrintshopIcon style={{ marginRight: '16px' }} />
                        PRINT PATIENT LIST
                    </Button>
                </Grid>
            </Grid>

            <div ref={patientListDoc}>
                <Grid container direction='column' sx={{ p: 2 }}>
                    <Grid>
                        <Typography>
                            <span style={{ fontSize: '26px' }}>DISPATCH CHECKLIST</span>  <br /><br />
                            Patient List for Register ID: {uniqueReportId}  <br />
                            Number of Orders: {orderPatient.length}<br />
                            Date: {today}<br /><br />
                        </Typography>
                    </Grid>
                    <TableContainer>
                        <Table sx={{
                            minWidth: 650, border: '1px solid rgba(224, 224, 224, 1)', // outer border
                            '& .MuiTableCell-root': {
                                border: '1px solid rgba(224, 224, 224, 1)', // cell borders
                            },
                        }} aria-label="simple table">
                            <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
                                <TableRow>
                                    <TableCell align="center">Full Name</TableCell>
                                    <TableCell align="center">Email</TableCell>
                                    <TableCell align="center">Contact ID</TableCell>
                                    <TableCell align="center">Order Number</TableCell>
                                    <TableCell align="center">Dr Name</TableCell>
                                    <TableCell align="center">Payment Date</TableCell>
                                    <TableCell align="center">Completed</TableCell>
                                    <TableCell align="center">Date</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {loading ? <CircularProgress size="1rem" style={{ marginRight: '16px' }} /> : null}
                                {orderPatient.length > 0 && orderPatient.map((item, index) => {

                                    return (<Fragment key={`${item.user_email}${index}`}>
                                        {/* Main product row */}
                                        <TableRow>
                                            <TableCell align="center" sx={{ width: '150px' }}>{`${item.first_name} ${item.last_name}`}</TableCell>
                                            <TableCell align="center" sx={{ width: '150px' }}>{item.user_email}</TableCell>
                                            <TableCell align="center" sx={{ width: '150px' }}><a href={`https://crm.zoho.com.au/crm/org7002688441/tab/Contacts/${item.zoho_contact_id}`} target="_blank" rel="noopener noreferrer">{item.zoho_contact_id}</a></TableCell>
                                            <TableCell align="center" sx={{ width: '150px' }}>{item.order_number}</TableCell>
                                            <TableCell align="center" sx={{ width: '150px' }}>{item.consulting_doctor}</TableCell>
                                            <TableCell align="center" sx={{ width: '150px' }}>{item.order_date_modified}</TableCell>
                                            <TableCell align="center"></TableCell>
                                            <TableCell align="center"></TableCell>
                                        </TableRow>
                                    </Fragment>)
                                })}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Grid>
            </div>


        </>
    );
}

export default DocsToPrint;