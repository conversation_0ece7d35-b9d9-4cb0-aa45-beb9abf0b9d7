.channel-preview__message p {
  margin: 0;
}

.str-chat {
  --str-chat__primary-color: #009688;
  --str-chat__active-primary-color: #004d40;
  --str-chat__surface-color: #f5f5f5;
  --str-chat__secondary-surface-color: #eeebeb;
  --str-chat__primary-surface-color: #e0f2f1;
  --str-chat__primary-surface-color-low-emphasis: #edf7f7;
  --str-chat__border-radius-circle: 6px;
  --str-chat__avatar-size: 28px;
}

/* Message Input Textarea Improvements */
.str-chat__textarea {
  min-height: 44px;
  max-height: 150px;
  overflow-y: auto !important;
  resize: none;
  line-height: 1.4;
  transition: height 0.1s ease-out;
}

.str-chat__textarea textarea {
  min-height: 44px;
  max-height: 150px;
  overflow-y: auto !important;
  padding-right: 10px;
  padding-left: 10px;
  resize: none;
  scrollbar-width: thin;
}

.str-chat__message-textarea-container {
  min-height: 44px;
  max-height: 150px;
}

/* Ensure the input grows with content but has a max height */
.str-chat__messaging-input {
  min-height: 44px;
  max-height: 150px;
  display: flex;
  align-items: flex-end;
}

/* Custom paperclip icon for attachment button */
.str-chat__input-flat .str-chat__send-button-container .str-chat__attach-button svg {
  display: none; /* Hide the default + icon */
}

.str-chat__input-flat .str-chat__send-button-container .str-chat__attach-button::before {
  content: "";
  width: 24px;
  height: 24px;
  display: block;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' d='M18.375 12.739l-7.693 7.693a4.5 4.5 0 01-6.364-6.364l10.94-10.94A3 3 0 1119.5 7.372L8.552 18.32m.009-.01l-.01.01m5.699-9.941l-7.81 7.81a1.5 1.5 0 002.112 2.13' /%3E%3C/svg%3E");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  color: inherit;
}
