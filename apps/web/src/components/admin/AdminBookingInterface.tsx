import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterLuxon } from '@mui/x-date-pickers/AdapterLuxon';
import { DateTime } from 'luxon';
import { ApiClient } from '../../services';
import { Dr } from '../../types';

interface AdminBookingItem {
  patient_id: string;
  range_date: string;
  slot_details: string;
  admin_name: string;
  admin_email: string;
  bookedByAdminId: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

type FilterPeriod = 'today' | 'week' | 'custom';

const AdminBookingInterface: React.FC = () => {
  const [filteredBookings, setFilteredBookings] = useState<AdminBookingItem[]>([]);
  const [admins, setAdmins] = useState<Dr[]>([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 5,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  // Filter states
  const [selectedAdmin, setSelectedAdmin] = useState<string>('all');
  const [filterPeriod, setFilterPeriod] = useState<FilterPeriod>('today');
  const [startDate, setStartDate] = useState<DateTime | null>(DateTime.now());
  const [endDate, setEndDate] = useState<DateTime | null>(DateTime.now());


  // Load admin bookings and filter them
  const loadAdminBookings = async (page: number = pagination.page) => {
    setLoading(true);
    try {
      const params: {
        page: number;
        limit: number;
        adminId?: string;
        period?: string;
        startDate?: string;
        endDate?: string;
      } = {
        page,
        limit: pagination.limit
      };

      // Add admin filter
      if (selectedAdmin !== 'all') {
        params.adminId = selectedAdmin;
      }

      // Add date filter
      if (filterPeriod === 'today' || filterPeriod === 'week') {
        params.period = filterPeriod;
      } else if (filterPeriod === 'custom' && startDate && endDate) {
        params.startDate = startDate.toFormat('yyyy-MM-dd');
        params.endDate = endDate.toFormat('yyyy-MM-dd');
      }

      const response = await ApiClient.getAdminBookings(params);
      setFilteredBookings(response.bookings || []);
      setPagination(response.pagination || {
        page: 1,
        limit: 5,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      });
      setMessage(null);
    } catch (error) {
      console.error('Error loading admin bookings:', error);
      setMessage({ type: 'error', text: 'Failed to load admin bookings' });
      // Set empty array on error to prevent undefined errors
      setFilteredBookings([]);
    } finally {
      setLoading(false);
    }
  };

  // Load admins for filter dropdown
  const loadAdmins = async () => {

    try {
      const adminList = await ApiClient.getDoctors();
      const filteredAdmins = adminList.filter(doc => doc.role === 'admin');
      setAdmins(filteredAdmins);
    } catch (error) {
      console.error('Error loading admins:', error);
    }
  };

  useEffect(() => {
    loadAdmins();
    loadAdminBookings();
  }, []);

  // Apply filters when filter criteria change
  useEffect(() => {
    applyFilters();
  }, [selectedAdmin, filterPeriod, startDate, endDate]);

  const applyFilters = () => {
    // Reset to page 1 when filters change
    loadAdminBookings(1);
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    loadAdminBookings(page);
  };



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ color: 'text.primary', fontWeight: 'bold', textAlign: 'center' }}>
        Take Control of Your Bookings
      </Typography>
      <Typography variant="h6" color="text.secondary" gutterBottom sx={{ textAlign: 'center', mb: 4 }}>
        Streamline your admin tasks with ease. Filter and manage patient bookings confidently.
      </Typography>

        {message && (
          <Alert severity={message.type} sx={{ mb: 2 }} onClose={() => setMessage(null)}>
            {message.text}
          </Alert>
        )}

        {/* Filter Controls */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Filter Options
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              {/* Admin Filter */}
              <FormControl sx={{ minWidth: 200 }}>
                <InputLabel>Filter by Admin</InputLabel>
                <Select
                  value={selectedAdmin}
                  onChange={(e) => setSelectedAdmin(e.target.value)}
                  label="Filter by Admin"
                >
                  <MenuItem value="all">All Admins</MenuItem>
                  {admins.map((admin) => (
                    <MenuItem key={admin.accessID} value={admin.accessID}>
                      {admin.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              {/* Period Filter */}
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Time Period</InputLabel>
                <Select
                  value={filterPeriod}
                  onChange={(e) => setFilterPeriod(e.target.value as FilterPeriod)}
                  label="Time Period"
                >
                  <MenuItem value="today">Today</MenuItem>
                  <MenuItem value="week">This Week</MenuItem>
                  <MenuItem value="custom">Custom Range</MenuItem>
                </Select>
              </FormControl>

              {/* Custom Date Range */}
              {filterPeriod === 'custom' && (
                <LocalizationProvider dateAdapter={AdapterLuxon}>
                  <DatePicker
                    label="Start Date"
                    value={startDate}
                    onChange={(newValue) => setStartDate(newValue)}
                    slotProps={{ textField: { size: 'small', sx: { minWidth: 150 } } }}
                  />
                  <DatePicker
                    label="End Date"
                    value={endDate}
                    onChange={(newValue) => setEndDate(newValue)}
                    slotProps={{ textField: { size: 'small', sx: { minWidth: 150 } } }}
                  />
                </LocalizationProvider>
              )}

              <Button variant="contained" onClick={applyFilters} disabled={loading}>
                Apply Filters
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Bookings Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Admin Bookings
            </Typography>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : filteredBookings.length === 0 ? (
              <Typography color="text.secondary" sx={{ textAlign: 'center', p: 3 }}>
                No admin bookings found for the selected criteria
              </Typography>
            ) : (
              <TableContainer component={Paper} variant="outlined">
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Lead ID</TableCell>
                      <TableCell>Date</TableCell>
                      <TableCell>Time Slot</TableCell>
                      <TableCell>Admin</TableCell>
                      <TableCell>Created At</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredBookings.map((booking, index) => (
                      <TableRow key={index}>
                        <TableCell>{booking.patient_id}</TableCell>
                        <TableCell>{formatDate(booking.range_date)}</TableCell>
                        <TableCell>{booking.slot_details}</TableCell>
                        <TableCell>{booking.admin_name}</TableCell>
                        <TableCell>{formatDateTime(booking.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}

            {/* Pagination Controls */}
            {!loading && filteredBookings && filteredBookings.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
                <Typography variant="body2" color="textSecondary">
                  Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                  {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                  {pagination.total} bookings
                </Typography>
                <Pagination
                  count={Math.max(pagination.totalPages, 1)}
                  page={pagination.page}
                  onChange={handlePageChange}
                  color="primary"
                  showFirstButton
                  showLastButton
                  size="medium"
                  disabled={pagination.totalPages <= 1}
                />
              </Box>
            )}
          </CardContent>
        </Card>
      </Box>
    );
  };

export default AdminBookingInterface;
