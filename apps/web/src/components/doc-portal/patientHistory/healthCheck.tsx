import Grid from "@mui/material/Grid2";
import { Divider, Typography } from "@mui/material";
import { usePatient } from "../../../hooks/patient-provider";
import { healthCheckSections } from "../../../utils";
import { HealthCheckShape } from "../../../types";


const HealthCheck: React.FC = () => {
    const { selectedPatientHistory } = usePatient();

    // Cast the selected history to HealthCheckShape
    const healthCheckData = selectedPatientHistory as HealthCheckShape;

    return (
        <>
            {Object.entries(healthCheckSections).map(([_key, section]) => {
                const start = section.start - 1;
                const end = section.end;

                // Make sure form exists and is an array before slicing
                const sectionInputs = healthCheckData?.form?.slice(start, end);

                return (
                    <div key={_key}>
                        <Typography sx={{ fontSize: '20px', fontWeight: '500', color: 'green' }}>
                            {section.title}
                        </Typography>
                        <Divider sx={{ mb: 3 }} />
                        {sectionInputs && sectionInputs.map((input, index) => {
                            return (
                                <Grid container direction={'column'} spacing={1} sx={{ mb: 5 }} key={input.id || index}>
                                    <Typography sx={{ fontSize: '14px' }}>
                                        {start + index + 1}. {input.question}
                                    </Typography>
                                    <Grid sx={{ backgroundColor: '#E9FFD1', p: 1 }}>
                                        <Typography sx={{ fontWeight: 'bold', fontSize: '14px' }}>
                                            {input.answers}
                                        </Typography>
                                    </Grid>
                                </Grid>
                            );
                        })}
                    </div>
                );
            })}
        </>
    );
};

export default HealthCheck;
