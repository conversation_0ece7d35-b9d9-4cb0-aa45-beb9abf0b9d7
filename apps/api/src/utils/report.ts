
import {PatientQueueDetailsD<PERSON>, QueueResult, TimeLine} from '../types'
import { WebClient } from '@slack/web-api';
import config from '../config';

const slack = new WebClient(config.slackToken); // Use environment variable


export const buildSlackBlocks = async (allResult: QueueResult, timelineDetails: TimeLine) => {
  let blocks: {
    type: string;
    text?: {
      type: string;
      text: string;
    };
    fields?: {
      type: string;
      text: string;
    }[];
  }[] = [];

  let countBlock = 0;
  for (const [email, result] of Object.entries(allResult)) {
    countBlock += 1;

    const timeline = timelineDetails[email] || [];
    const hasSummary = Object.keys(result).length > 0;

    const summaryFields: string[] = [];

    // Add only if values exist
    if (result.waitingFromLastOnline) summaryFields.push(`*From Last Online:*\n${result.waitingTime}`);
    if (result.waitingTime) summaryFields.push(`*Waiting Before Admission:*\n${result.waitingFromLastOnline}`);
    if (result.consultationTime) summaryFields.push(`*Consultation Time:*\n${result.consultationTime}`);

    // Header

    // Summary line
    if (hasSummary) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*👤 Patient:* ${email}`,
        },
      });

      blocks.push({
        type: 'section',
        fields: summaryFields.map((f) => ({
          type: 'mrkdwn',
          text: f,
        })),
      });
    } else {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `_No summary data available._`,
        },
      });
    }

    // Divider
    blocks.push({ type: 'divider' });

    // More Details (timeline)
    if (timeline.length > 0) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: '*More Details (Status Timeline)*',
        },
      });

      const timelineBlock = {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: timeline
            .map((entry) => {
              return `*${entry.status}* at \`${entry.updatedAt}\``;
            })
            .join('\n'),
        },
      };

      blocks.push(timelineBlock);
      blocks.push({ type: 'divider' });
    }
  }

  if (countBlock === 10) {
    await slack.chat.postMessage({
      channel: 'C08FW6GA9U4', // Replace with channel ID
      blocks: blocks,
      text: 'TP Report',
    });

    countBlock = 0;
    blocks = [];
  }
  return blocks;
};

// Calculate Waiting Time: First ONLINE to First ADMITTED

export const calculateWaitingTime = (data: PatientQueueDetailsData[]): string | undefined => {
  // Find first ONLINE status
  const firstOnline = data
    .filter((entry) => entry.status === 'ONLINE')
    .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())[0];

  // Find first ADMITTED status
  const firstAdmitted = data
    .filter((entry) => entry.status === 'ADMITTED')
    .sort((a, b) => new Date(a.createdAt!).getTime() - new Date(b.createdAt!).getTime())[0];

  if (!firstOnline || !firstAdmitted) return undefined;

  const onlineTime = new Date(firstOnline.createdAt).getTime();
  const admittedTime = new Date(firstAdmitted.createdAt!).getTime();
  const seconds = (admittedTime - onlineTime) / 1000;

  if (seconds < 0) return undefined;

  return formatSecondsToHHMMSS(seconds);
};

export const calculateWaitingTimeFromLastOnline = (data: PatientQueueDetailsData[]): string | undefined => {
  // Find last ONLINE status
  const lastOnline = data
    .filter((entry) => entry.status === 'ONLINE')
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];

  // Find first ADMITTED status
  const firstAdmitted = data
    .filter((entry) => entry.status === 'ADMITTED')
    .sort((a, b) => new Date(a.createdAt!).getTime() - new Date(b.createdAt!).getTime())[0];

  if (!lastOnline || !firstAdmitted) return undefined;

  const onlineTime = new Date(lastOnline.createdAt).getTime();
  const admittedTime = new Date(firstAdmitted.createdAt!).getTime();
  const seconds = (admittedTime - onlineTime) / 1000;

  // Return undefined if the time difference is negative
  if (seconds < 0) return undefined;

  return formatSecondsToHHMMSS(seconds);
};
// Calculate Consultation Time: Last ADMITTED to COMPLETED
export const calculateConsultationTime = (data: PatientQueueDetailsData[]): string | undefined => {
  // Find last ADMITTED status
  const lastAdmitted = data
    .filter((entry) => entry.status === 'ADMITTED')
    .sort((a, b) => new Date(b.createdAt!).getTime() - new Date(a.createdAt!).getTime())[0];

  // Find COMPLETED status
  const completed = data.find((entry) => entry.status === 'COMPLETED');

  if (!lastAdmitted || !completed) return undefined;

  const admittedTime = new Date(lastAdmitted.createdAt!).getTime();
  const completedTime = new Date(completed.createdAt!).getTime();
  return formatSecondsToHHMMSS((completedTime - admittedTime) / 1000); // Seconds
}

export const formatSecondsToHHMMSS = (seconds: number): string => {
  const hrs = Math.floor(seconds / 3600)
    .toString()
    .padStart(2, '0');
  const mins = Math.floor((seconds % 3600) / 60)
    .toString()
    .padStart(2, '0');
  const secs = Math.floor(seconds % 60)
    .toString()
    .padStart(2, '0');
  return `${hrs}:${mins}:${secs}`;
};