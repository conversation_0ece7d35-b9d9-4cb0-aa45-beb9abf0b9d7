import React, { useState } from "react";
import { Box, <PERSON>Field, Typography, useMedia<PERSON>uery, IconButton, Collapse } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useTheme } from "@mui/material/styles"
import { useEmailContext } from "../../../hooks/email-provider";
import { usePatient } from "../../../hooks/patient-provider";
import ArrowRightIcon from '@mui/icons-material/ArrowRight';

// TODO: Refactoring

const inputStyle = {
    width: '100%',
    '& .MuiInputBase-root': {
        borderRadius: '5px',
        borderColor: 'transparent',
        backgroundColor: 'white',
        fontSize: '14px'
    },
    '& .MuiOutlinedInput-root': {
        '& .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.2)',
        },
        '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'rgba(0, 0, 0, 0.5)',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'rgba(0, 0, 0, 0.5)',
        }
    },
}

const EmailTreatmentPlan: React.FC = () => {
    //const { listTitle, setListTitle, listItemText, setListItemText, otherTreatment, setOtherTreatment } = useEmailContext()
    const { listTitle, setListTitle } = useEmailContext()
    const { patientTreatmentPlan } = usePatient()
    const wordLimit = 240
    const [open, setOpen] = useState(false); // Changed from true to false to start collapsed

    const handleToggle = () => {
        setOpen(!open);
    };

    const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target
        setListTitle((prevValues) => {
            if (value.length <= wordLimit) {
                return {
                    ...prevValues,
                    [name]: value
                }
            }
            return prevValues
        })
    }

    // const handleListItemChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    //     const { name, value } = e.target
    //     setListItemText((prevValues) => {
    //         if (value.length <= wordLimit) {
    //             return {
    //                 ...prevValues,
    //                 [name]: value
    //             }
    //         }
    //         return prevValues
    //     })
    // }

    // const handleOtherTreatmentChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    //     const { name, value } = e.target
    //     setOtherTreatment((prevValues) => {
    //         if (value.length <= wordLimit) {
    //             return {
    //                 ...prevValues,
    //                 [name]: value
    //             }
    //         }
    //         return prevValues
    //     })
    // }

    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

    const emailTemplateValues = () => {
        const activeIngredient = patientTreatmentPlan?.outcome === 'Approve 22% Subject To CBD Trial' ? 'CBD Oil' : patientTreatmentPlan?.outcome ? 'Tetrahydrocannabinol (THC)' : 'None'
        const strength = patientTreatmentPlan?.[22] && patientTreatmentPlan[29] ? '22% and 29%' : patientTreatmentPlan?.[22] ? '22%' : patientTreatmentPlan?.[29] ? '29%' : 'none'

        let quantity;
        let numRepeat;
        let interval;
        let dosePerDay;
        let maxDosePerDay;
        if (patientTreatmentPlan?.[22]) {
            quantity = patientTreatmentPlan?.[22]?.totalQuantity || 'none'
            numRepeat = patientTreatmentPlan?.[22]?.numberOfRepeat || 'none'
            interval = patientTreatmentPlan?.[22]?.supplyInterval || 'none'
            dosePerDay = patientTreatmentPlan?.[22]?.dosePerDay || 'none'
            maxDosePerDay = patientTreatmentPlan?.[22]?.maxDosePerDay || 'none'
        }
        else if (patientTreatmentPlan?.[29]) {
            quantity = patientTreatmentPlan?.[29]?.totalQuantity || 'none'
            numRepeat = patientTreatmentPlan?.[29]?.numberOfRepeat || 'none'
            interval = patientTreatmentPlan?.[29]?.supplyInterval || 'none'
            dosePerDay = patientTreatmentPlan?.[29]?.dosePerDay || 'none'
            maxDosePerDay = patientTreatmentPlan?.[29]?.maxDosePerDay || 'none'
        }

        return {
            activeIngredient,
            strength,
            quantity,
            numRepeat,
            interval,
            dosePerDay,
            maxDosePerDay
        }
    }

    const getNextFollowUp = () => {
        const today = new Date();
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + 28);

        const formattedDate = `${futureDate.getFullYear()}-${String(futureDate.getMonth() + 1).padStart(2, '0')}-${String(futureDate.getDate()).padStart(2, '0')}`;

        return formattedDate
    }

    return (
        <Grid container sx={{ width: '100%', fontSize: '12px' }} direction={'column'}>
            <Grid container alignItems={'center'}>
                <IconButton
                    onClick={handleToggle}
                    sx={{
                        transform: open ? 'rotate(90deg)' : 'rotate(0deg)',
                        width: '20px',
                        height: '20px',
                        mr: 2,
                        transition: 'transform 0.3s'
                    }}
                >
                    <ArrowRightIcon sx={{ color: 'black' }} />
                </IconButton>
                <span style={{ fontWeight: 'bold', fontSize: '24px' }}>Treatment Plan Overview</span>
            </Grid>

            <Collapse in={open} timeout={10}>
                <Grid sx={{ width: '100%', fontSize: '14px', mt: 2 }}>
                    Your treatment plan includes a script for medicinal cannabis as follows: <br /><br />

                    <Box display="inline-flex" alignItems="center" sx={{ whiteSpace: 'nowrap' }}>
                        Active Ingredient: {emailTemplateValues().activeIngredient}
                    </Box> <br /><br />
                    <Box display="inline-flex" alignItems="center" sx={{ whiteSpace: 'nowrap' }}>
                        Strength/Concentration: {emailTemplateValues().strength}
                    </Box>
                    <br /><br />

                    <Box display="inline-flex" sx={{ whiteSpace: 'nowrap' }}>
                        Quantity Prescribed (g): {emailTemplateValues().quantity} (g) <br /><br />
                    </Box>

                    <Box>

                    </Box>
                    Number of Repeats: {emailTemplateValues().numRepeat} <br /><br />

                    Repeat Interval (days): {emailTemplateValues().interval} <br /><br />

                    When and how often to use: {emailTemplateValues().dosePerDay} (g) daily max {emailTemplateValues().maxDosePerDay} (g) <br /><br />

                    When to stop using: <br />

                    <ul style={{ listStyleType: 'disc', marginLeft: isDesktop ? '10px' : '0px' }}>
                        <li>
                            <TextField
                                multiline
                                fullWidth
                                rows={1}
                                value={listTitle.title1}
                                onChange={handleTitleChange}
                                name='title1'
                                sx={{ ...inputStyle }}
                            />
                        </li>
                        <li>
                            <TextField
                                multiline
                                fullWidth
                                rows={1}
                                value={listTitle.title2}
                                onChange={handleTitleChange}
                                name='title2'
                                sx={{ ...inputStyle }}
                            />
                        </li>
                        <li>
                            <TextField
                                multiline
                                fullWidth
                                rows={1}
                                value={listTitle.title3}
                                onChange={handleTitleChange}
                                name='title3'
                                sx={{ ...inputStyle }}
                            />
                        </li>
                    </ul>

                    <Typography sx={{ mt: 4, mb: 2 }}>
                        Follow-up appointment: {getNextFollowUp()}
                    </Typography>
                </Grid>
            </Collapse>
        </Grid>
    );
};

export default EmailTreatmentPlan