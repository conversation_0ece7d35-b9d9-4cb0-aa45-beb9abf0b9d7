/**
 * Type definitions for Zoho consultation scheduling structure fix
 */

export interface IncompleteConsultation {
  consultation_id: string;
  patientID: string;
  consultationDate: string;
  drId: string | null;
  queueTag: string;
  zohoID: string | null;
  fullName: string;
  email: string;
  issue_type: 'Missing zohoID' | 'Missing doctor assignment' | 'Missing PatientSlot' | 'Missing Range for date' | 'Range missing doctor';
}

export interface DoctorAssignment {
  doctorId: string;
  doctorName: string;
  accessID: string;
  consultations: IncompleteConsultation[];
}

export interface ConsultationAnalysis {
  totalIncomplete: number;
  detectedInterval: number;
  consultationClusters: IncompleteConsultation[][];
  issueBreakdown: {
    missingZohoID: number;
    missingPatientSlot: number;
    missingRange: number;
    missingDoctor: number;
  };
}

export interface CreatedRange {
  id: string;
  day: string;
  date: string;
  start: string;
  end: string;
  interval: number;
  availability: number;
  status: string;
  doctorID: string;
  consultations: IncompleteConsultation[];
}

export interface CreatedSlot {
  id: number;
  range_id: string;
  slot: string;
  remaining: number;
  noShowRemaining: number;
  consultations?: IncompleteConsultation[];
}

export interface CreatedPatientSlot {
  patient_id: string; // This is Patient.zohoID
  range_id: string;
  slot_id: number;
  patientID: string; // This is the actual patientID for reference
}

export interface FixResult {
  success: boolean;
  message: string;
  summary: {
    totalConsultationsProcessed: number;
    consultationsFixed: number;
    rangesCreated: number;
    slotsCreated: number;
    patientSlotsCreated: number;
    doctorAssignments: {
      [doctorId: string]: {
        doctorName: string;
        consultationsAssigned: number;
        rangesCreated: number;
      };
    };
  };
  errors?: string[];
  warnings?: string[];
  executionTimeMs: number;
}

export interface TargetDoctor {
  id: string;
  accessID: string;
  name: string;
  username: string;
  email: string;
  exists: boolean;
}

export interface ZohoConsultationManagerConfig {
  targetDoctors: {
    anjum: string; // Doctor ID
    lavett: string; // Doctor ID
  };
  defaultSlotDuration: number;
  defaultAvailability: number;
  rangeStartTime: string; // e.g., "09:00"
  rangeEndTime: string; // e.g., "17:00"
  // Additional properties for slot creation
  defaultSlotAvailability: number;
  defaultNoShowRemaining: number;
}

export interface ExistingRange {
  id: string;
  day: string;
  date: string;
  start: string;
  end: string;
  interval: number;
  availability: number;
  status: string;
  doctorID: string;
  createdAt: Date;
  updatedAt: Date;
  startMinutes: number;
  endMinutes: number;
}

export interface QueryResult {
  rows: Record<string, unknown>[];
}

export interface ExistingPatientSlot {
  id: number;
  patient_id: string;
  range_id: string;
  slot_id: number;
  createdAt: Date;
  updatedAt: Date;
  slot_time: string;
}

export interface SlotDbRow {
  id: number;
  range_id: string;
  slot: string;
  remaining: number;
  noShowRemaining: number;
  createdAt: Date;
  updatedAt: Date;
}
