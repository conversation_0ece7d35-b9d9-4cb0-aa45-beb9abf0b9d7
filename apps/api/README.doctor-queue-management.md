# Doctor Queue Management System

This system manages the assignment and notification of patients to doctors based on pre-booked appointments, ensuring that doctors only see and notify their own patients.

## Features

- **Doctor-Specific Patient Assignment**: Patients are pre-assigned to doctors during booking through the patientslot and range tables
- **Doctor-Specific Queues**: Each doctor only sees their own patients in the online and joined queues
- **Automatic Notifications**: Doctors can automatically notify their assigned patients when they're ready
- **Dynamic Patient Limits**: Calculates how many patients each doctor can notify based on their assigned workload
- **Seamless Integration**: Works with existing patient notification and queue management systems
- **Admin Controls**: Administrators can manually assign and notify patients while respecting doctor assignments

## System Components

### Core Functionality

1. **Patient Assignment**: Patients are assigned to doctors during booking via the patientslot and range tables
2. **Queue Filtering**: Online and joined queues are filtered by doctor assignment
3. **Notification System**: Doctors notify only their assigned patients
4. **Dynamic Limits**: Patient notification limits are calculated based on each doctor's assigned workload

### Database Relationships

- **Patient → patientslot**: Patient's zohoID links to patientslot.patient_id
- **patientslot → range**: patientslot.range_id links to range.id
- **range → doctor**: range.doctorID links to dr.id
- **PatientQueue**: Tracks patient status and doctor assignment

## Implementation Details

### Patient Assignment Flow

1. When a patient books an appointment, they're assigned to a doctor through:

   - An entry in the `patientslot` table linking their zohoID to a range_id
   - The `range` table links the range_id to a specific doctor's ID

2. When a doctor starts their shift:
   - They only see patients assigned to them in the online and joined queues
   - They can only notify patients assigned to them
   - The system calculates how many patients they can notify based on their assigned workload

### Doctor Queue Management

The system maintains two key relationships:

1. **Database Assignment**: Patient is assigned to a doctor through patientslot → range → doctor
2. **Queue Assignment**: Patient is assigned to a doctor in the PatientQueue table (assignedDoctorID field)

These relationships are kept in sync to ensure consistent patient assignment.

## Key Functions

### Patient Notification

- `notifyNextPatientMiddleWare`: Notifies the next patient for a specific doctor
- `getNextPatientsForDoctor`: Gets the next batch of patients to notify for a specific doctor
- `canDoctorNotifyMorePatients`: Determines if a doctor can notify more patients based on their dynamic limit
- `calculateDynamicPatientLimit`: Calculates the number of patients a doctor can notify based on their assigned workload

### Queue Management

- `OnlineQueue`: Filters online patients by their assigned doctor
- `JoinedQueue`: Filters joined patients by their assigned doctor
- `sendPatientToDoctor`: Assigns and notifies a patient to their pre-assigned doctor
- `assignPatientToDoctor`: Updates PatientQueue with assigned doctor information

### ID Mapping

- `getDoctorIdFromAccessId`: Converts between doctor accessID (used in PatientQueue) and ID (used in range table)

## Database Schema

### Key Tables and Relationships

1. **Patient Table**

   - `patientID`: Primary identifier for patients
   - `zohoID`: External identifier used to link to patientslot

2. **patientslot Table**

   - `patient_id`: Links to Patient.zohoID
   - `range_id`: Links to range.id
   - `slot_id`: Identifies the specific time slot

3. **range Table**

   - `id`: Primary identifier for ranges
   - `doctorID`: Links to dr.id (UUID type)
   - Other fields for time range details

4. **dr Table**

   - `id`: Primary identifier (UUID type)
   - `accessID`: External identifier used in PatientQueue

5. **PatientQueue Table**

   - `patientID`: Links to Patient.patientID
   - `assignedDoctorID`: Links to dr.accessID (not dr.id)
   - Status fields for patient queue management

6. **Consultation Table**
   - `patientID`: Links to Patient.patientID
   - `consultationDate`: Date and time of consultation
   - Notification and status fields

## API Endpoints

### Patient Status Management

```
PUT /api/patient/online/:id
```

- **Purpose**: Mark a patient as online and assign to their pre-booked doctor
- **Response**: Updated patient queue entry

```
PUT /api/patient/away/:id
```

- **Purpose**: Mark a patient as away while preserving doctor assignment
- **Response**: Updated patient queue entry

```
PUT /api/patient/joined/:id
```

- **Purpose**: Mark a patient as joined while preserving doctor assignment
- **Response**: Updated patient queue entry

### Doctor Queue Management

```
POST /api/doctor/timer
```

- **Purpose**: Start a doctor's timer and begin notifying their assigned patients
- **Response**: List of online and joined patients assigned to this doctor

```
POST /api/doctor/notify
```

- **Purpose**: Manually notify the next patient assigned to a specific doctor
- **Request Body**: `{ doctorId: string }`
- **Response**: Notification status

```
POST /api/doctor/send-patient
```

- **Purpose**: Send a patient to their pre-assigned doctor
- **Request Body**: `{ patientID: string }`
- **Response**: Updated patient queue entry

## Testing

### Manual Testing

1. **Test doctor assignment**:

   ```bash
   # Check if a patient is assigned to the correct doctor
   SELECT p."patientID", p."fullName", d.username as doctor_name
   FROM Patient p
   JOIN patientslot ps ON p."zohoID" = ps.patient_id
   JOIN range r ON ps.range_id = r.id
   JOIN dr d ON r."doctorID" = d.id
   WHERE p."patientID" = '18106';
   ```

2. **Test online queue filtering**:
   ```bash
   # Check if a doctor only sees their assigned patients
   SELECT p."patientID", p."fullName"
   FROM PatientQueue pq
   JOIN Patient p ON pq."patientID" = p."patientID"
   WHERE pq.status = 'ONLINE'
   AND pq."assignedDoctorID" = '8195c09b-5670-4cef-a399-947ea8ed7d8f';
   ```

## Troubleshooting

### Common Issues

1. **Patient not appearing in doctor's queue**:

   - Check if patient has a patientslot entry: `SELECT * FROM patientslot WHERE patient_id = '[zohoID]';`
   - Verify range assignment: `SELECT * FROM range WHERE id = '[range_id]';`
   - Confirm doctor ID mapping: `SELECT id, "accessID" FROM dr WHERE "accessID" = '[accessID]';`

2. **Doctor seeing wrong patients**:

   - Verify PatientQueue assignments: `SELECT * FROM PatientQueue WHERE "assignedDoctorID" = '[accessID]';`
   - Check for consultation date issues: `SELECT * FROM Consultation WHERE "patientID" = '[patientID]';`

3. **ID mapping issues**:
   - Remember that range.doctorID links to dr.id (UUID type)
   - PatientQueue.assignedDoctorID links to dr.accessID (string type)
   - Use getDoctorIdFromAccessId function to convert between them

## Migration Notes

If migrating from the previous system where patients were dynamically assigned to doctors:

1. Ensure all patients have entries in the patientslot table
2. Verify that ranges are properly linked to doctors
3. Update PatientQueue entries to match the doctor assignments from patientslot and range

## Architecture Benefits

This doctor-specific queue management approach provides:

- **Improved Patient Experience**: Patients are consistently handled by their assigned doctor
- **Better Workload Distribution**: Doctors only manage their own patients
- **Reduced Confusion**: Clear assignment of responsibilities
- **Streamlined Communication**: Doctors know which patients to expect
- **Consistent Patient Journey**: From booking to consultation with the same doctor
