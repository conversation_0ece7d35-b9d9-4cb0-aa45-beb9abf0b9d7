import { S3Client } from '@aws-sdk/client-s3';
import config from './index';
import { logger } from './logger';

/**
 * AWS S3 Client configuration for file storage
 * Used for uploading sales report images and other files to S3 bucket
 */
const bucketClient = new S3Client({
  region: config.awsRegion,
  credentials: {
    accessKeyId: config.awsAccessKeyId,
    secretAccessKey: config.awsSecretAccessKey,
  },
});

// Log S3 configuration (without sensitive data)
logger.info('S3 Client initialized', {
  region: config.awsRegion,
  bucketName: config.awsBucketName,
  hasCredentials: !!(config.awsAccessKeyId && config.awsSecretAccessKey),
});

export default bucketClient;
