# THC Increase Questionnaire API Endpoints Specification

## Overview
This document specifies the backend API endpoints needed to support the THC increase questionnaire functionality. The frontend is already implemented and expects these endpoints to be available.

## Database Table
The `thc_increase_questionnaire` table should be created using the provided migration script (`database_migration_thc_questionnaire.sql`).

## Required API Endpoints

### 1. Submit THC Increase Questionnaire
**Endpoint:** `POST /funnel/v1.0/patient/thc-increase-questionnaire`

**Purpose:** Submit a completed THC increase questionnaire with scoring

**Authentication:** Required (cookie-based, following existing patterns)

**Request Body:**
```json
{
  "questionsAndAnswers": [
    {
      "questionKey": "consistency",
      "questionText": "How often did you use 22% THC flower?",
      "answerValue": "every-day",
      "answerText": "Every day",
      "score": 3
    },
    {
      "questionKey": "dosage",
      "questionText": "What was your typical dosage per session?",
      "answerValue": "more-than-2g",
      "answerText": "More than 2g",
      "score": 4
    },
    {
      "questionKey": "effectiveness",
      "questionText": "On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?",
      "answerValue": "9-10",
      "answerText": "9-10 (Extremely effective)",
      "score": 4
    }
  ],
  "totalScore": 58,
  "maxScore": 61,
  "isEligible": true,
  "submittedAt": "2025-01-07T10:30:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "THC increase questionnaire submitted successfully",
  "data": {
    "id": "uuid-here",
    "totalScore": 58,
    "isEligible": true,
    "status": "submitted"
  }
}
```

**Implementation Notes:**
- Extract user email from authentication session/cookie
- Look up patient_id from the `patient` table using email
- Store the form data in the `questionnaire_data` JSONB column
- Set `total_score`, `is_eligible`, and `status` fields
- Include IP address and user agent in metadata
- Return success response with questionnaire ID and eligibility status

### 2. Get THC Questionnaire Status
**Endpoint:** `GET /funnel/v1.0/patient/thc-increase-questionnaire/status?email={email}`

**Purpose:** Check if user has completed the THC questionnaire and get their status

**Authentication:** Required (cookie-based)

**Query Parameters:**
- `email` (required): Patient email address

**Response (Completed):**
```json
{
  "success": true,
  "questionnaire": {
    "id": "uuid-here",
    "totalScore": 58,
    "maxScore": 61,
    "isEligible": true,
    "status": "submitted",
    "submittedAt": "2025-01-07T10:30:00Z",
    "reviewedAt": null,
    "approvedAt": null
  }
}
```

**Response (Not Completed):**
```json
{
  "success": false,
  "message": "No THC increase questionnaire found for this patient"
}
```

**Implementation Notes:**
- Query the `thc_increase_questionnaire` table by email
- Return the most recent submission if multiple exist
- Include all relevant status and scoring information

## Database Operations

### Insert New Questionnaire
```sql
INSERT INTO thc_increase_questionnaire (
    patient_id, 
    email, 
    zoho_id,
    questionnaire_data, 
    total_score, 
    max_score, 
    is_eligible,
    status
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, 'submitted'
);
```

### Query Questionnaire Status
```sql
SELECT 
    id,
    total_score,
    max_score,
    is_eligible,
    status,
    created_at as submitted_at,
    reviewed_at,
    approved_at
FROM thc_increase_questionnaire 
WHERE email = $1 
ORDER BY created_at DESC 
LIMIT 1;
```

## Error Handling

### Common Error Responses
```json
{
  "success": false,
  "error": "Authentication required",
  "code": 401
}
```

```json
{
  "success": false,
  "error": "Invalid questionnaire data",
  "code": 400
}
```

```json
{
  "success": false,
  "error": "Patient not found",
  "code": 404
}
```

## Security Considerations
- Validate all input data before storing
- Ensure user can only access their own questionnaire data
- Log all questionnaire submissions for audit purposes
- Rate limit submissions to prevent abuse

## Integration Points
- **Patient Authentication:** Use existing cookie-based auth system
- **Patient Lookup:** Query `patient` table to get `patientID` and `zohoID`
- **Logging:** Follow existing logging patterns for API requests
- **Error Handling:** Use consistent error response format

## Testing
- Test with valid questionnaire data
- Test with invalid/missing data
- Test authentication requirements
- Test duplicate submissions (should update existing or create new?)
- Test edge cases (missing patient, invalid email, etc.)
