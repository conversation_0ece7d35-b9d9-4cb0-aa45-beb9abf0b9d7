import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Tooltip,
  Alert,
  Pagination
} from '@mui/material';
import {
  TrendingUp as IncreaseIcon,
  Schedule as ExtendIcon,
  Add as AddIcon,
  ExpandMore as QuantityIcon,
  Block as NotEligibleIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import adminRequestsService, { AdminRequest, PaginationInfo } from '../../../services/admin-requests.service';
import RequestDetailsModal from './RequestDetailsModal';

interface NonEligibleRequestsTabProps {
  onShowSnackbar: (message: string, severity: 'success' | 'error' | 'info') => void;
}

const NonEligibleRequestsTab: React.FC<NonEligibleRequestsTabProps> = ({ onShowSnackbar }) => {
  const [requests, setRequests] = useState<AdminRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<AdminRequest | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  useEffect(() => {
    loadNonEligibleRequests();
  }, []);

  const loadNonEligibleRequests = async (page: number = pagination.page) => {
    try {
      setLoading(true);
      const response = await adminRequestsService.getNonEligibleRequests(pagination.limit, page);
      setRequests(response.requests || []);
      setPagination(response.pagination || {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      });
    } catch (error) {
      console.error('Error loading non-eligible requests:', error);
      onShowSnackbar('Failed to load non-eligible requests', 'error');
      setRequests([]);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    loadNonEligibleRequests(page);
  };

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .toFormat('dd/MM/yyyy HH:mm');
  };

  const getRequestTypeIcon = (type: string) => {
    switch (type) {
      case 'thc_increase':
        return <IncreaseIcon sx={{ color: '#ff9800' }} />;
      case 'extend_tp':
        return <ExtendIcon sx={{ color: '#2196f3' }} />;
      case 'add_22_thc':
        return <AddIcon sx={{ color: '#4caf50' }} />;
      case 'quantity_increase':
        return <QuantityIcon sx={{ color: '#9c27b0' }} />;
      default:
        return <NotEligibleIcon sx={{ color: '#757575' }} />;
    }
  };

  const getRequestTypeLabel = (type: string) => {
    switch (type) {
      case 'thc_increase':
        return 'THC Increase';
      case 'extend_tp':
        return 'Extend TP';
      case 'add_22_thc':
        return 'Add 22% THC';
      case 'quantity_increase':
        return 'Quantity Increase';
      default:
        return 'Unknown Request';
    }
  };

  const getRiskScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 70) return '#4caf50'; // Green
    if (percentage >= 40) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  const getQuestionsSummary = (questionnaireData: any) => {
    if (!questionnaireData) return null;

    // Parse questionnaire data if it's a string
    const data = typeof questionnaireData === 'string' ?
      JSON.parse(questionnaireData) : questionnaireData;

    // Handle different data structures
    const responses = Array.isArray(data) ? data : (data?.responses || []);

    if (responses.length === 0) return null;

    const scoredQuestions = responses.filter((r: any) => r.score > 0).length;
    const totalQuestions = responses.length;

    return { scoredQuestions, totalQuestions };
  };

  const getEligibilityReason = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage < 40) {
      return 'Score too low (< 40%)';
    }
    return 'Does not meet eligibility criteria';
  };

  const handleViewDetails = (request: AdminRequest) => {
    setSelectedRequest(request);
    setDetailsModalOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsModalOpen(false);
    setSelectedRequest(null);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (requests.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="h6" color="textSecondary">
          No non-eligible requests
        </Typography>
        <Typography variant="body2" color="textSecondary">
          All submitted requests meet eligibility criteria
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        <Typography variant="h6" gutterBottom>
          Non-Eligible Treatment Plan Requests ({requests.length})
        </Typography>
        <Alert severity="info" sx={{ mb: 2 }}>
          These requests do not meet the eligibility criteria and require manual review or patient follow-up.
        </Alert>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Patient</TableCell>
              <TableCell>Request Type</TableCell>
              <TableCell>Risk Score</TableCell>
              <TableCell>Questions Summary</TableCell>
              <TableCell>Submitted</TableCell>
              <TableCell>Eligibility Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {requests.map((request) => (
              <TableRow key={request.id} hover sx={{ backgroundColor: '#fafafa' }}>
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="bold">
                      {request.patient_name}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      ID: {request.patient_id}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getRequestTypeIcon(request.type)}
                    <Typography variant="body2">
                      {getRequestTypeLabel(request.type)}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={`${request.total_score}/${request.max_score}`}
                    size="small"
                    sx={{
                      backgroundColor: getRiskScoreColor(request.total_score, request.max_score),
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  />
                </TableCell>
                <TableCell>
                  {(() => {
                    const summary = getQuestionsSummary(request.questionnaire_data);
                    return summary ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={`${summary.scoredQuestions}/${summary.totalQuestions} scored`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      </Box>
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        No data
                      </Typography>
                    );
                  })()}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatTimestamp(request.created_at)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Tooltip title={getEligibilityReason(request.total_score, request.max_score)}>
                    <Chip
                      label="Not Eligible"
                      size="small"
                      color="error"
                      variant="outlined"
                      icon={<NotEligibleIcon />}
                    />
                  </Tooltip>
                </TableCell>
                <TableCell>
                  <Button
                    size="small"
                    startIcon={<ViewIcon />}
                    variant="outlined"
                    sx={{ minWidth: 100 }}
                    onClick={() => handleViewDetails(request)}
                  >
                    View Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination Controls */}
      {!loading && requests.length > 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
          <Typography variant="body2" color="textSecondary">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} non-eligible requests
          </Typography>
          <Pagination
            count={Math.max(pagination.totalPages, 1)}
            page={pagination.page}
            onChange={handlePageChange}
            color="primary"
            showFirstButton
            showLastButton
            size="medium"
            disabled={pagination.totalPages <= 1}
          />
        </Box>
      )}

      {/* Request Details Modal */}
      <RequestDetailsModal
        open={detailsModalOpen}
        request={selectedRequest}
        onClose={handleCloseDetails}
      />
    </Box>
  );
};

export default NonEligibleRequestsTab;
