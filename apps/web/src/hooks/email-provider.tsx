import React, { createContext, useContext, useState } from "react";
import { ListItemsAdvice, ListItemTextTpProps, ListTitleTpProps, OtherTreatmentTpProps } from '../types';

type EmailContextType = {
  checkedSativa: string[];
  setCheckedSativa: React.Dispatch<React.SetStateAction<string[]>>;
  checkedIndica: string[];
  setCheckedIndica: React.Dispatch<React.SetStateAction<string[]>>;
  checkedHybrid: string[];
  setCheckedHybrid: React.Dispatch<React.SetStateAction<string[]>>;
  listTitle: ListTitleTpProps;
  setListTitle: React.Dispatch<React.SetStateAction<ListTitleTpProps>>;
  listItemText: ListItemTextTpProps;
  setListItemText: React.Dispatch<React.SetStateAction<ListItemTextTpProps>>;
  otherTreatment: OtherTreatmentTpProps;
  setOtherTreatment: React.Dispatch<React.SetStateAction<OtherTreatmentTpProps>>;
  introMessage: { intro: string; conclusion: string };
  setIntroMessage: React.Dispatch<React.SetStateAction<{ intro: string; conclusion: string }>>;
  listValuesSativa: ListItemsAdvice;
  setListValuesSativa: React.Dispatch<React.SetStateAction<ListItemsAdvice>>
  listValuesIndica: ListItemsAdvice
  setListValuesIndica: React.Dispatch<React.SetStateAction<ListItemsAdvice>>
  listValuesHybrid: ListItemsAdvice;
  setListValuesHybrid: React.Dispatch<React.SetStateAction<ListItemsAdvice>>
};

// Create the context with a default value
const EmailContext = createContext<EmailContextType | undefined>(undefined);

export const EmailProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {

  // Strain Advice
  const [checkedSativa, setCheckedSativa] = useState<string[]>([]);
  const [checkedIndica, setCheckedIndica] = useState<string[]>([]);
  const [checkedHybrid, setCheckedHybrid] = useState<string[]>([]);

  // TP
  const [listTitle, setListTitle] = useState<ListTitleTpProps>({
    title1: "Signs of Psychosis or Severe Mental Health Changes",
    title2: "Severe Side Effects",
    title3: "Worsening of a Pre-existing Mental Health Condition",
  });
  const [listItemText, setListItemText] = useState<ListItemTextTpProps>({
    item1: 'If you experience symptoms like hallucinations, delusions, paranoia, or extreme mood swings, it could indicate psychosis. These symptoms require you to stop using medicinal cannabis right away and contact your doctor for an assessment.',
    item2: 'If you develop severe dizziness, confusion, chest pain, or difficulty breathing, stop using medicinal cannabis and seek medical attention immediately. These reactions may indicate that the treatment is not suitable for you.',
    item3: 'If you have a history of mental health conditions like anxiety, depression, or bipolar disorder and notice a sudden worsening of these symptoms, it’s important to stop using medicinal cannabis and inform your doctor as soon as possible'
  });
  const [otherTreatment, setOtherTreatment] = useState<OtherTreatmentTpProps>({
    otreat1: 'Lifestyle changes (exercise, diet, sleep hygiene, stress management)',
    otreat2: 'Physical therapy',
    otreat3: 'Cognitive Behavioral Therapy (CBT)',
    otreat4: 'Over-the-counter medications (paracetamol, ibuprofen)',
    otreat5: 'Prescription medications (antidepressants, anti-anxiety meds, pain relievers)',
    otreat6: 'Acupuncture',
    otreat7: 'Herbal supplements (turmeric, valerian root)',
    otreat8: 'Specialist care (pain management, rheumatologist)'
  });

  // Introduction
  const [introMessage, setIntroMessage] = useState({
    intro: `As discussed, your Treatment Plan has now been approved. Below, you'll find all the necessary details, including the steps to obtain your treatment`,
    conclusion: `You now have access to the Harvest Members Only shop, where you can purchase the therapeutic products outlined in your Treatment Plan. Please note that only the products included in your plan will be available for purchase.`,
  });

  const [listValuesSativa, setListValuesSativa] = useState<ListItemsAdvice>({
    option1: 'Avoid Sativa Strains Before Bedtime. These products can disturb sleep. Use calming strains instead.',
    option2: 'Do Not Use Sativa Before Driving. These strains can impair reaction time and judgment.',
    option3: 'Limit Sativa Use for Deep Focus Tasks. Mental stimulation may hinder calm concentration.',
    option4: 'If You Have Anxiety, Avoid Sativa. These products can heighten anxiety and panic attacks.',
    option5: 'Avoid Sativa If You Have Stomach Issues. These strains may worsen nausea or sensitivity.',
    option6: 'Other'
  });
  const [listValuesIndica, setListValuesIndica] = useState<ListItemsAdvice>({
    option1: 'Avoid Indica During the Day If You Need to Be Alert. Its sedative effects can cause drowsiness.',
    option2: 'Avoid Indica When Feeling Depressed. It may increase lethargy and heaviness.',
    option3: 'Avoid Indica If Prone to Low Blood Pressure. It may cause dizziness or light-headedness.',
    option4: 'Be Cautious with Indica at Social Events. It can make you withdrawn or overly relaxed.',
    option5: 'Do Not Use Indica If You Need Energy. Its relaxing effects can lower motivation.',
    option6: 'Other'
  });

  const [listValuesHybrid, setListValuesHybrid] = useState<ListItemsAdvice>({
    option1: 'Use Hybrids Carefully for Focus. Effects vary; some strains help focus, others cloud the mind.',
    option2: 'Avoid Hybrids When Quick Reflexes Are Needed. These strains may impair your response time.',
    option3: 'Do Not Rely on Hybrids for Predictable Effects. Their balance can vary and affect mood unpredictably.',
    option4: 'Avoid Hybrids If Prone to Anxiety. Stimulating effects may increase anxiety or panic.',
    option5: 'Limit Hybrids Before Physical Activity. Effects depend on strain; some may relax or reduce energy.',
    option6: 'Other'
  });

  const contextValue: EmailContextType = {
    checkedSativa,
    setCheckedSativa,
    checkedIndica,
    setCheckedIndica,
    checkedHybrid,
    setCheckedHybrid,
    listTitle,
    setListTitle,
    listItemText,
    setListItemText,
    otherTreatment,
    setOtherTreatment,
    introMessage,
    setIntroMessage,
    listValuesSativa,
    setListValuesSativa,
    listValuesHybrid,
    setListValuesHybrid,
    listValuesIndica,
    setListValuesIndica
  };

  return <EmailContext.Provider value={contextValue}>{children}</EmailContext.Provider>;
};

// Custom hook for easier access to context
export const useEmailContext = () => {
  const context = useContext(EmailContext);
  if (context === undefined) {
    throw new Error("useEmailContext must be used within an EmailProvider");
  }
  return context;
};
