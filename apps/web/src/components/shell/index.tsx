import { Outlet, useLocation } from "@tanstack/react-location"
import ErrorBoundary from "../error/errorBoundary"
import NavigationBar from "../navigationBar"
import { Box, Toolbar } from "@mui/material"
import RequestsWindow from '../doc-portal/requests'
import { useAuth } from "../../hooks/auth-provider"
import { useHasRequests } from "../../hooks/useHasRequests"

const Shell = () => {

    const location = useLocation()
    const { doctor } = useAuth()
    const { hasRequests, loading: requestsLoading } = useHasRequests()

    // Only show requests window for doctors (not on login page) and when they have requests
    // Show during loading to avoid flickering, hide only when we confirm no requests
    const isLoginPage = location.current.pathname === '/login'
    const shouldShowRequests = !isLoginPage && doctor?.role === 'doctor' && (requestsLoading || hasRequests)

    return isLoginPage ? (
        <ErrorBoundary>
            <Outlet />
        </ErrorBoundary>
    ) : (
        <ErrorBoundary>
            <Box sx={{ display: 'flex' }}>
                <NavigationBar />
                <Box
                    component="main"
                    sx={{
                        p: 2,
                        width: '100%'
                    }}
                >
                    <Toolbar />
                    <Outlet />
                </Box>
                {shouldShowRequests && (
                <Box sx={{ width: 500, minWidth: 400, borderLeft: '1px solid #eee', height: '100vh', bgcolor: '#fafbfc', display: { xs: 'none', md: 'block' }, position: 'relative' }}>
                    <Toolbar />
                    <Box sx={{ height: 'calc(100vh - 64px)', overflowY: 'auto', pl: 2, pr: 1 }}>
                        <RequestsWindow />
                    </Box>
                </Box>
                 )}
            </Box>
        </ErrorBoundary>
    )
}

export default Shell