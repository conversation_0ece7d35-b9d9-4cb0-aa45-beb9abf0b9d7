import dotenv from 'dotenv';
// import path from 'path';

// dotenv.config({ path: path.join(`.env.${process.env.NODE_ENV || 'development'}`) });
dotenv.config();

export default {
  env: process.env.NODE_ENV ?? 'development',
  host: process.env.HOST ?? 'localhost',
  port: process.env.PORT ?? '5000',
  dbUser: process.env.DB_USER ?? 'docui',
  dbHost: process.env.DB_HOST ?? 'localhost',
  dbName: process.env.DB_NAME ?? 'docui',
  dbPassword: process.env.PASSWORD ?? 'docui',
  zohoClient: process.env.CLIENT_ID ?? '',
  zohoSecret: process.env.CLIENT_SECRET ?? '',
  zohoRefreshToken: process.env.REFRESH_TOKEN ?? '',
  zohoInventoryRefreshToken: process.env.INVENTORY_REFRESH_TOKEN ?? '',
  zohoBooksClient: process.env.BOOKS_CLIENT_ID ?? '',
  zohoBooksSecret: process.env.BOOKS_CLIENT_SECRET ?? '',
  zohoBooksRefreshToken: process.env.BOOKS_REFRESH_TOKEN ?? '',

  zohoClientDev: process.env.CLIENT_ID_DEV ?? '',
  zohoSecretDev: process.env.CLIENT_SECRET_DEV ?? '',
  zohoRefreshTokenDev: process.env.REFRESH_TOKEN_DEV ?? '',
  zenithEmail: process.env.ZENITH_EMAIL ?? '',
  notifyTestxOnly: process.env.NOTIFY_TESTX_ONLY === 'true' ? true : false,
  devMode: process.env.DEV_MODE === 'true' ? true : false,
  secureFunnelCookie: process.env.NODE_ENV === 'production' ? true : false,

  funnelZenithConfirmUrl: process.env.FUNNEL_ZENITH_CONFIRM_URL ?? '',

  wpApiUrl: process.env.WP_API_URL ?? '',
  wpUsername: process.env.WP_USERNAME ?? '',
  wpPassword: process.env.WP_PASSWORD ?? '',

  autodialerApiUrl: process.env.AUTODIALER_API_URL ?? '',
  // Open Ai Key
  openAiKey: process.env.OPENAI_API_KEY ?? '',
  stipeTestApiKey:
    'sk_test_51PntzyP2Dwh932qzib9Sc57PlhRSLv8ICJKx7r7CEB2jYoJgQuueNfSgHfUgrnM9c4vuJk8RFjawY7xqTuMbLDKK00rZ4TLyK8',
  stripeApiKey: process.env.STRIPE_API_KEY ?? '',

  // Stripe API Sync Configuration
  stripeSync: {
    enabled: process.env.STRIPE_SYNC_ENABLED === 'true',
    apiVersion: process.env.STRIPE_API_VERSION ?? '2025-06-30.basil',
    maxRetries: parseInt(process.env.STRIPE_MAX_RETRIES ?? '3', 10),
    timeout: parseInt(process.env.STRIPE_TIMEOUT ?? '10000', 10),
    batchSize: parseInt(process.env.STRIPE_SYNC_BATCH_SIZE ?? '100', 10),
    rateLimitDelay: parseInt(process.env.STRIPE_RATE_LIMIT_DELAY ?? '100', 10),
  },

  // Stream Chat configuration
  streamChat: {
    apiKey: process.env.STREAM_API_KEY ?? '7v5w8h2zr4ee',
    apiSecret: process.env.STREAM_API_SECRET ?? 'kf9wyxtgpxa68cvc2cnj7feppmq4ge42cyk6fs67z8ws8cdy6gm99bkakp7afajv',
  },

  slackToken: process.env.SLACK_TOKEN ?? '',
  slackEventToken: process.env.SLACK_EVENT_TOKEN ?? '',
  slackReportChannel: process.env.SLACK_REPORT_CHANNEL ?? '',
  slackEventReportingChannel: process.env.SLACK_EVENT_REPORTING_CHANNEL ?? '',
  slackAvailabilityReportChannel: process.env.SLACK_AVAILABILITY_REPORT_CHANNEL ?? '',
  slackSalesReportChannel: process.env.SLACK_SALES_REPORT_CHANNEL ?? '',
  slackMessengerReportChannel: process.env.SLACK_MESSENGER_REPORT_CHANNEL ?? '',

  // Sales reporting configuration
  salesReportingEnabled: process.env.SALES_REPORTING_ENABLED === 'true',
  salesDataRetentionDays: parseInt(process.env.SALES_DATA_RETENTION_DAYS ?? '365', 10),

  googleSheet:
    process.env.GOOGLE_SHEET ??
    'https://docs.google.com/spreadsheets/d/1SIqlPwnkjxmK54nISH5rxkpWkklm6MQtLr_xblnGsuw/edit#gid=',
  googleWeeklyReportChannel: process.env.SLACK_WEEKLY_REPORT_CHANNEL ?? 'C08RK7XCX28',

  // Zelda Test Patient System Configuration
  zelda: {
    enabled: process.env.ENABLE_ZELDA_SYSTEM === 'true',
    patientEmail: process.env.ZELDA_TEST_PATIENT_EMAIL ?? '<EMAIL>',
    excludedDoctorEmails: process.env.EXCLUDE_DOCTORS_FROM_ZELDA?.split(',').map((email) => email.trim()) ?? [],
    consultationLeadTimeMinutes: parseInt(process.env.ZELDA_CONSULTATION_LEAD_TIME ?? '40', 10),
  },
  softphoneProEmail: process.env.SOFT_PHONE_ACCOUNT ?? '',
  softphoneProPassword: process.env.SOFT_PHONE_ACCOUNT_PASSWORD ?? '',
  softphoneProURL: process.env.SOFT_PHONE_ACCOUNT_URL ?? 'https://vas.softphone.pro',
  allowedHostList: process.env.ALLOWED_HOSTS ?? '',
  publicBaseUrl: process.env.PUBLIC_BASE_URL ?? 'https://doctor.zenith.clinic/api',

  // AWS S3 Configuration for file storage
  awsRegion: process.env.AWS_REGION ?? 'ap-southeast-2',
  awsAccessKeyId: process.env.AWS_ACCESS_KEY_ID ?? '********************',
  awsSecretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ?? 'hcD2FC545z5eQrkPm0hR+Aclda0N8khUDDipaF/e',
  awsBucketName: process.env.AWS_BUCKET_NAME ?? 'salesreportsbucket',
};
