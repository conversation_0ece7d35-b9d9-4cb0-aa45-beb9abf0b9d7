import React, { useEffect, useState } from "react";
import Grid from "@mui/material/Grid2";
import {
	<PERSON><PERSON><PERSON>,
	<PERSON>ton,
	DialogContent,
	Dialog,
	FormControl,
	InputLabel,
	Select,
	MenuItem,
	SelectChangeEvent,
	FormHelperText,
	Checkbox,
} from "@mui/material";
import CircleOutlinedIcon from "@mui/icons-material/CircleOutlined";
import { useNavigate } from "@tanstack/react-location";
import { usePatient } from "../../hooks/patient-provider";
import { ConsultationOutCome } from "../../types";
import CancelIcon from "@mui/icons-material/Cancel";
import SelectionAlert from "./selectionAlert";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import MissingFieldAlert from "./missing-field-alert";
import InfoIcon from "@mui/icons-material/Info";
import DoctorNotes from "./doctor-note";
import { ApiClient } from "../../services";
import { AxiosError } from "axios";
import { generateMaxDosePerDay, generateTotalQuantity } from "./helper";
import LoadingScreen from "../../utils/loading-screen";
import { useSearch, MakeGenerics } from "@tanstack/react-location";
import { useAuth } from "../../hooks/auth-provider";
import { DailyCall } from "@daily-co/daily-js";
import PatientHistories from "./patientHistory";
import { UserActions } from "../../utils";
import PatientReport from "./PatientReportModal";
import { useTracker } from "../../hooks/activity-tracker-provider";
//import { TextField } from "@mui/material";

type UrlProps = MakeGenerics<{
	Search: {
		token: string;
	};
}>;

const inputStyle = {
	"& .MuiInputBase-root": {
		borderRadius: "5px",
		fontSize: "0.6rem",
		height: "30px",
		padding: "4px 8px",
	},
	"& .MuiOutlinedInput-root": {
		"&:hover": {
			borderColor: "green",
		},
		"&.Mui-focused fieldset": {
			borderColor: "green",
		},
	},
	"& .MuiInputLabel-root": {
		color: "#3B3B3B",
		fontSize: "0.7rem",
	},
	"& .MuiInputLabel-root.Mui-focused": {
		color: "green",
	},
};

type PatientFormProps = {
	handleClose: () => void;
	reloadForm: boolean;
	setReloadForm: React.Dispatch<React.SetStateAction<boolean>>;
	frameRef: React.MutableRefObject<DailyCall | null>;
	clearAllConsultationTimers?: () => void;
	onTypingStart?: () => void;
	onTypingStop?: () => void;
	onFocusStart?: () => void;
	onFocusEnd?: () => void;
};

type RequiredFields = "dosePerDay" | "maxDosePerDay" | "totalQuantity" | "numberOfRepeat" | "supplyInterval";

const TREATMENT_PLANS: { [key: string]: ConsultationOutCome } = {
	unrestricted: "Approve Unrestricted",
	combined: "Approve 29% Subject To 22% Trial",
	discharged: "Approve Subject To Discharge Form",
	cbd: "Approve 22% Subject To CBD Trial",
	gpReferral: "Approve Subject To GP Referral",
};

const formFields = {
	dosePerDay: "Dose Per Day",
	maxDosePerDay: "Maximum Dose Per Day",
	totalQuantity: "Total Quantity (g)",
	numberOfRepeat: "Number of Repeat",
	supplyInterval: "Supply Interval (Days)",
};

const PatientForm: React.FC<PatientFormProps> = ({
	handleClose,
	reloadForm,
	setReloadForm,
	frameRef,
	clearAllConsultationTimers,
	onTypingStart,
	onTypingStop,
	onFocusStart,
	onFocusEnd,
}) => {
	const {
		selectedPatient,
		setSelectedPatient,
		patients,
		patientTreatmentPlan,
		setPatientTreatmentPlan,
		selectedPlan,
		setSelectedPlan,
		strength,
		setStrength,
		selectedForms,
		setSelectedForms,
		setError,
		drId,
		updateLockStatus,
		fetchHistory,
	} = usePatient();
	const [open, setOpen] = useState(false);
	const [openMissingField, setOpenMissingField] = useState(false);
	const [formError, setFromError] = useState<string | undefined>(undefined);
	const [openResultDialog, setOpenResultDialog] = useState(false);
	const [title, _setTitle] = useState("");
	const [message, _setMessage] = useState("");
	const [success, setSuccess] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const { token } = useSearch<UrlProps>();
	const { doctor } = useAuth();
	const [checkedMentalHealthDocument, setCheckedMentalHealthDocument] = React.useState(false);
	const [checkedIdVerification, setCheckedIdVerification] = React.useState(false);
	const [openPatientReport, setOpenPatientReport] = useState(false);
	const [hasReportData, setHasReportData] = useState(false);
	const { trackActivity } = useTracker();

	const requiredFields: RequiredFields[] = [
		"dosePerDay",
		"maxDosePerDay",
		"totalQuantity",
		"numberOfRepeat",
		"supplyInterval",
	];

	const navigate = useNavigate();

	const validateForm = () => {
		let status = true;
		if (selectedPlan === TREATMENT_PLANS.combined) {
			if (selectedForms.length < 2) {
				setIsLoading(false);
				setFromError("Both 22% and 29% must be filled for Pre-approve 29% approve 22%");
				setOpenMissingField(true);
				return false;
			}
		}

		if (selectedPlan === "") {
			setIsLoading(false);
			setFromError(`No Treatment Plan Selected`);
			setOpenMissingField(true);
			status = false;
			return false;
		}
		if (selectedForms.length <= 0) {
			setIsLoading(false);
			setFromError(`No Strength Selected`);
			setOpenMissingField(true);
			return false;
		}

		selectedForms.forEach((form) => {
			requiredFields.forEach((field) => {
				if (!patientTreatmentPlan?.[`${form}`]?.[`${field}`]) {
					setFromError(`Field '${formFields[field]}' is mandatory for the ${form}% strength.`);
					setOpenMissingField(true);
					setIsLoading(false);
					status = false;
					return;
				}
			});
		});

		return status;
	};

	const handleStengthSwitch = (strength: "22" | "29") => {
		trackActivity(
			"Doctor",
			"",
			UserActions.CLICKED,
			`Doctor swtiched to strength ${strength}%`,
			doctor?.id as string,
			token as string
		);
		setSelectedForms((prevValue) => {
			const currentIndex = prevValue.indexOf(strength);
			if (currentIndex === -1) {
				return [...prevValue, strength];
			} else {
				return prevValue.filter((item) => item !== strength);
			}
		});
	};

	const handlePlanChange = (plan: ConsultationOutCome) => {
		// If you want to store event then send all of them at once to avoid to many request to the backend that's fine.
		// Find the best easiest approach
		trackActivity(
			"Doctor",
			"",
			UserActions.CLICKED,
			`Doctor set the new Treatment Plan --> ${plan}`,
			doctor?.id as string,
			token as string
		);
		setSelectedPlan(plan);
		if (selectedPatient) {
			setPatientTreatmentPlan((prev) => {
				return {
					...prev,
					outcome: plan,
				};
			});
		}
	};

	const handleSupportingDocumentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		trackActivity(
			"Doctor",
			"",
			UserActions.CLICKED,
			`Doctor clicked on Mental Health Document button and picked ${event.target.checked ? "Yes" : "No"}`,
			doctor?.id as string,
			token as string
		);
		setCheckedMentalHealthDocument(event.target.checked);
		const value = event.target.checked ? "Yes" : "No";
		if (selectedPatient) {
			setPatientTreatmentPlan((prev) => {
				return {
					...prev,
					mentalHealthSupportingDocumentation: value,
				};
			});
		}
	};

	const handleIdVerificationChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		trackActivity(
			"Doctor",
			"",
			UserActions.CLICKED,
			`Doctor clicked ID Verification button and selected ${event.target.checked ? "Yes" : "No"}`,
			doctor?.id as string,
			token as string
		);
		setCheckedIdVerification(event.target.checked);
		const value = event.target.checked ? "yes" : "no";
		if (selectedPatient) {
			setPatientTreatmentPlan((prev) => {
				return {
					...prev,
					idVerified: value,
				};
			});
		}
	};

	const handleFromChange = (e: SelectChangeEvent) => {
		const { name, value } = e.target;
		trackActivity(
			"Doctor",
			token || "",
			UserActions.CLICKED,
			`Doctor Changed the patient's ${name} to ${value}`,
			doctor?.id as string,
			token as string
		);

		setPatientTreatmentPlan((prev) => {
			return {
				...prev,
				[strength]: {
					...prev?.[strength],
					[name]: value === "" ? undefined : value,
				},
			};
		});
	};

	// const handleDiagnosisChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
	//     const newValue = e.target.value;

	//     if (selectedPatient) {
	//         const previousDiagnosis = patientTreatmentPlan?.diagnosis;

	//         setPatientTreatmentPlan((prev) => ({
	//             ...prev,
	//             diagnosis: newValue
	//         }))

	//         // Log diagnosis creation/update
	//         const action = previousDiagnosis ? UserActions.DIAGNOSIS_UPDATED : UserActions.DIAGNOSIS_CREATED;
	//         const comment = `Doctor ${doctor?.username} ${previousDiagnosis ? 'updated' : 'created'} diagnosis for patient`;
	//         ApiClient.userActions('Doctor', selectedPatient.patientID || '', action, comment)
	//             .catch((error) => console.error('Failed to log diagnosis action:', error));
	//     }
	// }

	const generateDosePerDayValues = (): JSX.Element[] => {
		const options = [];
		for (let i = 0.1; i <= 1; i += 0.1) {
			options.push(
				<MenuItem value={`${i.toFixed(1)}`} key={`${i.toFixed(1)}`} sx={{ fontSize: "12px" }}>
					{i.toFixed(1)}g
				</MenuItem>
			);
		}
		options.push(
			<MenuItem value="1.5" key={"1.5"} sx={{ fontSize: "12px" }}>
				1.5g
			</MenuItem>
		);
		return options;
	};

	const handleFormConfirm = async () => {
		setIsLoading(true);
		if (selectedPatient) {
			if (validateForm()) {
				setPatientTreatmentPlan((prev) => {
					if (selectedForms.includes("22") && selectedForms.includes("29")) {
						return {
							date: new Date().toISOString(),
							...prev,
						};
					} else if (selectedForms.includes("22")) {
						delete prev?.[29];
						return {
							date: new Date().toISOString(),
							...prev,
						};
					} else if (selectedForms.includes("29")) {
						delete prev?.[22];
						return {
							date: new Date().toISOString(),
							...prev,
						};
					}
				});
				localStorage.setItem("TpPlan", JSON.stringify(patientTreatmentPlan));

				try {
					await ApiClient.confirmedPatientWaitingQueue(selectedPatient.patientID);
					trackActivity(
						"Doctor",
						selectedPatient.patientID || "",
						UserActions.CONFIRMED,
						`${doctor?.username} clicked the confirm button`,
						doctor?.id as string,
						token as string
					);

					const onlineQueuePatient = await ApiClient.fetchOnlineQueue();
					if (onlineQueuePatient.length <= 0) {
						if (patients) {
							const drIdLocal = localStorage.getItem("xdr");
							const doctorID =
								drIdLocal && drIdLocal !== ""
									? drIdLocal
									: doctor && doctor.accessID
										? doctor.accessID
										: "";
							await ApiClient.notifyNextPatient(doctorID);
						}
					}
					localStorage.setItem("END_CALL", "no");
					// Clear all consultation timers since consultation is ending with confirmation
					clearAllConsultationTimers?.();
					navigate({ to: "/email-confirmation" });
					handleClose();
				} catch (error) {
					setError(error as AxiosError<unknown, any>);
					setIsLoading(false);
					//proceed to email confirmation only on 503
					if (error instanceof AxiosError && error.response?.status === 503) {
						localStorage.setItem("END_CALL", "no");
						navigate({ to: "/email-confirmation" });
					}
				}
			}
		} else {
			setOpen(true);
		}
	};

	const handleDialogClose = () => {
		setOpen(false);
		setOpenMissingField(false);
	};

	const handleCompleteButton = () => {
		setPatientTreatmentPlan(undefined);
		setStrength("22");
		setSelectedPlan("");
		localStorage.removeItem("reloaded");
		navigate({ to: "/online-patients" });
		setOpenResultDialog(false);
	};

	// Flag to track if a treatment plan submission is in progress
	const [isSubmittingTreatmentPlan, setIsSubmittingTreatmentPlan] = useState(false);

	const handleDecisionClicked = async (decision: ConsultationOutCome) => {
		// Prevent multiple submissions using both the loading state and our specific submission flag
		if (isLoading || isSubmittingTreatmentPlan) {
			console.log("Preventing duplicate submission - already processing");
			return;
		}

		try {
			if (selectedPatient && patientTreatmentPlan) {
				if (decision) {
					// Set both loading flags to prevent duplicate submissions
					setIsLoading(true);
					setIsSubmittingTreatmentPlan(true);

					// Create the updated plan outside of the state update function
					// to avoid race conditions
					const updatedPlan = {
						...patientTreatmentPlan,
						date: new Date().toISOString(),
						outcome: decision,
						drName: doctor?.username,
						drAphraNumber: doctor?.aphraNumber,
					};

					// Remove unnecessary fields
					if (updatedPlan[22]) {
						delete updatedPlan[22];
					}
					if (updatedPlan[29]) {
						delete updatedPlan[29];
					}

					// Update state with the plan
					// setPatientTreatmentPlan(updatedPlan);

					try {
						// Submit the treatment plan
						await ApiClient.postTreamentPlan(updatedPlan);

						// Process the patient queue
						if (selectedPatient.patientID) {
							await ApiClient.completedPatientWaitingQueue(selectedPatient.patientID);
							const onlineQueuePatient = await ApiClient.fetchOnlineQueue();
							if (onlineQueuePatient.length <= 0 && patients) {
								const drIdLocal = localStorage.getItem("xdr");
								const doctorID =
									drIdLocal && drIdLocal !== ""
										? drIdLocal
										: doctor && doctor.accessID
											? doctor.accessID
											: "";
								await ApiClient.notifyNextPatient(doctorID);
							}
						}

						// Get doctor ID and update local storage
						const drIdLocal = localStorage.getItem("xdr");
						const doctorID =
							drIdLocal && drIdLocal !== ""
								? drIdLocal
								: doctor && doctor.accessID
									? doctor.accessID
									: "";
						localStorage.setItem("END_CALL", "no");

						// Log user action
						trackActivity(
							"Doctor",
							token || "",
							UserActions.COMPLETED,
							"The Patient treatment plan was rejected",
							doctor?.id as string,
							selectedPatient.patientID || ""
						);

						// Get next patient
						const nextPatient = await ApiClient.getNextPatientAutomatically(doctorID);

						if (nextPatient?.patientID) {
							// Update lock status
							updateLockStatus(selectedPatient, false, doctorID);
							updateLockStatus(nextPatient, true, doctorID);
							setOpenResultDialog(false);

							// Process next patient
							await ApiClient.alertAwayPatientOnAdmit(nextPatient);
							await ApiClient.postPatientAdmission(nextPatient.patientID, doctorID);
							await ApiClient.postRedirect(nextPatient);
							await frameRef?.current?.destroy();
							trackActivity(
								"Doctor",
								nextPatient.patientID || "",
								UserActions.ADMITTED,
								"Admitted next patient after rejection",
								doctor?.id as string,
								nextPatient.patientID || ""
							);

							// Reset state and navigate
							setSelectedPatient(undefined);
							handleCompleteButton();
							localStorage.removeItem("reloaded");
							// Clear all consultation timers since consultation is ending with decision
							clearAllConsultationTimers?.();
							navigate({ to: "/doctor-consultation", search: { token: nextPatient.patientID } });
							setReloadForm(!reloadForm);
						} else {
							// No next patient, clean up and navigate to patient list
							await frameRef?.current?.destroy();
							setOpenResultDialog(false);
							localStorage.removeItem("reloaded");
							navigate({ to: "/online-patients" });
						}

						// Update UI state
						setSuccess(true);
						setStrength("22");
						setSelectedPlan("");
						setSelectedPatient(undefined);
						setPatientTreatmentPlan(undefined);
					} catch (error) {
						console.error("Error submitting treatment plan:", error);
						setError(error as AxiosError<unknown, any>);
					} finally {
						// Always reset loading states
						setIsLoading(false);
						setIsSubmittingTreatmentPlan(false);
					}
				}
			} else {
				setOpen(true);
			}
		} catch (e) {
			console.error("Error in handleDecisionClicked:", e);
			setError(e as AxiosError<unknown, any>);
			setIsLoading(false);
			setIsSubmittingTreatmentPlan(false);
		}
	};

	// const handleNoShows = async () => {
	//     if (token) {
	//         await ApiClient.callEndedPatientWaitingQueue(token)
	//         trackActivity('Doctor', token || '', UserActions.NOSHOW, 'Patient was no show.')

	//         const drIdLocal = localStorage.getItem('xdr')
	//         const doctorID = drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : ""

	//         const nextPatient = selectedPatient ? await ApiClient.updatePatientToNoShowInQueue(selectedPatient, doctorID) : undefined

	//         if (nextPatient?.patientID) {

	//             const drIdLocal = localStorage.getItem('xdr')
	//             const doctorID = drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : ""
	//             await ApiClient.alertAwayPatientOnAdmit(nextPatient)
	//             updateLockStatus(nextPatient, true, doctorID)
	//             await ApiClient.postRedirect(nextPatient)
	//             await ApiClient.postPatientAdmission(nextPatient.patientID, doctorID)
	//             setSelectedPatient(undefined)
	//             trackActivity('Doctor', nextPatient.patientID || '', UserActions.REDIRECTED, 'Redirected because of no show')
	//             localStorage.removeItem('reloaded')
	//             navigate({ to: '/doctor-consultation', search: { token: nextPatient.patientID } })
	//             setReloadForm(!reloadForm)
	//         }
	//         else {
	//             trackActivity('Doctor', '', UserActions.REDIRECTED, `Doctor ${doctor?.username} auto redirect to online patient after they left the call. No available patient`)
	//             localStorage.removeItem('reloaded')
	//             navigate({ to: '/online-patients' })
	//         }

	//     }
	// }

	const handleTechIssue = async () => {
		if (token) {
			await ApiClient.callEndedPatientWaitingQueue(token);
			trackActivity(
				"Doctor",
				token || "",
				UserActions.TECHISSUE,
				"Patient had technical issues.",
				doctor?.id as string,
				token as string
			);

			const drIdLocal = localStorage.getItem("xdr");
			const doctorID =
				drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : "";

			const nextPatient = selectedPatient
				? await ApiClient.updatePatientWithTechIssue(selectedPatient, doctorID)
				: undefined;

			if (nextPatient?.patientID) {
				const drIdLocal = localStorage.getItem("xdr");
				const doctorID =
					drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : "";
				await ApiClient.alertAwayPatientOnAdmit(nextPatient);
				updateLockStatus(nextPatient, true, doctorID);
				await ApiClient.postRedirect(nextPatient);
				await ApiClient.postPatientAdmission(nextPatient.patientID, doctorID);
				setSelectedPatient(undefined);
				trackActivity(
					"Doctor",
					nextPatient.patientID || "",
					UserActions.REDIRECTED,
					"Doctor was redirected because of patient's technical issues",
					doctor?.id as string,
					token as string
				);
				localStorage.removeItem("reloaded");
				// Clear all consultation timers since consultation is ending due to tech issue
				clearAllConsultationTimers?.();

				navigate({ to: "/doctor-consultation", search: { token: nextPatient.patientID } });
				setReloadForm(!reloadForm);
			} else {
				trackActivity(
					"Doctor",
					"",
					UserActions.REDIRECTED,
					`${doctor?.username} auto redirect to online patient after they left the call due to technical issues. No available patient`,
					doctor?.id as string,
					token as string
				);
				localStorage.removeItem("reloaded");
				navigate({ to: "/online-patients" });
			}
		}
	};

	const checkReportAvailability = async (patientId: string) => {
		try {
			const reportData = await ApiClient.getAICheckResponseByPatientId(patientId);
			setHasReportData(!!reportData && Object.keys(reportData).length > 0);
		} catch (error) {
			console.error("Error checking report availability:", error);
			setHasReportData(false);
		}
	};

	// Just reload anyway
	useEffect(() => {
		const reloaded = localStorage.getItem("reloaded");
		if (!reloaded) {
			localStorage.setItem("reloaded", "yes");
			window.location.reload();
		}
	}, [patients, reloadForm]);

	useEffect(() => {
		if (selectedPatient) {
			localStorage.removeItem("TpPlan");
			const localDrId = localStorage.getItem("xdr");
			const doctorID = drId && drId !== "" ? drId : localDrId ? localDrId : "";

			setPatientTreatmentPlan((prev) => {
				// If we already have values for this strength, don't override them
				const existing22 = prev?.[22] || {};
				const existing29 = prev?.[29] || {};

				return {
					...prev,
					patient: selectedPatient || {},
					drId: doctorID,
					22: {
						...existing22,
						dosePerDay: existing22.dosePerDay || "1.0",
						maxDosePerDay: existing22.maxDosePerDay || "2.0",
						numberOfRepeat: selectedPatient.returningPatient ? "6" : "3",
						supplyInterval: existing22.supplyInterval || "28",
						totalQuantity: existing22.totalQuantity || "28",
					},
					29: {
						...existing29,
						dosePerDay: existing29.dosePerDay || "0.1",
						maxDosePerDay: existing29.maxDosePerDay || "1.0",
						numberOfRepeat: selectedPatient.returningPatient ? "6" : "3",
						supplyInterval: existing29.supplyInterval || "28",
						totalQuantity: existing29.totalQuantity || "28",
					},
				};
			});

			// Check report availability when patient is selected
			if (selectedPatient?.patientID) {
				checkReportAvailability(selectedPatient.patientID);
			} else {
				setHasReportData(false);
			}
		}
	}, [selectedPatient]);

	useEffect(() => {
		localStorage.removeItem("TpPlan");
		if (patients && token) {
			const previousSelectedPatient = patients.find((p) => String(p.patientID) === String(token));
			previousSelectedPatient && fetchHistory(previousSelectedPatient);
			setSelectedPatient(previousSelectedPatient);

			// Check report availability for the selected patient
			if (previousSelectedPatient?.patientID) {
				checkReportAvailability(previousSelectedPatient.patientID);
			}
		}
	}, [patients, reloadForm]);

	return (
		<>
			{isLoading && <LoadingScreen />}

			<SelectionAlert
				handleDialogClose={handleDialogClose}
				open={open}
				title="No Patient Selected"
				Icon={<CancelIcon sx={{ width: "100px", height: "100px", color: "red", mb: 2 }} />}
				message="Please select a patient before confirming."
			/>

			<MissingFieldAlert
				handleDialogClose={handleDialogClose}
				open={openMissingField}
				title="Form Incomplete"
				Icon={<InfoIcon sx={{ width: "100px", height: "100px", color: "red", mb: 2 }} />}
				message={formError || " "}
			/>

			<Dialog open={openResultDialog} fullWidth={true} maxWidth={"xs"} onClose={handleDialogClose}>
				<DialogContent>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%" }}
						justifyContent={"center"}
						alignItems={"center"}
					>
						<Typography sx={{ fontSize: "18px", fontWeight: "bold", mb: 3 }}>{title}</Typography>
						{success ? (
							<CheckCircleIcon sx={{ width: "100px", height: "100px", color: "green", mb: 2 }} />
						) : (
							<CancelIcon sx={{ width: "100px", height: "100px", color: "red", mb: 2 }} />
						)}
						<Typography sx={{ fontSize: "14px" }} align="center">
							{message}
						</Typography>
						{/* {success && <Grid sx={{ mt: 2 }}>
                            <Button sx={{ color: 'green' }} onClick={handleCompleteButton}>
                                Return to Patients Queue
                            </Button>
                        </Grid>} */}
					</Grid>
				</DialogContent>
			</Dialog>

			<Grid container direction={"column"} sx={{ mt: 0 }}>
				<Grid container direction={"column"}>
					{/* <Grid container sx={{ mt: 1 }}>
                        <Typography sx={{ fontWeight: 'light', fontSize: '12px', flexGrow: 1 }}>
                            Switch Forms <span style={{ fontWeight: 'bold', color: 'green' }}>( {strength}% )</span>
                        </Typography>
                        <Grid container spacing={1}>
                            <Button
                                variant={strength === '22' ? 'outlined' : 'text'}
                                endIcon={selectedForms.includes('22') ?
                                    <CheckCircleIcon sx={{ width: '12px' }} /> :
                                    <CircleOutlinedIcon sx={{ width: '12px' }} />
                                }
                                sx={{ height: '20px', fontSize: '12px', color: selectedForms.includes('22') ? 'green' : '#676767', borderColor: 'green' }}
                                onClick={() => {
                                    setStrength('22')
                                }}
                                onDoubleClick={() => handleStengthSwitch('22')}
                            >
                                22%
                            </Button>
                            {
                                selectedPlan !== TREATMENT_PLANS.cbd &&
                                <Button
                                    variant={strength === '29' ? 'outlined' : 'text'}
                                    endIcon={selectedForms.includes('29') ?
                                        <CheckCircleIcon sx={{ width: '12px' }} /> :
                                        <CircleOutlinedIcon sx={{ width: '12px' }} />
                                    }
                                    sx={{ height: '20px', color: selectedForms.includes('29') ? 'green' : '#676767', fontSize: '12px', borderColor: 'green' }}
                                    onClick={() => {
                                        setStrength('29')
                                    }}
                                    onDoubleClick={() => handleStengthSwitch('29')}
                                >
                                    29%
                                </Button>
                            }
                        </Grid>
                    </Grid>
                    <Grid container alignItems={'center'} sx={{ mb: 1 }}>
                        <Checkbox
                            checked={checkedMentalHealthDocument}
                            onChange={handleSupportingDocumentChange}
                            size="small"
                            disableRipple={true}
                            disableFocusRipple
                            sx={{
                                '& .MuiSvgIcon-root': { fontSize: '16px' }, // Change size (default is 24)
                                width: '25px',
                                height: '25px'
                            }} />
                        <Typography sx={{ fontWeight: 'light', fontSize: '12px' }}>
                            Mental Health Supporting Document
                        </Typography>
                    </Grid> */}

					<Grid container>
						<Grid container direction="column" sx={{ width: "50%" }}>
							<Grid
								sx={{ backgroundColor: "#6B7785", p: 0.5 }}
								justifyContent={"center"}
								alignItems={"center"}
								container
							>
								<Typography sx={{ color: "white", fontWeight: "500" }}>Treatment plan</Typography>
							</Grid>
							<Grid sx={{ mt: 1, mb: 1 }} container alignItems={"center"}>
								<Typography sx={{ fontSize: "12px", flexGrow: 1 }}>
									{selectedPatient?.fullName ? (
										<>
											Select plan for{" "}
											<span style={{ fontWeight: "500" }}>{selectedPatient?.fullName}</span>
										</>
									) : (
										<span style={{ fontWeight: "500" }}>No patient selected</span>
									)}
								</Typography>
								{selectedPatient?.returningPatient ? (
									<Typography sx={{ fontSize: "12px", fontWeight: "500" }}>
										Returning Patient
									</Typography>
								) : (
									<Typography sx={{ fontSize: "12px", fontWeight: "500" }}>New Patient</Typography>
								)}
							</Grid>
							<Grid container spacing={1} sx={{ mb: 1 }}>
								<Button
									name="treatmentPlan"
									variant="outlined"
									endIcon={
										selectedPlan === TREATMENT_PLANS.unrestricted ? (
											<CheckCircleIcon sx={{ width: "12px" }} />
										) : (
											<CircleOutlinedIcon sx={{ width: "12px" }} />
										)
									}
									onClick={() => handlePlanChange(TREATMENT_PLANS.unrestricted)}
									sx={{
										fontSize: "11px",
										textTransform: "none",
										borderColor:
											selectedPlan === TREATMENT_PLANS.unrestricted ? "green" : "#676767",
										color: selectedPlan === TREATMENT_PLANS.unrestricted ? "green" : "#676767",
										height: "25px",
									}}
								>
									THC Patient
								</Button>
								<Button
									variant="outlined"
									name="treatmentPlan"
									onClick={() => handlePlanChange(TREATMENT_PLANS.combined)}
									endIcon={
										selectedPlan === TREATMENT_PLANS.combined ? (
											<CheckCircleIcon sx={{ width: "12px" }} />
										) : (
											<CircleOutlinedIcon sx={{ width: "12px" }} />
										)
									}
									sx={{
										fontSize: "11px",
										textTransform: "none",
										borderColor: selectedPlan === TREATMENT_PLANS.combined ? "green" : "#676767",
										color: selectedPlan === TREATMENT_PLANS.combined ? "green" : "#676767",
										height: "25px",
									}}
								>
									Pre-approve 29% approve 22%
								</Button>
								<Button
									variant="outlined"
									name="treatmentPlan"
									onClick={() => handlePlanChange(TREATMENT_PLANS.discharged)}
									endIcon={
										selectedPlan === TREATMENT_PLANS.discharged ? (
											<CheckCircleIcon sx={{ width: "12px" }} />
										) : (
											<CircleOutlinedIcon sx={{ width: "12px" }} />
										)
									}
									sx={{
										fontSize: "11px",
										textTransform: "none",
										borderColor: selectedPlan === TREATMENT_PLANS.discharged ? "green" : "#676767",
										color: selectedPlan === TREATMENT_PLANS.discharged ? "green" : "#676767",
										height: "25px",
									}}
								>
									Approve patient subject to discharge
								</Button>
								<Button
									variant="outlined"
									name="treatmentPlan"
									onClick={() => handlePlanChange(TREATMENT_PLANS.gpReferral)}
									endIcon={
										selectedPlan === TREATMENT_PLANS.gpReferral ? (
											<CheckCircleIcon sx={{ width: "12px" }} />
										) : (
											<CircleOutlinedIcon sx={{ width: "12px" }} />
										)
									}
									sx={{
										fontSize: "11px",
										textTransform: "none",
										borderColor: selectedPlan === TREATMENT_PLANS.gpReferral ? "green" : "#676767",
										color: selectedPlan === TREATMENT_PLANS.gpReferral ? "green" : "#676767",
										height: "25px",
									}}
								>
									Approve Subject To GP Referral
								</Button>
							</Grid>
							<Grid container sx={{ mt: 1 }}>
								<Typography sx={{ fontWeight: "light", fontSize: "12px", flexGrow: 1 }}>
									Switch Forms{" "}
									<span style={{ fontWeight: "bold", color: "green" }}>( {strength}% )</span>
								</Typography>
								<Grid container spacing={1}>
									<Button
										variant={strength === "22" ? "outlined" : "text"}
										endIcon={
											selectedForms.includes("22") ? (
												<CheckCircleIcon sx={{ width: "12px" }} />
											) : (
												<CircleOutlinedIcon sx={{ width: "12px" }} />
											)
										}
										sx={{
											height: "20px",
											fontSize: "12px",
											color: selectedForms.includes("22") ? "green" : "#676767",
											borderColor: "green",
										}}
										onClick={() => {
											setStrength("22");
										}}
										onDoubleClick={() => handleStengthSwitch("22")}
									>
										22%
									</Button>
									{selectedPlan !== TREATMENT_PLANS.cbd && (
										<Button
											variant={strength === "29" ? "outlined" : "text"}
											endIcon={
												selectedForms.includes("29") ? (
													<CheckCircleIcon sx={{ width: "12px" }} />
												) : (
													<CircleOutlinedIcon sx={{ width: "12px" }} />
												)
											}
											sx={{
												height: "20px",
												color: selectedForms.includes("29") ? "green" : "#676767",
												fontSize: "12px",
												borderColor: "green",
											}}
											onClick={() => {
												setStrength("29");
											}}
											onDoubleClick={() => handleStengthSwitch("29")}
										>
											29%
										</Button>
									)}
								</Grid>
							</Grid>

							<Grid container alignItems={"center"} sx={{ mb: 2 }}>
								<Checkbox
									checked={checkedMentalHealthDocument}
									onChange={handleSupportingDocumentChange}
									size="small"
									disableRipple={true}
									disableFocusRipple
									sx={{
										"& .MuiSvgIcon-root": { fontSize: "16px" }, // Change size (default is 24)
										width: "25px",
										height: "25px",
									}}
								/>
								<Typography sx={{ fontWeight: "light", fontSize: "12px" }}>
									Mental Health Supporting Document
								</Typography>
							</Grid>
							<Grid container alignItems={"center"} sx={{ mb: 2 }}>
								<Checkbox
									checked={checkedIdVerification}
									onChange={handleIdVerificationChange}
									size="small"
									disableRipple={true}
									disableFocusRipple
									sx={{
										"& .MuiSvgIcon-root": { fontSize: "16px" }, // Change size (default is 24)
										width: "25px",
										height: "25px",
									}}
								/>
								<Typography sx={{ fontWeight: "light", fontSize: "12px" }}>ID Verification</Typography>
							</Grid>
							<Grid
								container
								direction="row"
								alignItems={"start"}
								justifyContent={"start"}
								spacing={1}
								sx={{ height: "30px", mb: { xs: 7, lg: 4 } }}
							>
								<Grid sx={{ width: { xs: "100%", lg: "48%" } }}>
									<FormControl fullWidth sx={{ ...inputStyle }} size="small">
										<InputLabel id="doseparday" sx={{ zIndex: 0 }}>
											Dose Per Day
										</InputLabel>
										<Select
											labelId="doseparday"
											id="dosepardaySelect"
											value={patientTreatmentPlan?.[strength]?.dosePerDay || ""}
											onChange={handleFromChange}
											name="dosePerDay"
										>
											<MenuItem value="" sx={{ fontSize: "12px" }}>
												None
											</MenuItem>
											{generateDosePerDayValues()}
										</Select>
										<FormHelperText sx={{ fontSize: "8px" }}>
											Dose per day {strength === "22" ? "29" : "22"}% :{" "}
											{patientTreatmentPlan?.[strength === "22" ? "29" : "22"]?.dosePerDay ||
												"None"}
										</FormHelperText>
									</FormControl>
								</Grid>
								<Grid sx={{ width: { xs: "100%", lg: "48%" }, mt: { xs: 2, lg: 0 } }}>
									<FormControl fullWidth sx={{ ...inputStyle }} size="small">
										<InputLabel id="maximumDosePerDay" sx={{ zIndex: 0 }}>
											Max Dose Per Day
										</InputLabel>
										<Select
											labelId="maximumDosePerDay"
											id="maximumDosePerDaySelect"
											value={patientTreatmentPlan?.[strength]?.maxDosePerDay || ""}
											onChange={handleFromChange}
											name="maxDosePerDay"
										>
											<MenuItem value="" sx={{ fontSize: "12px" }}>
												None
											</MenuItem>
											{generateMaxDosePerDay()}
										</Select>
										<FormHelperText sx={{ fontSize: "8px" }}>
											Max Dose Per Day {strength === "22" ? "29" : "22"}% :{" "}
											{patientTreatmentPlan?.[strength === "22" ? "29" : "22"]?.maxDosePerDay ||
												"None"}
										</FormHelperText>
									</FormControl>
								</Grid>
							</Grid>
							<Grid sx={{ mb: { xs: 1, lg: 0 }, width: { xs: "100%", lg: "98%" } }}>
								<Grid sx={{ width: { xs: "100%", lg: "100%" }, mb: 2, mt: { xs: 8, lg: 0 } }}>
									<FormControl fullWidth sx={{ ...inputStyle }} size="small">
										<InputLabel id="totalQuantity" sx={{ zIndex: 0 }}>
											Total Quantity (g)
										</InputLabel>
										<Select
											labelId="totalQuantity"
											id="totalQuantitySelect"
											value={patientTreatmentPlan?.[strength]?.totalQuantity || ""}
											onChange={handleFromChange}
											name="totalQuantity"
										>
											<MenuItem value="" sx={{ fontSize: "12px" }}>
												None
											</MenuItem>
											{generateTotalQuantity()}
										</Select>
										<FormHelperText sx={{ fontSize: "8px" }}>
											Total Quantity (g) {strength === "22" ? "29" : "22"}% :{" "}
											{patientTreatmentPlan?.[strength === "22" ? "29" : "22"]?.totalQuantity ||
												"None"}
										</FormHelperText>
									</FormControl>
								</Grid>
							</Grid>
							<Grid
								container
								direction="row"
								spacing={1}
								alignItems={"start"}
								justifyContent={"start"}
								sx={{ height: "35px", mb: { xs: 2, lg: 1 } }}
							>
								<Grid sx={{ width: { xs: "100%", lg: "48%" } }}>
									<FormControl fullWidth sx={{ ...inputStyle }} size="small">
										<InputLabel id="numberOfRepeat" sx={{ zIndex: 0 }}>
											Number of repeat
										</InputLabel>
										<Select
											labelId="numberOfRepeat"
											id="numberOfRepeatSelect"
											value={
												patientTreatmentPlan?.[strength]?.numberOfRepeat ||
												(selectedPatient?.returningPatient ? "6" : "3")
											}
											onChange={handleFromChange}
											name="numberOfRepeat"
										>
											{selectedPatient?.returningPatient
												? [
														<MenuItem value={"6"} key="6" sx={{ fontSize: "12px" }}>
															6
														</MenuItem>,
													]
												: [
														<MenuItem value={"3"} key="3" sx={{ fontSize: "12px" }}>
															3
														</MenuItem>,
														<MenuItem value={"6"} key="6" sx={{ fontSize: "12px" }}>
															6
														</MenuItem>,
													]}
										</Select>
										<FormHelperText sx={{ fontSize: "8px" }}>
											Number of repeat {strength === "22" ? "29" : "22"}% :{" "}
											{patientTreatmentPlan?.[strength === "22" ? "29" : "22"]?.numberOfRepeat ||
												"None"}
										</FormHelperText>
									</FormControl>
								</Grid>
								<Grid sx={{ width: { xs: "100%", lg: "48%" }, mt: { xs: 2, lg: 0 } }}>
									<FormControl fullWidth sx={{ ...inputStyle }} size="small">
										<InputLabel id="supplyInterval" sx={{ zIndex: 0 }}>
											Supply Interval (Days)
										</InputLabel>
										<Select
											labelId="supplyInterval"
											id="supplyIntervalSelect"
											value={patientTreatmentPlan?.[strength]?.supplyInterval || "28"}
											onChange={handleFromChange}
											name="supplyInterval"
										>
											<MenuItem value={"28"} sx={{ fontSize: "12px" }}>
												28
											</MenuItem>
										</Select>
										<FormHelperText sx={{ fontSize: "8px" }}>
											Supply Interval (Days) {strength === "22" ? "29" : "22"}% :{" "}
											{patientTreatmentPlan?.[strength === "22" ? "29" : "22"]?.supplyInterval ||
												"None"}
										</FormHelperText>
									</FormControl>
								</Grid>
							</Grid>
						</Grid>

						<Grid container sx={{ width: "50%" }} alignItems={"start"}>
							<PatientHistories mobile={true} />
							{hasReportData && (
								<Grid size={12} container alignItems={"center"} justifyContent={"center"} p={2}>
									<Button
										variant="outlined"
										fullWidth
										onClick={() => {
											setOpenPatientReport(!openPatientReport);
											trackActivity(
												"Doctor",
												selectedPatient?.patientID || "",
												UserActions.CLICKED,
												`Viewed Patient's Report`,
												doctor?.id || "",
												selectedPatient?.patientID
											);
										}}
									>
										View Patient Report
									</Button>
								</Grid>
							)}
							{hasReportData && (
								<PatientReport
									open={openPatientReport}
									onClose={() => setOpenPatientReport(!openPatientReport)}
									id={token as string}
								/>
							)}
						</Grid>
					</Grid>

					{/* Patient Diagnosis Section */}
					{/* <Grid sx={{ mb: 1, mt: 2 }}>
                        <TextField
                            multiline
                            placeholder="Enter patient diagnosis..."
                            fullWidth
                            rows={2}
                            label="Patient Diagnosis"
                            value={patientTreatmentPlan?.diagnosis || ''}
                            onChange={handleDiagnosisChange}
                            inputProps={{
                                maxLength: 2000
                            }}
                            helperText={`${(patientTreatmentPlan?.diagnosis || '').length}/2000 characters`}
                            sx={{
                                '& .MuiInputBase-root': {
                                    borderRadius: '8px',
                                    backgroundColor: '#f8f9fa',
                                    fontSize: '14px'
                                },
                                '& .MuiOutlinedInput-root': {
                                    '&:hover fieldset': {
                                        borderColor: 'green',
                                    },
                                    '&.Mui-focused fieldset': {
                                        borderColor: 'green',
                                    }
                                },
                                '& .MuiInputLabel-root.Mui-focused': {
                                    color: 'green',
                                }
                            }}
                        />
                    </Grid> */}

					<Grid sx={{ mb: 1, mt: { lg: 4, xs: 12 } }}>
						<DoctorNotes
							onTypingStart={onTypingStart}
							onTypingStop={onTypingStop}
							onFocusStart={onFocusStart}
							onFocusEnd={onFocusEnd}
						/>
					</Grid>
					<Grid container direction="column" alignItems={"center"} sx={{ width: "100%" }}>
						<Typography sx={{ fontSize: "12px" }}>These two buttons will end the call</Typography>
						<Grid
							container
							spacing={2}
							sx={{ width: "100%" }}
							alignItems={"center"}
							justifyContent={"center"}
						>
							<Button
								variant="contained"
								sx={{ textTransform: "none", fontSize: "12px", backgroundColor: "green", width: "20%" }}
								onClick={() => handleFormConfirm()}
								disabled={isLoading}
							>
								Confirm
							</Button>
							<Button
								onClick={() => handleDecisionClicked("Reject")}
								sx={{
									color: "white",
									textTransform: "none",
									backgroundColor: "red",
									width: "20%",
								}}
								disabled={isLoading}
							>
								Reject
							</Button>
							<Button
								variant="outlined"
								onClick={() => handleTechIssue()}
								sx={{
									color: "red",
									textTransform: "none",
									borderColor: "red",
									width: "20%",
								}}
								disabled={isLoading}
							>
								Tech Issue
							</Button>
						</Grid>
					</Grid>
				</Grid>
			</Grid>
		</>
	);
};

export default PatientForm;
