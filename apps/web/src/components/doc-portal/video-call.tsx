import { <PERSON><PERSON>, <PERSON>ir<PERSON>Progress, <PERSON>Field, Typography } from "@mui/material"
import { useEffect } from "react"
import { DailyCall } from '@daily-co/daily-js'
import Grid from "@mui/material/Grid2";
import { usePatient } from "../../hooks/patient-provider";

const inputStyle = {
    '& .MuiInputBase-root': {
        borderRadius: '5px',
    },
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

type VideoCallProps = {
    handleClickOpen: (token: string | undefined) => void
    handleClose: () => void
    open: boolean
    iFrameDestroyed: boolean
    setIFrameDestroyed: React.Dispatch<React.SetStateAction<boolean>>
    componentRef: React.MutableRefObject<HTMLElement | null>
    handleSubmitName: () => void
    handleTextFieldChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
    username: string
    setUserName: React.Dispatch<React.SetStateAction<string>>
    newName: string
    frame: DailyCall | null
    setFrame: React.Dispatch<React.SetStateAction<DailyCall | null>>
}

const VideoCall: React.FC<VideoCallProps> =
    ({
        handleClickOpen,
        iFrameDestroyed,
        setIFrameDestroyed,
        handleSubmitName,
        handleTextFieldChange,
        username,
        setUserName,
        newName,
        frame,
        setFrame
    }) => {

        const { patients, setSelectedPatient } = usePatient()

        useEffect(() => {
            return () => {
                if (frame) {
                    frame.destroy();
                    setIFrameDestroyed(true)
                }
            }
        }, [frame])

        useEffect(() => {
            setFrame(null)
            frame?.destroy();

            const existingUserName = localStorage.getItem('HAR_PARTICIPANT_NAME')
            if (existingUserName) {
                setUserName(existingUserName)
                frame?.setUserName(newName)
                setIFrameDestroyed(false)
            }
        }, [])

        return (
            <>
                {iFrameDestroyed && !username &&
                    <Grid container justifyContent={'center'} alignItems={'center'} direction={'column'} spacing={5}>
                        <Typography sx={{ color: 'green', fontSize: '12px', fontWeight: 'bold' }}>
                            Daily is Updating... You might want to reload your window.
                        </Typography>
                        <CircularProgress size="2.5rem" sx={{ color: 'green' }} />
                    </Grid>}

                {!username &&
                    <Grid container direction='column' spacing={1} justifyContent={'center'} alignItems={'center'} >
                        <Typography sx={{ fontSize: '0.90rem', fontWeight: 'bold', mb: 2 }}>
                            Harvest Video Consultation
                        </Typography>
                        <TextField label='Enter Your Name Here' sx={{ width: '100%', ...inputStyle }} value={newName} onChange={handleTextFieldChange} />
                        <Button sx={{ backgroundColor: 'green', width: '100%' }} variant='contained' onClick={handleSubmitName}>
                            Submit
                        </Button>
                    </Grid>}

                {username &&
                    <Grid container justifyContent={'center'} alignItems={'center'} direction={'column'} spacing={2}>
                        <Typography align="center">
                            <span style={{ fontSize: '0.90rem', fontWeight: 'bold' }}>Welcome Back {username}</span> <br /><br /> Select a patient or start consulting the first patient by clicking below
                        </Typography>
                        <Button sx={{ backgroundColor: 'green', width: '100%', textTransform: 'none' }} variant='contained' onClick={() => {
                            setSelectedPatient(patients?.[0])
                            handleClickOpen(patients?.[0]?.patientID)
                        }}>
                            Start Consulting
                        </Button>
                    </Grid>}
            </>
        )
    }

export default VideoCall