export const MOCK_DATA = {
  output: {
    "Dr H. Anjum": {
      products: {
        "Galactic Adventure": {
          baseName: "Galactic Adventure",
          totalQuantity: 3,
          totalWeight: 28,
          totalBudQuantity: 8,
          tradeName: "20-01/28-02/05-03/27-04/09-05",
        },
        "Bald Eagle": {
          baseName: "Bald Eagle",
          totalQuantity: 3,
          totalWeight: 42,
          totalBudQuantity: 12,
          tradeName: "25-10/05-11/11-12/21-01/19-02",
        },
        "Karen’s Nightmare": {
          baseName: "<PERSON>’s Nightmare",
          totalQuantity: 1,
          totalWeight: 28,
          totalBudQuantity: 8,
          tradeName: "23-03/17-04/14-05/28-06/09-07",
        },
        "Midnight Magic": {
          baseName: "Midnight Magic",
          totalQuantity: 2,
          totalWeight: 21,
          totalBudQuantity: 6,
          tradeName: "16-03/15-04/16-05/23-06/28-07",
        },
        "Raspberry Ripple": {
          baseName: "Raspberry Ripple",
          totalQuantity: 4,
          totalWeight: 49,
          totalBudQuantity: 14,
          tradeName: "04-05/27-06/05-07/28-08/14-09",
        },
        "Cookies n’ Cuddles": {
          baseName: "Cookies n’ Cuddles",
          totalQuantity: 3,
          totalWeight: 21,
          totalBudQuantity: 6,
          tradeName: "19-02/31-03/16-04/05-05/24-06",
        },
        "Rainbow Sherbert": {
          baseName: "Rainbow Sherbert",
          totalQuantity: 1,
          totalWeight: 14,
          totalBudQuantity: 4,
          tradeName: "19-04/03-05/11-06/18-07/26-08",
        },
      },
    },
    "Dr J. Lavett": {
      products: {
        "Galactic Adventure": {
          baseName: "Galactic Adventure",
          totalQuantity: 1,
          totalWeight: 7,
          totalBudQuantity: 2,
          tradeName: "20-01/28-02/05-03/27-04/09-05",
        },
        "Passionfruit Pav": {
          baseName: "Passionfruit Pav",
          totalQuantity: 1,
          totalWeight: 14,
          totalBudQuantity: 4,
          tradeName: "15-07/20-08/03-09/27-10/29-11",
        },
        "Midnight Magic": {
          baseName: "Midnight Magic",
          totalQuantity: 2,
          totalWeight: 35,
          totalBudQuantity: 10,
          tradeName: "16-03/15-04/16-05/23-06/28-07",
        },
        "Supernova Sorbet": {
          baseName: "Supernova Sorbet",
          totalQuantity: 1,
          totalWeight: 14,
          totalBudQuantity: 4,
          tradeName: "21-08/30-09/12-10/24-11/05-12",
        },
        "Sherb Heights": {
          baseName: "Sherb Heights",
          totalQuantity: 1,
          totalWeight: 14,
          totalBudQuantity: 4,
          tradeName: "03-10/10-11/15-12/18-01/20-02",
        },
        "Raspberry Ripple": {
          baseName: "Raspberry Ripple",
          totalQuantity: 1,
          totalWeight: 7,
          totalBudQuantity: 2,
          tradeName: "04-05/27-06/05-07/28-08/14-09",
        },
      },
    },
  },
  productCentricData: {
    "Galactic Adventure": {
      requests: [
        {
          drName: "Dr H. Anjum",
          budQuantity: 8,
        },
        {
          drName: "Dr J. Lavett",
          budQuantity: 2,
        },
      ],
      totalBudQuantity: 10,
      tradeName: "20-01/28-02/05-03/27-04/09-05",
      strength: "22%",
      balance: 290,
    },
    "Bald Eagle": {
      requests: [
        {
          drName: "Dr H. Anjum",
          budQuantity: 12,
        },
      ],
      totalBudQuantity: 12,
      tradeName: "25-10/05-11/11-12/21-01/19-02",
      strength: "22%",
      balance: 288,
    },
    "Karen’s Nightmare": {
      requests: [
        {
          drName: "Dr H. Anjum",
          budQuantity: 8,
        },
      ],
      totalBudQuantity: 8,
      tradeName: "23-03/17-04/14-05/28-06/09-07",
      strength: "29%",
      balance: 292,
    },
    "Midnight Magic": {
      requests: [
        {
          drName: "Dr H. Anjum",
          budQuantity: 6,
        },
        {
          drName: "Dr J. Lavett",
          budQuantity: 10,
        },
      ],
      totalBudQuantity: 16,
      tradeName: "16-03/15-04/16-05/23-06/28-07",
      strength: "22%",
      balance: 284,
    },
    "Raspberry Ripple": {
      requests: [
        {
          drName: "Dr H. Anjum",
          budQuantity: 14,
        },
        {
          drName: "Dr J. Lavett",
          budQuantity: 2,
        },
      ],
      totalBudQuantity: 16,
      tradeName: "04-05/27-06/05-07/28-08/14-09",
      strength: "22%",
      balance: 284,
    },
    "Cookies n’ Cuddles": {
      requests: [
        {
          drName: "Dr H. Anjum",
          budQuantity: 6,
        },
      ],
      totalBudQuantity: 6,
      tradeName: "19-02/31-03/16-04/05-05/24-06",
      strength: "22%",
      balance: 294,
    },
    "Rainbow Sherbert": {
      requests: [
        {
          drName: "Dr H. Anjum",
          budQuantity: 4,
        },
      ],
      totalBudQuantity: 4,
      tradeName: "19-04/03-05/11-06/18-07/26-08",
      strength: "29%",
      balance: 296,
    },
    "Passionfruit Pav": {
      requests: [
        {
          drName: "Dr J. Lavett",
          budQuantity: 4,
        },
      ],
      totalBudQuantity: 4,
      tradeName: "15-07/20-08/03-09/27-10/29-11",
      strength: "22%",
      balance: 296,
    },
    "Supernova Sorbet": {
      requests: [
        {
          drName: "Dr J. Lavett",
          budQuantity: 4,
        },
      ],
      totalBudQuantity: 4,
      tradeName: "21-08/30-09/12-10/24-11/05-12",
      strength: "29%",
      balance: 296,
    },
    "Sherb Heights": {
      requests: [
        {
          drName: "Dr J. Lavett",
          budQuantity: 4,
        },
      ],
      totalBudQuantity: 4,
      tradeName: "03-10/10-11/15-12/18-01/20-02",
      strength: "29%",
      balance: 296,
    },
  },
  orders: {
    data: [
      {
        first_name: "Anthony",
        last_name: "Orvad",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000012187986",
        wp_user_id: 9235,
        consulting_doctor: null,
        order_id: 16975,
        order_number: "42000011551",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 20:42:38",
        order_date_modified: "2025-06-16 20:45:45",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "227.86",
        order_total_tax: "0.36",
        order_customer_id: 9235,
        order_billing_first_name: "Anthony",
        order_billing_last_name: "Orvad",
        order_billing_company: "",
        order_billing_address_1: "13 Eucalyptus Grove",
        order_billing_address_2: "",
        order_billing_city: "Buxton",
        order_billing_state: "NSW",
        order_billing_postcode: "2571",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0412098479",
        order_shipping_first_name: "Anthony",
        order_shipping_last_name: "Orvad",
        order_shipping_company: "",
        order_shipping_address_1: "13 Eucalyptus Grove",
        order_shipping_address_2: "",
        order_shipping_city: "Buxton",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2571",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 160,
            variation_id: 0,
            product_name: "Grape Pack n' Puff",
            quantity: 1,
            subtotal: "1.818182",
            total: "1.818182",
            tax: "0.18",
            tax_class: "gst",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/8821496816693/",
            trade_name: "",
          },
          {
            product_id: 163,
            variation_id: 0,
            product_name: "Mango Pack n' Puff",
            quantity: 1,
            subtotal: "1.818182",
            total: "1.818182",
            tax: "0.18",
            tax_class: "gst",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/8821496816691/",
            trade_name: "",
          },
          {
            product_id: 3199,
            variation_id: 3203,
            product_name: "Raspberry Ripple - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[04-05|27-06|05-07|28-08|14-09]",
          },
          {
            product_id: 3060,
            variation_id: 3064,
            product_name: "Bald Eagle - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[25-10|05-11|11-12|21-01|19-02]",
          },
        ],
      },
      {
        first_name: "David",
        last_name: "Coombes",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000012629615",
        wp_user_id: 10102,
        consulting_doctor: "Dr J. Lavett",
        order_id: 16973,
        order_number: "42000011549",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 20:08:57",
        order_date_modified: "2025-06-16 20:10:46",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "203.16",
        order_total_tax: "0",
        order_customer_id: 10102,
        order_billing_first_name: "David",
        order_billing_last_name: "Coombes",
        order_billing_company: "",
        order_billing_address_1: "39 Washington Street",
        order_billing_address_2: "",
        order_billing_city: "Traralgon",
        order_billing_state: "VIC",
        order_billing_postcode: "3844",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0402238955",
        order_shipping_first_name: "David",
        order_shipping_last_name: "Coombes",
        order_shipping_company: "",
        order_shipping_address_1: "39 Washington Street",
        order_shipping_address_2: "",
        order_shipping_city: "Traralgon",
        order_shipping_state: "VIC",
        order_shipping_postcode: "3844",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3199,
            variation_id: 3204,
            product_name: "Raspberry Ripple - 14g - Half",
            quantity: 1,
            subtotal: "180",
            total: "180",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-14/",
            trade_name: "[04-05|27-06|05-07|28-08|14-09]",
          },
        ],
      },
      {
        first_name: "Lamin",
        last_name: "Fofanah",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000011202219",
        wp_user_id: 7201,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16969,
        order_number: "42000011547",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 18:50:05",
        order_date_modified: "2025-06-16 18:52:57",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "120.84",
        order_total_tax: "0",
        order_customer_id: 7201,
        order_billing_first_name: "Lamin",
        order_billing_last_name: "Fofanah",
        order_billing_company: "",
        order_billing_address_1: "13 Gregory Avenue",
        order_billing_address_2: "",
        order_billing_city: "Oxley Park",
        order_billing_state: "NSW",
        order_billing_postcode: "2760",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0431348552",
        order_shipping_first_name: "Lamin",
        order_shipping_last_name: "Fofanah",
        order_shipping_company: "",
        order_shipping_address_1: "13 Gregory Avenue",
        order_shipping_address_2: "",
        order_shipping_city: "Oxley Park",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2760",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3225,
            variation_id: 3229,
            product_name: "Passionfruit Pav - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[15-07|20-08|03-09|27-10|29-11]",
          },
        ],
      },
      {
        first_name: "Liam",
        last_name: "Jacques",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000012274942",
        wp_user_id: 8421,
        consulting_doctor: null,
        order_id: 16967,
        order_number: "42000011545",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 18:09:44",
        order_date_modified: "2025-06-16 18:15:57",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "0",
        order_shipping_tax: "0",
        order_total: "391.52",
        order_total_tax: "0",
        order_customer_id: 8421,
        order_billing_first_name: "Liam",
        order_billing_last_name: "Jacques",
        order_billing_company: "",
        order_billing_address_1: "174 storrs road",
        order_billing_address_2: "",
        order_billing_city: "Peachester",
        order_billing_state: "QLD",
        order_billing_postcode: "4519",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0477631603",
        order_shipping_first_name: "Liam",
        order_shipping_last_name: "Jacques",
        order_shipping_company: "",
        order_shipping_address_1: "174 storrs road",
        order_shipping_address_2: "",
        order_shipping_city: "Peachester",
        order_shipping_state: "QLD",
        order_shipping_postcode: "4519",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3232,
            variation_id: 3236,
            product_name: "Midnight Magic - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[16-03|15-04|16-05|23-06|28-07]",
          },
          {
            product_id: 2750,
            variation_id: 2830,
            product_name: "Cookies n’ Cuddles - 14g - Half",
            quantity: 1,
            subtotal: "180",
            total: "180",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-14/",
            trade_name: "[19-02|31-03|16-04|05-05|24-06]",
          },
          {
            product_id: 3199,
            variation_id: 3203,
            product_name: "Raspberry Ripple - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[04-05|27-06|05-07|28-08|14-09]",
          },
        ],
      },
      {
        first_name: "Ronan",
        last_name: "Madam",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000008653447",
        wp_user_id: 4529,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16953,
        order_number: "42000011542",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 17:10:45",
        order_date_modified: "2025-06-16 17:12:53",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "0",
        order_shipping_tax: "0",
        order_total: "340.07",
        order_total_tax: "0",
        order_customer_id: 4529,
        order_billing_first_name: "Ronan",
        order_billing_last_name: "Madam",
        order_billing_company: "",
        order_billing_address_1: "2015 Sarina Homebush Road",
        order_billing_address_2: "",
        order_billing_city: "Oakenden",
        order_billing_state: "QLD",
        order_billing_postcode: "4741",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0459936369",
        order_shipping_first_name: "Ronan",
        order_shipping_last_name: "Madam",
        order_shipping_company: "",
        order_shipping_address_1: "2015 Sarina Homebush Road",
        order_shipping_address_2: "",
        order_shipping_city: "Oakenden",
        order_shipping_state: "QLD",
        order_shipping_postcode: "4741",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3225,
            variation_id: 3231,
            product_name: "Passionfruit Pav - 28g - Ounce",
            quantity: 1,
            subtotal: "330",
            total: "330",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-28/",
            trade_name: "[15-07|20-08|03-09|27-10|29-11]",
          },
        ],
      },
      {
        first_name: "Cassius",
        last_name: "Collis",
        user_email: "<EMAIL>",
        zoho_contact_id: "",
        wp_user_id: 12421,
        consulting_doctor: "Dr J. Lavett",
        order_id: 16951,
        order_number: "42000011541",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 16:54:18",
        order_date_modified: "2025-06-16 17:11:22",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "120.84",
        order_total_tax: "0",
        order_customer_id: 12421,
        order_billing_first_name: "Cassius",
        order_billing_last_name: "Collis",
        order_billing_company: "",
        order_billing_address_1: "611 Old South Head road",
        order_billing_address_2: "2",
        order_billing_city: "Rose bay",
        order_billing_state: "NSW",
        order_billing_postcode: "2029",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0411474824",
        order_shipping_first_name: "Cassius",
        order_shipping_last_name: "Collis",
        order_shipping_company: "",
        order_shipping_address_1: "611 Old South Head road",
        order_shipping_address_2: "2",
        order_shipping_city: "Rose bay",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2029",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 2750,
            variation_id: 2829,
            product_name: "Cookies n’ Cuddles - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[19-02|31-03|16-04|05-05|24-06]",
          },
        ],
      },
      {
        first_name: "Kathleen",
        last_name: "Gould",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000006864301",
        wp_user_id: 569,
        consulting_doctor: "Dr. Gazale",
        order_id: 16948,
        order_number: "42000011539",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 16:27:13",
        order_date_modified: "2025-06-16 16:28:59",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "0",
        order_shipping_tax: "0",
        order_total: "360.65",
        order_total_tax: "0",
        order_customer_id: 569,
        order_billing_first_name: "Kathleen",
        order_billing_last_name: "Gould",
        order_billing_company: "",
        order_billing_address_1: "11 Theodore Street",
        order_billing_address_2: "",
        order_billing_city: "Brassall",
        order_billing_state: "QLD",
        order_billing_postcode: "4305",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0423720854",
        order_shipping_first_name: "Kathleen",
        order_shipping_last_name: "Gould",
        order_shipping_company: "",
        order_shipping_address_1: "11 Theodore Street",
        order_shipping_address_2: "",
        order_shipping_city: "Brassall",
        order_shipping_state: "QLD",
        order_shipping_postcode: "4305",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 2775,
            variation_id: 2806,
            product_name: "Toffee Glaze - 28g - Ounce",
            quantity: 1,
            subtotal: "350",
            total: "350",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-28/",
            trade_name: "[26-11|09-12|29-01|22-02|10-03]",
          },
        ],
      },
      {
        first_name: "Sheenal",
        last_name: "Singh",
        user_email: "<EMAIL>",
        zoho_contact_id: "",
        wp_user_id: 12255,
        consulting_doctor: "Dr J. Lavett",
        order_id: 16944,
        order_number: "42000011538",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 16:01:07",
        order_date_modified: "2025-06-16 19:05:59",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "233.00",
        order_total_tax: "0.82",
        order_customer_id: 12255,
        order_billing_first_name: "sheenal",
        order_billing_last_name: "singh",
        order_billing_company: "",
        order_billing_address_1: "114 Stanwell Crescent",
        order_billing_address_2: "",
        order_billing_city: "Ashcroft",
        order_billing_state: "NSW",
        order_billing_postcode: "2168",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0404886304",
        order_shipping_first_name: "sheenal",
        order_shipping_last_name: "singh",
        order_shipping_company: "",
        order_shipping_address_1: "114 Stanwell Crescent",
        order_shipping_address_2: "",
        order_shipping_city: "Ashcroft",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2168",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 153,
            variation_id: 0,
            product_name: "Rep'n Harvest Chain",
            quantity: 1,
            subtotal: "8.181818",
            total: "8.181818",
            tax: "0.82",
            tax_class: "gst",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********/",
            trade_name: "",
          },
          {
            product_id: 2750,
            variation_id: 2829,
            product_name: "Cookies n’ Cuddles - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[19-02|31-03|16-04|05-05|24-06]",
          },
          {
            product_id: 3209,
            variation_id: 3221,
            product_name: "Galactic Adventure - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[20-01|28-02|05-03|27-04|09-05]",
          },
          {
            product_id: 168,
            variation_id: 0,
            product_name: "The Seagull Buddy Pouch",
            quantity: 1,
            subtotal: "0",
            total: "0",
            tax: "0",
            tax_class: "gst",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********/",
            trade_name: "",
          },
        ],
      },
      {
        first_name: "Tod",
        last_name: "Ward",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000012187941",
        wp_user_id: 9731,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16939,
        order_number: "42000011536",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 13:23:29",
        order_date_modified: "2025-06-16 13:25:05",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "0",
        order_shipping_tax: "0",
        order_total: "340.07",
        order_total_tax: "0",
        order_customer_id: 9731,
        order_billing_first_name: "Tod",
        order_billing_last_name: "Ward",
        order_billing_company: "",
        order_billing_address_1: "43 Hargraves Rd",
        order_billing_address_2: "",
        order_billing_city: "Upper coomera",
        order_billing_state: "QLD",
        order_billing_postcode: "4209",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0434741538",
        order_shipping_first_name: "Tod",
        order_shipping_last_name: "Ward",
        order_shipping_company: "",
        order_shipping_address_1: "43 Hargraves Rd",
        order_shipping_address_2: "",
        order_shipping_city: "Upper coomera",
        order_shipping_state: "QLD",
        order_shipping_postcode: "4209",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3209,
            variation_id: 3223,
            product_name: "Galactic Adventure - 28g - Ounce",
            quantity: 1,
            subtotal: "330",
            total: "330",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-28/",
            trade_name: "[20-01|28-02|05-03|27-04|09-05]",
          },
        ],
      },
      {
        first_name: "Danika",
        last_name: "Clarkson",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000010114706",
        wp_user_id: 6287,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16937,
        order_number: "42000011535",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 12:34:23",
        order_date_modified: "2025-06-16 13:13:29",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "203.16",
        order_total_tax: "0",
        order_customer_id: 6287,
        order_billing_first_name: "Danika",
        order_billing_last_name: "Clarkson",
        order_billing_company: "",
        order_billing_address_1: "62 Peveril Street",
        order_billing_address_2: "",
        order_billing_city: "Tinonee",
        order_billing_state: "NSW",
        order_billing_postcode: "2430",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0403739566",
        order_shipping_first_name: "Danika",
        order_shipping_last_name: "Clarkson",
        order_shipping_company: "",
        order_shipping_address_1: "62 Peveril Street",
        order_shipping_address_2: "",
        order_shipping_city: "Tinonee",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2430",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3199,
            variation_id: 3204,
            product_name: "Raspberry Ripple - 14g - Half",
            quantity: 1,
            subtotal: "180",
            total: "180",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-14/",
            trade_name: "[04-05|27-06|05-07|28-08|14-09]",
          },
        ],
      },
      {
        first_name: "Patricia",
        last_name: "Rosas",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000010114759",
        wp_user_id: 6394,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16935,
        order_number: "42000011533",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 10:52:57",
        order_date_modified: "2025-06-16 10:58:53",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "120.84",
        order_total_tax: "0",
        order_customer_id: 6394,
        order_billing_first_name: "Patricia",
        order_billing_last_name: "Rosas",
        order_billing_company: "",
        order_billing_address_1: "315 Canterbury rd",
        order_billing_address_2: "",
        order_billing_city: "Canterbury",
        order_billing_state: "NSW",
        order_billing_postcode: "2193",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0456416245",
        order_shipping_first_name: "Patricia",
        order_shipping_last_name: "Rosas",
        order_shipping_company: "",
        order_shipping_address_1: "315 Canterbury rd",
        order_shipping_address_2: "",
        order_shipping_city: "Canterbury",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2193",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3209,
            variation_id: 3221,
            product_name: "Galactic Adventure - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[20-01|28-02|05-03|27-04|09-05]",
          },
        ],
      },
      {
        first_name: "Kim",
        last_name: "Taylor",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000013255316",
        wp_user_id: 12155,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16933,
        order_number: "42000011531",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-16 09:55:59",
        order_date_modified: "2025-06-16 10:23:28",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "0",
        order_shipping_tax: "0",
        order_total: "319.49",
        order_total_tax: "0",
        order_customer_id: 12155,
        order_billing_first_name: "Kim",
        order_billing_last_name: "Taylor",
        order_billing_company: "",
        order_billing_address_1: "82 Tallyann Point Road",
        order_billing_address_2: "",
        order_billing_city: "Basin View",
        order_billing_state: "NSW",
        order_billing_postcode: "2540",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0431044505",
        order_shipping_first_name: "Kim",
        order_shipping_last_name: "Taylor",
        order_shipping_company: "",
        order_shipping_address_1: "82 Tallyann Point Road",
        order_shipping_address_2: "",
        order_shipping_city: "Basin View",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2540",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 2755,
            variation_id: 2817,
            product_name: "Pink Lemonade - 14g - Half",
            quantity: 1,
            subtotal: "190",
            total: "190",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-14/",
            trade_name: "[29-09|18-10|20-11|01-12|15-01]",
          },
          {
            product_id: 2762,
            variation_id: 2821,
            product_name: "Karen’s Nightmare - 7g - 1/4",
            quantity: 1,
            subtotal: "120",
            total: "120",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[23-03|17-04|14-05|28-06|09-07]",
          },
        ],
      },
      {
        first_name: "Scott",
        last_name: "McDougall",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000013488160",
        wp_user_id: 11897,
        consulting_doctor: "Dr J. Lavett",
        order_id: 16913,
        order_number: "42000011512",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-15 11:36:24",
        order_date_modified: "2025-06-16 14:17:31",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "0",
        order_shipping_tax: "0",
        order_total: "370.94",
        order_total_tax: "0",
        order_customer_id: 11897,
        order_billing_first_name: "Scott",
        order_billing_last_name: "McDougall",
        order_billing_company: "",
        order_billing_address_1: "2 TRAFALGAR ST",
        order_billing_address_2: "",
        order_billing_city: "Woolgoolga",
        order_billing_state: "NSW",
        order_billing_postcode: "2456",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0419210545",
        order_shipping_first_name: "Scott",
        order_shipping_last_name: "McDougall",
        order_shipping_company: "",
        order_shipping_address_1: "2 TRAFALGAR ST",
        order_shipping_address_2: "",
        order_shipping_city: "Woolgoolga",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2456",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3225,
            variation_id: 3230,
            product_name: "Passionfruit Pav - 14g - Half",
            quantity: 1,
            subtotal: "180",
            total: "180",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-14/",
            trade_name: "[15-07|20-08|03-09|27-10|29-11]",
          },
          {
            product_id: 3232,
            variation_id: 3237,
            product_name: "Midnight Magic - 14g - Half",
            quantity: 1,
            subtotal: "180",
            total: "180",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-14/",
            trade_name: "[16-03|15-04|16-05|23-06|28-07]",
          },
        ],
      },
      {
        first_name: "Kimberly-claire",
        last_name: "Leung",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000009252186",
        wp_user_id: 5478,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16907,
        order_number: "42000011506",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-14 22:10:18",
        order_date_modified: "2025-06-14 22:12:01",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "223.74",
        order_total_tax: "0",
        order_customer_id: 5478,
        order_billing_first_name: "Kimberly",
        order_billing_last_name: "Leung",
        order_billing_company: "",
        order_billing_address_1: "7 belinda cres",
        order_billing_address_2: "",
        order_billing_city: "Brisbane",
        order_billing_state: "QLD",
        order_billing_postcode: "4127",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0416744531",
        order_shipping_first_name: "Kimberly",
        order_shipping_last_name: "Leung",
        order_shipping_company: "",
        order_shipping_address_1: "7 belinda cres",
        order_shipping_address_2: "",
        order_shipping_city: "Brisbane",
        order_shipping_state: "QLD",
        order_shipping_postcode: "4127",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3209,
            variation_id: 3221,
            product_name: "Galactic Adventure - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[20-01|28-02|05-03|27-04|09-05]",
          },
          {
            product_id: 3232,
            variation_id: 3236,
            product_name: "Midnight Magic - 7g - 1/4",
            quantity: 1,
            subtotal: "100",
            total: "100",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-7/",
            trade_name: "[16-03|15-04|16-05|23-06|28-07]",
          },
        ],
      },
      {
        first_name: "Glen",
        last_name: "Verdich",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000013449297",
        wp_user_id: 12220,
        consulting_doctor: "Dr H. Anjum",
        order_id: 16828,
        order_number: "42000011467",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-13 12:37:26",
        order_date_modified: "2025-06-13 16:10:56",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "326.64",
        order_total_tax: "0",
        order_customer_id: 12220,
        order_billing_first_name: "Glen",
        order_billing_last_name: "Verdich",
        order_billing_company: "",
        order_billing_address_1: "112/72 Alice St",
        order_billing_address_2: "",
        order_billing_city: "Newtown",
        order_billing_state: "NSW",
        order_billing_postcode: "2042",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0412474620",
        order_shipping_first_name: "Glen",
        order_shipping_last_name: "Verdich",
        order_shipping_company: "",
        order_shipping_address_1: "112/72 Alice St",
        order_shipping_address_2: "",
        order_shipping_city: "Newtown",
        order_shipping_state: "NSW",
        order_shipping_postcode: "2042",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3060,
            variation_id: 3063,
            product_name: "Bald Eagle - 3.5g - 1/8th",
            quantity: 1,
            subtotal: "50",
            total: "50",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-3.5/",
            trade_name: "[25-10|05-11|11-12|21-01|19-02]",
          },
          {
            product_id: 2750,
            variation_id: 2828,
            product_name: "Cookies n’ Cuddles - 3.5g - 1/8th",
            quantity: 1,
            subtotal: "50",
            total: "50",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-3.5/",
            trade_name: "[19-02|31-03|16-04|05-05|24-06]",
          },
          {
            product_id: 3199,
            variation_id: 3202,
            product_name: "Raspberry Ripple - 3.5g - 1/8th",
            quantity: 1,
            subtotal: "50",
            total: "50",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-3.5/",
            trade_name: "[04-05|27-06|05-07|28-08|14-09]",
          },
          {
            product_id: 3209,
            variation_id: 3220,
            product_name: "Galactic Adventure - 3.5g - 1/8th",
            quantity: 1,
            subtotal: "50",
            total: "50",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-3.5/",
            trade_name: "[20-01|28-02|05-03|27-04|09-05]",
          },
          {
            product_id: 3232,
            variation_id: 3235,
            product_name: "Midnight Magic - 3.5g - 1/8th",
            quantity: 1,
            subtotal: "50",
            total: "50",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-3.5/",
            trade_name: "[16-03|15-04|16-05|23-06|28-07]",
          },
          {
            product_id: 3225,
            variation_id: 3228,
            product_name: "Passionfruit Pav - 3.5g - 1/8th",
            quantity: 1,
            subtotal: "50",
            total: "50",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********-3.5/",
            trade_name: "[15-07|20-08|03-09|27-10|29-11]",
          },
        ],
      },
      {
        first_name: "Harold James",
        last_name: "Elsbury",
        user_email: "<EMAIL>",
        zoho_contact_id: "51445000006860408",
        wp_user_id: 2314,
        consulting_doctor: "Dr. Gazale",
        order_id: 16741,
        order_number: "42000011421",
        order_parent_id: 0,
        order_status: "processing",
        order_currency: "AUD",
        order_version: "9.3.3",
        order_payment_method: "invoice",
        order_payment_method_title: "Payment Link",
        order_date_created: "2025-06-12 09:37:47",
        order_date_modified: "2025-06-14 18:26:02",
        order_discount_total: "0",
        order_discount_tax: "0",
        order_shipping_total: "16.95",
        order_shipping_tax: "0",
        order_total: "223.69",
        order_total_tax: "1.81",
        order_customer_id: 2314,
        order_billing_first_name: "Harold James",
        order_billing_last_name: "Elsbury",
        order_billing_company: "",
        order_billing_address_1: "6 Atwell Court",
        order_billing_address_2: "",
        order_billing_city: "Lindisfarne",
        order_billing_state: "TAS",
        order_billing_postcode: "7015",
        order_billing_country: "AU",
        order_billing_email: "<EMAIL>",
        order_billing_phone: "0439929873",
        order_shipping_first_name: "Harold James",
        order_shipping_last_name: "Elsbury",
        order_shipping_company: "",
        order_shipping_address_1: "6 Atwell Court",
        order_shipping_address_2: "",
        order_shipping_city: "Lindisfarne",
        order_shipping_state: "TAS",
        order_shipping_postcode: "7015",
        order_shipping_country: "AU",
        order_items: [
          {
            product_id: 3060,
            variation_id: 3065,
            product_name: "Bald Eagle - 14g - Half",
            quantity: 1,
            subtotal: "180",
            total: "180",
            tax: "0",
            tax_class: "gst-free",
            tax_status: "none",
            product_sku: "https://harvest.delivery/*********-14/",
            trade_name: "[25-10|05-11|11-12|21-01|19-02]",
          },
          {
            product_id: 170,
            variation_id: 0,
            product_name: "Travelling Tool Kit",
            quantity: 1,
            subtotal: "18.136364",
            total: "18.136364",
            tax: "1.81",
            tax_class: "gst",
            tax_status: "taxable",
            product_sku: "https://harvest.delivery/*********/",
            trade_name: "",
          },
        ],
      },
    ],
  },
};
