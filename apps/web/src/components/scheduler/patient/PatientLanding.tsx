import React from 'react';
import { But<PERSON>, Box, Typography } from '@mui/material';

const PatientLanding: React.FC<{ onContinue: () => void }> = ({ onContinue }) => (
  <Box sx={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
    {/* Header */}
    <Box
      sx={{
        width: '100%',
        background: '#007F00',
        py: 3,
        display: { xs: 'block', md: 'none' },
        textAlign: 'center',
      }}
    >
      <Typography variant="h4" sx={{ color: 'white', fontWeight: 700, letterSpacing: 1, display: 'inline' }}>
        Zenith<span style={{ fontWeight: 400, color: 'white', display: 'inline' }}>Clinics</span>
      </Typography>
      <Typography sx={{ color: 'white', fontSize: 14, letterSpacing: 4, mt: 0 }}>
        Clinic & Dispensary
      </Typography>
    </Box>
    {/* Top Explanation */}
    <Box sx={{ mt: 3, mb: 1, px: 2, maxWidth: 560, mx: 'auto', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
      <Typography 
        variant="subtitle1"
        sx={{ 
          color: '#388e3c', 
          fontWeight: 400, 
          textAlign: 'center',
          letterSpacing: 0.2,
        }}
      >
        Welcome to ZenithClinics
      </Typography>
      <Typography 
        variant="subtitle1"
        sx={{ 
          color: '#388e3c', 
          fontWeight: 400, 
          textAlign: 'center',
          letterSpacing: 0.2,
        }}
      >
        Your online doctor consultation platform.<br />
        Connect with licensed doctors from the comfort of your home.
      </Typography>
    </Box>
    {/* Main Content */}
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', flex: 1, p: 2, mt: 0 }}>
      <Typography variant="h4" sx={{ mb: 2, fontWeight: 700, textAlign: 'center', color: '#007F00' }}>
        How Consultations Work
      </Typography>
      <Box sx={{ width: '100%', maxWidth: 560, mb: 3 }}>
        <div style={{ position: 'relative', paddingBottom: '56.25%', height: 0 }}>
          <iframe
            src="https://www.youtube.com/embed/0b-lfpbINu4"
            title="How Consultations Work"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
          />
        </div>
      </Box>
      <Button
        variant="contained"
        color="primary"
        size="large"
        onClick={onContinue}
        sx={{
          fontWeight: 700,
          fontSize: 18,
          px: 4,
          py: 1.5,
          background: '#007F00',
          '&:hover': { background: '#005a00' },
        }}
      >
        Continue to Booking
      </Button>
    </Box>
    {/* Bottom Explanation */}
    <Box sx={{ mt: 4, mb: 2, px: 2, maxWidth: 560, mx: 'auto' }}>
      <Typography 
        variant="body2"
        sx={{ 
          color: '#555', 
          fontWeight: 400, 
          textAlign: 'center',
          letterSpacing: 0.1,
        }}
      >
        Your privacy and health are our top priorities throughout your consultation experience.
      </Typography>
    </Box>
    {/* Footer */}
    <Box
      sx={{
        borderTop: '1px solid #444',
        pt: 2,
        pb: 0,
        ml: 3,
        mr: 3,
        textAlign: 'center',
        display: { xs: 'block', md: 'none' },
        background: 'white',
      }}
    >
      <Typography sx={{ fontSize: 18 }}>
        Provided by <Box component="span" sx={{ color: '#007F00', fontWeight: 700, display: 'inline' }}>ZenithClinics</Box> Pty Ltd
      </Typography>
    </Box>
  </Box>
);

export default PatientLanding; 