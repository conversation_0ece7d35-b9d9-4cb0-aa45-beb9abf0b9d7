# Stripe API Sync Environment Variables

This document outlines the environment variables required for the new Stripe API-based sync system.

## Required Environment Variables

### Existing Stripe Configuration (Already Configured)
```bash
# Production Stripe API key (required)
STRIPE_API_KEY=sk_live_your_stripe_key

# Existing sales reporting configuration
SALES_REPORTING_ENABLED=true
SLACK_SALES_REPORT_CHANNEL=C1234567890
SLACK_TOKEN=xoxb-your-slack-bot-token
```

### New Stripe API Sync Configuration

#### Core Sync Settings
```bash
# Enable/disable Stripe API sync (default: false)
STRIPE_SYNC_ENABLED=true

# Stripe API version to use (default: 2025-06-30.basil - latest)
STRIPE_API_VERSION=2025-06-30.basil
```

#### Performance & Rate Limiting
```bash
# Maximum retry attempts for failed API calls (default: 3)
STRIPE_MAX_RETRIES=3

# API request timeout in milliseconds (default: 10000)
STRIPE_TIMEOUT=10000

# Batch size for processing payments (default: 100)
STRIPE_SYNC_BATCH_SIZE=100

# Delay between API calls in milliseconds (default: 100)
STRIPE_RATE_LIMIT_DELAY=100
```

## Environment-Specific Configurations

### Development Environment
```bash
# Development settings
NODE_ENV=development
STRIPE_SYNC_ENABLED=true
STRIPE_SYNC_BATCH_SIZE=50
STRIPE_RATE_LIMIT_DELAY=200
```

### Production Environment
```bash
# Production settings
NODE_ENV=production
STRIPE_SYNC_ENABLED=true
STRIPE_SYNC_BATCH_SIZE=100
STRIPE_RATE_LIMIT_DELAY=100
STRIPE_MAX_RETRIES=3
```

## Configuration Validation

The system will validate configuration on startup:

1. **STRIPE_API_KEY** - Must be present for production environment
2. **STRIPE_SYNC_ENABLED** - Must be 'true' to enable API sync
3. **Numeric values** - Must be valid positive integers
4. **API connectivity** - System will test Stripe API connection on startup

## Default Values

If environment variables are not set, the system uses these defaults:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| STRIPE_SYNC_ENABLED | false | API sync disabled by default |
| STRIPE_API_VERSION | 2023-10-16 | Latest stable API version |
| STRIPE_MAX_RETRIES | 3 | Reasonable retry count |
| STRIPE_TIMEOUT | 10000 | 10 second timeout |
| STRIPE_SYNC_BATCH_SIZE | 100 | Stripe's recommended batch size |
| STRIPE_RATE_LIMIT_DELAY | 100 | Conservative rate limiting |

## Setup Instructions

### 1. Add Environment Variables

Add the required variables to your `.env` file:

```bash
# Add to your .env file
STRIPE_SYNC_ENABLED=true
STRIPE_API_VERSION=2023-10-16
STRIPE_MAX_RETRIES=3
STRIPE_TIMEOUT=10000
STRIPE_SYNC_BATCH_SIZE=100
STRIPE_RATE_LIMIT_DELAY=100
```

### 2. Restart Application

Restart your application to load the new configuration:

```bash
# Restart your application
npm run dev  # or your production restart command
```

### 3. Verify Configuration

Check that configuration is loaded correctly:

```bash
# Test configuration endpoint
curl http://localhost:5000/api/sales/v1/status

# Test Stripe API connectivity
curl -X POST http://localhost:5000/api/sales/v1/test-stripe-connection
```

## Troubleshooting

### Common Issues

**Stripe API sync not working:**
- Check that `STRIPE_SYNC_ENABLED=true`
- Verify `STRIPE_API_KEY` is set correctly
- Ensure API key has read permissions for payment intents

**Performance issues:**
- Reduce `STRIPE_SYNC_BATCH_SIZE` if experiencing timeouts
- Increase `STRIPE_RATE_LIMIT_DELAY` if hitting rate limits
- Increase `STRIPE_TIMEOUT` for slow network connections

**Rate limiting errors:**
- Increase `STRIPE_RATE_LIMIT_DELAY` to 200-500ms
- Reduce `STRIPE_SYNC_BATCH_SIZE` to 50 or lower
- Check Stripe dashboard for rate limit information

### Monitoring

Monitor sync performance and adjust settings based on:

1. **Sync duration** - Should complete within 5 minutes for daily sync
2. **Error rates** - Should be < 1% for stable operation
3. **API usage** - Monitor Stripe dashboard for rate limit warnings

## Migration Notes

### From Webhook to API Sync

When migrating from webhook-based to API-based sync:

1. **Keep both systems running** initially for validation
2. **Compare data accuracy** between webhook and API sync
3. **Monitor performance** and adjust batch sizes as needed
4. **Gradually disable webhook** processing after validation

### Rollback Plan

If issues occur, you can quickly rollback:

1. Set `STRIPE_SYNC_ENABLED=false`
2. Re-enable webhook processing for sales data
3. Restart application
4. Investigate and fix issues before re-enabling API sync

## Security Considerations

- **API Keys**: Never commit API keys to version control
- **Environment Files**: Ensure `.env` files are in `.gitignore`
- **Production Keys**: Use separate API keys for production and development
- **Access Control**: Limit API key permissions to minimum required (read-only for sync)
