import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import svgr from "vite-plugin-svgr";
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), svgr()],
  server: {
     // @ts-ignore
     allowedHosts: ["zenith.ad","doctor.zenith.clinic", "localhost","medicine-register.simdoar.net"],
    port: 5173,
  },
  preview: {
    port: 5173,
  },
  build: {
    outDir: "dist",
    sourcemap: false, // Disable source maps in production
  },
});
