import { createServer } from './config/express';
import http from 'http';
import config from './config/index';
import { logger } from './config/logger';
import { AddressInfo } from 'net';
import { WebSocketManager } from './helpers/webSocketManager';

const startServer = async () => {
  const app = createServer();
  const server = http.createServer(app).listen({ host: config.host, port: config.port }, async () => {
    const addressInfo = server.address() as AddressInfo;
    logger.info(`Server ready at http://${addressInfo.address}:${addressInfo.port} (${config.env} environment)`);
  });

  WebSocketManager.setUp()

  const signalTraps: NodeJS.Signals[] = ['SIGTERM', 'SIGINT', 'SIGUSR2', 'SIGQUIT'];

  signalTraps.forEach((signal) => {
    process.once(signal, async () => {
      logger.info(`process.once ${signal}`);
      server.close(() => {
        logger.debug('HTTP server closed');
      });
      process.exit()
    });
  });
};

startServer();