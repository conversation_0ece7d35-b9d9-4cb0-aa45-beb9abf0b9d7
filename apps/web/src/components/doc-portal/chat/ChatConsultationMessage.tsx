import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { useChatEmail } from '../../../hooks/chat-email-provider';

const ChatConsultationMessage: React.FC = () => {
    const { patient } = useChatEmail();

    return (
        <Paper 
            elevation={0} 
            sx={{ 
                p: { xs: 2, sm: 3 }, 
                bgcolor: '#f8f9fa',
                borderRadius: '8px',
                width: '100%'
            }}
        >
            <Typography variant="body1" sx={{ mb: 1.5, color: '#2d3748' }}>
                Hi Doctor,
            </Typography>
            <Typography variant="body1" sx={{ mb: 1.5, color: '#2d3748' }}>
                You are about to prepare a treatment plan for:
            </Typography>
            <Box sx={{ ml: 2, mb: 1.5 }}>
                <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#2d3748' }}>
                    {patient?.fullName || 'Patient Name Not Available'}
                </Typography>
                <Typography variant="body2" sx={{ color: '#718096' }}>
                    {patient?.returningPatient ? 'Returning Patient' : 'New Patient'}
                </Typography>
            </Box>
            <Typography variant="body1" sx={{ color: '#2d3748' }}>
                Please review and complete each step to generate the patient's treatment plan email.
            </Typography>
        </Paper>
    );
};

export default ChatConsultationMessage; 