import config from '../config';
import axios, { AxiosError } from 'axios';
import { formatDate, extractValues, extractOrderId } from '../utils/misc';
import { db } from '../utils/db';
import { logger } from '../config/logger';
import { WebClient } from '@slack/web-api';

interface ZohoTokenResponse {
  access_token: string;
  expires_in: number; // Expiry time in seconds
}

export class ZohoAuth {
  private static zohoAccessToken: string | null = null;
  private static tokenExpirationTime: number | null = null;
  private static zohoInventoryAccessToken: string | null = null;
  private static tokenInventoryExpirationTime: number | null = null;
  private static zohoBooksAccessToken: string | null = null;
  private static tokenBooksExpirationTime: number | null = null;

  // Method to get Zoho Access Token
  public static async getAccessToken(): Promise<string> {
    const currentTime = Date.now();
    if (this.zohoAccessToken && this.tokenExpirationTime && currentTime < this.tokenExpirationTime) {
      return this.zohoAccessToken;
    }

    // Otherwise, request a new token
    return await this.refreshAccessToken();
  }

  public static async getInventoryAccessToken(): Promise<string> {
    const currentTime = Date.now();
    if (
      this.zohoInventoryAccessToken &&
      this.tokenInventoryExpirationTime &&
      currentTime < this.tokenInventoryExpirationTime
    ) {
      return this.zohoInventoryAccessToken;
    }

    // Otherwise, request a new token
    return await this.refreshAccessToken(true);
  }

  public static async getBooksAccessToken(): Promise<string> {
    const currentTime = Date.now();
    if (this.zohoBooksAccessToken && this.tokenBooksExpirationTime && currentTime < this.tokenBooksExpirationTime) {
      return this.zohoBooksAccessToken;
    }

    // Otherwise, request a new token
    return await this.refreshBooksAccessToken();
  }

  public static async getHeaders() {
    const token = await this.getAccessToken();
    const headers = {
      Authorization: `Zoho-oauthtoken ${token}`,
      'Content-Type': 'application/json',
    };
    return headers;
  }

  public static async getInventoryHeaders() {
    const token = await this.getInventoryAccessToken();
    const headers = {
      Authorization: `Zoho-oauthtoken ${token}`,
      'Content-Type': 'application/json',
    };
    return headers;
  }

  public static async getBooksHeaders() {
    const token = await this.getBooksAccessToken();
    const headers = {
      Authorization: `Zoho-oauthtoken ${token}`,
      'Content-Type': 'application/json',
    };
    return headers;
  }

  public static async forcedTokenRefresh() {
    await this.refreshAccessToken();
  }

  public static async getZohoLeadById(leadId: string) {
    const headers = await this.getHeaders();
    const response = await axios.get(`${zohoLeadURL}/${leadId}`, { headers });
    let jsonResponse;
    if (response.status === 200) {
      jsonResponse = response.data;
    } else {
      jsonResponse = null;
    }
    return jsonResponse;
  }

  public static async updateDealsPaymentSteps(step: string, orderid: string) {
    const headers = await this.getHeaders();
    const response = await axios.get(`${zohoDealURL}/search?criteria=(Supply_Serial_Number_1:equals:${orderid})`, {
      headers,
    });
    let jsonResponse;
    if (response.status === 200) {
      jsonResponse = {
        success: false,
        dealid: 0,
      };

      if (response.data.data != undefined && response.data.data.length > 0) {
        const dealid = response.data.data[0].id;

        const data = {
          data: [
            {
              Deal_Progress: step + '',
            },
          ],
        };

        const result = await axios.put(`${zohoDealURL}/${dealid}`, data, { headers });
        if (response.status !== 200) {
          jsonResponse = result;
        } else {
          const updatedid = result.data.data?.[0].details?.id;

          if (updatedid != undefined) {
            jsonResponse = {
              success: true,
              dealid: updatedid,
            };
          }
        }
      }
    } else {
      jsonResponse = null;
    }
    return jsonResponse;
  }

  public static async getInvoiceByOrderId(orderId: string) {
    const headers = await this.getInventoryHeaders();
    const url = `${zohoInvoiceURL}?reference_number=${orderId}`;
    const response = await axios.get(url, {
      headers,
    });
    let jsonResponse;

    if (response.status === 200) {
      jsonResponse = response.data;
      if (response != null && response.data.invoices && response.data.invoices.length > 0) {
        const invoiceId = jsonResponse.invoices[0].invoice_id;

        const payload = {
          payment_options: {
            payment_gateways: [
              {
                gateway_name: 'stripe',
              },
            ],
          },
        };

        await axios.put(`${zohoInvoiceURL}/${invoiceId}`, payload, {
          headers,
        });
      }
    } else {
      jsonResponse = null;
    }
    return jsonResponse;
  }

  public static async getZohoLeadByEmail(email: string) {
    const headers = await this.getHeaders();
    const response = await axios.get(`${zohoLeadURL}/search?criteria=(Email:equals:${email})`, { headers });
    let jsonResponse;
    if (response.status === 200) {
      jsonResponse = response.data;
    } else {
      jsonResponse = null;
    }
    return jsonResponse;
  }

  public static async getZohoContactByEmail(email: string) {
    const headers = await this.getHeaders();
    const response = await axios.get(`${zohoContactURL}/search?criteria=(Email:equals:${email})`, { headers });
    let jsonResponse;
    if (response.status === 200) {
      jsonResponse = response.data;
    } else {
      jsonResponse = null;
    }
    return jsonResponse;
  }

  // Method to refresh Zoho Access Token

  private static async refreshAccessToken(inventory?: boolean): Promise<string> {
    const params = new URLSearchParams();

    params.append('client_id', config.zohoClient as string);
    params.append('client_secret', config.zohoSecret as string);
    if (inventory == undefined || inventory == false) params.append('refresh_token', config.zohoRefreshToken as string);
    else params.append('refresh_token', config.zohoInventoryRefreshToken as string);
    params.append('grant_type', 'refresh_token');

    try {
      const response = await axios.post<ZohoTokenResponse>('https://accounts.zoho.com.au/oauth/v2/token', params);
      this.zohoAccessToken = response.data.access_token;
      this.tokenExpirationTime = Date.now() + (response.data.expires_in - 60) * 1000; // Subtract 1 minute to ensure it's not used after expiration

      return this.zohoAccessToken;
    } catch (error) {
      const axiosError = error as AxiosError;
      console.error('Axios error fetching Zoho access token:', axiosError.response?.data || axiosError.message);
      throw new Error(`Axios error: ${axiosError.message}`);
    }
  }

  private static async refreshBooksAccessToken(): Promise<string> {
    const params = new URLSearchParams();

    params.append('client_id', config.zohoBooksClient as string);
    params.append('client_secret', config.zohoBooksSecret as string);
    params.append('refresh_token', config.zohoBooksRefreshToken as string);
    params.append('grant_type', 'refresh_token');

    try {
      const response = await axios.post<ZohoTokenResponse>('https://accounts.zoho.com.au/oauth/v2/token', params);
      this.zohoBooksAccessToken = response.data.access_token;
      this.tokenBooksExpirationTime = Date.now() + (response.data.expires_in - 60) * 1000; // Subtract 1 minute to ensure it's not used after expiration

      return this.zohoBooksAccessToken;
    } catch (error) {
      const axiosError = error as AxiosError;
      console.error('Axios error fetching Zoho books access token:', axiosError.response?.data || axiosError.message);
      throw new Error(`Axios error: ${axiosError.message}`);
    }
  }

  public static async getAuthorizationCode(clientId: string): Promise<{ authCode: string; redirectUri: string }> {
    const scopes = 'ZohoCRM.modules.ALL,ZohoCRM.settings.ALL,ZohoInventory.FullAccess.all';
    const redirectUri = 'http://localhost';

    const authUrl = `https://accounts.zoho.com.au/oauth/v2/auth?scope=${encodeURIComponent(
      scopes,
    )}&client_id=${clientId}&response_type=code&access_type=offline&redirect_uri=${encodeURIComponent(redirectUri)}`;

    console.log('\n=== Step 1: Get Authorization Code ===');
    console.log('1. Copy this URL and paste it in your browser:', authUrl);
    console.log('2. Log in to Zoho if prompted');
    console.log('3. Authorize the application');
    console.log("4. You'll be redirected to a URL containing the code");
    console.log("5. Copy the 'code' parameter from the URL\n");

    // const authCode = await askQuestion("Enter the authorization code from the URL: ");
    const authCode = '';
    return { authCode: authCode.trim(), redirectUri };
  }

  public static async getRefreshToken(clientId: string, clientSecret: string, authCode: string, redirectUri: string) {
    console.log('\n=== Step 2: Getting Refresh Token ===');
    const tokenUrl = 'https://accounts.zoho.com.au/oauth/v2/token';

    try {
      const response = await axios.post(
        tokenUrl,
        JSON.stringify({
          grant_type: 'authorization_code',
          client_id: clientId,
          client_secret: clientSecret,
          code: authCode,
          redirect_uri: redirectUri,
        }),
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        },
      );

      console.log('\nSuccess! Here are your tokens:');
      console.log('Refresh Token:', response.data.refresh_token);
      console.log('Access Token:', response.data.access_token);
      return response.data;
    } catch (e) {
      const error = e as AxiosError;
      console.error('\nError getting tokens:', error.response?.data || error.message);
      return null;
    }
  }

  public static async getPaymentLinkByOrderId(orderId: string) {
    let headers = await this.getBooksHeaders();
    const notifications: { message: string; type: string }[] = [];

    logger.info(`Getting invoice :: OID: ${orderId}`);

    const invoiceUrl = `${zohoBooksURL}/invoices/?reference_number=${orderId}`;
    const response = await axios.get(invoiceUrl, {
      headers,
    });
    let jsonResponse;
    const client = await db.connect();

    if (response.status === 200) {
      jsonResponse = response.data;
      if (response != null && response.data.invoices && response.data.invoices.length > 0) {
        const customerId = jsonResponse.invoices[0].customer_id;
        const customer_name = jsonResponse.invoices[0].customer_name;
        const email = jsonResponse.invoices[0].email;

        let payment_link = '';
        let expiryDate = formatDate(new Date(Date.now() + 1 * 24 * 60 * 60 * 1000)); // expires in 1 day
        let total = jsonResponse.invoices[0].total;
        let invoice_id = jsonResponse.invoices[0].invoice_id;

        logger.info(`Customer Id for invoice :: ${invoice_id}`);

        const existingLink = await client.query(
          `SELECT payment_link_id, payment_link, amount, expiry_date, invoice_id
          FROM paymentlinks WHERE customer_id = $1 AND order_id = $2 LIMIT 1`,
          [customerId, orderId],
        );

        if (existingLink.rows.length > 0) {
          const current = existingLink.rows[0];

          const payment_link_id = current.payment_link_id;
          payment_link = current.payment_link;
          expiryDate = current.expiry_date;
          total = current.amount;
          invoice_id = current.invoice_id;

          logger.info(`Payment link id from DB :: ${payment_link_id}`);

          jsonResponse = {
            payment_link: {
              url: payment_link,
              payment_amount: total,
            },
          };
        } else {
          const payload = {
            customer_id: customerId,
            payment_amount: total,
            description: `Payment Link for order #${orderId}`,
            expiry_time: expiryDate,
          };

          headers = await this.getBooksHeaders();

          const linkResponse = await axios.post(`${zohoBooksURL}/paymentlinks`, payload, {
            headers,
          });

          if (linkResponse.status === 200) {
            jsonResponse = linkResponse.data;

            if (existingLink.rows.length == 0) {
              const payment_link_id = jsonResponse.payment_link.payment_link_id;
              const payment_link_number = jsonResponse.payment_link.payment_link_number;
              payment_link = jsonResponse.payment_link.url;

              logger.info(`Payment link id inserted DB :: ${payment_link_id}`);
              notifications.push({
                message: `${customer_name} payment link generated | ${email} | ${orderId} | ${payment_link} | ${total} | *[ZOHO]*`,
                type: 'notice',
              });

              const query = `
              INSERT INTO paymentlinks
              ("customer_id", "payment_link_id", "payment_link","amount", "payment_status", "expiry_date", "created_at", "invoice_id", "order_id", "payment_link_number")
              VALUES ($1, $2, $3, $4,$5,$6,CURRENT_TIMESTAMP,$7,$8,$9)
              `;

              await client.query(query, [
                customerId,
                payment_link_id,
                payment_link,
                total,
                'generated',
                expiryDate,
                invoice_id,
                orderId,
                payment_link_number,
              ]);

              try {
                const dealHeaders = await this.getHeaders();
                const response = await axios.get(
                  `${zohoDealURL}/search?criteria=(Supply_Serial_Number_1:equals:${orderId})`,
                  { headers: dealHeaders },
                );
                if (response.status === 200) {
                  if (response.data.data != undefined && response.data.data.length > 0) {
                    const dealid = response.data.data[0].id;

                    const data = {
                      data: [
                        {
                          Payment_Link_URL: payment_link + '',
                        },
                      ],
                    };

                    axios.put(`${zohoDealURL}/${dealid}`, data, { headers: dealHeaders }).catch((err) => {
                      logger.info(`Payment link to Deal sync error :: ${err}`);
                    });
                  }
                } else {
                  logger.info(`Payment link to Deal sync error :: Deal not found (${orderId})`);
                }
              } catch (err) {
                logger.info(`Payment link to Deal sync error :: ${err}`);
              }
            }
          } else {
            logger.info(`Payment link generation error`);

            notifications.push({
              message: `${customer_name} failed to generate payment link | ${email} | ${orderId} | ${email} | ${total} | *[ZOHO]*`,
              type: 'error',
            });
          }
        }
      } else {
        logger.info(`Getting invoice error :: OID ${orderId}`);
        notifications.push({ message: `Could not find invoice for OID: ${orderId} | *[ZOHO]*`, type: 'error' });
        jsonResponse = null;
      }
    } else {
      logger.info(`Getting invoice error :: OID ${orderId}`);
      notifications.push({ message: `Could not find invoice for OID: ${orderId} | *[ZOHO]*`, type: 'error' });
      client.release();
      jsonResponse = null;
    }

    if (notifications.length > 0) {
      await this.sendToSlack(notifications);
    }

    client.release();
    return jsonResponse;
  }

  public static async setZohoInvoicePaid(paymentNumber: string) {
    let jsonResponse;

    const client = await db.connect();

    const existingLink = await client.query(
      `SELECT payment_link_id, invoice_id
      FROM paymentlinks WHERE payment_link_number = $1 LIMIT 1`,
      [paymentNumber],
    );

    if (existingLink.rows.length > 0) {
      const current = existingLink.rows[0];

      const payment_link_id = current.payment_link_id;
      const invoice_id = current.invoice_id;

      const headers = await this.getBooksHeaders();
      const paymentLinkUrl = `${zohoBooksURL}/paymentlinks/${payment_link_id}`;
      const response = await axios.get(paymentLinkUrl, {
        headers,
      });

      if (response.status === 200) {
        jsonResponse = response.data;
        if (response != null && response.data.payment_link && response.data.payment_link.status == 'paid') {
          const payments = jsonResponse.payment_link.customer_payments;

          const credits: { payment_id: string; payment_amount: number }[] = payments.map((payment) => ({
            payment_id: payment.payment_id,
            amount_applied: payment.amount,
          }));

          const payload = {
            invoice_payments: credits,
          };

          if (credits.length > 0) {
            const updateResponse = await axios.post(`${zohoBooksURL}/invoices/${invoice_id}/credits`, payload, {
              headers,
            });

            if (updateResponse.status === 200) {
              jsonResponse = updateResponse.data;

              const updateQuery = `
              UPDATE paymentlinks
              SET "payment_status" = $1
              WHERE "invoice_id" = $2
              `;

              await client.query(updateQuery, ['paid', invoice_id]);
            }
          }
        }
      } else {
        client.release();
        jsonResponse = null;
      }
    }

    client.release();

    return jsonResponse;
  }

  public static async setZohoInvoicePaymentStatusFromStripe(
    paymentStatus: string,
    paymentDescription: string,
    name: string,
    email: string,
    amount: number,
    message: string,
  ) {
    const notifications: { message: string; type: string }[] = [];
    let jsonResponse;
    const client = await db.connect();
    let orderId = '';

    const code = extractValues(paymentDescription);
    if (code == null) {
      client.release();

      logger.info(`Unknown payment :: ${paymentDescription}`);

      if (paymentStatus != 'payment_intent.succeeded') {
        notifications.push({
          message: `${name} payment failed for legacy link | ${email} | ${paymentDescription} | ${paymentStatus} | *[STRIPE]*`,
          type: 'error',
        });
      } else {
        notifications.push({
          message: `${name} payment was successful for legacy link | ${email} | ${paymentDescription} | ${amount} | *[STRIPE]*`,
          type: 'error',
        });
      }

      if (notifications.length > 0) {
        await this.sendToSlack(notifications);
      }

      return {
        error: 'Unknown payment',
        payment_description: paymentDescription,
      };
    }

    const paymentNumber = code.plCode;

    if (paymentStatus != 'payment_intent.succeeded') {
      const updateQuery = `
        UPDATE paymentlinks
        SET "payment_status" = $1
        WHERE "payment_link_number" = $2
        RETURNING *`;

      await client.query(updateQuery, [paymentStatus, paymentNumber]);

      client.release();

      logger.info(`Payment unsuccessful :: ${paymentStatus}`);
      notifications.push({
        message: `${name} payment unsuccessful | ${email} | ${paymentDescription} | ${amount} | ${paymentStatus} | *[STRIPE]*\n\n${message}`,
        type: 'notice',
      });

      if (notifications.length > 0) {
        await this.sendToSlack(notifications);
      }

      return {
        error: 'Payment unsuccessful',
        payment_status: paymentStatus,
      };
    }

    const existingLink = await client.query(
      `SELECT payment_link_id, invoice_id, payment_status, order_id
      FROM paymentlinks WHERE payment_link_number = $1 LIMIT 1`,
      [paymentNumber],
    );

    if (existingLink.rows.length > 0) {
      const current = existingLink.rows[0];

      const payment_link_id = current.payment_link_id;
      const invoice_id = current.invoice_id;
      const dbpayment_status = current.payment_status;
      orderId = current.order_id;

      if (dbpayment_status == 'paid') {
        client.release();

        logger.info(`Payment already made :: ${dbpayment_status}`);

        return {
          error: 'Payment already made',
          payment_status: dbpayment_status,
        };
      }

      const headers = await this.getBooksHeaders();
      const paymentLinkUrl = `${zohoBooksURL}/paymentlinks/${payment_link_id}`;
      const response = await axios.get(paymentLinkUrl, {
        headers,
      });

      if (response.status === 200) {
        const responseData = response.data;
        if (response != null && response.data.payment_link && response.data.payment_link.status == 'paid') {
          const payments = responseData.payment_link.customer_payments;

          const credits: { payment_id: string; payment_amount: number }[] = payments.map((payment) => ({
            payment_id: payment.payment_id,
            amount_applied: payment.amount,
          }));

          const payload = {
            invoice_payments: credits,
          };

          if (credits.length > 0) {
            const updateResponse = await axios.post(`${zohoBooksURL}/invoices/${invoice_id}/credits`, payload, {
              headers,
            });

            if (updateResponse.status >= 200 && updateResponse.status < 300) {
              const updateQuery = `
              UPDATE paymentlinks
              SET "payment_status" = $1
              WHERE "payment_link_number" = $2
              RETURNING *`;

              await client.query(updateQuery, ['paid', paymentNumber]);

              logger.info(`Payment successful :: ${orderId} ${paymentDescription}`);
              notifications.push({
                message: `${name} payment was successful | ${email} | ${orderId} | ${paymentDescription} | ${amount} | *[STRIPE]*`,
                type: 'notice',
              });

              try {
                const dealHeaders = await this.getHeaders();
                const response = await axios.get(
                  `${zohoDealURL}/search?criteria=(Supply_Serial_Number_1:equals:${orderId})`,
                  { headers: dealHeaders },
                );
                if (response.status === 200) {
                  if (response.data.data != undefined && response.data.data.length > 0) {
                    const dealid = response.data.data[0].id;

                    this.updateZohoWithRetry(dealid, orderId, name);

                    axios
                      .put(`${shopURL}/orderPaid/${orderId}`, {})
                      .then((response) => {
                        return response.data;
                      })
                      .then((data) => {
                        logger.info(`Paid status to Shop updated :: ${orderId}`);
                        logger.info(`Request :: ${data}`);
                      })
                      .catch((err) => {
                        logger.info(`Paid status to Shop sync error :: ${err}`);
                      });
                  }
                } else {
                  logger.info(`Paid status to Deal sync error :: Deal not found (${orderId})`);
                  this.updateZohoWithRetry(null, orderId, name);
                }
              }
              catch (err) {
                notifications.push({ message: `${name} Paid status failed to update in Deal | Unexpected Error | *[ZOHO]*`, type: 'error' });
                notifications.push({ message: `\`\`\`${err} \`\`\``, type: 'error' });
                notifications.push({ message: `${name} update will retry | *[ZOHO]*`, type: 'error' });
                logger.info(`Paid status to Deal sync error :: ${err}`);
                this.updateZohoWithRetry(null, orderId, name);
              }
            } else {
              logger.info(`Payment update issue :: ${orderId}`);
              logger.info(updateResponse);

              notifications.push({ message: `Payment update issue: ${orderId}`, type: 'error' });
              notifications.push({ message: `\`\`\` ${JSON.stringify(updateResponse)} \`\`\``, type: 'error' });

            }
          }
        } else {
          notifications.push({
            message: `${name} payment made but not marked as paid in Zoho | ${email} | ${amount} | Payment link ID: ${payment_link_id} | Will retry | *[ZOHO]*`,
            type: 'error',
          });

          jsonResponse = {
            error: 'not_marked_paid',
            message: 'Payment not marked as paid in Zoho',
            payment_link_id: payment_link_id,
            zoho_response: responseData,
          };
        }
      } else {
        logger.info(`Payment not found :: Payment link ID: ${payment_link_id}`);
        notifications.push({
          message: `${name} payment not found | ${email} | ${amount} | Payment link ID: ${payment_link_id} | *[DB]*`,
          type: 'error',
        });

        jsonResponse = {
          error: 'Payment not found',
          payment_link_id: payment_link_id,
        };
      }
    } else {
      const headers = await this.getBooksHeaders();
      const paymentLinkUrl = `${zohoBooksURL}/paymentlinks?payment_link_number=${paymentNumber}`;
      const response = await axios.get(paymentLinkUrl, {
        headers,
      });

      if (response.status === 200) {
        const responseData = response.data;
        if (response != null && response.data.payment_links && response.data.payment_links.length > 0) {
          const description = responseData.payment_links[0].description;
          const customerId = responseData.payment_links[0].customer_id;
          const payment_link_id = responseData.payment_links[0].payment_link_id;
          const payment_link = responseData.payment_links[0].url;
          const expiryDate = responseData.payment_links[0].expiry_time;
          const oid = extractOrderId(description);
          const customer_name = responseData.payment_links[0].customer_name;
          let total = 0;
          let invoice_id = '';

          if (oid) {
            const invoiceUrl = `${zohoBooksURL}/invoices/?reference_number=${oid}`;
            const response = await axios.get(invoiceUrl, {
              headers,
            });
            let jsonResponse;

            if (response.status === 200) {
              jsonResponse = response.data;
              if (response != null && response.data.invoices && response.data.invoices.length > 0) {
                total = jsonResponse.invoices[0].total;
                invoice_id = jsonResponse.invoices[0].invoice_id;
              }

              const query = `
              INSERT INTO paymentlinks
              ("customer_id", "payment_link_id", "payment_link","amount", "payment_status", "expiry_date", "created_at", "invoice_id", "order_id", "payment_link_number")
              VALUES ($1, $2, $3, $4,$5,$6,CURRENT_TIMESTAMP,$7,$8,$9)
              `;

              await client.query(query, [
                customerId,
                payment_link_id,
                payment_link,
                total,
                'paid',
                expiryDate,
                invoice_id,
                oid,
                paymentNumber,
              ]);

              notifications.push({
                message: `${customer_name} payment was successful | ${email} | ${oid} | ${paymentDescription} | ${amount} | *[STRIPE]*`,
                type: 'notice',
              });

              try {
                const dealHeaders = await this.getHeaders();
                const response = await axios.get(
                  `${zohoDealURL}/search?criteria=(Supply_Serial_Number_1:equals:${oid})`,
                  { headers: dealHeaders },
                );
                if (response.status === 200) {
                  if (response.data.data != undefined && response.data.data.length > 0) {
                    const dealid = response.data.data[0].id;

                    this.updateZohoWithRetry(dealid, orderId, name);

                    axios
                      .put(`${shopURL}/orderPaid/${oid}`, {})
                      .then((response) => {
                        return response.data;
                      })
                      .then((data) => {
                        logger.info(`Paid status to Shop updated :: ${oid}`);
                        logger.info(`Request :: ${data}`);
                      })
                      .catch((err) => {
                        logger.info(`Paid status to Shop sync error :: ${err}`);
                      });
                  }
                } else {
                  logger.info(`Paid status to Deal sync error :: Deal not found (${oid})`);
                  this.updateZohoWithRetry(null, orderId, name);
                }
              }
              catch (err) {
                notifications.push({ message: `${name} Paid status failed to update in Deal | Unexpected Error | *[ZOHO]*`, type: 'error' });
                notifications.push({ message: `\`\`\`${err} \`\`\``, type: 'error' });
                notifications.push({ message: `${name} update will retry | *[ZOHO]*`, type: 'error' });
                logger.info(`Paid status to Deal sync error :: ${err}`);
                this.updateZohoWithRetry(null, orderId, name);
              }
            }
            else {

              logger.info(`Payment not found :: ${paymentDescription}`);
              notifications.push({
                message: `${name} payment not found in db | ${email} | ${amount} | ${paymentDescription} | *[DB]*`,
                type: 'error',
              });

              jsonResponse = {
                error: 'Unknown payment',
                payment_description: paymentDescription,
              };
            }
          }
        } else {
          logger.info(`Payment not found :: ${paymentDescription}`);
          notifications.push({
            message: `${name} payment not found in db | ${email} | ${amount} | ${paymentDescription} | *[DB]*`,
            type: 'error',
          });

          jsonResponse = {
            error: 'Unknown payment',
            payment_description: paymentDescription,
          };
        }
      } else {
        logger.info(`Payment not found :: ${paymentDescription}`);
        notifications.push({
          message: `${name} payment not found in db | ${email} | ${amount} | ${paymentDescription} | *[DB]*`,
          type: 'error',
        });

        jsonResponse = {
          error: 'Unknown payment',
          payment_description: paymentDescription,
        };
      }
    }

    client.release();

    if (notifications.length > 0) {
      this.sendToSlack(notifications);
    }

    return jsonResponse;
  }

  public static async sendToSlack(notifications: { message: string; type: string }[]) {
    const channels = {
      error: 'C095Z8J9RFE',
      notice: 'C07ESLY2CKH',
    };

    if (notifications.length > 0) {
      const slack = new WebClient(config.slackToken);
      // const message = notifications.join("\n");
      const messages = notifications
        .filter((notification) => notification.type === 'notice')
        .map((notification) => notification.message)
        .join('\n');

      const errors = notifications
        .filter((notification) => notification.type === 'error')
        .map((notification) => notification.message)
        .join('\n');

      if (messages) {
        // Send message to channel
        await slack.chat.postMessage({
          channel: channels.notice,
          text: messages,
        });
      }

      if (errors) {
        // Send message to channel
        await slack.chat.postMessage({
          channel: channels.error,
          text: errors,
        });
      }
    }
  }

  public static async updateZohoWithRetry(dealId: string | null, orderId: string ,name: string) {
    const MAX_RETRIES = 4;
    const INITIAL_DELAY_MS = 10000; // 10 seconds

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        console.log(`Attempt ${attempt}: Trying to update Zoho Deal ${orderId}...`);

        const wasUpdated = await this.updateZohoDeal(dealId, orderId, name);

        if (wasUpdated) {
          return;
        }

        console.log(`Deal ${orderId} not found. Waiting to retry...`);

      } catch (error) {
        console.error(`Error on attempt ${attempt} for Deal ${orderId}:`, error);
      }

      // Wait for an increasing amount of time before the next attempt
      if (attempt < MAX_RETRIES) {
        await this.delayedZohoUpdate(INITIAL_DELAY_MS * attempt);
      }
    }
        
    this.sendToSlack([{ message: `${name} Paid status failed to update in Deal after 4 attempts | *[ZOHO]*`, type: 'error' }]);
    console.error(`Failed to update Zoho Deal ${orderId} after ${MAX_RETRIES} attempts.`);
  }

  public static async updateZohoDeal(dealId: string | null, orderId: string, name: string) {

    const dealHeaders = await this.getHeaders();

    if (dealId == null) {
      const response = await axios.get(
        `${zohoDealURL}/search?criteria=(Supply_Serial_Number_1:equals:${orderId})`,
        { headers: dealHeaders },
      );
      if (response.status === 200) {
        if (response.data.data != undefined && response.data.data.length > 0) {
          dealId = response.data.data[0].id;
        }
        else
          return false;
      }
      else
        return false;
    }

    const data = {
      data: [
        {
          "Paid_Status": "Paid"
        },
      ],
    };

    try {
      const response = await axios.put(`${zohoDealURL}/${dealId}`, data, { headers: dealHeaders });

      if (response.data.data != undefined && response.data.data[0].code == "SUCCESS") {
        const data = response.data;
        logger.info(`Paid status to Deal updated :: ${orderId}`);
        logger.info(`Request :: ${data}`);
        this.sendToSlack([{ message: `${name} Paid status updated in Deal | *[ZOHO]*`, type: 'notice' }]);
        return true;
      }
      else{
        logger.info(`Paid status to Deal sync failed :: ${response.data}`);
        this.sendToSlack([{ message: `${name} Paid status failed to update in Deal | *[ZOHO]*`, type: 'error' }]);
        this.sendToSlack([{ message: `\`\`\`${JSON.stringify(response.data)} \`\`\``, type: 'error' }]);
      
        return false;}
    }
    catch (err) {
      logger.info(`Paid status to Deal sync error :: ${err}`);
      this.sendToSlack([{ message: `${name} Paid status failed to update in Deal | Will be retried | *[ZOHO]*`, type: 'error' }]);
      return false;
    }

  }

  private static delayedZohoUpdate(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

}

export const zohoInvoiceURL = 'https://www.zohoapis.com.au/inventory/v1/invoices';
export const zohoBooksURL = 'https://www.zohoapis.com.au/books/v3';
export const zohoDealURL = 'https://www.zohoapis.com.au/crm/v2/deals';
export const zohoLeadURL = 'https://www.zohoapis.com.au/crm/v2/Leads';
export const zohoContactURL = 'https://www.zohoapis.com.au/crm/v2/Contacts';
export const zohoQueryURL = 'https://www.zohoapis.com.au/crm/v6/coql';
export const shopURL = 'https://shop.harvest.delivery/api/shop';
// export const ZohoOAtuh = new ZohoAuth()
