import { Pool } from 'pg';
import { logger } from '../config/logger';
import config from '../config';
import { ZohoAuth, zohoLeadURL } from '../helpers/zoho';
import { DateTime } from 'luxon';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { getFormatedZohoDate, getStrength } from '../utils/misc';
import { PatientTreatmentPlan } from '../types';

export interface ZeldaPatient {
  patientID: string;
  fullName: string;
  email: string;
  zohoID: string;
  mobile: string;
}

export interface ZeldaConsultation {
  id: string;
  patientID: string;
  consultationDate: DateTime;
  drId: string;
}

export class ZeldaManager {
  private static instance: ZeldaManager;
  private db: Pool;

  constructor(db: Pool) {
    this.db = db;
  }

  public static getInstance(db: Pool): ZeldaManager {
    if (!ZeldaManager.instance) {
      ZeldaManager.instance = new ZeldaManager(db);
    }
    return ZeldaManager.instance;
  }

  /**
   * Check if Zelda system is enabled
   */
  public isEnabled(): boolean {
    return config.zelda.enabled;
  }

  /**
   * Check if a doctor should be excluded from Zelda assignments by email
   */
  public async isDoctorExcluded(doctorId: string): Promise<boolean> {
    if (config.zelda.excludedDoctorEmails.length === 0) {
      return false;
    }

    const client = await this.db.connect();
    try {
      const query = `SELECT email FROM Dr WHERE "accessID" = $1`;
      const result = await client.query(query, [doctorId]);

      if (result.rows.length === 0) {
        return false; // Doctor not found, don't exclude
      }

      const doctorEmail = result.rows[0].email;
      return config.zelda.excludedDoctorEmails.includes(doctorEmail);
    } finally {
      client.release();
    }
  }

  /**
   * Get Zelda patient information
   */
  public async getZeldaPatient(): Promise<ZeldaPatient | null> {
    if (!this.isEnabled()) {
      return null;
    }

    const client = await this.db.connect();
    try {
      const query = `
        SELECT "patientID", "fullName", email, "zohoID", mobile
        FROM Patient
        WHERE email = $1
      `;
      const result = await client.query(query, [config.zelda.patientEmail]);

      if (result.rows.length > 0) {
        return result.rows[0] as ZeldaPatient;
      }
      return null;
    } finally {
      client.release();
    }
  }

  /**
   * Create Zelda patient if it doesn't exist
   */
  public async createZeldaPatient(): Promise<ZeldaPatient> {
    if (!this.isEnabled()) {
      throw new Error('Zelda system is not enabled');
    }

    const client = await this.db.connect();
    try {
      await client.query('BEGIN');

      // Check if Zelda already exists in database
      const existingZelda = await this.getZeldaPatient();
      if (existingZelda) {
        // Patient exists, but ensure Zoho lead exists too
        await this.createOrGetZohoLead(existingZelda);
        await client.query('COMMIT');
        return existingZelda;
      }

      // Create patient record first with default Zelda data
      const patientID = uuidv4(); // Generate unique patient ID
      const defaultZeldaData: Omit<ZeldaPatient, 'zohoID'> = {
        patientID,
        fullName: 'Zelda Xray',
        email: config.zelda.patientEmail,
        mobile: '+61400000000'
      };

      const query = `
        INSERT INTO Patient (
          "patientID", "fullName", email, mobile,
          "returningPatient", state, "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING "patientID", "fullName", email, mobile
      `;

      const values = [
        defaultZeldaData.patientID,
        defaultZeldaData.fullName,
        defaultZeldaData.email,
        defaultZeldaData.mobile,
        false, // returningPatient
        'NSW' // state
      ];

      const result = await client.query(query, values);
      const createdPatient = result.rows[0];

      // Now create Zoho lead using the patient data
      const patientWithoutZoho: ZeldaPatient = {
        ...createdPatient,
        zohoID: '' // Temporary, will be updated
      };

      const zohoId = await this.createOrGetZohoLead(patientWithoutZoho);

      // Update patient record with Zoho ID
      const updateQuery = `
        UPDATE Patient
        SET "zohoID" = $1, "updatedAt" = CURRENT_TIMESTAMP
        WHERE "patientID" = $2
        RETURNING "patientID", "fullName", email, "zohoID", mobile
      `;

      const updateResult = await client.query(updateQuery, [zohoId, createdPatient.patientID]);
      await client.query('COMMIT');

      const finalPatient = updateResult.rows[0] as ZeldaPatient;
      logger.info('Zelda test patient created successfully', {
        patientID: finalPatient.patientID,
        zohoId: finalPatient.zohoID
      });

      return finalPatient;

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error creating Zelda patient:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Create or get Zoho lead for Zelda using patient data from database
   */
  private async createOrGetZohoLead(patientData: ZeldaPatient): Promise<string> {
    try {
      // Check if lead already exists
      const existingLead = await ZohoAuth.getZohoLeadByEmail(patientData.email);
      if (existingLead?.data?.length > 0) {
        const leadId = existingLead.data[0].id;

        // Verify the lead still exists by ID (double-check)
        const headers = await ZohoAuth.getHeaders();
        const leadExists = await this.verifyZohoLeadExists(leadId, headers);
        if (leadExists) {
          logger.info('Zelda Zoho lead already exists and verified', { leadId });
          return leadId;
        } else {
          logger.warn('Zelda Zoho lead found by email but verification failed, creating new lead');
        }
      }

      // Create new lead using patient data from database
      const headers = await ZohoAuth.getHeaders();
      const [firstName, ...lastNameParts] = patientData.fullName.split(' ');
      const lastName = lastNameParts.join(' ') || 'Xray'; // Fallback to 'Xray' if no last name

      // Use the SAME consultation time that was stored in the database
      // Get the consultation time from the database (stored in UTC)
      const client = await this.db.connect();
      let consultationResult: { rows: Array<{ consultationDate: Date }> };
      try {
        const consultationQuery = `
          SELECT "consultationDate" FROM Consultation
          WHERE "patientID" = $1
          ORDER BY "createdAt" DESC LIMIT 1
        `;
        consultationResult = await client.query(consultationQuery, [patientData.patientID]);
      } finally {
        client.release();
      }

      let consultationDateTime: DateTime;
      if (consultationResult && consultationResult.rows.length > 0) {
        // Use existing consultation time from database (convert from UTC to Sydney for Zoho)
        const dbDateTime = consultationResult.rows[0].consultationDate;

        // Parse the datetime from database (it's stored as UTC)
        consultationDateTime = DateTime.fromJSDate(new Date(dbDateTime))
          .setZone('Australia/Sydney');

        logger.info('Using existing consultation time from database');
      } else {
        // Calculate new consultation time (this should match what we store in createConsultationForZelda)
        consultationDateTime = DateTime.now()
          .setZone('Australia/Sydney')
          .plus({ minutes: config.zelda.consultationLeadTimeMinutes });
        logger.info('Calculated new consultation time for Zoho lead');
      }

      // Validate DateTime before formatting
      if (!consultationDateTime.isValid) {
        logger.error('Invalid consultation DateTime:', {
          invalidReason: consultationDateTime.invalidReason,
          invalidExplanation: consultationDateTime.invalidExplanation
        });
        throw new Error(`Invalid consultation DateTime: ${consultationDateTime.invalidReason}`);
      }

      const data = {
        data: [
          {
            Email: patientData.email,
            Phone: patientData.mobile,
            Mobile: patientData.mobile,
            First_Name: firstName,
            Last_Name: lastName,
            Is_Stored_In_DB: 'yes',
            Lead_Status: 'Test Patient - Zelda',
            Consult_Date_Time: consultationDateTime.toFormat("yyyy-MM-dd'T'HH:mm:ssZZ")
          }
        ]
      };

      const response = await axios.post(`${zohoLeadURL}`, data, { headers });

      const leadId = response.data.data?.[0].details?.id;

      if (!leadId) {
        logger.error('Failed to extract lead ID from Zoho response:', JSON.stringify(response.data, null, 2));
        throw new Error('Failed to create Zoho lead for Zelda');
      }

      // Verify the newly created lead exists
      const leadExists = await this.verifyZohoLeadExists(leadId, headers);
      if (!leadExists) {
        throw new Error(`Zoho lead ${leadId} was created but verification failed`);
      }

      logger.info('Zelda Zoho lead created and verified successfully', { leadId, patientData: patientData.fullName });
      return leadId;

    } catch (error) {
      logger.error('Error creating/getting Zoho lead for Zelda:', error);
      throw error;
    }
  }

  /**
   * Get currently logged-in doctors (excluding excluded ones by email)
   */
  public async getAvailableDoctors(): Promise<string[]> {
    const client = await this.db.connect();
    try {
      const query = `
        WITH latest_sessions AS (
          SELECT DISTINCT ON (ds."doctorId")
            ds."doctorId",
            ds.action
          FROM doctor_sessions ds
          INNER JOIN Dr d ON ds."doctorId" = d."accessID"
          WHERE d.role = 'doctor'
          ORDER BY ds."doctorId", ds.timestamp DESC
        )
        SELECT d."accessID"
        FROM Dr d
        INNER JOIN latest_sessions ls ON d."accessID" = ls."doctorId"
        WHERE d.role = 'doctor'
        AND ls.action = 'LOGIN'
        ${config.zelda.excludedDoctorEmails.length > 0 ?
          `AND d.email NOT IN (${config.zelda.excludedDoctorEmails.map((_, i) => `$${i + 1}`).join(', ')})` :
          ''}
      `;

      const result = await client.query(query, config.zelda.excludedDoctorEmails);
      return result.rows.map(row => row.accessID);

    } finally {
      client.release();
    }
  }

  /**
   * Create PatientSlot entry for Zelda to ensure she's picked up by consultation queries
   * Only creates if a Range already exists for the doctor (meaning they're available)
   */
  private async createPatientSlotForZelda(
    zelda: ZeldaPatient,
    doctorId: string,
    consultationTime: DateTime,
    client: import('pg').PoolClient
  ): Promise<boolean> {
    try {
      // Get doctor's internal ID from accessID
      const doctorQuery = `SELECT id FROM Dr WHERE "accessID" = $1`;
      const doctorResult = await client.query(doctorQuery, [doctorId]);

      if (doctorResult.rows.length === 0) {
        throw new Error(`Doctor with accessID ${doctorId} not found`);
      }

      const doctorInternalId = doctorResult.rows[0].id;

      // Check if Range entry exists for today for this doctor
      const today = consultationTime.toFormat('yyyy-MM-dd');
      const rangeQuery = `
        SELECT id FROM Range
        WHERE "doctorID" = $1
        AND date::DATE = $2::DATE
        AND status = 'active'
      `;
      const rangeResult = await client.query(rangeQuery, [doctorInternalId, today]);

      if (rangeResult.rows.length === 0) {
        logger.info(`No active Range found for doctor ${doctorId} on ${today}, skipping Zelda assignment`);
        return false;
      }

      const rangeId = rangeResult.rows[0].id;

      // Check if ANY PatientSlot already exists for Zelda today (regardless of time)
      const existingPatientSlotQuery = `
        SELECT ps.slot_id, s.slot
        FROM PatientSlot ps
        JOIN Range r ON ps.range_id = r.id
        JOIN Slot s ON ps.slot_id = s.id
        WHERE ps.patient_id = $1
        AND r.date::DATE = $2::DATE
      `;
      const existingPatientSlotResult = await client.query(existingPatientSlotQuery, [zelda.zohoID, today]);

      if (existingPatientSlotResult.rows.length > 0) {
        logger.info('PatientSlot already exists for Zelda today', {
          slotId: existingPatientSlotResult.rows[0].slot_id,
          slotTime: existingPatientSlotResult.rows[0].slot
        });
        return true;
      }

      // Get the range information to determine the interval
      const intervalQuery = `SELECT interval FROM Range WHERE id = $1`;
      const intervalResult = await client.query(intervalQuery, [rangeId]);
      const interval = intervalResult.rows[0]?.interval || 30; // Default to 30 minutes if not found

      // Create slot time in the correct range format (start - end)
      const slotStart = consultationTime.toFormat('HH:mm');
      const slotEnd = consultationTime.plus({ minutes: interval }).toFormat('HH:mm');
      const slotTime = `${slotStart} - ${slotEnd}`;

      // Check if any slot exists for this time and range (not just for Zelda)
      const findSlotQuery = `
        SELECT id, remaining, "noShowRemaining" FROM Slot
        WHERE range_id = $1 AND slot = $2
      `;
      const slotResult = await client.query(findSlotQuery, [rangeId, slotTime]);

      let slotId: number;

      if (slotResult.rows.length === 0) {
        // No slot exists for this time, create a new one with zero remaining
        const createSlotQuery = `
          INSERT INTO Slot (
            range_id, slot, remaining, "noShowRemaining", "createdAt", "updatedAt"
          ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id
        `;

        const createSlotResult = await client.query(createSlotQuery, [
          rangeId,
          slotTime,
          0, // remaining
          0  // noShowRemaining
        ]);

        slotId = createSlotResult.rows[0].id;
        logger.info('Created new Slot entry for Zelda', { slotId, rangeId, slotTime });
      } else {
        // Slot exists, use it and ensure remaining is set to 0
        slotId = slotResult.rows[0].id;
        
        // If the slot has remaining > 0, update it to 0 for Zelda
        if (slotResult.rows[0].remaining > 0 || slotResult.rows[0].noShowRemaining > 0) {
          const updateSlotQuery = `
            UPDATE Slot
            SET remaining = 0, "noShowRemaining" = 0, "updatedAt" = CURRENT_TIMESTAMP
            WHERE id = $1
          `;
          await client.query(updateSlotQuery, [slotId]);
          logger.info('Updated existing Slot for Zelda: set remaining to 0', { slotId, slotTime });
        } else {
          logger.info('Using existing Slot for Zelda (already has remaining=0)', { slotId, slotTime });
        }
      }

      // Create PatientSlot entry for Zelda
      const createPatientSlotQuery = `
        INSERT INTO PatientSlot (
          patient_id, range_id, slot_id, "queueType", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `;

      const patientSlotValues = [
        zelda.zohoID, // patient_id (uses zohoID)
        rangeId,
        slotId, // slot_id (references Slot table)
        'regular' // queueType
      ];

      await client.query(createPatientSlotQuery, patientSlotValues);

      logger.info('Created PatientSlot entry for Zelda', {
        patient_id: zelda.zohoID,
        slotId,
        rangeId,
        doctorId,
        slotTime
      });

      return true;

    } catch (error) {
      logger.error('Error creating PatientSlot for Zelda:', error);
      throw error;
    }
  }

  /**
   * Create consultation for Zelda scheduled before doctor's shift
   */
  public async createConsultationForZelda(doctorId: string): Promise<ZeldaConsultation | null> {
    if (!this.isEnabled() || await this.isDoctorExcluded(doctorId)) {
      return null;
    }

    const zelda = await this.getZeldaPatient();
    if (!zelda) {
      logger.warn('Zelda patient not found, creating...');
      await this.createZeldaPatient();
      return this.createConsultationForZelda(doctorId);
    }

    const client = await this.db.connect();
    try {
      await client.query('BEGIN');

      // Calculate consultation time for today (40+ minutes in the future to trigger 30-minute notifications)
      const consultationTime = DateTime.now()
        .setZone('Australia/Sydney')
        .plus({ minutes: config.zelda.consultationLeadTimeMinutes });

      // Check if consultation already exists for today
      const existingQuery = `
        SELECT id FROM Consultation
        WHERE "patientID" = $1
        AND "consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND "consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      `;
      const existingResult = await client.query(existingQuery, [zelda.patientID]);

      if (existingResult.rows.length > 0) {
        // Consultation exists, but ensure PatientSlot also exists (if doctor is available)
        const slotCreated = await this.createPatientSlotForZelda(zelda, doctorId, consultationTime, client);
        await client.query('COMMIT');

        if (slotCreated) {
          logger.info('Consultation already exists for Zelda today, ensured PatientSlot exists');
        } else {
          logger.info('Consultation exists for Zelda but doctor has no active Range for today');
        }

        return {
          id: existingResult.rows[0].id,
          patientID: zelda.patientID,
          consultationDate: consultationTime,
          drId: doctorId
        };
      }

      // Create PatientSlot entry for Zelda to ensure she's picked up by consultation queries
      const slotCreated = await this.createPatientSlotForZelda(zelda, doctorId, consultationTime, client);

      if (!slotCreated) {
        await client.query('ROLLBACK');
        logger.info(`Doctor ${doctorId} has no active Range for today, skipping Zelda consultation creation`);
        return null;
      }

      // Create new consultation
      const consultationId = uuidv4();
      const insertQuery = `
        INSERT INTO Consultation (
          id, "patientID", "drId", email, "consultationDate",
          "notificationSent", "queueTag", "createdAt", "updatedAt"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
      `;

      const values = [
        consultationId,
        zelda.patientID,
        doctorId,
        zelda.email,
        consultationTime.toISO(),
        false, // Will be set to true when notification is sent
        'pre-consult'
      ];

      await client.query(insertQuery, values);
      await client.query('COMMIT');

      const doctorName = await this.getDoctorName(doctorId);
      logger.info('Consultation created for Zelda', {
        consultationId,
        doctorName,
        consultationTime: consultationTime.toISO()
      });

      return {
        id: consultationId,
        patientID: zelda.patientID,
        consultationDate: consultationTime,
        drId: doctorId
      };

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error creating consultation for Zelda:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Create automated treatment plan for Zelda
   */
  public async createTreatmentPlanForZelda(doctorId: string, consultationId?: string): Promise<void> {
    if (!this.isEnabled() || await this.isDoctorExcluded(doctorId)) {
      return;
    }

    const zelda = await this.getZeldaPatient();
    if (!zelda) {
      logger.warn('Zelda patient not found for treatment plan creation');
      return;
    }

    const client = await this.db.connect();
    try {
      await client.query('BEGIN');

      // Get doctor information
      const doctorQuery = `SELECT name, username FROM Dr WHERE "accessID" = $1`;
      const doctorResult = await client.query(doctorQuery, [doctorId]);
      const doctor = doctorResult.rows[0];
      const doctorName = doctor?.name || doctor?.username || 'Test Doctor';

      // Get consultation ID if not provided
      let finalConsultationId = consultationId;
      if (!finalConsultationId) {
        const consultationQuery = `
          SELECT id FROM Consultation
          WHERE "patientID" = $1 AND "drId" = $2
          ORDER BY "createdAt" DESC LIMIT 1
        `;
        const consultationResult = await client.query(consultationQuery, [zelda.patientID, doctorId]);
        finalConsultationId = consultationResult.rows[0]?.id;
      }

      // Check if treatment plan already exists for this consultation
      if (finalConsultationId) {
        const existingPlanQuery = `
          SELECT id FROM TreatmentPlan
          WHERE "patientID" = $1 AND "consultationId" = $2
        `;
        const existingPlan = await client.query(existingPlanQuery, [zelda.patientID, finalConsultationId]);
        if (existingPlan.rows.length > 0) {
          await client.query('COMMIT');
          logger.info('Treatment plan already exists for Zelda consultation');
          return;
        }
      }

      // Create automated treatment plan with standard test values
      const treatmentPlanQuery = `
        INSERT INTO TreatmentPlan (
          "patientID", "drId", "consultationId", outcome, "drNotes", "diagnosis", date, "drName",
          "strengthAndConcentration22", "dosePerDay22", "maxDose22", "totalQuantity22",
          "numberOfRepeat22", "supplyInterval22",
          "strengthAndConcentration29", "dosePerDay29", "maxDose29", "totalQuantity29",
          "numberOfRepeat29", "supplyInterval29",
          email, "mentalHealthSupprtingDocument", "type", "source", "createdAt", "updatedAt"
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        )
      `;

      const treatmentValues = [
        zelda.patientID,
        doctorId,
        finalConsultationId,
        'Approve Unrestricted', // outcome
        'Automated test treatment plan for communication testing purposes. This is a test patient (Zelda Xray) used for system validation.', // drNotes
        'Test diagnosis for automated Zelda patient - system validation purposes only', // diagnosis
        new Date().toISOString().split('T')[0], // date
        doctorName,
        '22%', // strengthAndConcentration22
        '1.0', // dosePerDay22
        '2.0', // maxDose22
        '28', // totalQuantity22
        6, // numberOfRepeat22
        28, // supplyInterval22
        '29%', // strengthAndConcentration29
        '0.1', // dosePerDay29
        '1.0', // maxDose29
        '28', // totalQuantity29
        6, // numberOfRepeat29
        28, // supplyInterval29
        zelda.email,
        'Yes', // mentalHealthSupprtingDocument
        'zelda-automated', // type
        'consultation' // source - Zelda plans are treated as consultation-based
      ];

      await client.query(treatmentPlanQuery, treatmentValues);

      // Submit treatment plan to Zoho
      await this.submitTreatmentPlanToZoho(zelda, doctorName, treatmentValues);

      await client.query('COMMIT');

      logger.info('Automated treatment plan created and submitted to Zoho for Zelda', {
        doctorName,
        consultationId: finalConsultationId
      });

    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error creating treatment plan for Zelda:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Verify that a Zoho lead exists before submitting data
   */
  private async verifyZohoLeadExists(zohoID: string, headers: Record<string, string>): Promise<boolean> {
    try {
      const response = await axios.get(`${zohoLeadURL}/${zohoID}`, { headers });

     

      if (response.status === 200 && response.data?.data && response.data.data.length > 0) {
        logger.info(`Zoho lead ${zohoID} verified successfully for Zelda`);
        return true;
      } else {
        logger.warn(`Zoho lead ${zohoID} not found or empty response`);
        return false;
      }
    } catch (error) {
      logger.error(`Error verifying Zoho lead ${zohoID}:`, error);
      return false;
    }
  }

  /**
   * Update patient record with new Zoho ID
   */
  private async updatePatientZohoID(patientID: string, zohoID: string): Promise<void> {
    const client = await this.db.connect();
    try {
      const updateQuery = `
        UPDATE Patient
        SET "zohoID" = $1, "updatedAt" = CURRENT_TIMESTAMP
        WHERE "patientID" = $2
      `;
      await client.query(updateQuery, [zohoID, patientID]);
      logger.info(`Updated patient ${patientID} with new Zoho ID: ${zohoID}`);
    } catch (error) {
      logger.error(`Error updating patient ${patientID} with Zoho ID ${zohoID}:`, error);
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Submit treatment plan to Zoho
   */
  private async submitTreatmentPlanToZoho(zelda: ZeldaPatient, doctorName: string, treatmentValues: (string | number | boolean | Date)[]): Promise<void> {
    try {
      const headers = await ZohoAuth.getHeaders();

      // First, verify the Zoho lead exists
      let currentZohoID = zelda.zohoID;
      const leadExists = await this.verifyZohoLeadExists(currentZohoID, headers);

      if (!leadExists) {
        logger.warn(`Zoho lead ${currentZohoID} not found, creating new lead for Zelda`);

        // Create new Zoho lead
        const newZohoID = await this.createOrGetZohoLead(zelda);
        if (!newZohoID) {
          logger.error('Failed to create Zoho lead for Zelda, skipping treatment plan submission');
          return;
        }

        // Update patient record with new Zoho ID
        await this.updatePatientZohoID(zelda.patientID, newZohoID);
        currentZohoID = newZohoID;

        logger.info(`Created new Zoho lead ${newZohoID} for Zelda and updated patient record`);
      }

      // Extract treatment plan values (matching the order from treatmentValues array)
      const [
        , , , outcome, , , drName,
        strengthAndConcentration22, dosePerDay22, maxDose22, totalQuantity22, numberOfRepeat22, ,
        strengthAndConcentration29, dosePerDay29, maxDose29, totalQuantity29, numberOfRepeat29, ,
        , mentalHealthSupprtingDocument
      ] = treatmentValues;

      // Create treatment plan body object for getStrength function
      const treatmentPlanBody: PatientTreatmentPlan = {};

      if (strengthAndConcentration22) {
        treatmentPlanBody[22] = {
          dosePerDay: String(dosePerDay22),
          maxDosePerDay: String(maxDose22),
          totalQuantity: String(totalQuantity22),
          numberOfRepeat: String(numberOfRepeat22),
          supplyInterval: '28'
        };
      }

      if (strengthAndConcentration29) {
        treatmentPlanBody[29] = {
          dosePerDay: String(dosePerDay29),
          maxDosePerDay: String(maxDose29),
          totalQuantity: String(totalQuantity29),
          numberOfRepeat: String(numberOfRepeat29),
          supplyInterval: '28'
        };
      }

      // Prepare Zoho data payload (matching the exact real treatment plan submission format)
      const data = {
        data: [
          {
            Dr_Approve_Date_Time: getFormatedZohoDate(),
            Strength_Concentration: getStrength(treatmentPlanBody),
            Dr_Trigger: outcome,
            Mental_Health_Supporting_Documentation: mentalHealthSupprtingDocument,
            ID_Verified: treatmentPlanBody.idVerified,
            Consulting_Doctor: drName,
            Prescription_Date_1: getFormatedZohoDate().split('T')[0],
            Specified_Dose: dosePerDay22,
            Maximum_Doses_per_Day: maxDose22,
            Total_Qty_22_1: totalQuantity22,
            Number_of_Repeats_22: String(numberOfRepeat22),
            Dose_Per_Day_29: dosePerDay29,
            Maximum_Doses_per_Day_29: maxDose29,
            Number_of_Repeats_29: String(numberOfRepeat22 ? numberOfRepeat22 : numberOfRepeat29),
            Total_Qty_29_1: totalQuantity29,
            Dispensing_Interval_Period_1: treatmentPlanBody[22]?.supplyInterval ? treatmentPlanBody[22].supplyInterval : treatmentPlanBody[29]?.supplyInterval,
            Doctor_Notes: 'Automated test treatment plan for Zelda Xray - DO NOT DELETE',
            Introduction1: 'Automated test treatment plan for Zelda Xray - DO NOT DELETE Patient',
            Introduction2: 'This is an automated treatment plan created for communication testing purposes.',
            SideEffect1: 'Standard side effects apply',
            SideEffect2: 'Monitor for adverse reactions',
            SideEffect3: 'Contact clinic if concerns arise',
            SubSideEffect1: 'Drowsiness may occur',
            SubSideEffect2: 'Start with low doses',
            SubSideEffect3: 'Increase gradually as needed'
          }
        ]
      };

      // Submit to Zoho
      const result = await axios.put(`${zohoLeadURL}/${currentZohoID}`, data, { headers });

      if (result?.data?.data?.[0]) {
        if (result?.data?.data?.[0].status === 'error') {
          logger.error('Error submitting Zelda treatment plan to Zoho:', result?.data?.data?.[0]);
          throw new Error(`Zoho submission failed: ${result?.data?.data?.[0].message || 'Unknown error'}`);
        }

        if (result?.data?.data?.[0].status === 'success') {
          logger.info(`Successfully submitted Zelda treatment plan to Zoho for ${doctorName}`);
        }
      }

    } catch (error) {
      logger.error('Error submitting Zelda treatment plan to Zoho:', error);
      // Don't throw error to avoid breaking the treatment plan creation
      // Just log the error and continue
    }
  }

  /**
   * Get doctor name by ID for logging
   */
  private async getDoctorName(doctorId: string): Promise<string> {
    const client = await this.db.connect();
    try {
      const query = `SELECT name, username FROM Dr WHERE "accessID" = $1`;
      const result = await client.query(query, [doctorId]);

      if (result.rows.length > 0) {
        const doctor = result.rows[0];
        return doctor.name || doctor.username || `Doctor ${doctorId}`;
      }
      return `Doctor ${doctorId}`;
    } catch (error) {
      logger.error(`Error getting doctor name for ${doctorId}:`, error);
      return `Doctor ${doctorId}`;
    } finally {
      client.release();
    }
  }

  /**
   * Main Zelda management function - called when doctor logs in or timer ends
   */
  public async manageZelda(doctorId: string, action: 'LOGIN' | 'TIMER_END'): Promise<void> {
    if (!this.isEnabled() || await this.isDoctorExcluded(doctorId)) {
      const doctorName = await this.getDoctorName(doctorId);
      logger.debug(`Zelda management skipped for ${doctorName} - system disabled or doctor excluded`);
      return;
    }

    try {
      const doctorName = await this.getDoctorName(doctorId);
      logger.info(`Managing Zelda for ${doctorName}, action: ${action}`);

      switch (action) {
        case 'LOGIN':
          // When doctor logs in, create consultation and assign Zelda
          await this.handleDoctorLogin(doctorId);
          break;

        case 'TIMER_END':
          // When doctor timer ends, submit treatment plan
          await this.handleDoctorTimerEnd(doctorId);
          break;

        default:
          logger.warn(`Unknown Zelda management action: ${action}`);
      }

    } catch (error) {
      const doctorName = await this.getDoctorName(doctorId);
      logger.error(`Error in Zelda management for ${doctorName}:`, error);
    }
  }

  /**
   * Handle doctor login - create consultation and assign Zelda
   */
  private async handleDoctorLogin(doctorId: string): Promise<void> {
    // Ensure Zelda patient exists
    let zelda = await this.getZeldaPatient();
    if (!zelda) {
      zelda = await this.createZeldaPatient();
    }

    // Create consultation for Zelda (only if doctor has active Range for today)
    const consultation = await this.createConsultationForZelda(doctorId);
    if (!consultation) {
      logger.info(`Doctor ${doctorId} has no active availability today, skipping Zelda assignment`);
      return;
    }

    const doctorName = await this.getDoctorName(doctorId);
    logger.info(`Zelda assigned to ${doctorName} upon login`);
  }

  /**
   * Handle doctor timer end - submit treatment plan
   */
  private async handleDoctorTimerEnd(doctorId: string): Promise<void> {
    // Create treatment plan for Zelda
    await this.createTreatmentPlanForZelda(doctorId);

    
  }

  /**
   * Add Zelda to patient queue for specified doctor
   */
  // private async addZeldaToPatientQueue(doctorId: string, zelda: ZeldaPatient): Promise<void> {
  //   const client = await this.db.connect();
  //   try {
  //     await client.query('BEGIN');

  //     // Check if already in queue for today
  //     const existingQueueQuery = `
  //       SELECT "patientID" FROM PatientQueue
  //       WHERE "patientID" = $1
  //       AND "createdAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
  //     `;
  //     const existingQueue = await client.query(existingQueueQuery, [zelda.patientID]);

  //     if (existingQueue.rows.length > 0) {
  //       await client.query('COMMIT');
  //       logger.info('Zelda already in patient queue for today');
  //       return;
  //     }

  //     // Add to PatientQueue
  //     const queueQuery = `
  //       INSERT INTO PatientQueue (
  //         "patientID", email, status, "assignedDoctorID", "notificationSent",
  //         "createdAt", "updatedAt"
  //       ) VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  //       ON CONFLICT ("patientID")
  //       DO UPDATE SET
  //         "assignedDoctorID" = EXCLUDED."assignedDoctorID",
  //         "updatedAt" = CURRENT_TIMESTAMP
  //     `;

  //     await client.query(queueQuery, [
  //       zelda.patientID,
  //       zelda.email,
  //       'OFFLINE', // Initial status
  //       doctorId,
  //       false
  //     ]);

  //     // Add to DoctorQueue for assignment tracking
  //     const doctorQueueQuery = `
  //       INSERT INTO DoctorQueue ("patientID", "doctorID", status, "createdAt", "updatedAt")
  //       VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  //       ON CONFLICT ("patientID", "doctorID")
  //       DO UPDATE SET
  //         status = EXCLUDED.status,
  //         "updatedAt" = CURRENT_TIMESTAMP
  //     `;

  //     await client.query(doctorQueueQuery, [
  //       zelda.patientID,
  //       doctorId,
  //       'ASSIGNED'
  //     ]);

  //     await client.query('COMMIT');

  //     const doctorName = await this.getDoctorName(doctorId);
  //     logger.info(`Zelda added to patient queue for ${doctorName}`);

  //   } catch (error) {
  //     await client.query('ROLLBACK');
  //     logger.error('Error adding Zelda to patient queue:', error);
  //     throw error;
  //   } finally {
  //     client.release();
  //   }
  // }
}
