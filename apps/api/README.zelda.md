# Zelda Test Patient System

## Overview

The Zelda Test Patient System is an automated testing framework that creates and manages a fictional patient named "<PERSON><PERSON><PERSON> Xray" for communication testing and system validation. This system allows developers and administrators to test the complete patient communication pipeline without affecting real patients.

## Features

- **Automated Patient Creation**: Creates a fictional patient "<PERSON>eld<PERSON> Xray" with email `<EMAIL>`
- **Doctor Assignment**: Automatically assigns <PERSON><PERSON><PERSON> to logged-in doctors (with exclusion support)
- **Treatment Plan Automation**: Creates treatment plans when doctor timers end
- **Communication Testing**: Enables testing of SMS, email, and notification systems
- **Environment-Based Exclusion**: Prevents <PERSON><PERSON><PERSON> from appearing for specific doctors (e.g., Dr. Anju<PERSON>)
- **Consultation Scheduling**: Schedules consultations 40+ minutes in the future to trigger 30-minute notifications
- **Zoho Lead Management**: Self-healing Zoho integration with automatic lead creation and recovery
- **Timezone Accuracy**: Proper UTC database storage with Sydney timezone conversion for Zoho
- **Production Ready**: Clean logging and robust error handling suitable for production environments
- **Data Consistency**: Single source of truth for consultation times between database and Zoho

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# Enable/disable the Zelda system
ENABLE_ZELDA_SYSTEM=true

# Zelda's email address (used for identification)
ZELDA_TEST_PATIENT_EMAIL=<EMAIL>

# Comma-separated list of doctor emails to exclude from Zelda assignments
EXCLUDE_DOCTORS_FROM_ZELDA=<EMAIL>,<EMAIL>

# Minutes in the future to schedule Zelda consultations (default: 40)
ZELDA_CONSULTATION_LEAD_TIME=40
```

### Doctor Exclusion

To exclude specific doctors from seeing Zelda:

1. Add their email addresses to `EXCLUDE_DOCTORS_FROM_ZELDA` environment variable
2. Separate multiple emails with commas
3. Example: `EXCLUDE_DOCTORS_FROM_ZELDA=<EMAIL>,<EMAIL>`

## How It Works

### 1. Doctor Timer Integration

The system automatically triggers when:

- **Timer Start**: When a doctor starts their shift

  - Creates Zelda patient in database if doesn't exist
  - Creates/gets Zoho lead using patient data with future consultation time
  - Schedules consultation 40+ minutes in the future for the doctor
  - Adds Zelda to the doctor's patient queue

- **Timer End**: When a doctor's timer ends
  - Automatically creates and submits a treatment plan for Zelda
  - Uses standard test values for treatment plan fields

### 2. Patient Creation (Database-First Approach)

Zelda follows a database-first approach for better data consistency:

1. **Database Creation**: Patient record created in database first
2. **Zoho Integration**: Zoho lead created using patient data from database
3. **Data Consistency**: All patient details stored in database, not hardcoded

Zelda is created as a normal patient with:

- **Name**: Zelda Xray (stored in database)
- **Email**: <EMAIL> (configurable via environment)
- **Mobile**: +61400000000 (stored in database)
- **State**: NSW
- **Zoho Integration**: Creates Zoho lead using patient data from database with future consultation time

### 3. Consultation Scheduling

- Consultations are scheduled 40+ minutes **in the future** from current time
- This ensures the 30-minute pre-consultation notification system will trigger
- With 40-minute lead time, Zelda gets notified immediately, then warned at 30 minutes, then at 10 minutes
- Consultations are linked to the currently logged-in doctor

### 4. Treatment Plan Automation

When a doctor's timer ends, an automated treatment plan is created and submitted:

**Database Creation:**

- **Outcome**: "Approve Unrestricted"
- **THC 22%**: Standard test dosages and quantities (1.0 dose, 2.0 max, 28 quantity, 6 repeats)
- **THC 29%**: Standard test dosages and quantities (0.1 dose, 1.0 max, 28 quantity, 6 repeats)
- **Notes**: Indicates this is an automated test treatment plan

**Zoho Submission:**

- **Self-Healing Integration**: Automatically recovers from missing or corrupted Zoho leads
- **Lead Verification**: Verifies Zoho lead exists before submitting treatment plan data
- **Automatic Lead Creation**: Creates new Zoho lead if existing one is not found or invalid
- **Database Synchronization**: Updates patient record with new Zoho ID when lead is recreated
- **Timezone Consistency**: Uses same consultation time from database, converted from UTC to Sydney timezone
- **Proper DateTime Format**: Uses Zoho-compatible datetime format (`yyyy-MM-dd'T'HH:mm:ssZZ`)
- **Complete Treatment Data**: Includes all THC concentrations, dosages, and doctor information
- **Comprehensive Documentation**: Provides side effect and introduction information
- **Workflow Integration**: Triggers Zoho workflows for treatment plan completion
- **Robust Error Handling**: Gracefully handles Zoho API errors without breaking treatment plan creation
- **Production Logging**: Clean, concise logs suitable for production monitoring

## Timezone Handling

The system properly handles timezone conversion between database storage and Zoho integration:

### Database Storage (UTC)

- All timestamps stored in UTC in the database
- Consultation times stored using `consultationTime.toISO()` (UTC format)
- Follows standard database practices for timezone-agnostic storage

### Zoho Integration (Sydney Timezone)

- Consultation times converted from UTC to Australia/Sydney timezone for Zoho
- Uses proper Zoho datetime format: `yyyy-MM-dd'T'HH:mm:ssZZ`
- Matches real booking system timezone handling
- Ensures consistency with existing patient workflows

### Data Consistency

- **Single Source of Truth**: Uses database consultation time as authoritative source
- **Proper Conversion**: UTC database time → Sydney timezone for Zoho
- **Format Validation**: Validates DateTime objects before formatting
- **Error Handling**: Detailed logging for timezone conversion issues

## Database Schema

Zelda uses the existing database schema without modifications:

- **Patient Table**: Normal patient record (no special fields)
- **Consultation Table**: Standard consultation entries (UTC timestamps)
- **TreatmentPlan Table**: Standard treatment plan records
- **PatientQueue Table**: Normal queue entries
- **DoctorQueue Table**: Standard doctor assignment records

## Service Architecture

### ZeldaManager Class

Located in `apps/api/src/services/zeldaManager.ts`

**Key Methods:**

- `isEnabled()`: Check if Zelda system is enabled
- `isDoctorExcluded(doctorId)`: Check if doctor should be excluded
- `getZeldaPatient()`: Retrieve Zelda patient information from database
- `createZeldaPatient()`: Create Zelda patient in database, then create/get Zoho lead
- `createOrGetZohoLead(patientData)`: Create Zoho lead using patient data with future consultation time
- `manageZelda(doctorId, action)`: Main management function
- `createConsultationForZelda(doctorId)`: Schedule consultation
- `createTreatmentPlanForZelda(doctorId)`: Create treatment plan and submit to Zoho
- `submitTreatmentPlanToZoho(zelda, doctorName, treatmentValues)`: Submit treatment plan data to Zoho
- `verifyZohoLeadExists(zohoID, headers)`: Verify Zoho lead exists before operations
- `updatePatientZohoID(patientID, zohoID)`: Update patient record with new Zoho ID

### Integration Points

**Timer System Integration:**

```typescript
// In apps/api/src/controllers/doctor/index.ts
const zeldaManager = ZeldaManager.getInstance(db);
await zeldaManager.manageZelda(doctorId, 'TIMER_START');
await zeldaManager.manageZelda(doctorId, 'TIMER_END');
```

## Usage Examples

### Enable Zelda System

```env
ENABLE_ZELDA_SYSTEM=true
ZELDA_TEST_PATIENT_EMAIL=<EMAIL>
```

### Exclude Dr. Anjum

```env
EXCLUDE_DOCTORS_FROM_ZELDA=<EMAIL>
```

### Custom Consultation Lead Time

```env
ZELDA_CONSULTATION_LEAD_TIME=60  # 60 minutes in the future
```

## Testing Communication Pipeline

With Zelda enabled, you can test:

1. **Patient Notifications**: SMS and email <NAME_EMAIL>
2. **Treatment Plan Communications**: Automated treatment plan creation and Zoho submission
3. **Appointment Reminders**: Pre-consultation notifications (30-minute warnings)
4. **Chat System**: Patient-doctor communication testing
5. **Queue Management**: Patient queue and assignment workflows
6. **Zoho Integration**: Complete patient lifecycle from lead creation to treatment plan completion

### Notification Timeline

When a doctor starts their timer with Zelda enabled:

1. **T+0**: Zelda consultation scheduled 40 minutes in the future
2. **T+0**: Zelda immediately gets notified to join queue (if no patients online)
3. **T+10**: Zelda gets 30-minute consultation starting notification
4. **T+30**: Zelda gets 10-minute warning notification
5. **T+40**: Consultation time arrives

This allows testing of the complete notification pipeline including the critical 30-minute pre-consultation warnings.

## Safety Features

- **Environment-Based**: Only enabled when explicitly configured
- **Doctor Exclusion**: Prevents interference with specific doctors
- **Normal Patient Flow**: Uses existing patient workflows
- **No Database Changes**: Works with current schema
- **Error Isolation**: Zelda errors don't affect timer operations

## Monitoring and Logging

The system logs all Zelda operations:

```typescript
logger.info('Zelda patient created successfully', { patientID, zohoId });
logger.info('Zelda assigned to doctor for shift', { doctorId });
logger.info('Treatment plan submitted for Zelda', { doctorId });
```

## Troubleshooting

### Zelda Not Appearing

1. Check `ENABLE_ZELDA_SYSTEM=true`
2. Verify doctor is not in exclusion list
3. Check logs for Zelda creation errors
4. Ensure doctor has started their timer (Zelda is created on timer start)

### Notifications Not Working

1. Verify consultation is scheduled in the future (not past)
2. Check `ZELDA_CONSULTATION_LEAD_TIME` is set correctly (default: 40)
3. Ensure no other patients are online (Zelda gets notified when queue is empty)
4. Check logs for notification system errors

### Treatment Plans Not Created

1. Ensure doctor timer is ending properly
2. Check Zelda patient exists in database
3. Verify consultation was created for today
4. Check logs for treatment plan creation errors

### Doctor Exclusion Not Working

1. Verify doctor email in `EXCLUDE_DOCTORS_FROM_ZELDA`
2. Check environment variable format (comma-separated emails)
3. Ensure email matches exactly what's in the Dr table
4. Restart application after environment changes
5. Check logs for exclusion logic execution

### Zoho Integration Issues

1. **API Configuration**: Verify Zoho API credentials are configured
2. **Patient Data**: Check if Zelda patient exists in database first
3. **Lead Creation**: Ensure Zoho lead creation uses patient data from database
4. **Lead Verification**: Check logs for Zoho lead verification failures
5. **Treatment Plan Submission**: Verify Zoho lead exists before treatment plan submission
6. **API Errors**: Check logs for Zoho API errors and rate limiting
7. **DateTime Format**: Look for "Invalid DateTime" errors in logs
8. **Timezone Issues**: Verify consultation times are properly converted from UTC to Sydney
9. **Self-Healing**: Check if system automatically creates new leads when old ones are missing
10. **Database Sync**: Verify patient records are updated with new Zoho IDs

### DateTime and Timezone Issues

1. **Invalid DateTime Errors**: Check database consultation time format
2. **Timezone Conversion**: Verify UTC to Sydney timezone conversion
3. **Format Validation**: Look for DateTime validation errors in logs
4. **Database Timestamps**: Ensure consultation times are stored in UTC
5. **Zoho Format**: Verify datetime format matches `yyyy-MM-dd'T'HH:mm:ssZZ`

## Development Notes

- Zelda is a **service-only** system (no API endpoints or routes)
- Uses singleton pattern for ZeldaManager
- Database-first approach: patient created in database, then Zoho lead
- Integrates with existing timer and queue systems
- Maintains consistency with current authentication patterns
- Follows existing database transaction patterns
- Uses normal patient schema without special fields
- Consultation scheduling in future ensures notification system triggers

## Key Implementation Features

### Database-First Architecture

- Patient record created in database first with proper data
- Zoho lead created using actual patient data (not hardcoded)
- Ensures data consistency and follows proper patient workflow

### Self-Healing Zoho Integration

- **Automatic Recovery**: Detects missing or corrupted Zoho leads
- **Lead Recreation**: Creates new Zoho leads when needed
- **Database Synchronization**: Updates patient records with new Zoho IDs
- **Seamless Operation**: Treatment plan submission continues even after lead recreation

### Timezone Management

- **UTC Database Storage**: All timestamps stored in UTC for consistency
- **Sydney Timezone Conversion**: Proper conversion for Zoho integration
- **Format Validation**: Validates DateTime objects before API calls
- **Single Source of Truth**: Uses database consultation time as authoritative source

### Future Consultation Scheduling

- Consultations scheduled 40+ minutes **in the future** (not past)
- Triggers the complete notification pipeline including 30-minute warnings
- Allows testing of time-based notification system

### Production-Ready Design

- **Clean Logging**: Concise, production-suitable log output
- **Error Isolation**: Zelda errors don't affect timer operations
- **Robust Error Handling**: Graceful handling of API failures
- **Service-Only Architecture**: No API endpoints or routes (runs as background service)

### Smart Doctor Exclusion

- Email-based exclusion prevents Zelda from appearing to specific doctors
- More user-friendly than using doctor IDs
- Maintains normal patient flow for non-excluded doctors
- Configurable via environment variables

## Production Readiness

The Zelda Test Patient System is now production-ready with the following enhancements:

### Recent Improvements (2025)

- **Self-Healing Zoho Integration**: Automatically recovers from missing or corrupted Zoho leads
- **Timezone Accuracy**: Proper UTC database storage with Sydney timezone conversion
- **DateTime Validation**: Robust parsing and validation of database timestamps
- **Clean Logging**: Production-suitable log output with essential information only
- **Error Recovery**: Graceful handling of API failures without breaking workflows
- **Data Consistency**: Single source of truth for consultation times between systems

### Production Features

- **Zero Downtime**: Operates without affecting real patient workflows
- **Error Isolation**: Zelda failures don't impact timer or patient operations
- **Monitoring Ready**: Clear, actionable log messages for production monitoring
- **Self-Maintaining**: Automatically handles data inconsistencies and API issues
- **Scalable**: Uses existing database schema and patterns

### Deployment Considerations

- Enable only in environments where testing is needed
- Monitor Zoho API usage to avoid rate limiting
- Ensure proper timezone configuration for accurate testing
- Regular monitoring of log output for system health

## Security Considerations

- Zelda uses a real email address for testing
- Ensure test environment isolation
- Monitor for unintended production usage
- Regularly clean up test data if needed
- Only enable in development/testing environments
- Zoho lead creation follows same security patterns as real patients
