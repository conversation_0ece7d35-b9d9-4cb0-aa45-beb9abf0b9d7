import Grid from "@mui/material/Grid2";
import EmailPreview from "./email-preview";
import { EmailProvider } from "../../../hooks/email-provider";

const EmailConfirmation: React.FC = () => {
    return (
        <>
            <EmailProvider>
                <Grid container sx={{ width: '100%', mt: 2 }} direction={'column'} spacing={3} justifyContent={'start'} alignItems={'center'}>
                    <Grid container justifyContent={'center'} alignItems={'center'} sx={{width: '100%'}}>
                        <EmailPreview />
                    </Grid>
                </Grid>
            </EmailProvider>
        </>
    )
}

export default EmailConfirmation