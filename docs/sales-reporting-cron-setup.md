# Sales Reporting Cron Job Setup

This document provides instructions for setting up automated cron jobs on your Linux server to trigger sales reports.

## Overview

The sales reporting system uses server-level cron jobs that make HTTP requests to API endpoints to trigger report generation. This approach ensures reports are generated consistently and allows for easy monitoring and troubleshooting.

## Prerequisites

1. **API Server Running**: Ensure your API server is running and accessible
2. **Environment Variables**: Verify all required environment variables are configured
3. **Database Migration**: Ensure the `stripe_payments` table has been created
4. **Slack Integration**: Verify Slack channel and bot permissions are configured

## Cron Job Configuration

### 1. Daily Sales Report

**Schedule**: Every day at 9:00 AM (Australia/Sydney timezone)

```bash
# Add to crontab
0 9 * * * curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report
```

**Alternative with logging**:
```bash
0 9 * * * curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report >> /var/log/sales-reports.log 2>&1
```

### 2. Weekly Sales Report

**Schedule**: Every Monday at 9:00 AM (Australia/Sydney timezone)

```bash
# Add to crontab
0 9 * * 1 curl -X POST https://your-domain.com/api/sales/v1/trigger-weekly-report
```

**Alternative with logging**:
```bash
0 9 * * 1 curl -X POST https://your-domain.com/api/sales/v1/trigger-weekly-report >> /var/log/sales-reports.log 2>&1
```

### 3. Monthly Sales Report

**Schedule**: First day of every month at 9:00 AM (Australia/Sydney timezone)

```bash
# Add to crontab
0 9 1 * * curl -X POST https://your-domain.com/api/sales/v1/trigger-monthly-report
```

**Alternative with logging**:
```bash
0 9 1 * * curl -X POST https://your-domain.com/api/sales/v1/trigger-monthly-report >> /var/log/sales-reports.log 2>&1
```

## Installation Instructions

### Step 1: Access Server Crontab

```bash
# SSH into your server
ssh <EMAIL>

# Edit crontab for the current user
crontab -e
```

### Step 2: Add Cron Jobs

Add the following lines to your crontab file:

```bash
# Sales Reporting Cron Jobs
# Daily report at 9:00 AM
0 9 * * * curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report >> /var/log/sales-reports.log 2>&1

# Weekly report every Monday at 9:00 AM
0 9 * * 1 curl -X POST https://your-domain.com/api/sales/v1/trigger-weekly-report >> /var/log/sales-reports.log 2>&1

# Monthly report on 1st of each month at 9:00 AM
0 9 1 * * curl -X POST https://your-domain.com/api/sales/v1/trigger-monthly-report >> /var/log/sales-reports.log 2>&1
```

### Step 3: Verify Cron Jobs

```bash
# List current cron jobs
crontab -l

# Check cron service status
sudo systemctl status cron
```

### Step 4: Create Log Directory (Optional)

```bash
# Create log directory if it doesn't exist
sudo mkdir -p /var/log
sudo touch /var/log/sales-reports.log
sudo chmod 644 /var/log/sales-reports.log
```

## Environment-Specific Configuration

### Development Environment

```bash
# Use development domain
0 9 * * * curl -X POST https://dev.your-domain.com/api/sales/v1/trigger-daily-report
```

### Production Environment

```bash
# Use production domain
0 9 * * * curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report
```

### Local Testing

```bash
# For local testing (adjust port as needed)
0 9 * * * curl -X POST http://localhost:5000/api/sales/v1/trigger-daily-report
```

## Advanced Configuration

### With Authentication Headers (if needed)

```bash
# If you add authentication to the endpoints later
0 9 * * * curl -X POST -H "Authorization: Bearer YOUR_TOKEN" https://your-domain.com/api/sales/v1/trigger-daily-report
```

### With Timeout and Retry

```bash
# Add timeout and retry logic
0 9 * * * timeout 30 curl -X POST --retry 3 --retry-delay 5 https://your-domain.com/api/sales/v1/trigger-daily-report
```

### With Health Check

```bash
# Check API health before triggering report
0 9 * * * curl -f https://your-domain.com/health && curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report
```

## Monitoring and Troubleshooting

### Check Cron Logs

```bash
# View cron system logs
sudo tail -f /var/log/cron

# View sales report logs
tail -f /var/log/sales-reports.log
```

### Test Endpoints Manually

```bash
# Test daily report endpoint
curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report

# Check system status
curl https://your-domain.com/api/sales/v1/status

# Send test report
curl -X POST https://your-domain.com/api/sales/v1/test-report
```

### Common Issues and Solutions

#### 1. Cron Job Not Running

```bash
# Check if cron service is running
sudo systemctl status cron

# Start cron service if stopped
sudo systemctl start cron

# Enable cron service to start on boot
sudo systemctl enable cron
```

#### 2. Network Issues

```bash
# Test network connectivity
ping your-domain.com

# Test HTTPS connectivity
curl -I https://your-domain.com/health
```

#### 3. Permission Issues

```bash
# Check log file permissions
ls -la /var/log/sales-reports.log

# Fix permissions if needed
sudo chmod 644 /var/log/sales-reports.log
```

#### 4. Timezone Issues

```bash
# Check server timezone
timedatectl

# Set timezone to Australia/Sydney if needed
sudo timedatectl set-timezone Australia/Sydney
```

## Monitoring Setup

### Log Rotation

Create a logrotate configuration to manage log file size:

```bash
# Create logrotate config
sudo nano /etc/logrotate.d/sales-reports

# Add the following content:
/var/log/sales-reports.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
```

### Monitoring Script

Create a monitoring script to check if reports are being generated:

```bash
#!/bin/bash
# /usr/local/bin/check-sales-reports.sh

LOG_FILE="/var/log/sales-reports.log"
CURRENT_DATE=$(date +%Y-%m-%d)

# Check if daily report ran today
if grep -q "$CURRENT_DATE" "$LOG_FILE"; then
    echo "Sales reports are running normally"
    exit 0
else
    echo "WARNING: No sales reports found for today"
    exit 1
fi
```

## API Endpoints Reference

| Endpoint | Method | Purpose | Cron Schedule |
|----------|--------|---------|---------------|
| `/api/sales/v1/trigger-daily-report` | POST | Trigger daily report | `0 9 * * *` |
| `/api/sales/v1/trigger-weekly-report` | POST | Trigger weekly report | `0 9 * * 1` |
| `/api/sales/v1/trigger-monthly-report` | POST | Trigger monthly report | `0 9 1 * *` |
| `/api/sales/v1/status` | GET | Check system status | Manual |
| `/api/sales/v1/test-report` | POST | Send test report | Manual |

## Security Considerations

1. **Network Security**: Ensure your server can reach the API endpoints
2. **Log Security**: Protect log files from unauthorized access
3. **Endpoint Security**: Consider adding authentication if endpoints become public
4. **SSL/TLS**: Always use HTTPS for production endpoints

## Maintenance

### Regular Checks

- Monitor log files for errors
- Verify reports are being delivered to Slack
- Check system status endpoint weekly
- Review cron job execution logs monthly

### Updates

When updating the API:
1. Test endpoints manually before updating cron jobs
2. Update cron job URLs if endpoint paths change
3. Verify environment variables after deployments
4. Test report generation after major updates

## Support

For troubleshooting:
1. Check the system status endpoint: `GET /api/sales/v1/status`
2. Review application logs for detailed error messages
3. Test manual report generation: `POST /api/sales/v1/test-report`
4. Verify Slack integration and permissions