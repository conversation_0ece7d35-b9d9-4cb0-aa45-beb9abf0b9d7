-- Migration: Add doctor notification fields to chat_notifications table
-- Date: 2025-07-22
-- Purpose: Enable bidirectional chat notifications - doctors can see when patients send messages

-- Add fields to track patient messages and doctor notification state
ALTER TABLE chat_notifications 
ADD COLUMN IF NOT EXISTS last_patient_message_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS doctor_notification BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS unread_patient_messages INTEGER DEFAULT 0;

-- Add indexes for performance on new fields
CREATE INDEX IF NOT EXISTS idx_chat_notifications_doctor_notification 
ON chat_notifications(doctor_notification);

CREATE INDEX IF NOT EXISTS idx_chat_notifications_last_patient_message 
ON chat_notifications(last_patient_message_at);

CREATE INDEX IF NOT EXISTS idx_chat_notifications_unread_count 
ON chat_notifications(unread_patient_messages);

-- Add comments to document the new fields
COMMENT ON COLUMN chat_notifications.last_patient_message_at IS 'Timestamp of the last message sent by the patient to the doctor';
COMMENT ON COLUMN chat_notifications.doctor_notification IS 'Boolean flag indicating if doctor has unread patient messages';
COMMENT ON COLUMN chat_notifications.unread_patient_messages IS 'Count of unread messages from patient that doctor needs to see';

-- Update existing records to have default values
UPDATE chat_notifications 
SET doctor_notification = FALSE, 
    unread_patient_messages = 0 
WHERE doctor_notification IS NULL 
   OR unread_patient_messages IS NULL;
