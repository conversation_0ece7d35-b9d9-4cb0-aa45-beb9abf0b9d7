import React from "react";
import Grid from "@mui/material/Grid2";
import { <PERSON><PERSON>, Divider, Typography } from "@mui/material";
import { usePatient } from "../../hooks/patient-provider";

const SelectedPatient: React.FC = () => {
    const { selectedPatient } = usePatient()

    return (
        <>
            <Grid sx={{ border: '1px solid green', borderRadius: 2, p: 1, mt: { xs: 2, md: 5, lg: 0 } }} container direction={'column'}>
                <Grid container justifyContent={'center'} sx={{ mb: 1 }}>
                    <Typography sx={{ fontSize: '0.90rem', fontWeight: 'bold' }}>
                        Selected Patient
                    </Typography>
                </Grid>
                <Divider sx={{ mb: 2 }} />
                {selectedPatient ? <Grid sx={{ mb: 2 }} container direction={'column'}>
                    <Grid container sx={{ mb: 2 }}>
                        <Grid size={{ xs: 2 }}>
                            <Typography sx={{ fontSize: '14px', whiteSpace: 'nowrap' }}>
                                Name:
                            </Typography>
                        </Grid>
                        <Grid size={{ xs: 10 }}>
                            <Typography sx={{ fontSize: '14px', whiteSpace: 'nowrap' }}>
                                {selectedPatient.fullName}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container sx={{ mb: 2 }}>
                        <Grid size={{ xs: 2 }}>
                            <Typography sx={{ fontSize: '14px', whiteSpace: 'nowrap' }}>
                                Email:
                            </Typography>
                        </Grid>
                        <Grid size={{ xs: 10 }}>
                            <Typography sx={{ fontSize: '14px', whiteSpace: 'nowrap' }}>
                                {selectedPatient.email}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid container sx={{ mb: 2 }}>
                        <Grid size={{ xs: 2 }}>
                            <Typography sx={{ fontSize: '14px', whiteSpace: 'nowrap' }}>
                                Phone:
                            </Typography>
                        </Grid>
                        <Grid size={{ xs: 10 }}>
                            <Typography sx={{ fontSize: '14px', whiteSpace: 'nowrap' }}>
                                {selectedPatient.phone}
                            </Typography>
                        </Grid>
                    </Grid>
                </Grid> :
                    <>
                        <Grid container justifyContent={'center'} sx={{ mt: 4, mb: 4 }}>
                            <Typography sx={{ fontSize: '14px', whiteSpace: 'nowrap' }}>
                                No patient selected
                            </Typography>
                        </Grid>
                    </>}
                <Grid container justifyContent={'center'}>
                    <Button
                        sx={{
                            backgroundColor: 'green',
                            transform: 'none',
                            textTransform: 'none',
                            fontSize: '0.75rem',
                            borderRadius: '1.25rem',
                            outline: "none",
                            "&:focus": { outline: "none" },
                            "&:focusVisible": { outline: "none" },
                        }}
                        disabled={selectedPatient ? false : true}
                        variant="contained"
                    >
                        View Patient Details
                    </Button>
                </Grid>
            </Grid>

        </>
    )
}

export default SelectedPatient
