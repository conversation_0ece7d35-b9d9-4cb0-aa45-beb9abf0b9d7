import React from 'react';
import { Grid, Button, useMediaQuery, useTheme } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CircleOutlinedIcon from '@mui/icons-material/CircleOutlined';

export type StepsType = 'step1' | 'step2' | 'step3' | 'step4';

interface ChatBreadcrumbsProps {
    step: StepsType;
    onStepChange: (step: StepsType) => void;
}

const ChatBreadcrumbs: React.FC<ChatBreadcrumbsProps> = ({ step, onStepChange }) => {
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

    const steps = [
        { id: 'step1', label: 'Introduction' },
        { id: 'step2', label: 'Treatment Plan' },
        { id: 'step3', label: 'Strain Advice' },
        { id: 'step4', label: 'Review Patient Email' }
    ] as const;

    return (
        <Grid 
            container 
            justifyContent={isDesktop ? 'center' : 'start'} 
            alignItems="center" 
            spacing={isDesktop ? 5 : 0}
            sx={{ width: '100%' }}
        >
            {steps.map(({ id, label }) => (
                <Grid item key={id}>
                    <Button
                        variant={step === id ? 'outlined' : 'text'}
                        endIcon={step === id ? 
                            <CheckCircleIcon sx={{ width: isDesktop ? undefined : '12px' }} /> : 
                            <CircleOutlinedIcon sx={{ width: isDesktop ? undefined : '12px' }} />
                        }
                        onClick={() => onStepChange(id as StepsType)}
                        sx={{
                            textTransform: 'none',
                            color: step === id ? 'green' : '#676767',
                            borderColor: 'green',
                            width: isDesktop ? '200px' : 'auto',
                            fontSize: isDesktop ? undefined : '10px',
                            height: isDesktop ? undefined : '25px'
                        }}
                    >
                        {label}
                    </Button>
                </Grid>
            ))}
        </Grid>
    );
};

export default ChatBreadcrumbs; 