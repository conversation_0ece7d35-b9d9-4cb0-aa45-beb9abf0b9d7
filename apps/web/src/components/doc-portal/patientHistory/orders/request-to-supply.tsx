import { Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { PatientOrder } from "../../../../types";
import { usePatient } from "../../../../hooks/patient-provider";
import { convertTimeStampToReadableFormat } from "../../../../utils";
import OrderDetailsTable from "./order-details-table";

const RequestSupply: React.FC = () => {
    const { selectedPatientHistory } = usePatient()
    const orderSelected = selectedPatientHistory as PatientOrder

    return <>
        <Grid container direction='column' sx={{ width: '100%' }}>
            <Grid>
                <Typography>
                    Date: {convertTimeStampToReadableFormat(orderSelected?.updatedAt)}
                </Typography>
            </Grid>
            <Grid>
                <Typography>
                    <br />
                    Medicinal Cannabis Wholesale Australia Pty Ltd<br />
                    114/545-553 Pacific Highway<br />
                    St Leonards  NSW  2065<br /><br />
                    Re: Supply Request for Medicinal Cannabis Flower<br /><br />
                    I am writing to formally request the supply of Medicinal Cannabis Flower for our patients as part of the Authorised Prescriber Scheme. Our clinic, Zenith Clinics Pty Ltd, is authorised under the Therapeutic Goods Administration (TGA) guidelines to prescribe and supply unapproved therapeutic goods directly to patients in our immediate care.<br /><br />
                    Details of Medicinal Cannabis to be Collected:<br />
                </Typography>
            </Grid>
            <OrderDetailsTable />
            <Grid>
                <Typography>
                    <br />
                    NOTE: Only provide supply if the Quantity field contains an amount.<br /><br />
                    Purpose of the Request: These products are essential for the treatment plans of patients who have been pre-approved and meet the clinical criteria established by our practice. The use of these medicinal cannabis flowers is in strict compliance with the protocols outlined by the TGA and our internal clinical guidelines.<br /><br />
                    We assure you that all supplied goods will be used strictly in accordance with the Therapeutic Goods Act 1989 and the Therapeutic Goods Regulations 1990. Each patient will receive the product with the appropriate dispensing label and monitoring protocols to ensure their safety and the efficacy of the treatment.<br /><br />
                    Contact Information: Should you require any further information or have any queries regarding this request, please do not hesitate to contact me directly at 02 7228 8399 or via <NAME_EMAIL><br /><br />
                    Thank you for your prompt attention to this matter.<br /><br />
                    Yours sincerely,<br /><br />

                    <span style={{ fontStyle: 'italic', fontSize: '14px' }}>Your signature</span><br /><br />

                    {orderSelected?.drName} <br />

                    Zenith Clinics Pty Ltd <br />

                    114A/545-553 Pacific Highway <br />

                    St Leonards  NSW  2065 <br />
                </Typography>
            </Grid>

            <Grid>
                <Typography sx={{fontSize: '12px'}}>
                    <br/><br/>
                    Disclaimer:

                    This email and any attachments are intended solely for the designated recipient and may contain confidential or privileged information. If you are not the intended recipient:

                    <br/>(a) Please notify us immediately by phone and ensure the permanent deletion of this email from your system;

                    <br/>(b) Be advised that any unauthorized use, sharing, distribution, or reproduction of this email’s content is strictly forbidden.

                    <br/><br/>Opinions expressed herein are those of the author and do not necessarily represent the views of Zenith Clinics unless explicitly stated. Zenith Clinics and its directors disclaim any liability for potential harm from computer viruses transmitted through this email.
                </Typography>
            </Grid>
        </Grid>
    </>
}

export default RequestSupply
