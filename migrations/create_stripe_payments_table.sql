-- Migration: Create stripe_payments table for sales reporting
-- This table will store Stripe payment data extracted from webhooks
-- for generating daily, weekly, and monthly sales reports

CREATE TABLE IF NOT EXISTS stripe_payments (
  id SERIAL PRIMARY KEY,
  stripe_payment_intent_id VARCHAR(255) UNIQUE NOT NULL,
  customer_name VARCHAR(255),
  customer_email VARCHAR(255) NOT NULL,
  amount_cents INTEGER NOT NULL,
  currency VARCHAR(10) DEFAULT 'AUD',
  status VARCHAR(50) NOT NULL,
  description TEXT,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_stripe_payments_created_at ON stripe_payments (created_at);
CREATE INDEX IF NOT EXISTS idx_stripe_payments_status ON stripe_payments (status);
CREATE INDEX IF NOT EXISTS idx_stripe_payments_email ON stripe_payments (customer_email);
CREATE INDEX IF NOT EXISTS idx_stripe_payments_date_status ON stripe_payments (created_at, status);

-- Add comments for documentation
COMMENT ON TABLE stripe_payments IS 'Stores Stripe payment data for sales reporting and analytics';
COMMENT ON COLUMN stripe_payments.stripe_payment_intent_id IS 'Unique Stripe payment intent ID from webhook';
COMMENT ON COLUMN stripe_payments.amount_cents IS 'Payment amount in cents (e.g., 2999 for $29.99)';
COMMENT ON COLUMN stripe_payments.status IS 'Payment status from Stripe (e.g., payment_intent.succeeded, payment_intent.payment_failed)';
COMMENT ON COLUMN stripe_payments.metadata IS 'Additional Stripe metadata as JSON';
COMMENT ON COLUMN stripe_payments.created_at IS 'Timestamp when payment was created in Stripe';
COMMENT ON COLUMN stripe_payments.processed_at IS 'Timestamp when record was processed by our system';

-- Verify the table was created successfully
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'stripe_payments'
ORDER BY ordinal_position;

-- Verify indexes were created
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'stripe_payments'
ORDER BY indexname;