import { db } from '../../utils/db';
import { logger } from '../../config/logger';
import { findPatientsForReassignment, reassignPatient } from './doctorQueueManager';
import { timerMap } from './index';
import { WebSocketManager } from '../../helpers/webSocketManager';
import { TOPICS } from '../../helpers/topics';

/**
 * Periodically checks for patients who have been waiting for more than 30 seconds
 * and reassigns them to available doctors if needed
 */

export const startPatientReassignmentProcess = async (): Promise<void> => {

  try {
    // Find patients who have been waiting for more than 30 seconds
    const patientsToReassign = await findPatientsForReassignment(db);

    if (patientsToReassign.length === 0) {
      return;
    }

    // Get all available doctors from timer map
    const availableDoctorIDs = Array.from(timerMap.keys()).filter(key => key !== 'patientInterval') as string[];

    if (availableDoctorIDs.length === 0) {
      logger.info('No available doctors for reassignment');
      return;
    }

    logger.info(`📋 BATCH REASSIGNMENT - ${patientsToReassign.length} patients to distribute across ${availableDoctorIDs.length} doctors`);

    // Distribute patients evenly across available doctors using round-robin
    for (let i = 0; i < patientsToReassign.length; i++) {
      const doctorIndex = i % availableDoctorIDs.length;
      const selectedDoctorID = availableDoctorIDs[doctorIndex];
      const patientID = patientsToReassign[i];

      const success = await reassignPatient(db, patientID, selectedDoctorID);

      if (success) {
       

        // Notify via WebSocket
        WebSocketManager.dispatch(TOPICS.PATIENT_REASSIGNED, {
          patientID,
          newDoctorID: selectedDoctorID
        });
      }
    }

    // Log final distribution summary
    logger.info(`✅ BATCH REASSIGNMENT COMPLETE - ${patientsToReassign.length} patients distributed evenly across ${availableDoctorIDs.length} doctors`);

  } catch (error) {
    logger.error('Error in patient reassignment process:', error);
  }

};
// export const startPatientReassignmentProcess = (): NodeJS.Timeout => {
//   logger.info('Starting patient reassignment process');

//   const reassignmentInterval = setInterval(async () => {
//     try {
//       // Find patients who have been waiting for more than 30 seconds
//       const patientsToReassign = await findPatientsForReassignment(db);

//       if (patientsToReassign.length === 0) {
//         return;
//       }

//       logger.info(`Found ${patientsToReassign.length} patients eligible for reassignment`);

//       // Find the least busy doctor
//       const leastBusyDoctorID = await findLeastBusyDoctor(db);

//       if (!leastBusyDoctorID) {
//         logger.info('No available doctors for reassignment');
//         return;
//       }

//       // Reassign each patient to the least busy doctor
//       for (const patientID of patientsToReassign) {
//         const success = await reassignPatient(db, patientID, leastBusyDoctorID);

//         if (success) {
//           //logger.info(`Reassigned patient ${patientID} to doctor ${leastBusyDoctorID}`);

//           // Notify via WebSocket
//           WebSocketManager.dispatch(TOPICS.PATIENT_REASSIGNED, {
//             patientID,
//             newDoctorID: leastBusyDoctorID
//           });
//         }
//       }
//     } catch (error) {
//       logger.error('Error in patient reassignment process:', error);
//     }
//   }, 30000); // Run every 30 seconds

//   return reassignmentInterval;
// };

/**
 * Stops the patient reassignment process
 * @param intervalId The interval ID to clear
 */
export const stopPatientReassignmentProcess = (intervalId: NodeJS.Timeout): void => {
  clearInterval(intervalId);
  logger.info('Stopped patient reassignment process');
};
