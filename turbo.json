{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"], "cache": false}, "lint": {"dependsOn": ["^lint"], "cache": true}, "dev": {"cache": false, "persistent": true}, "docker:start:db": {"cache": true, "persistent": true}, "checks": {"cache": true, "persistent": true}, "prod": {"cache": true, "persistent": true}, "test": {"outputs": ["coverage/**"], "cache": true}, "precommit": {"cache": true, "persistent": true}}}