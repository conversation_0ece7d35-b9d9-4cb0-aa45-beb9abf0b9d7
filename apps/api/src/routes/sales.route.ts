import express from 'express';
import {
  triggerDailySalesReport,
  triggerWeeklySalesReport,
  triggerMonthlySalesReport,
  getSalesReportStatus,
  testSalesReport,
  getSalesData,
  generateTestData,
  // Stripe API Sync endpoints
  syncDailyPayments,
  syncWeeklyPayments,
  syncDateRange,
  backfillPayments,
  getSyncStatus,
  getSyncHistory,
  getSyncHealthCheck,
  testStripeConnection,
  getStripeAPIUsage,

  // Monitoring endpoints
  getMonitoringMetrics,
  getSyncLogs,
  getMonitoringDashboard,
  // Enhanced Backfill endpoints
  createBackfillPlan,
  executeSmartBackfill,
  getBackfillProgress,
  getActiveBackfills,
  triggerDailySalesReportImage
} from '../controllers/sales';

const router = express.Router();
const currentVersion = 'v1';

// ============================================================================
// SALES REPORTING ENDPOINTS (Original)
// ============================================================================

// Cron job trigger endpoints (no auth required for server-level cron)
router.post(`/${currentVersion}/trigger-daily-report`, triggerDailySalesReport);
router.post(`/${currentVersion}/trigger-weekly-report`, triggerWeeklySalesReport);
router.post(`/${currentVersion}/trigger-monthly-report`, triggerMonthlySalesReport);
router.post(`/${currentVersion}/trigger-daily-report-image`, triggerDailySalesReportImage);

// Status and monitoring endpoints
router.get(`/${currentVersion}/status`, getSalesReportStatus);

// Testing and manual data endpoints
router.post(`/${currentVersion}/test-report`, testSalesReport);
router.post(`/${currentVersion}/generate-test-data`, generateTestData);
router.get(`/${currentVersion}/data`, getSalesData);

// ============================================================================
// STRIPE API SYNC ENDPOINTS (New)
// ============================================================================

// Sync trigger endpoints (designed for cron jobs)
router.post(`/${currentVersion}/sync-daily-payments`, syncDailyPayments);
router.post(`/${currentVersion}/sync-weekly-payments`, syncWeeklyPayments);
router.post(`/${currentVersion}/sync-date-range`, syncDateRange);
router.post(`/${currentVersion}/backfill-payments`, backfillPayments);

// Sync monitoring and status endpoints
router.get(`/${currentVersion}/sync-status`, getSyncStatus);
router.get(`/${currentVersion}/sync-history`, getSyncHistory);
router.get(`/${currentVersion}/sync-health`, getSyncHealthCheck);

// Stripe API testing endpoints
router.post(`/${currentVersion}/test-stripe-connection`, testStripeConnection);
router.get(`/${currentVersion}/stripe-api-usage`, getStripeAPIUsage);



// ============================================================================
// MONITORING ENDPOINTS
// ============================================================================

// Comprehensive monitoring endpoints
router.get(`/${currentVersion}/monitoring-metrics`, getMonitoringMetrics);
router.get(`/${currentVersion}/monitoring-dashboard`, getMonitoringDashboard);
router.get(`/${currentVersion}/sync-logs`, getSyncLogs);

// ============================================================================
// ENHANCED BACKFILL ENDPOINTS
// ============================================================================

// Backfill planning and execution
router.post(`/${currentVersion}/create-backfill-plan`, createBackfillPlan);
router.post(`/${currentVersion}/smart-backfill`, executeSmartBackfill);

// Backfill monitoring
router.get(`/${currentVersion}/backfill-progress/:planId`, getBackfillProgress);
router.get(`/${currentVersion}/active-backfills`, getActiveBackfills);

export default router;