import { <PERSON>quest<PERSON>and<PERSON> } from 'express';
import { catchAll } from '../../utils/catchAll';
import { logger } from '../../config/logger';
import config from '../../config';
import salesReportingService from '../../services/salesReporting.service';
import StripeSyncService from '../../services/stripeSyncService';
import StripeAPIService from '../../services/stripeAPI.service';

import SyncMonitoringService from '../../services/syncMonitoring.service';
import BackfillService from '../../services/backfillService';
import { DateTime } from 'luxon';

/**
 * Trigger daily sales report (designed to be called by cron job)
 * @route POST /api/sales/v1/trigger-daily-report
 */
export const triggerDailySalesReport: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Daily sales report triggered via API endpoint');

    await salesReportingService.sendDailySalesReport();

    const response = {
      success: true,
      message: 'Daily sales report sent successfully',
      timestamp: new Date().toISOString(),
      reportType: 'daily'
    };

    logger.info('Daily sales report completed successfully');
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to trigger daily sales report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send daily sales report',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Trigger daily sales report as image (for Slack)
 * @route POST /api/sales/v1/trigger-daily-report-image
 */
export const triggerDailySalesReportImage: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Daily sales report (image) triggered via API endpoint');
    await salesReportingService.sendDailySalesReportAsImage();
    const response = {
      success: true,
      message: 'Daily sales report image sent successfully',
      timestamp: new Date().toISOString(),
      reportType: 'daily-image'
    };
    logger.info('Daily sales report image completed successfully');
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to trigger daily sales report image:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send daily sales report image',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Trigger weekly sales report (designed to be called by cron job)
 * @route POST /api/sales/v1/trigger-weekly-report
 */
export const triggerWeeklySalesReport: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Weekly sales report triggered via API endpoint');

    await salesReportingService.sendWeeklySalesReport();

    const response = {
      success: true,
      message: 'Weekly sales report sent successfully',
      timestamp: new Date().toISOString(),
      reportType: 'weekly'
    };

    logger.info('Weekly sales report completed successfully');
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to trigger weekly sales report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send weekly sales report',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Trigger monthly sales report (designed to be called by cron job)
 * @route POST /api/sales/v1/trigger-monthly-report
 */
export const triggerMonthlySalesReport: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Monthly sales report triggered via API endpoint');

    await salesReportingService.sendMonthlySalesReport();

    const response = {
      success: true,
      message: 'Monthly sales report sent successfully',
      timestamp: new Date().toISOString(),
      reportType: 'monthly'
    };

    logger.info('Monthly sales report completed successfully');
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to trigger monthly sales report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send monthly sales report',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get sales reporting system status and configuration
 * @route GET /api/sales/v1/status
 */
export const getSalesReportStatus: RequestHandler = catchAll(async (_req, res) => {
  try {
    const now = DateTime.now().setZone('Australia/Sydney');

    const status = {
      service: 'Sales Reporting',
      enabled: config.salesReportingEnabled,
      environment: config.env,
      timezone: 'Australia/Sydney',
      currentTime: now.toISO(),
      configuration: {
        slackChannel: config.slackSalesReportChannel ? 'Configured' : 'Not configured',
        slackToken: config.slackToken ? 'Configured' : 'Not configured',
        stripeApiKey: config.stripeApiKey ? 'Configured' : 'Not configured',
        dataRetentionDays: config.salesDataRetentionDays
      },
      endpoints: {
        dailyReport: '/api/sales/v1/trigger-daily-report',
        weeklyReport: '/api/sales/v1/trigger-weekly-report',
        monthlyReport: '/api/sales/v1/trigger-monthly-report',
        testReport: '/api/sales/v1/test-report'
      },
      cronJobs: {
        daily: {
          schedule: '0 9 * * *',
          description: 'Daily at 9:00 AM',
          command: 'curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report'
        },
        weekly: {
          schedule: '0 9 * * 1',
          description: 'Weekly on Monday at 9:00 AM',
          command: 'curl -X POST https://your-domain.com/api/sales/v1/trigger-weekly-report'
        },
        monthly: {
          schedule: '0 9 1 * *',
          description: 'Monthly on 1st at 9:00 AM',
          command: 'curl -X POST https://your-domain.com/api/sales/v1/trigger-monthly-report'
        }
      },
      lastChecked: now.toISO()
    };

    res.status(200).json(status);
  } catch (error) {
    logger.error('Failed to get sales report status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Generate sample test data for testing reports
 * @route POST /api/sales/v1/generate-test-data
 */
export const generateTestData: RequestHandler = catchAll(async (req, res) => {
  try {
    const { days = 7, paymentsPerDay = 5, targetMonth } = req.body;

    if (targetMonth) {
      logger.info(`Generating test data for ${targetMonth} with ${paymentsPerDay} payments per day`);
    } else {
      logger.info(`Generating test data for ${days} days with ${paymentsPerDay} payments per day`);
    }

    const result = await salesReportingService.generateSampleData(days, paymentsPerDay, targetMonth);

    res.status(200).json({
      success: true,
      message: 'Test data generated successfully',
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to generate test data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate test data',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Send a test sales report with current data
 * @route POST /api/sales/v1/test-report
 */
export const testSalesReport: RequestHandler = catchAll(async (req, res) => {
  try {
    const { startDate, endDate, reportType = 'daily' } = req.body;

    logger.info('Test sales report triggered via API endpoint', { startDate, endDate, reportType });

    if (startDate && endDate) {
      // Test with custom date range
      const salesData = await salesReportingService.getSalesDataForDateRange(startDate, endDate);

      res.status(200).json({
        success: true,
        message: 'Test sales data retrieved successfully',
        data: salesData,
        dateRange: { startDate, endDate },
        timestamp: new Date().toISOString()
      });
    } else {
      // Send test report to Slack based on type
      switch (reportType) {
        case 'daily':
          await salesReportingService.sendTestReport();
          break;
        case 'weekly':
          await salesReportingService.sendTestWeeklyReport();
          break;
        case 'monthly':
          await salesReportingService.sendTestMonthlyReport();
          break;
        default:
          await salesReportingService.sendTestReport();
      }

      res.status(200).json({
        success: true,
        message: `Test ${reportType} sales report sent to Slack successfully`,
        reportType,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Failed to send test sales report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test sales report',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get sales data for a specific date range (for manual analysis)
 * @route GET /api/sales/v1/data?startDate=YYYY-MM-DD&endDate=YYYY-MM-DD
 */
export const getSalesData: RequestHandler = catchAll(async (req, res): Promise<void> => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      res.status(400).json({
        success: false,
        message: 'startDate and endDate query parameters are required',
        example: '/api/sales/v1/data?startDate=2024-01-01&endDate=2024-01-31'
      });
      return;
    }

    logger.info(`Sales data requested for date range: ${startDate} to ${endDate}`);

    const salesData = await salesReportingService.getSalesDataForDateRange(
      startDate as string,
      endDate as string
    );

    res.status(200).json({
      success: true,
      data: salesData,
      dateRange: { startDate, endDate },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get sales data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve sales data',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// ============================================================================
// STRIPE API SYNC ENDPOINTS
// ============================================================================

/**
 * Trigger daily Stripe API sync (designed to be called by cron job)
 * @route POST /api/sales/v1/sync-daily-payments
 */
export const syncDailyPayments: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Daily Stripe API sync triggered via API endpoint');

    const stripeSyncService = new StripeSyncService();
    const result = await stripeSyncService.syncDailyPayments();

    const response = {
      success: true,
      message: 'Daily payments sync completed successfully',
      timestamp: new Date().toISOString(),
      syncType: 'daily',
      result: {
        paymentsProcessed: result.paymentsProcessed,
        paymentsInserted: result.paymentsInserted,
        paymentsUpdated: result.paymentsUpdated,
        errors: result.errors,
        syncDuration: result.syncDuration
      }
    };

    logger.info('Daily Stripe API sync completed successfully', { result });
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to sync daily payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync daily payments',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Trigger weekly Stripe API sync (catchup for missed payments)
 * @route POST /api/sales/v1/sync-weekly-payments
 */
export const syncWeeklyPayments: RequestHandler = catchAll(async (req, res) => {
  try {
    const { daysBack = 7 } = req.body;
    logger.info(`Weekly Stripe API sync triggered for ${daysBack} days back`);

    const stripeSyncService = new StripeSyncService();
    const result = await stripeSyncService.syncWeeklyPayments(daysBack);

    const response = {
      success: true,
      message: `Weekly payments sync completed for ${daysBack} days`,
      timestamp: new Date().toISOString(),
      syncType: 'weekly',
      daysBack,
      result: {
        paymentsProcessed: result.paymentsProcessed,
        paymentsInserted: result.paymentsInserted,
        paymentsUpdated: result.paymentsUpdated,
        errors: result.errors,
        syncDuration: result.syncDuration
      }
    };

    logger.info('Weekly Stripe API sync completed successfully', { result });
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to sync weekly payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync weekly payments',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Sync payments for a specific date range
 * @route POST /api/sales/v1/sync-date-range
 */
export const syncDateRange: RequestHandler = catchAll(async (req, res) => {
  try {
    const { startDate, endDate, syncType = 'manual' } = req.body;

    if (!startDate || !endDate) {
      res.status(400).json({
        success: false,
        message: 'startDate and endDate are required',
        example: { startDate: '2024-01-01', endDate: '2024-01-31' }
      });
      return;
    }

    logger.info(`Date range sync triggered: ${startDate} to ${endDate}`);

    const stripeSyncService = new StripeSyncService();
    const startDateTime = DateTime.fromISO(startDate).setZone('Australia/Sydney');
    const endDateTime = DateTime.fromISO(endDate).setZone('Australia/Sydney');

    const result = await stripeSyncService.syncPaymentsForDateRange({
      startDate: startDateTime,
      endDate: endDateTime,
      syncType: syncType as 'daily' | 'weekly' | 'manual' | 'backfill'
    });

    const response = {
      success: true,
      message: `Date range sync completed: ${startDate} to ${endDate}`,
      timestamp: new Date().toISOString(),
      syncType,
      dateRange: { startDate, endDate },
      result: {
        paymentsProcessed: result.paymentsProcessed,
        paymentsInserted: result.paymentsInserted,
        paymentsUpdated: result.paymentsUpdated,
        errors: result.errors,
        syncDuration: result.syncDuration
      }
    };

    logger.info('Date range sync completed successfully', { result });
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to sync date range:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync date range',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Backfill historical payments
 * @route POST /api/sales/v1/backfill-payments
 */
export const backfillPayments: RequestHandler = catchAll(async (req, res) => {
  try {
    const { daysBack = 30 } = req.body;

    if (daysBack > 365) {
      res.status(400).json({
        success: false,
        message: 'Cannot backfill more than 365 days at once',
        maxDaysBack: 365
      });
      return;
    }

    logger.info(`Backfill payments triggered for ${daysBack} days back`);

    const stripeSyncService = new StripeSyncService();
    const result = await stripeSyncService.backfillPayments(daysBack);

    const response = {
      success: true,
      message: `Backfill completed for ${daysBack} days`,
      timestamp: new Date().toISOString(),
      syncType: 'backfill',
      daysBack,
      result: {
        paymentsProcessed: result.paymentsProcessed,
        paymentsInserted: result.paymentsInserted,
        paymentsUpdated: result.paymentsUpdated,
        errors: result.errors,
        syncDuration: result.syncDuration
      }
    };

    logger.info('Backfill payments completed successfully', { result });
    res.status(200).json(response);
  } catch (error) {
    logger.error('Failed to backfill payments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to backfill payments',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get sync status for a specific date
 * @route GET /api/sales/v1/sync-status?date=YYYY-MM-DD
 */
export const getSyncStatus: RequestHandler = catchAll(async (req, res) => {
  try {
    const { date } = req.query;

    if (!date) {
      res.status(400).json({
        success: false,
        message: 'date query parameter is required',
        example: '/api/sales/v1/sync-status?date=2024-01-01'
      });
      return;
    }

    logger.info(`Sync status requested for date: ${date}`);

    const stripeSyncService = new StripeSyncService();
    const syncStatus = await stripeSyncService.getSyncStatus(date as string);

    res.status(200).json({
      success: true,
      date,
      syncStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get sync status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get sync status',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get recent sync history
 * @route GET /api/sales/v1/sync-history?limit=10
 */
export const getSyncHistory: RequestHandler = catchAll(async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const limitNum = parseInt(limit as string, 10);

    if (limitNum > 100) {
      res.status(400).json({
        success: false,
        message: 'Limit cannot exceed 100',
        maxLimit: 100
      });
      return;
    }

    logger.info(`Sync history requested with limit: ${limitNum}`);

    const stripeSyncService = new StripeSyncService();
    const syncHistory = await stripeSyncService.getRecentSyncHistory(limitNum);

    res.status(200).json({
      success: true,
      limit: limitNum,
      count: syncHistory.length,
      syncHistory,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get sync history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get sync history',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Health check for Stripe API sync system
 * @route GET /api/sales/v1/sync-health
 */
export const getSyncHealthCheck: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Sync health check requested');

    const stripeSyncService = new StripeSyncService();
    const healthCheck = await stripeSyncService.healthCheck();

    const statusCode = healthCheck.healthy ? 200 : 503;

    res.status(statusCode).json({
      success: healthCheck.healthy,
      health: healthCheck,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to perform health check:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform health check',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Test Stripe API connection
 * @route POST /api/sales/v1/test-stripe-connection
 */
export const testStripeConnection: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Stripe API connection test requested');

    const stripeAPIService = new StripeAPIService();
    const connectionTest = await stripeAPIService.testConnection();

    res.status(200).json({
      success: connectionTest,
      message: connectionTest ? 'Stripe API connection successful' : 'Stripe API connection failed',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to test Stripe connection:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test Stripe connection',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get Stripe API usage information
 * @route GET /api/sales/v1/stripe-api-usage
 */
export const getStripeAPIUsage: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Stripe API usage information requested');

    const stripeAPIService = new StripeAPIService();
    const apiUsage = await stripeAPIService.getAPIUsage();

    res.status(200).json({
      success: true,
      apiUsage,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get Stripe API usage:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get Stripe API usage',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});











// ============================================================================
// SYNC MONITORING ENDPOINTS
// ============================================================================

/**
 * Get comprehensive monitoring metrics and alerts
 * @route GET /api/sales/v1/monitoring-metrics
 */
export const getMonitoringMetrics: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Monitoring metrics requested');

    const syncMonitoringService = new SyncMonitoringService();
    const metrics = await syncMonitoringService.getMonitoringMetrics();

    res.status(200).json({
      success: true,
      metrics,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get monitoring metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get monitoring metrics',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get detailed sync logs with filtering
 * @route GET /api/sales/v1/sync-logs?limit=50&status=success&syncType=daily&startDate=2024-01-01&endDate=2024-01-31
 */
export const getSyncLogs: RequestHandler = catchAll(async (req, res) => {
  try {
    const { limit, status, syncType, startDate, endDate } = req.query;

    logger.info('Sync logs requested', { limit, status, syncType, startDate, endDate });

    const syncMonitoringService = new SyncMonitoringService();
    const logs = await syncMonitoringService.getSyncLogs({
      limit: limit ? parseInt(limit as string, 10) : undefined,
      status: status as string,
      syncType: syncType as string,
      startDate: startDate as string,
      endDate: endDate as string,
    });

    res.status(200).json({
      success: true,
      count: logs.length,
      logs,
      filters: { limit, status, syncType, startDate, endDate },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get sync logs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get sync logs',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get monitoring dashboard data (combined metrics for dashboard view)
 * @route GET /api/sales/v1/monitoring-dashboard
 */
export const getMonitoringDashboard: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Monitoring dashboard data requested');

    const syncMonitoringService = new SyncMonitoringService();

    // Get all monitoring data
    const [metrics, recentLogs] = await Promise.all([
      syncMonitoringService.getMonitoringMetrics(),
      syncMonitoringService.getSyncLogs({ limit: 10 }),
    ]);

    const dashboard = {
      overview: {
        systemHealthy: metrics.alerts.every(alert => alert.level !== 'error'),
        totalAlerts: metrics.alerts.length,
        criticalAlerts: metrics.alerts.filter(alert => alert.level === 'error').length,
        lastSyncStatus: recentLogs[0]?.sync_status || 'unknown',
        lastSyncTime: recentLogs[0]?.completed_at || null,
      },
      metrics,
      recentActivity: recentLogs,
      quickActions: {
        testStripeConnection: '/api/sales/v1/test-stripe-connection',
        syncHealthCheck: '/api/sales/v1/sync-health',
        triggerDailyReport: '/api/sales/v1/trigger-daily-report',
        syncDailyPayments: '/api/sales/v1/sync-daily-payments',
      }
    };

    res.status(200).json({
      success: true,
      dashboard,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get monitoring dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get monitoring dashboard',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

// ============================================================================
// ENHANCED BACKFILL ENDPOINTS
// ============================================================================

/**
 * Create a backfill plan for a date range
 * @route POST /api/sales/v1/create-backfill-plan
 */
export const createBackfillPlan: RequestHandler = catchAll(async (req, res) => {
  try {
    const { startDate, endDate, chunkSizeDays = 7 } = req.body;

    if (!startDate || !endDate) {
      res.status(400).json({
        success: false,
        message: 'startDate and endDate are required',
        example: { startDate: '2024-01-01', endDate: '2024-01-31', chunkSizeDays: 7 }
      });
      return;
    }

    logger.info(`Creating backfill plan: ${startDate} to ${endDate}`);

    const backfillService = new BackfillService();
    const startDateTime = DateTime.fromISO(startDate).setZone('Australia/Sydney');
    const endDateTime = DateTime.fromISO(endDate).setZone('Australia/Sydney');

    const plan = await backfillService.createBackfillPlan(startDateTime, endDateTime, chunkSizeDays);

    res.status(200).json({
      success: true,
      message: 'Backfill plan created successfully',
      plan,
      dateRange: { startDate, endDate },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to create backfill plan:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create backfill plan',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Execute smart backfill (automatically finds missing data)
 * @route POST /api/sales/v1/smart-backfill
 */
export const executeSmartBackfill: RequestHandler = catchAll(async (req, res) => {
  try {
    const { startDate, endDate } = req.body;

    if (!startDate || !endDate) {
      res.status(400).json({
        success: false,
        message: 'startDate and endDate are required',
        example: { startDate: '2024-01-01', endDate: '2024-01-31' }
      });
      return;
    }

    logger.info(`Starting smart backfill: ${startDate} to ${endDate}`);

    const backfillService = new BackfillService();
    const startDateTime = DateTime.fromISO(startDate).setZone('Australia/Sydney');
    const endDateTime = DateTime.fromISO(endDate).setZone('Australia/Sydney');

    const result = await backfillService.smartBackfill(startDateTime, endDateTime);

    res.status(200).json({
      success: result.success,
      message: 'Smart backfill completed',
      result,
      dateRange: { startDate, endDate },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to execute smart backfill:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to execute smart backfill',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get backfill progress
 * @route GET /api/sales/v1/backfill-progress/:planId
 */
export const getBackfillProgress: RequestHandler = catchAll(async (req, res) => {
  try {
    const { planId } = req.params;

    if (!planId) {
      res.status(400).json({
        success: false,
        message: 'planId parameter is required'
      });
      return;
    }

    logger.info(`Backfill progress requested for plan: ${planId}`);

    const backfillService = new BackfillService();
    const progress = backfillService.getBackfillProgress(planId);

    if (!progress) {
      res.status(404).json({
        success: false,
        message: 'Backfill plan not found or expired',
        planId
      });
      return;
    }

    res.status(200).json({
      success: true,
      progress,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get backfill progress:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get backfill progress',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * List all active backfills
 * @route GET /api/sales/v1/active-backfills
 */
export const getActiveBackfills: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Active backfills list requested');

    const backfillService = new BackfillService();
    const activeBackfills = backfillService.getActiveBackfills();

    res.status(200).json({
      success: true,
      count: activeBackfills.length,
      activeBackfills,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to get active backfills:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get active backfills',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

