import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  IconButton,
  Pagination
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  SwapHoriz as ReverseIcon,
  AccessTime as TimeIcon,
  Lock as LockIcon,
  AttachFile as AttachFileIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import moderationService, { ConversationModeration, PaginationInfo } from '../../../services/moderation.service';
import ConversationDetailsModal from './ConversationDetailsModal';

interface ModeratedMessagesTabProps {
  onStatsUpdate: () => void;
  onShowSnackbar: (message: string, severity: 'success' | 'error' | 'warning' | 'info') => void;
}

const ModeratedMessagesTab: React.FC<ModeratedMessagesTabProps> = ({
  onStatsUpdate,
  onShowSnackbar
}) => {
  const [conversations, setConversations] = useState<ConversationModeration[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'both' | 'approved' | 'rejected'>('both');
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 5,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  const [reverseDialog, setReverseDialog] = useState<{
    open: boolean;
    conversation: ConversationModeration | null;
    newStatus: 'approved' | 'rejected' | null;
  }>({
    open: false,
    conversation: null,
    newStatus: null
  });
  const [reverseReason, setReverseReason] = useState('');
  const [reversing, setReversing] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [imageModal, setImageModal] = useState<{
    open: boolean;
    imageUrl: string;
    alt: string;
  }>({
    open: false,
    imageUrl: '',
    alt: ''
  });
  const [detailsModal, setDetailsModal] = useState<{
    open: boolean;
    conversation: ConversationModeration | null;
  }>({
    open: false,
    conversation: null
  });

  // Load moderated conversations
  const loadConversations = async (page: number = pagination.page) => {
    try {
      setLoading(true);
      const statusFilter = filter === 'both' ? undefined : filter;
      const response = await moderationService.getModeratedMessages(pagination.limit, page, statusFilter);
      setConversations(response.data);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Error loading moderated conversations:', error);
      onShowSnackbar('Failed to load moderated conversations', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Reset to page 1 when filter changes
    setPagination(prev => ({ ...prev, page: 1 }));
    loadConversations(1);
  }, [filter]);

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    loadConversations(page);
  };

  // Update current time every minute for countdown timers
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, []);

  // Check if conversation can be reversed (within 30 minutes)
  const canReverse = (conversation: ConversationModeration): boolean => {
    if (!conversation.moderatedAt) return false;

    const moderatedTime = new Date(conversation.moderatedAt);
    const diffMinutes = (currentTime.getTime() - moderatedTime.getTime()) / (1000 * 60);

    return diffMinutes <= 30;
  };

  // Get time remaining for reversal
  const getTimeRemaining = (conversation: ConversationModeration): string => {
    if (!conversation.moderatedAt) return '';

    const moderatedTime = new Date(conversation.moderatedAt);
    const diffMinutes = (currentTime.getTime() - moderatedTime.getTime()) / (1000 * 60);
    const remainingMinutes = Math.max(0, 30 - diffMinutes);

    if (remainingMinutes <= 0) return 'Final';
    if (remainingMinutes < 1) return '< 1 min';
    return `${Math.floor(remainingMinutes)} min`;
  };

  // Handle filter change
  const handleFilterChange = (
    _event: React.MouseEvent<HTMLElement>,
    newFilter: 'both' | 'approved' | 'rejected'
  ) => {
    if (newFilter !== null) {
      setFilter(newFilter);
    }
  };

  // Handle reverse action
  const handleReverse = (conversation: ConversationModeration) => {
    const newStatus = conversation.moderationStatus === 'approved' ? 'rejected' : 'approved';
    setReverseDialog({
      open: true,
      conversation,
      newStatus
    });
    setReverseReason('');
  };

  // Confirm reverse action
  const confirmReverse = async () => {
    if (!reverseDialog.conversation || !reverseDialog.newStatus) return;

    try {
      setReversing(true);

      await moderationService.moderateConversation({
        channelId: reverseDialog.conversation.channelId,
        action: reverseDialog.newStatus === 'approved' ? 'approve' : 'reject',
        reason: reverseReason || `Reversed from ${reverseDialog.conversation.moderationStatus}`
      });

      onShowSnackbar(
        `Conversation ${reverseDialog.newStatus} successfully`,
        'success'
      );

      // Refresh data
      await loadConversations();
      onStatsUpdate();

      // Close dialog
      setReverseDialog({ open: false, conversation: null, newStatus: null });
      setReverseReason('');
    } catch (error) {
      console.error('Error reversing moderation:', error);
      onShowSnackbar('Failed to reverse moderation decision', 'error');
    } finally {
      setReversing(false);
    }
  };

  // Handle moderation from details modal
  const handleDetailsModeration = (channelId: string, action: 'approve' | 'reject') => {
    // Find the conversation to reverse
    const conversation = conversations.find(c => c.channelId === channelId);
    if (!conversation) return;

    // Close details modal first
    setDetailsModal({ open: false, conversation: null });

    // Open reverse dialog - convert action to status
    const newStatus: 'approved' | 'rejected' = action === 'approve' ? 'approved' : 'rejected';
    setReverseDialog({
      open: true,
      conversation,
      newStatus
    });
    setReverseReason('');
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-AU', {
      timeZone: 'Australia/Sydney',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Moderated Messages
        </Typography>

        <ToggleButtonGroup
          value={filter}
          exclusive
          onChange={handleFilterChange}
          aria-label="message filter"
          size="small"
        >
          <ToggleButton value="both" aria-label="show both">
            Both
          </ToggleButton>
          <ToggleButton value="approved" aria-label="show approved" sx={{
            '&.Mui-selected': {
              backgroundColor: '#008000',
              color: 'white',
              '&:hover': {
                backgroundColor: '#006600',
              }
            }
          }}>
            <ApproveIcon sx={{ mr: 1, fontSize: 16 }} />
            Approved
          </ToggleButton>
          <ToggleButton value="rejected" aria-label="show rejected">
            <RejectIcon sx={{ mr: 1, fontSize: 16 }} />
            Rejected
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {conversations.length === 0 ? (
        <Alert severity="info">
          No moderated conversations found.
        </Alert>
      ) : (
        <>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Patient</TableCell>
                  <TableCell>Conversation</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Started</TableCell>
                  <TableCell>Moderated</TableCell>
                  <TableCell>Moderator</TableCell>
                  <TableCell>Reason</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {conversations.map((conversation) => {
                  const reversible = canReverse(conversation);
                  const timeRemaining = getTimeRemaining(conversation);

                  return (
                    <TableRow key={conversation.id}>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {conversation.patientName}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            ID: {conversation.patientId}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ maxWidth: 250 }}>
                          {/* Show conversation preview */}
                          {conversation.messagePreview && conversation.messagePreview.trim() && (
                            <Typography
                              variant="body2"
                              sx={{
                                wordBreak: 'break-word',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                mb: 1
                              }}
                            >
                              {conversation.messagePreview}
                            </Typography>
                          )}

                          {/* Show message count and attachment summary */}
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                            <Typography variant="body2" color="textSecondary">
                              {conversation.messageCount} message{conversation.messageCount !== 1 ? 's' : ''}
                            </Typography>
                            {conversation.hasAttachments && (
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <AttachFileIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                                <Typography variant="body2" color="primary.main">
                                  Has attachments
                                </Typography>
                              </Box>
                            )}
                          </Box>

                          {/* Show placeholder if no content */}
                          {!conversation.messagePreview?.trim() && conversation.messageCount === 0 && (
                            <Typography variant="body2" color="textSecondary" fontStyle="italic">
                              [No messages]
                            </Typography>
                          )}

                          {/* View Details Button */}
                          <Button
                            size="small"
                            startIcon={<ViewIcon />}
                            onClick={() => setDetailsModal({ open: true, conversation })}
                            sx={{ mt: 1 }}
                          >
                            View Details
                          </Button>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={conversation.moderationStatus === 'approved' ? <ApproveIcon /> : <RejectIcon />}
                          label={conversation.moderationStatus.toUpperCase()}
                          size="small"
                          sx={{
                            backgroundColor: conversation.moderationStatus === 'approved' ? '#008000' : undefined,
                            color: conversation.moderationStatus === 'approved' ? 'white' : undefined,
                            '& .MuiChip-icon': {
                              color: conversation.moderationStatus === 'approved' ? 'white' : undefined
                            }
                          }}
                          color={conversation.moderationStatus === 'approved' ? undefined : 'error'}
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatTimestamp(conversation.createdAt)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {conversation.moderatedAt ? formatTimestamp(conversation.moderatedAt) : 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {conversation.moderatorName || conversation.moderatedBy || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" sx={{ maxWidth: 200, wordBreak: 'break-word' }}>
                          {conversation.moderationReason || 'No reason provided'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {reversible ? (
                            <>
                              <Tooltip title={`Reverse to ${conversation.moderationStatus === 'approved' ? 'rejected' : 'approved'}`}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleReverse(conversation)}
                                  color="primary"
                                >
                                  <ReverseIcon />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title={`Time remaining: ${timeRemaining}`}>
                                <Chip
                                  icon={<TimeIcon />}
                                  label={timeRemaining}
                                  size="small"
                                  variant="outlined"
                                  color="warning"
                                />
                              </Tooltip>
                            </>
                          ) : (
                            <Tooltip title="Decision is final (30+ minutes elapsed)">
                              <Chip
                                icon={<LockIcon />}
                                label="Final"
                                size="small"
                                variant="outlined"
                                color="default"
                              />
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination Controls */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
            <Typography variant="body2" color="textSecondary">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} messages
            </Typography>
            <Pagination
              count={Math.max(pagination.totalPages, 1)} // Always show at least 1 page
              page={pagination.page}
              onChange={handlePageChange}
              color="primary"
              showFirstButton
              showLastButton
              size="medium"
              disabled={pagination.totalPages <= 1} // Disable when only one page
            />
          </Box>
        </>
      )}

      {/* Reverse Confirmation Dialog */}
      <Dialog open={reverseDialog.open} onClose={() => setReverseDialog({ open: false, conversation: null, newStatus: null })} maxWidth="sm" fullWidth>
        <DialogTitle>
          Reverse Moderation Decision
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" sx={{ mb: 2 }}>
            Are you sure you want to change this conversation from{' '}
            <strong>{reverseDialog.conversation?.moderationStatus}</strong> to{' '}
            <strong>{reverseDialog.newStatus}</strong>?
          </Typography>

          <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
            Patient: {reverseDialog.conversation?.patientName} (ID: {reverseDialog.conversation?.patientId})
          </Typography>

          <TextField
            fullWidth
            label="Reason for reversal (optional)"
            multiline
            rows={3}
            value={reverseReason}
            onChange={(e) => setReverseReason(e.target.value)}
            placeholder="Explain why you're reversing this decision..."
          />
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setReverseDialog({ open: false, conversation: null, newStatus: null })}
            disabled={reversing}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmReverse}
            variant="contained"
            disabled={reversing}
            startIcon={reversing ? <CircularProgress size={16} /> : <ReverseIcon />}
          >
            {reversing ? 'Reversing...' : 'Confirm Reversal'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Image Modal */}
      <Dialog
        open={imageModal.open}
        onClose={() => setImageModal({ open: false, imageUrl: '', alt: '' })}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Patient Image Attachment
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <img
              src={imageModal.imageUrl}
              alt={imageModal.alt}
              style={{
                maxWidth: '100%',
                maxHeight: '80vh',
                objectFit: 'contain'
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImageModal({ open: false, imageUrl: '', alt: '' })}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Conversation Details Modal */}
      <ConversationDetailsModal
        open={detailsModal.open}
        conversation={detailsModal.conversation}
        onClose={() => setDetailsModal({ open: false, conversation: null })}
        onModerate={handleDetailsModeration}
      />
    </Box>
  );
};

export default ModeratedMessagesTab;
