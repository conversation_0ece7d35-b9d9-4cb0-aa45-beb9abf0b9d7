import React, { useEffect } from "react";
import Grid from "@mui/material/Grid2";
import PatientQueue from "./patient-queue";
import Daily from '@daily-co/daily-js'

export type nextPatient = {
    fullName: string
    waitingDuration: number
    online: boolean
}

const DocPortal: React.FC = () => {

    useEffect(() => {
        const existingFrame = Daily.getCallInstance()
        if (existingFrame) {
            existingFrame.destroy()
        }
    }, [])

    return (
        <>
            <Grid container sx={{ mt: 1 }} justifyContent={'center'}>
                <Grid container size={{ xs: 12, lg: 6 }} direction={'column'} sx={{ mr: { xs: 0, md: 1, lg: 7 } }}>
                    <PatientQueue />
                </Grid>
            </Grid>
        </>
    )
}

export default DocPortal