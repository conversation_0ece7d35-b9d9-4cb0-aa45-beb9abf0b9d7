import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import {
  MessageList,
  useChannelStateContext,
  useChatContext,
  MessageSimple,
  useMessageContext,
  MessageUIComponentProps,
  DefaultStreamChatGenerics
} from 'stream-chat-react';
import chatService from '../../../services/chat.service';

interface CustomMessageListProps {
  userRole?: 'doctor' | 'patient';
}

// Context to share conversation visibility
const ModerationContext = createContext<{
  hiddenMessageIds: Set<string>;
  isConversationVisible: boolean;
  userRole: 'doctor' | 'patient';
}>({
  hiddenMessageIds: new Set(),
  isConversationVisible: true,
  userRole: 'patient'
});

// Custom Message component that respects conversation-level moderation for doctors
const ModerationAwareMessage = (props: MessageUIComponentProps<DefaultStreamChatGenerics>) => {
  const { isConversationVisible, userRole } = useContext(ModerationContext);
  const messageContext = useMessageContext();
  const { message } = messageContext;

  // If no message, return null
  if (!message || !message.user) {
    return <MessageSimple {...props} />;
  }

  // For doctors: Hide ALL patient messages if conversation is not approved
  // This means the entire conversation (all patient messages) is hidden until moderation approval
  if (userRole === 'doctor' &&
      message.user.id?.startsWith('p_') &&
      !isConversationVisible) {
    return null; // Hide all patient messages in unapproved conversations
  }

  // For patients: Always show all messages (they can see their own conversation)
  // For doctors: Show all messages (both patient and doctor) in approved conversations
  // For doctors: Show only doctor messages in unapproved conversations
  return <MessageSimple {...messageContext} {...props} />;
};

/**
 * Custom MessageList component that implements conversation-level moderation
 *
 * Behavior:
 * - For patients: Always show all messages in their conversations
 * - For doctors:
 *   - Hide ALL patient messages in unapproved conversations
 *   - Show ALL messages (patient + doctor) in approved conversations
 *   - Always show doctor messages regardless of approval status
 */
const CustomMessageList: React.FC<CustomMessageListProps> = ({ userRole = 'patient' }) => {
  const { channel } = useChannelStateContext();
  const { client } = useChatContext();
  const [hiddenMessageIds, setHiddenMessageIds] = useState<Set<string>>(new Set());
  // For doctors, start with false (hidden) until we check moderation status
  // For patients, always start with true (visible)
  const [isConversationVisible, setIsConversationVisible] = useState<boolean>(userRole !== 'doctor');
  const lastCheckedRef = useRef<number>(0);

  // Function to check conversation visibility (new approach)
  const checkConversationVisibility = async (channelId: string) => {
    try {
      const response = await chatService.checkConversationVisibility([channelId]);
      if (response.success) {
        const visibility = response.data[channelId] || true; // Default to visible if not found
        setIsConversationVisible(visibility);
      }
    } catch (error) {
      console.error('Error checking conversation visibility:', error);
      // Default to visible on error
      setIsConversationVisible(true);
    }
  };

  // Legacy function to check message visibility (kept for backward compatibility)
  const checkMessageVisibility = async (messageIds: string[]) => {
    if (messageIds.length === 0) return;

    try {
      const visibilityMap = await chatService.checkMessageVisibility(messageIds);

      const newHiddenIds = new Set<string>();
      Object.entries(visibilityMap).forEach(([messageId, isVisible]) => {
        if (!isVisible) {
          newHiddenIds.add(messageId);
        }
      });

      setHiddenMessageIds(newHiddenIds);
    } catch (error) {
      console.error('Error checking message visibility:', error);
    }
  };

  // Effect to check conversation visibility when channel changes (new approach)
  useEffect(() => {
    if (!channel || !client || userRole !== 'doctor') {
      setIsConversationVisible(true); // Always visible for patients
      return;
    }

    // Check immediately for doctors to prevent flash of visible content
    if (channel.id) {
      checkConversationVisibility(channel.id);
    }
  }, [channel?.id, client, userRole]);

  // Listen for moderation events via WebSocket to refresh visibility
  useEffect(() => {
    if (!channel || !client || userRole !== 'doctor') {
      return;
    }

    const handleModerationUpdate = () => {
      // Force a conversation visibility check when moderation status changes
      if (channel.id) {
        checkConversationVisibility(channel.id);
      }
    };

    // Connect to WebSocket for real-time moderation updates
    let ws: WebSocket | null = null;
    let reconnectTimeout: NodeJS.Timeout | null = null;

    const connectWebSocket = () => {
      try {
        ws = new WebSocket(`${import.meta.env.VITE_WSS}`);

       

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);

            if (message.type === 'ping') {
              ws?.send(JSON.stringify({ type: 'pong' }));
              return;
            }

            if (message.type === 'moderationStatusChanged' || message.type === 'conversationModerationChanged') {
              handleModerationUpdate();
            }
          } catch (error) {
            //console.error('Error parsing WebSocket message:', error);
          }
        };

       

        ws.onclose = () => {
         
          // Attempt to reconnect after 3 seconds
          reconnectTimeout = setTimeout(() => {
            if (channel && client && userRole === 'doctor') {
              connectWebSocket();
            }
          }, 3000);
        };
      } catch (error) {
        //console.error('Failed to create WebSocket connection:', error);
      }
    };

    connectWebSocket();

    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (ws) {
        ws.close();
      }
    };
  }, [channel?.id, client, userRole]);

  // Effect to periodically check for newly approved messages
  useEffect(() => {
    if (!channel || !client || userRole !== 'doctor') {
      return;
    }

    const interval = setInterval(() => {
      const now = Date.now();

      // Only check every 10 seconds to avoid spam
      if (now - lastCheckedRef.current < 10000) {
        return;
      }

      lastCheckedRef.current = now;

      const messages = channel.state.messages || [];
      const patientMessageIds = messages
        .filter(msg => msg.user?.id?.startsWith('p_') && msg.id)
        .map(msg => msg.id!)
        .filter(id => id);

      if (patientMessageIds.length > 0) {
        checkMessageVisibility(patientMessageIds);
      }
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [channel, client, userRole]);

  return (
    <ModerationContext.Provider value={{ hiddenMessageIds, isConversationVisible, userRole }}>
      <MessageList Message={ModerationAwareMessage} />
    </ModerationContext.Provider>
  );
};

export default CustomMessageList;
