import express from 'express';
import {
  triggerAvailabilityReport,
  getAvailabilityData,
  getSystemStatus,
  sendTestReport,
  debugReportData,
  sendTestReportWithDate
} from '../controllers/availability';

const router = express.Router();

// POST /api/availability/trigger-report - Trigger availability report (called by cron job)
router.post('/trigger-report', triggerAvailabilityReport);

// GET /api/availability/data?startDate=YYYY-MM-DD&endDate=YYYY-MM-DD - Get availability data for date range
router.get('/data', getAvailabilityData);

// GET /api/availability/status - Get availability reporting system status
router.get('/status', getSystemStatus);

// POST /api/availability/test-report - Send test report
router.post('/test-report', sendTestReport);

// GET /api/availability/debug - Debug report data
router.get('/debug', debugReportData);

// POST /api/availability/test-report-with-date?startDate=YYYY-MM-DD - Send test report with custom start date
router.post('/test-report-with-date', sendTestReportWithDate);

export default router;
