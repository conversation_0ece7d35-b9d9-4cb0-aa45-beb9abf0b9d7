import { useState, useEffect, useCallback } from 'react';
import { ApiClient } from '../services';
import { useAuth } from './auth-provider';

interface RequestCounts {
  all: number;
  requests: number;
  unread: number;
}

export const useHasRequests = () => {
  const { doctor } = useAuth();
  const [hasRequests, setHasRequests] = useState<boolean>(true); // Start with true to show initially
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const checkForRequests = useCallback(async () => {
    // Don't check for requests if doctor is not authenticated yet
    if (!doctor?.accessID) {
      setLoading(true);
      setHasRequests(true); // Show panel while waiting for auth
      return;
    }

    try {
      setLoading(true);
      // First check for pending/submitted requests (faster check)
      const pendingResponse = await ApiClient.getDoctorPendingRequests(false);
      const pendingCounts: RequestCounts = pendingResponse.data.counts || { all: 0, requests: 0, unread: 0 };

      // If we have pending requests, show immediately
      if (pendingCounts.requests > 0) {
        setHasRequests(true);
        setError(null);
        setLoading(false);
        return;
      }

      // If no pending requests, check for all requests (including past)
      const allResponse = await ApiClient.getDoctorPendingRequests(true);
      const allCounts: RequestCounts = allResponse.data.counts || { all: 0, requests: 0, unread: 0 };

      // Show RequestsWindow if there are any requests at all (pending or past)
      setHasRequests(allCounts.all > 0);
      setError(null);
    } catch (err: any) {
      console.error('Error checking for requests:', err);

      // If it's an authentication error, don't show error message
      // The auth system will handle re-authentication
      if (err?.response?.status === 401) {
        setError(null);
        setHasRequests(true); // Show panel while auth resolves
      } else {
        setError('Failed to check requests');
        setHasRequests(true); // Default to showing requests window on error to be safe
      }
    } finally {
      setLoading(false);
    }
  }, [doctor?.accessID]);

  useEffect(() => {
    checkForRequests();

    // Set up polling to check for new requests every 30 seconds
    // Only start polling if doctor is authenticated
    if (doctor?.accessID) {
      const interval = setInterval(checkForRequests, 30000);
      return () => clearInterval(interval);
    }
  }, [checkForRequests, doctor?.accessID]);

  return { hasRequests, loading, error, refetch: checkForRequests };
};
