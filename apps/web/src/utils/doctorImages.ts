/**
 * Utility to handle doctor image mapping
 */

// Default doctor image to use if no specific image is found
const DEFAULT_DOCTOR_IMAGE = '/images/default_user.png';

// Map of doctor names to their image filenames
// This can be expanded as more doctor images are added
const DOCTOR_IMAGE_MAP: Record<string, string> = {
  // Add specific doctor image mappings here as they become available
  // For example:
  // 'Dr. John': '/images/dr_john.jpg',
  // 'Kate': '/images/kate.jpg',
  
  // Dr. <PERSON> has a specific image
  'Dr. <PERSON>': '/images/dr_hussain.jpg',
  'Dr H. Anjum': '/images/dr_hussain.jpg',
  'Dr. Anjum': '/images/dr_hussain.jpg',
  
  // All other doctors will use the default image through the fallback mechanism
};

/**
 * Convert a doctor name to a standardized filename format
 * 
 * @param name The doctor's name
 * @returns Standardized filename (lowercase, spaces replaced with underscores)
 */
const nameToFilename = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/[^a-z0-9_\.]/g, '') // Remove special characters except underscores and periods
    .replace(/^dr_?/, 'dr_'); // Ensure "dr" is followed by underscore
};

/**
 * Get the image URL for a doctor based on their name
 * 
 * @param doctorName The name of the doctor
 * @returns The URL of the doctor's image
 */
export const getDoctorImageUrl = (doctorName: string | undefined): string => {
  if (!doctorName) {
    return DEFAULT_DOCTOR_IMAGE;
  }
  
  // Try to find an exact match in the map
  if (DOCTOR_IMAGE_MAP[doctorName]) {
    return DOCTOR_IMAGE_MAP[doctorName];
  }
  
  // Try to find a case-insensitive match in the map
  const lowerCaseName = doctorName.toLowerCase();
  const matchingKey = Object.keys(DOCTOR_IMAGE_MAP).find(
    key => key.toLowerCase() === lowerCaseName
  );
  
  if (matchingKey) {
    return DOCTOR_IMAGE_MAP[matchingKey];
  }
  
  // Try to find an image based on naming convention
  const filename = nameToFilename(doctorName);
  
  // Since we can't check if files exist on the client side before trying to load them,
  // we'll return a data URL that tries multiple formats using the picture element approach
  // The component using this function should handle the onError event to fall back to the default image
  
  // We'll return the first format, and the component's onError handler will handle fallback
  // The order of preference: jpg (most common), webp (efficient), png (good quality), jpeg (alternative)
  return `/images/${filename}.jpg`;
}; 