import { Button, Typography } from "@mui/material"
import Grid from "@mui/material/Grid2";

const ConsultationTime: React.FC = () => {
    return (
        <>
            <Grid sx={{ border: '1px solid green', borderRadius: 2, p: 1, mt: {xs: 0, md: 5, lg: 0} }} container direction={'column'} justifyContent={'center'} alignItems={'center'}>
                <Grid>
                    <Typography sx={{ fontSize: '0.90rem', fontWeight: 'bold' }}>
                        Consultation Duration
                    </Typography>
                </Grid>
                <Grid>
                    <Button variant="contained"
                        sx={{
                            backgroundColor: 'green', mt: 1, width: '25px',
                            height: '25px', textTransform: 'none',
                            fontSize: '12px'
                        }}>
                        Live
                    </Button>
                </Grid>
                <Grid>
                    <Typography sx={{ fontSize: '2.7rem' }}>
                        {'00:00'}
                    </Typography>
                </Grid>
                <Grid sx={{ mt: 3 }}>
                    <Typography sx={{ fontSize: '0.75rem' }}>
                        Consulting: {"Patient Name"}
                    </Typography>
                </Grid>
            </Grid>
        </>
    )
}

export default ConsultationTime