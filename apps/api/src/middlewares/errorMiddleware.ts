import config from '../config';
import { logger } from '../config/logger';
import { ApiError, ErrorResponse } from '../utils/ApiError';
import { ErrorRequestHandler } from 'express';
import httpStatus from 'http-status';
import { inspect } from 'util';

export const errorConverter: ErrorRequestHandler = (err, _req, _res, next) => {
  let error = err;
  if (!(error instanceof ApiError)) {
    const statusCode = error.statusCode || error.status ? httpStatus.BAD_REQUEST : httpStatus.INTERNAL_SERVER_ERROR;
    const message = error.message || httpStatus[statusCode];
    error = new ApiError(statusCode, message, error.data, false, error.stack);
  }
  next(error);
};

export const errorHandler: ErrorRequestHandler = (err, _req, res) => {
  let { statusCode, message } = err;
  const { errors } = err;

  if (config.env === 'production' && !err.isOperational) {
    statusCode = httpStatus.INTERNAL_SERVER_ERROR;
    message = httpStatus[httpStatus.INTERNAL_SERVER_ERROR];
  }

  const response: ErrorResponse = {
    httpCode: statusCode ? parseInt(statusCode) : 500,
    message,
    errors,
    ...(config.env === 'development' && { stack: err.stack }),
  };

  if (config.env === 'development') {
    logger.error('Error Handler: ' + JSON.stringify(err, null, 2));
  }

  try {
    res.status(response.httpCode).send(response);
  } catch (error) {
    res.status(httpStatus.INTERNAL_SERVER_ERROR).send({
      httpCode: response.httpCode,
      message,
      error,
      stack: inspect(response),
    } as ErrorResponse);
  }
};
