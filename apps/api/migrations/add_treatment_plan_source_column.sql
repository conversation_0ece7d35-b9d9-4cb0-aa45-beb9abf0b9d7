-- Migration: Add source column to TreatmentPlan table
-- Date: 2025-07-16
-- Purpose: Distinguish between consultation-based and request-based treatment plans for better reporting

-- Add source column to TreatmentPlan table
ALTER TABLE TreatmentPlan 
ADD COLUMN "source" TEXT NOT NULL DEFAULT 'consultation';

-- Add comment to document the purpose of this column
COMMENT ON COLUMN TreatmentPlan."source" IS 'Source of treatment plan creation: consultation (normal consultations), messenger (chat, requests, approvals via messaging)';

-- Create index for better query performance when filtering by source
CREATE INDEX IF NOT EXISTS idx_treatmentplan_source 
ON TreatmentPlan("source");

-- Update existing records based on current patterns:
-- 1. Records with consultationId are from consultations
-- 2. Records with type='chat-treatmentplan' are from chat
-- 3. Records without consultationId and type='treatmentplan' need manual review
--    (these could be either old consultation records or request-based records)

-- Update chat-based treatment plans
UPDATE TreatmentPlan
SET "source" = 'messenger'
WHERE "type" = 'chat-treatmentplan';

-- For records without consultationId and type='treatmentplan', we'll keep them as 'consultation' 
-- since we can't definitively determine if they're from requests without additional context.
-- Future request-based plans will be properly tagged with the new source values.

-- Add constraint to ensure only valid source values
ALTER TABLE TreatmentPlan
ADD CONSTRAINT chk_treatmentplan_source
CHECK ("source" IN ('consultation', 'messenger'));
