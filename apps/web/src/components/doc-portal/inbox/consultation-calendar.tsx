import { Divider, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';

import moment from 'moment';
import { ApiClient } from "../../../services";
import { convertTimeStampToReadableFormat } from "../../../utils";
import { useHistory } from "../../../hooks/history-provider";

const ConsultationCalendar: React.FC = () => {
    const { calendarValue, setCalendarValue, selectedDateCalendar, setSelectedDateCalendar, selectedConsultation, setSelectedConsultation, consultations, setConsultations } = useHistory()

    const handleDateChange = async (date: moment.Moment) => {
        setCalendarValue(date)
        const formattedDate = moment(date).format('YYYY-MM-DD')
        const result = await ApiClient.fetchConsultationByDate(formattedDate)
        const sortedData = result.sort((a, b) => new Date(a.consultationDate).getTime() - new Date(b.consultationDate).getTime());
        setSelectedDateCalendar(formattedDate)
        setConsultations(sortedData)
    }


    return (
        <>
            <Grid container sx={{ width: '100%' }}>
                <Grid size={{ lg: 3 }}>
                    <Grid sx={{ width: '100%' }}>
                        <LocalizationProvider dateAdapter={AdapterMoment}>
                            <DateCalendar
                                views={['year', 'month', 'day']}
                                onChange={handleDateChange}
                                value={calendarValue}
                                sx={{
                                    color: 'green',
                                    m: 0,
                                    "& .MuiCalendarPicker-root": {
                                        width: "500px",
                                        height: "500px",
                                    },

                                    "& .MuiPickersDay-root.Mui-selected": {
                                        backgroundColor: "green",
                                    },
                                    "& .MuiPickersDay-root.Mui-selected:hover": {
                                        backgroundColor: "#006400",
                                    },
                                    "& .MuiPickersDay-root:hover": {
                                        backgroundColor: "#d9f5d9",
                                    },
                                }} />
                        </LocalizationProvider>
                    </Grid>
                </Grid>
                <Grid size={{ lg: 9 }} sx={{ pl: 2 }}>

                    {consultations && consultations?.length < 1 ?
                        <>
                            <Grid container sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                                <Typography sx={{ fontSize: '14px' }}>
                                    No Consultation for the selected date: {selectedDateCalendar}
                                </Typography>
                            </Grid>
                        </> : <></>}
                        {consultations && consultations?.length > 0 ?
                        <>
                            <Grid container sx={{ width: '100%', pr: 2, mb: 2 }} justifyContent={'end'} alignItems={'center'}>
                                <Typography sx={{ fontSize: '14px', fontWeight: 'bold' }}>
                                    Total consultations: {consultations?.length}
                                </Typography>
                                <Typography sx={{ fontSize: '14px', fontWeight: 'bold', ml: 2 }}>
                                    Completed: {consultations?.filter((c) => c.completed).length}
                                </Typography>
                            </Grid>
                        </> : <></>}
                    <Grid sx={{ overflow: 'auto', maxHeight: 'calc(75vh - 80px)' }} >
                        
                        {consultations && consultations.map((c, i) => {
                            return (
                                <div key={`${i}`} onClick={async () => {
                                    setSelectedConsultation(c)

                                }} style={{
                                    cursor: 'pointer',
                                    width: '100%',
                                }}>
                                    <Grid container
                                        sx={{
                                            mr: '4px',
                                            ml: 1,
                                            p: 0.5,
                                            backgroundColor: c?.id === selectedConsultation?.id ? '#e4fade' : 'white',
                                            "&:hover": {
                                                // boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.5)" ,
                                                backgroundColor: '#f0f0f0'
                                            }

                                        }}
                                        direction={"column"}
                                        key={`${i}`}
                                    >
                                        <Grid container sx={{ width: '100%' }}>
                                            <Grid sx={{ width: '100%' }} container direction={'column'}>
                                                <Grid container alignItems={'center'}>
                                                    <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                                        {`${c?.fullName || "No Name"}`}
                                                    </Typography>
                                                </Grid>
                                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem' }}>
                                                    Email: {c.email}
                                                </Typography>
                                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem' }}>
                                                    Consult Date: {convertTimeStampToReadableFormat(c?.consultationStart ? c?.consultationStart : c?.consultationDate)}
                                                </Typography>
                                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem' }}>
                                                    Completed: {c.completed ? 'Yes' : 'No'}
                                                </Typography>
                                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem' }}>
                                                    Returning Patient: {c.returningPatient ? 'Yes' : 'No'}
                                                </Typography>
                                                <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem' }}>
                                                    State: {c.state}
                                                </Typography>

                                            </Grid>
                                        </Grid>
                                    </Grid>
                                    <Divider style={{ marginBottom: "8px", marginLeft: '12px' }} />
                                </div>
                            )
                        })}
                    </Grid>
                </Grid>
            </Grid>
        </>
    )
}

export default ConsultationCalendar
