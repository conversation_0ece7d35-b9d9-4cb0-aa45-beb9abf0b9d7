import { Button, <PERSON>alog, DialogContent, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import moonStar from '../../assets/Moon-star-tiny.png'

import { useNavigate } from "@tanstack/react-location";
import { useAuth } from "../../hooks/auth-provider";
import { usePatient } from "../../hooks/patient-provider";
import { useState } from "react";
import { ApiClient } from "../../services";
const HomePage: React.FC = () => {
    const navigate = useNavigate()
    const { doctor } = useAuth()
    const { timerKey, setTimerKey } = usePatient()
    const [displayMessage, setDisplayMessage] = useState(false)
    const [isStartingShift, setIsStartingShift] = useState(false)

    // add a dialog for ramadan mubarak wish

    // useEffect(() => {
    //     const status = localStorage.getItem('ramadanMessage')
    //     if (!status && user) {
    //         setTimeout(() => {
    //             setDisplayMessage(true)
    //             localStorage.setItem('ramadanMessage', 'done')
    //         }, 1500);
    //     }

    // }, [])
    return (
        <>
            <Dialog open={displayMessage} fullWidth={true} maxWidth={'xs'} onClose={() => setDisplayMessage(false)}>
                <DialogContent>
                    <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold' }}>
                            Ramadan Kareem
                        </Typography>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }}>
                            رمضان كريم
                        </Typography>
                        {/* <CheckCircleIcon sx={{ width: '100px', height: '100px', color: 'green', mb: 2 }} /> */}
                        <img src={moonStar} alt="islam symbole image for Ramadan" style={{ width: '60%' }} />
                        <Typography sx={{ fontSize: '14px' }} align="center">
                            Wishing you a blessed and peaceful Ramadan.
                        </Typography>
                        <Grid sx={{ mt: 2 }}>
                            <Button variant='contained' onClick={() => setDisplayMessage(false)}>
                                Allahu Akram
                            </Button>
                        </Grid>
                    </Grid>
                </DialogContent>
            </Dialog>


            <Grid container sx={{ width: '100%', height: '50vh' }} alignItems={'center'} justifyContent={'center'} direction='column' >
                <Grid>
                    <Typography sx={{ fontSize: '36px', fontWeight: 'bold', color: 'green', mb: 4 }}>
                        Welcome {doctor?.username}
                    </Typography>
                </Grid>
                <Grid>
                    <Typography align='center'>
                        Click the <span style={{ fontWeight: 'bold' }}>Start Shift button</span> below to begin your shift and start sending invitations to your patients.
                    </Typography>
                </Grid>
                <Grid>
                    <Typography sx={{ fontSize: '14px', mb: 5, mt: 1 }} align='center'>
                        After starting your shift, you'll be taken to the patient queue where you can view online patients.
                    </Typography>
                </Grid>
                <Grid sx={{ width: '100%' }} container justifyContent={'center'} spacing={2}>
                    {doctor?.role === 'doctor' && (
                        <Button
                            onClick={async () => {
                                if (doctor && !isStartingShift) {
                                    setIsStartingShift(true);
                                    try {
                                        const result = await ApiClient.startTimer(doctor.accessID);
                                        setTimerKey((prev) => ({
                                            ...prev,
                                            [doctor.accessID]: result.timerKey
                                        }));
                                        navigate({ to: '/online-patients' });
                                    } catch (error) {
                                        console.error('Error starting shift:', error);
                                        setIsStartingShift(false);
                                    }
                                }
                            }}
                            variant={'contained'}
                            disabled={isStartingShift || (doctor?.accessID && timerKey[doctor.accessID] ? true : false)}
                            sx={{
                                backgroundColor: 'green',
                                width: { lg: '40%', xs: '100%' },
                                height: '60px'
                            }}
                        >
                            {isStartingShift ? 'Starting Shift...' : (doctor?.accessID && timerKey[doctor.accessID] ? 'Shift Started' : 'Start Shift')}
                        </Button>
                    )}
                    {doctor?.role !== 'doctor' && (
                        <Button
                            onClick={() => navigate({ to: '/online-patients' })}
                            variant={'contained'}
                            sx={{
                                backgroundColor: 'green',
                                width: { lg: '40%', xs: '100%' },
                                height: '60px'
                            }}
                        >
                            View Online Patients
                        </Button>
                    )}

                    <Button
                        onClick={() => navigate({ to: '/patient/history' })}
                        variant={'outlined'}
                        sx={{
                            borderColor: 'green',
                            color: 'green',
                            width: { lg: '40%', xs: '100%' }, height: '60px'
                        }}
                    >
                        Patient History
                    </Button>
                </Grid>
            </Grid>
        </>
    )
}

export default HomePage