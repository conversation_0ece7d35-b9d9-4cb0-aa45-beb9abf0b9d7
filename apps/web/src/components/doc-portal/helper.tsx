import { MenuItem } from "@mui/material"

export const generateMaxDosePerDay = () => {
    const options = []
    for (let i = 0.5; i <= 3.0; i += 0.5) {
        options.push(
            <MenuItem value={`${i.toFixed(1)}`} key={`${i.toFixed(1)}`} sx={{ fontSize: '12px' }}>{i.toFixed(1)}g</MenuItem>
        )
    }
    return options
}

export const generateTotalQuantity = () => {
    const options = []
    for (let i = 14; i <= 84; i += 14) {
        options.push(
            <MenuItem value={`${i}`} key={`${i}`} sx={{ fontSize: '12px' }}>{i.toFixed(0)}g</MenuItem>
        )
    }

    return options
}

export const generateRepeatOptionsExistingPatient = () => {
    const options = []
    for (let i = 3; i <= 6; i++) {
        options.push(
            <MenuItem value={`${i}`} key={`${i}`} sx={{ fontSize: '12px' }}>{i}</MenuItem>
        )
    }
    return options
}

export const generateRepeatOptionsReturningPatient = () => {
    const options = []
    for (let i = 3; i <= 6; i++) {
        options.push(
            <MenuItem value={`${i}`} key={`${i}`} sx={{ fontSize: '12px' }}>{i}</MenuItem>
        )
    }
    return options
}
