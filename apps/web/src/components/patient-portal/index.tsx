import React from "react";
import { Box, Toolbar } from "@mui/material";
import NavigationBar from "./navigation-bar";
import WelcomeRoom from "./welcome-room";
import Grid from "@mui/material/Grid2";
import SalesIQ from "./support-window";
// import AppointmentTimer from "./appointment-timer";

const PatientPortal: React.FC = () => {
    return (
        <>
            <SalesIQ />
            <Box sx={{ display: 'flex' }}>
                <NavigationBar />
                <Box
                    component="main"
                    sx={{
                        p: 2,
                        width: '100%',
                    }}
                >
                    <Toolbar />
                    <Grid container>
                        <Grid size={{ xs: 12, lg: 6 }}>
                            <WelcomeRoom />
                        </Grid>
                        <Grid size={{ xs: 12, lg: 6 }}>
                            {/* <AppointmentTimer /> */}
                        </Grid>
                    </Grid>
                </Box>
            </Box>
        </>
    )
}

export default PatientPortal
