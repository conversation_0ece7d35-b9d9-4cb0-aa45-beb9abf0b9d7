import { <PERSON><PERSON><PERSON>, But<PERSON>, useMediaQuery, Box, CircularProgress, Card, CardContent, Container, AppB<PERSON>, Toolbar } from "@mui/material";
import Grid from "@mui/material/Grid2";
import timerImage from '../../assets/waitingRoomIcon.png'
import { useTheme } from "@mui/material/styles"
import { useEffect, useState, useRef } from "react";
import { useSearch, MakeGenerics, useNavigate } from "@tanstack/react-location";
import { useMeeting } from "../../hooks/meeting-provider";
import { ApiClient } from "../../services";
import SalesIQ from "./support-window";
import { useSnackbar } from "notistack";
import LoadingScreen from "../../utils/loading-screen";
import { UserActions } from "../../utils";
import MicIcon from '@mui/icons-material/Mic';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import RadioButtonCheckedIcon from '@mui/icons-material/RadioButtonChecked';

type UrlProps = MakeGenerics<{
    Search: {
        token: string
    }
}>

// Audio context type definition - fixed return type to match the AudioContext interface
// interface AudioContextWithClose extends AudioContext {
//     close: () => Promise<void>;
// }

const WaitingRoom: React.FC = () => {
    const navigate = useNavigate()
    const theme = useTheme();
    const { admittedPatient, patientQueue } = useMeeting()
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
    const { token } = useSearch<UrlProps>();
    const [showButton, setShowButton] = useState(false)
    const [isEligible, setIsEligible] = useState(false)
    const [patientWaiting, setPatientWaiting] = useState<number>(0)
    const { enqueueSnackbar } = useSnackbar()
    const [isLoading, setIsLoading] = useState(true)
    const [noShow, setNoShow] = useState(false)
    const [consultationDuration, setConsultationDuration] = useState<number>(6) // Default 6 minutes

    // Microphone testing states
    const [microphoneReady, setMicrophoneReady] = useState(false);
    const [isTesting, setIsTesting] = useState(false);
    const [hasTestedMic, setHasTestedMic] = useState(false);

    // Advanced audio testing states
    const [isRecording, setIsRecording] = useState(false);
    const [isPlaying, setIsPlaying] = useState(false);
    const [_audioBlob, setAudioBlob] = useState<Blob | null>(null);
    const [audioURL, setAudioURL] = useState<string | null>(null);
    const [audioLevel, setAudioLevel] = useState<number>(0);
    const [showAdvancedTest, setShowAdvancedTest] = useState(false);

    // Toggling states
    const [speakTestExpanded, _setSpeakTestExpanded] = useState(false);

    // Refs
    const mediaRecorderRef = useRef<MediaRecorder | null>(null);
    const audioStreamRef = useRef<MediaStream | null>(null);
    const audioContextRef = useRef<AudioContext | null>(null);
    const audioAnalyserRef = useRef<AnalyserNode | null>(null);
    const animationFrameRef = useRef<number | null>(null);
    const audioElementRef = useRef<HTMLAudioElement | null>(null);

    // New states for help dialog


    useEffect(() => {
        if (token && admittedPatient && String(admittedPatient.patientID) === String(token)) {
            ApiClient.userActions('Patient', token, UserActions.REDIRECTED, 'Patient was successfully redirected to video URL screen after waiting room.')
                .catch((e) => {
                    enqueueSnackbar(e.message, {
                        variant: 'error'
                    })
                })
            navigate({ to: '/consultation', search: { token: token } })
        }

    }, [admittedPatient])

    useEffect(() => {
        const init = async () => {
            let timerId: NodeJS.Timeout
            if (token) {
                const noShows = await ApiClient.checkNoShow(token)
                if (noShows && noShows.length <= 0) {
                    const eligible = await ApiClient.checkQueueEligibility(token)
                    if (eligible.length > 0) {
                        setIsEligible(true)

                        // Fetch doctor's consultation duration
                        try {
                            const doctorInfo = await ApiClient.getPatientDoctorInfo(token)
                            setConsultationDuration(doctorInfo.consultationDurationMinutes)
                        } catch (e) {
                            console.error('Failed to fetch doctor info:', e)
                            // Keep default duration if fetch fails
                        }

                        await ApiClient.updateMeetingStatus(token, true)
                            .then(() => {
                                setIsLoading(false)
                            })
                            .catch((e) => {
                                setIsLoading(false)
                                enqueueSnackbar(e.message, {
                                    variant: 'error'
                                })
                            })

                        ApiClient.postPatientWaitingQueue(token)
                            .then(() => {
                                setIsLoading(false)
                            })
                            .catch((e) => {
                                setIsLoading(false)
                                enqueueSnackbar(e.message, {
                                    variant: 'error'
                                })
                            })

                        ApiClient.userActions('Patient', token, UserActions.ONLINE, 'Patient came online')
                            .catch((e) => {
                                enqueueSnackbar(e.message, {
                                    variant: 'error'
                                })
                            })

                        setIsLoading(false)
                    }
                    else {
                        setIsLoading(false)
                    }
                }
                else if (noShows) {
                    setNoShow(true)
                }

                setIsLoading(false)
            }

            timerId = setInterval(async () => {
                try {
                    if (!showButton) {
                        const result = await ApiClient.fetchLatestAdmission()
                        const userAdmitted = result?.some(v => String(v.patientID) === String(token))
                        if (userAdmitted) {
                            if (token) {
                                clearInterval(timerId)
                                navigate({ to: '/consultation', search: { token: token } })
                                setShowButton(true)
                            }
                        }
                    }
                    else {
                        clearInterval(timerId)
                    }
                }
                catch (e) {
                    const error = e as Error
                    enqueueSnackbar(error.message, {
                        variant: 'error'
                    })
                }
            }, 5000) // TODO: ENV
        }
        init()
    }, [token])

    useEffect(() => {
        const init = async () => {
            const onlineQueuePatient = await ApiClient.fetchOnlineQueue()
            const sortedData = onlineQueuePatient.sort((a, b) => {
                const timeA = new Date(a.updatedAt as string | number | Date).getTime();
                const timeB = new Date(b.updatedAt as string | number | Date).getTime();
                return timeA - timeB;
            });

            const index = sortedData.findIndex(p => String(p.patientID) === String(token))
            setPatientWaiting(index + 1)
        }
        init()

    }, [patientQueue])

    // useEffect(() => {
    //     const handleBeforeUnload = async (event: BeforeUnloadEvent) => {
    //         if (token) {
    //             const eligible = await ApiClient.checkQueueEligibility(token)
    //             if (eligible.length > 0) {
    //                 ApiClient.leftPatientWaitingQueue(token)
    //                     .catch((e) => {
    //                         enqueueSnackbar(e.message, {
    //                             variant: 'error'
    //                         })
    //                     })

    //                 ApiClient.updateMeetingStatus(token, false)
    //                     .catch((e) => {
    //                         enqueueSnackbar(e.message, {
    //                             variant: 'error'
    //                         })
    //                     })
    //             }
    //         }
    //         event.preventDefault();
    //         event.returnValue = '';
    //     };

    //     window.addEventListener('beforeunload', handleBeforeUnload);

    //     return () => {
    //         window.removeEventListener('beforeunload', handleBeforeUnload);
    //     };
    // }, []);

    useEffect(() => {
        const handleVisibilityChange = async () => {
            if (document.visibilityState === 'hidden') {
                if (token) {
                    const noShows = await ApiClient.checkNoShow(token)
                    if (noShows && noShows.length <= 0) {
                        const eligible = await ApiClient.checkQueueEligibility(token)
                        if (eligible.length > 0) {
                            ApiClient.leftPatientWaitingQueue(token)
                                .catch((e) => {
                                    enqueueSnackbar(e.message, {
                                        variant: 'error'
                                    })
                                })

                            ApiClient.updateMeetingStatus(token, false)
                                .catch((e) => {
                                    enqueueSnackbar(e.message, {
                                        variant: 'error'
                                    })
                                })

                            ApiClient.userActions('Patient', token, UserActions.AWAY, 'Patient Left Waiting Room')
                                .catch((e) => {
                                    enqueueSnackbar(e.message, {
                                        variant: 'error'
                                    })
                                })
                        }
                    }
                    else {
                        setNoShow(true)
                    }
                }
                // Trigger any save logic or API call here
            } else if (document.visibilityState === 'visible') {
                if (token) {
                    const noShows = await ApiClient.checkNoShow(token)
                    if (noShows && noShows.length <= 0) {
                        const eligible = await ApiClient.checkQueueEligibility(token)
                        if (eligible.length > 0) {
                            setIsEligible(true)
                            await ApiClient.updateMeetingStatus(token, true)
                                .catch((e) => {
                                    enqueueSnackbar(e.message, {
                                        variant: 'error'
                                    })
                                })

                            ApiClient.postPatientWaitingQueue(token)
                                .catch((e) => {
                                    enqueueSnackbar(e.message, {
                                        variant: 'error'
                                    })
                                })
                            ApiClient.userActions('Patient', token, UserActions.ONLINE, 'Patient came online again')
                                .catch((e) => {
                                    enqueueSnackbar(e.message, {
                                        variant: 'error'
                                    })
                                })
                        }
                    } else {
                        setNoShow(true)
                    }
                }
            }
        };

        const handleBeforeUnload = async () => {
            if (token) {
                navigator.sendBeacon(`${import.meta.env.VITE_EXPRESS_API_URL}/doc/v1.0/left/queue`, JSON.stringify({ token }));
            }
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, []);




    // Clean up audio resources
    const cleanupAudio = () => {
        stopAllAudio();

        if (audioContextRef.current) {
            if (audioContextRef.current.state !== 'closed') {
                audioContextRef.current.close().catch(err => {
                    console.error("Error closing audio context:", err);
                });
            }
            audioContextRef.current = null;
        }

        if (audioAnalyserRef.current) {
            audioAnalyserRef.current = null;
        }

        if (mediaRecorderRef.current) {
            mediaRecorderRef.current = null;
        }
    };

    // Clean up on component unmount
    useEffect(() => {
        return () => {
            stopAllAudio();
            if (audioContextRef.current) {
                audioContextRef.current.close().catch(err => {
                    console.error("Error closing audio context:", err);
                });
            }
            if (audioURL) {
                URL.revokeObjectURL(audioURL);
            }
        };
    }, []);

    // Handle basic microphone test
    const handleTestDevices = async () => {
        setIsTesting(true);
        try {
            // Request microphone access
            await navigator.mediaDevices.getUserMedia({ audio: true })
                .then(() => {
                    setMicrophoneReady(true);
                    setHasTestedMic(true);
                    // Show advanced test after basic test passes
                    setShowAdvancedTest(true);
                })
                .catch((error) => {
                    console.error("Microphone access denied:", error);
                    setMicrophoneReady(false);
                    setHasTestedMic(true);
                    setShowAdvancedTest(false);
                });
        } catch (error) {
            console.error("Error testing devices:", error);
            setMicrophoneReady(false);
            setHasTestedMic(true);
            setShowAdvancedTest(false);
        } finally {
            setIsTesting(false);
        }
    };

    // Start recording and analyzing audio
    const startRecording = async () => {
        try {
            // Clean up any existing audio resources
            cleanupAudio();

            // Get microphone stream
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            audioStreamRef.current = stream;

            // Set up audio context and analyzer
            const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
            audioContextRef.current = audioContext;

            const audioSource = audioContext.createMediaStreamSource(stream);
            const analyser = audioContext.createAnalyser();
            audioAnalyserRef.current = analyser;

            analyser.fftSize = 256;
            audioSource.connect(analyser);

            // Create media recorder
            const mediaRecorder = new MediaRecorder(stream);
            mediaRecorderRef.current = mediaRecorder;

            const chunks: BlobPart[] = [];
            mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    chunks.push(e.data);
                }
            };

            mediaRecorder.onstop = () => {
                const blob = new Blob(chunks, { type: 'audio/webm' });
                setAudioBlob(blob);

                // Release old URL if exists
                if (audioURL) {
                    URL.revokeObjectURL(audioURL);
                }

                const url = URL.createObjectURL(blob);
                setAudioURL(url);
            };

            // Start animation loop for visualizing audio immediately
            const updateAudioLevel = () => {
                if (audioAnalyserRef.current) {
                    const bufferLength = audioAnalyserRef.current.frequencyBinCount;
                    const dataArray = new Uint8Array(bufferLength);

                    audioAnalyserRef.current.getByteFrequencyData(dataArray);

                    // Calculate average volume level (0-100)
                    let sum = 0;
                    for (let i = 0; i < dataArray.length; i++) {
                        sum += dataArray[i];
                    }
                    const volumeAverage = sum / dataArray.length;

                    // Scale to 0-100 with higher multiplier for better sensitivity
                    const scaledAverage = Math.min(100, Math.max(0, volumeAverage * 2.5));
                    setAudioLevel(scaledAverage);
                }

                // Continue animation loop regardless of isRecording state
                // This ensures the meter keeps updating as long as we have access to the microphone
                animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
            };

            // Start visualization
            animationFrameRef.current = requestAnimationFrame(updateAudioLevel);

            // Start recording
            mediaRecorder.start();
            setIsRecording(true);

            // Auto stop recording after exactly 3 seconds
            setTimeout(() => {
                if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
                    // Only stop the recording, keep the visualization running
                    if (mediaRecorderRef.current) {
                        mediaRecorderRef.current.stop();
                    }

                    // Update recording state but don't clean up audio resources yet
                    setIsRecording(false);
                }
            }, 3000);

        } catch (error) {
            console.error("Error starting recording:", error);
            setIsRecording(false);
            cleanupAudio();
        }
    };

    // Stop recording
    const stopRecording = () => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
            mediaRecorderRef.current.stop();
        }

        setIsRecording(false);
    };

    // Completely clean up all audio resources (used when navigating away)
    const stopAllAudio = () => {
        // Stop recording if active
        if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
            mediaRecorderRef.current.stop();
        }

        // Stop audio level updates
        if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
            animationFrameRef.current = null;
        }

        // Stop microphone stream
        if (audioStreamRef.current) {
            audioStreamRef.current.getTracks().forEach(track => track.stop());
            audioStreamRef.current = null;
        }

        setIsRecording(false);
        setAudioLevel(0);
    };

    // Play recorded audio
    const playRecording = () => {
        if (audioURL) {
            const audioElement = new Audio(audioURL);
            audioElementRef.current = audioElement;

            audioElement.onended = () => {
                setIsPlaying(false);
                // Reset audio level when playback ends
                setAudioLevel(0);
            };

            audioElement.play();
            setIsPlaying(true);
        }
    };

    // Stop playback
    const stopPlayback = () => {
        if (audioElementRef.current) {
            audioElementRef.current.pause();
            audioElementRef.current.currentTime = 0;
            audioElementRef.current = null;
        }
        setIsPlaying(false);
        // Reset audio level when stopping playback
        setAudioLevel(0);
    };



    return (
        <>
            {isLoading && <LoadingScreen />}
            <SalesIQ />

            {/* Keep screen awake video */}
            <video
                id="keep-awake-video"
                autoPlay
                muted
                loop
                playsInline
                style={{ display: 'none' }}
            >
                <source src="/videos/tiny-silent-video.mp4" type="video/mp4" />
            </video>

            {/* ZenithClinics Header */}
            <AppBar position="static" elevation={0} sx={{ backgroundColor: 'green', mb: 0 }}>
                <Toolbar sx={{ justifyContent: 'center', py: 2 }}>
                    <Typography variant="h5" sx={{ color: 'white', fontWeight: 'bold' }}>
                        ZenithClinics
                    </Typography>
                </Toolbar>
            </AppBar>

            {/* Main Content Container */}
            <Container maxWidth="sm" sx={{ py: 4, minHeight: '80vh', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>

                {/* Waiting Room Title */}
                <Typography variant="h3" sx={{ color: 'green', fontWeight: 'bold', mb: 3, textAlign: 'center' }}>
                    Waiting Room
                </Typography>

                {/* Queue Status Pill */}
                <Box sx={{
                    backgroundColor: 'green',
                    color: 'white',
                    px: 10,
                    py: 1,
                    borderRadius: '5px',
                    mb: 4,
                    fontWeight: 'bold'
                }}>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                        {noShow ? 'You have missed too many consultations. Contact Admin for admission' :
                            patientWaiting <= 0 || !isEligible ? "Wait for a notification" :
                                `${patientWaiting} Patient${patientWaiting === 1 ? '' : 's'} Ahead of You`}
                    </Typography>
                </Box>

                {/* Consultation Duration Message */}
                {isEligible && (
                    <Box sx={{
                        backgroundColor: '#f5f5f5',
                        color: '#333',
                        px: 4,
                        py: 2,
                        borderRadius: '8px',
                        mb: 3,
                        textAlign: 'center',
                        border: '1px solid #e0e0e0'
                    }}>
                        <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                            Your consultation will be approximately <strong>{consultationDuration} minutes</strong>
                        </Typography>
                    </Box>
                )}

                {/* Icons Section */}
                {isEligible && <Grid container justifyContent={'center'} sx={{ mb: 2, mt: 2 }}>
                    <img src={timerImage} alt="time zenith image" style={{ width: isDesktop ? '20%' : '30%' }} />
                </Grid>}



                {/* Warning Message */}
                {!isEligible ? (
                    <Box sx={{ textAlign: 'center', mb: 4 }}>
                        <Typography sx={{ color: 'red', fontWeight: 'bold', mb: 1 }}>
                            You do not have an available booking with this consultation link
                        </Typography>
                        <Typography>
                            If this is not correct, contact our Admin using the chat box below for a new consultation link.
                        </Typography>
                    </Box>
                ) : (
                    <Box sx={{ textAlign: 'center', mb: 4 }}>
                        <Typography variant="h6" sx={{ color: 'red', fontWeight: 'bold', mb: 1 }}>
                            Please Stay on This Page
                        </Typography>
                        <Typography sx={{ color: 'grey.700', mb: 1 }}>
                            Your consultation will begin shortly.
                        </Typography>
                        <Typography sx={{ color: 'grey.700' }}>
                            <strong>Leaving may cause you to lose your place in line.</strong>
                        </Typography>
                    </Box>
                )}

                {/* Check Your Sound Card */}
                {isEligible && (
                    <Card sx={{
                        width: '100%',
                        maxWidth: 400,
                        mb: 4,
                        border: '1px solid #e0e0e0',
                        borderRadius: 3,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}>
                        <CardContent sx={{ p: 3, textAlign: 'center' }}>
                            <Typography variant="h6" sx={{ color: 'green', fontWeight: 'bold', mb: 2 }}>
                                Check Your Sound
                            </Typography>

                            <Typography variant="body2" sx={{ color: 'grey.700', mb: 1 }}>
                                <strong>Make sure your microphone works.</strong>
                            </Typography>

                            <Typography variant="body2" sx={{ color: 'grey.500', mb: 3 }}>
                                If asked, allow mic access in your browser.
                            </Typography>



                            <Button
                                variant="contained"
                                onClick={handleTestDevices}
                                disabled={isTesting}
                                startIcon={isTesting ? <CircularProgress size={20} color="inherit" /> : <MicIcon />}
                                sx={{
                                    backgroundColor: 'green',
                                    color: 'white',
                                    borderRadius: '20px',
                                    px: 3,
                                    py: 1,
                                    textTransform: 'none',
                                    fontWeight: 'bold',
                                    '&:hover': {
                                        backgroundColor: 'darkgreen'
                                    }
                                }}
                            >
                                {isTesting ? 'Testing...' : 'Test Microphone'}
                            </Button>

                            {hasTestedMic && (
                                <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                    {microphoneReady ? (
                                        <>
                                            <CheckCircleIcon sx={{ mr: 1, color: 'success.main' }} />
                                            <Typography variant="body2" sx={{ color: 'success.main', fontWeight: 'medium' }}>
                                                Microphone Ready
                                            </Typography>
                                        </>
                                    ) : (
                                        <>
                                            <ErrorIcon sx={{ mr: 1, color: 'error.main' }} />
                                            <Typography variant="body2" sx={{ color: 'error.main', fontWeight: 'medium' }}>
                                                Microphone Not Ready
                                            </Typography>
                                        </>
                                    )}
                                </Box>
                            )}
                        </CardContent>
                    </Card>
                )}

                {/* Important Reminder Section */}
                {isEligible && (
                    <Box sx={{ textAlign: 'center', mb: 4 }}>
                        <Typography variant="h6" sx={{ color: 'grey.700', fontWeight: 'bold', mb: 1 }}>
                            Important Reminder
                        </Typography>
                        <Typography variant="h6" sx={{ color: 'green', fontWeight: 'bold', mb: 2 }}>
                            Do not leave this page
                        </Typography>
                        <Typography sx={{ color: 'grey.700' }}>
                            The consult happens here. Leaving will put you to the <strong>back of the queue.</strong>
                        </Typography>
                    </Box>
                )}

                {/* Confidentiality Statement */}
                <Box sx={{ width: '100%', mt: 2, mb: 3 }}>
                    <Box sx={{ borderTop: '1px solid #e0e0e0', width: '100%', mb: 3 }} />

                    <Typography variant="body2" sx={{ color: 'grey.600', mb: 3, textAlign: 'center', px: 2 }}>
                        Any information you provide today is confidential and
                        compliant with the Medical Board of Australia Good
                        Medical Practice code, RACGP Standards of General
                        Practice and our Medical Confidentially Duty of
                        Conduct for doctors in Australia, which means we
                        protect your privacy and right to confidentiality
                    </Typography>


                </Box>

                {/* Advanced Audio Test - Keep existing functionality but hidden by default */}
                {showAdvancedTest && speakTestExpanded && (
                    <Box
                        sx={{
                            width: '100%',
                            maxWidth: 400,
                            border: '1px solid #e0e0e0',
                            borderRadius: 2,
                            p: 2,
                            mb: 2,
                            backgroundColor: 'rgba(255, 255, 255, 0.8)'
                        }}
                    >
                        <Typography variant="subtitle1" align="center" sx={{ mb: 2, fontWeight: 'medium' }}>
                            Speak Test: Make Sure You're Audible
                        </Typography>

                        <Grid container spacing={2} direction="column" alignItems="center">
                            {/* Audio Level Meter */}
                            <Grid>
                                <Box sx={{ width: '100%', textAlign: 'center', mb: 1 }}>
                                    <Typography variant="body2" color="text.secondary" gutterBottom>
                                        {isRecording ? 'Speak now...' : audioURL ? 'Recording complete' : 'Click to record your voice'}
                                    </Typography>
                                </Box>

                                <Box
                                    sx={{
                                        width: 250,
                                        height: 20,
                                        bgcolor: '#f5f5f5',
                                        borderRadius: 1,
                                        overflow: 'hidden',
                                        position: 'relative',
                                        mb: 2
                                    }}
                                >
                                    <Box
                                        sx={{
                                            height: '100%',
                                            width: `${audioLevel}%`,
                                            bgcolor: audioLevel > 80 ? '#f44336' : audioLevel > 30 ? '#4caf50' : '#90caf9',
                                            transition: 'width 0.1s ease-in-out'
                                        }}
                                    />
                                </Box>
                            </Grid>

                            {/* Recording Controls */}
                            <Grid container justifyContent="center" spacing={2}>
                                <Grid>
                                    <Button
                                        variant="contained"
                                        color={isRecording ? "error" : "primary"}
                                        onClick={isRecording ? stopRecording : startRecording}
                                        startIcon={isRecording ? <StopIcon /> : <RadioButtonCheckedIcon />}
                                        disabled={isPlaying}
                                    >
                                        {isRecording ? "Stop Recording" : "Record (3s)"}
                                    </Button>
                                </Grid>

                                <Grid>
                                    <Button
                                        variant="outlined"
                                        color="primary"
                                        onClick={isPlaying ? stopPlayback : playRecording}
                                        startIcon={isPlaying ? <StopIcon /> : <PlayArrowIcon />}
                                        disabled={isRecording || !audioURL}
                                    >
                                        {isPlaying ? "Stop" : "Play"}
                                    </Button>
                                </Grid>
                            </Grid>

                            {/* Instructions */}
                            <Grid>
                                <Typography variant="body2" color="text.secondary" align="center">
                                    1. Click "Record" and speak normally
                                    <br />
                                    2. The meter should move when you speak
                                    <br />
                                    3. Play back to verify your audio quality
                                </Typography>
                            </Grid>
                        </Grid>
                    </Box>
                )}

                {/* Admission Button */}
                {showButton && (
                    <Button
                        onClick={() => {
                            if (token) {
                                navigate({ to: '/consultation', search: { token: token } })
                                ApiClient.userActions('Patient', token, UserActions.REDIRECTED)
                                    .catch((e) => {
                                        enqueueSnackbar(e.message, {
                                            variant: 'error'
                                        })
                                    })
                            }
                        }}
                        variant='contained'
                        sx={{
                            backgroundColor: 'black',
                            textTransform: 'none',
                            color: 'white',
                            width: '100%',
                            maxWidth: 400,
                            mb: 4
                        }}
                    >
                        You have been admitted, Click here.
                    </Button>
                )}

                {/* Spacer to push footer down */}
                <Box sx={{ flexGrow: 1 }} />

                {/* Footer Section */}
                <Box sx={{
                    width: '100%',
                    borderTop: '1px solid #e0e0e0',
                    pt: 3,
                    mt: 4,
                    textAlign: 'center'
                }}>
                    {/* Trust Badges */}
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 4, mb: 3 }}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                            <Box component="img" src="/images/happy_customer.svg" alt="Happy Customer" sx={{ height: 40, mb: 1 }} />
                            <Typography variant="caption">
                                Trusted by
                            </Typography>
                            <Typography variant="caption">
                                Australians
                            </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                            <Box component="img" src="/images/3D_secure.svg" alt="3D Secure" sx={{ height: 40, mb: 1 }} />
                            <Typography variant="caption" >
                                Safe, Secure &
                            </Typography>
                            <Typography variant="caption">
                                Easy Checkout
                            </Typography>
                        </Box>
                    </Box>
                    <Box sx={{ borderTop: '1px solid #e0e0e0', width: '100%', mb: 3 }} />

                    {/* Copyright */}
                    <Typography variant="caption" sx={{ color: 'grey.600' }}>
                        Provided by <Box component="span" sx={{ color: 'green', fontWeight: 'bold' }}>ZenithClinics</Box> Pty Ltd, 2025. All rights reserved
                    </Typography>
                </Box>

            </Container>
        </>
    )
}

export default WaitingRoom

