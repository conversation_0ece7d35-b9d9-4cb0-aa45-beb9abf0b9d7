import httpStatus from 'http-status';
import { ApiError } from '../utils/ApiError';
import axios, { AxiosInstance } from 'axios';
import config from '../config';
import { logger } from '../config/logger';

interface ZohoTokenResponse {
  access_token: string;
  refresh_token?: string;
  expires_in: number;
  api_domain: string;
  token_type: string;
}

export interface EmailData {
  from?: { email: string };
  to: Array<{
    entity_id: string;
    module: 'Leads';
    email: string;
  }>;
  subject: string;
  content: string;
}

export class ZohoCRMEmailSender {
  private axiosInstance: AxiosInstance;
  private baseUrl: string;
  private clientId: string;
  private clientSecret: string;
  private refreshToken: string;
  private accessToken: string | null = null;

  constructor(clientId: string, clientSecret: string, refreshToken: string, domain: string = 'com.au') {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.refreshToken = refreshToken;
    this.baseUrl = `https://www.zohoapis.${domain}/crm/v7`;

    this.axiosInstance = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add interceptor to handle token refresh
    this.axiosInstance.interceptors.request.use(
      async (config) => {
        const token = await this.getValidAccessToken();
        config.headers['Authorization'] = `Zoho-oauthtoken ${token}`;
        return config;
      },
      (error) => {
        return Promise.reject(error);
      },
    );
  }

  private async refreshAccessToken(): Promise<string> {
    try {
      const tokenUrl = 'https://accounts.zoho.com.au/oauth/v2/token';
      const response = await axios.post<ZohoTokenResponse>(tokenUrl, null, {
        params: {
          refresh_token: this.refreshToken,
          client_id: this.clientId,
          client_secret: this.clientSecret,
          grant_type: 'refresh_token',
        },
      });

      this.accessToken = response.data.access_token;
      return this.accessToken;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Token refresh error:', {
          status: error.response?.status,
          data: error.response?.data,
        });
        throw new Error(`Failed to refresh token: ${JSON.stringify(error.response?.data || error.message)}`);
      }
      throw error;
    }
  }

  private async getValidAccessToken(): Promise<string> {
    if (!this.accessToken) {
      return await this.refreshAccessToken();
    }
    return this.accessToken;
  }

  async sendEmailToLead(
    leadId: string,
    leadEmail: string,
    subject: string,
    body: string,
    fromEmail?: string,
  ) {
    const endpoint = `/Leads/${leadId}/actions/send_mail`;

    const emailData = {
      data: [
        {
          ...(fromEmail ? { from: { email: fromEmail } } : {}),
          to: [
            {
              entity_id: leadId,
              module: 'Leads',
              email: leadEmail,
            },
          ],
          subject: subject,
          content: body,
        },
      ],
    };

    try {
      const response = await this.axiosInstance.post(endpoint, emailData);
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Send email error:', {
          status: error.response?.status,
          data: error.response?.data,
          headers: error.response?.headers,
        });
        throw new ApiError(httpStatus.BAD_REQUEST, error.message);
      }
      throw error;
    }
  }
}

export const setupEmail = () => {
  const sender = new ZohoCRMEmailSender(
    config.zohoClientDev,
    config.zohoSecretDev,
    config.zohoRefreshTokenDev,
    'com.au',
  );

  return sender;
};

export const sendEmail = async (toEmail: string, leadId: string, subject: string, body: string, from: string = config.zenithEmail,) => {
  try {
    const response = await setupEmail().sendEmailToLead(leadId, toEmail, subject, `${body}`, from);
    logger.info('Email sent successfully');
    return response
  } catch (error) {
    console.error('Error:', error);
  }
};
