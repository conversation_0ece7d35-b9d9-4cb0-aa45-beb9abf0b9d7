import React, { useState, useEffect } from "react";
//import {  Dialog, DialogContent, DialogActions, Grid, Card, CardContent, Divider, CircularProgress, IconButton } from "@mui/material";
import {  Dialog, DialogContent, DialogActions, Grid, Card, CardContent, Divider, IconButton, CircularProgress, Alert } from "@mui/material";
import Typography from "@mui/material/Typography";
import FormControl from "@mui/material/FormControl";
import InputLabel from "@mui/material/InputLabel";
import Select, { SelectChangeEvent } from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import FormHelperText from "@mui/material/FormHelperText";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
//import { ConsultationOutCome, TreatmentPlanHistory, PatientTreatmentPlan, OrderHistory, Questionnaire } from "../../../types";
import { ConsultationOutCome, TreatmentPlanHistory, PatientTreatmentPlan } from "../../../types";

import { generateMaxDosePerDay, generateTotalQuantity } from "../helper";
import { provinceAbbreviation, safeScriptLinksPerProvince, convertTimeStampToReadableFormat } from "../../../utils";
import { ApiClient } from "../../../services";
import { AxiosError } from "axios";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CircleOutlinedIcon from '@mui/icons-material/CircleOutlined';
import DoctorNotes from "../doctor-note";
//import ChatEmailConfirmation, { ChatEmailConfirmationProps } from "./ChatEmailConfirmation";
// import ChatEmailConfirmation from "./ChatEmailConfirmation";
// import { ChatEmailProvider } from "../../../hooks/chat-email-provider";
import { useAuth } from "../../../hooks/auth-provider";
import { usePatient } from "../../../hooks/patient-provider";
import chatService from "../../../services/chat.service";
import { config } from "../../../config";
//import EditIcon from '@mui/icons-material/Edit';


import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import HistoryIcon from '@mui/icons-material/History';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import Order from "../patientHistory/orders/order";
import QuestionnaireForm from "../patientHistory/questionnaire";
import TreatmentPlan from "../patientHistory/treatmentPlan";
import HealthCheck from "../patientHistory/healthCheck";
import CloseIcon from '@mui/icons-material/Close';

const inputStyle = {
    '& .MuiInputBase-root': {
        borderRadius: '5px',
        fontSize: '0.6rem',
        height: '30px',
        padding: '4px 8px',
    },
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
        fontSize: '0.7rem'
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

type RequiredFields = 'dosePerDay' | 'maxDosePerDay' | 'totalQuantity' | 'numberOfRepeat' | 'supplyInterval'

const TREATMENT_PLANS: { [key: string]: ConsultationOutCome } = {
    unrestricted: 'Approve Unrestricted',
    // combined: 'Approve 29% Subject To 22% Trial',
    // discharged: 'Approve Subject To Discharge Form',
    // cbd: 'Approve 22% Subject To CBD Trial',

}

// const TREATMENT_PLAN_DISPLAY: { [key: string]: string } = {
//     unrestricted: 'THC Patient',
//     // combined: 'Pre-approve 29% approve 22%',
//     // discharged: 'Approve patient subject to discharge',
//     // cbd: 'Approve 22% Subject To CBD Trial',

// }

const formFields = {
    dosePerDay: 'Dose Per Day',
    maxDosePerDay: 'Maximum Dose Per Day',
    totalQuantity: 'Total Quantity (g)',
    numberOfRepeat: 'Number of Repeat',
    supplyInterval: 'Supply Interval (Days)'
}

type ChatTreatmentPlanFormProps = {
    patientId: string;
    patientEmail?: string; // Add email for history fetching
    onSubmit: () => void;
    onCancel: () => void;
    isReturningPatient?: boolean;
    patientName?: string;
    patientDOB?: string;
    patientState?: string;
    existingTreatmentPlans?: TreatmentPlanHistory[];
    isLoadingPlans?: boolean;
    onComplete?: () => void; // Optional callback for when the form is fully completed
    onNotification?: (message: string, severity: 'success' | 'error') => void; // Added for parent notification
    onPlanSubmitted?: () => void; // New callback for parent to re-fetch plans
    onReturnToChannelList?: () => void; // New prop to navigate back to channel list
}

type FormValue = {
    dosePerDay?: string;
    maxDosePerDay?: string;
    totalQuantity?: string;
    numberOfRepeat?: string;
    supplyInterval?: string;
}

type PatientInfo = {
    patientID: string;
    fullName?: string;
    returningPatient: boolean;
}

interface TreatmentPlanWithPatient {
    [key: string]: FormValue | undefined | ConsultationOutCome | string | PatientInfo;
    22?: FormValue;
    29?: FormValue;
    outcome?: ConsultationOutCome;
    patient?: PatientInfo;
    zohoID?: string;
    date?: string;
    mentalHealthSupportingDocumentation?: string;
    drId?: string;
    drName?: string;
    drAphraNumber?: string;
    drNotes?: string;
}

// Interface for the simplified view of a previous treatment plan
// interface SimplifiedTreatmentPlan {
//     date: string;
//     outcome: string;
//     drName: string;
//     has22: boolean;
//     has29: boolean;
//     dosePerDay22?: string;
//     maxDosePerDay22?: string;
//     totalQuantity22?: string;
//     numberOfRepeat22?: string;
//     supplyInterval22?: string;
//     dosePerDay29?: string;
//     maxDosePerDay29?: string;
//     totalQuantity29?: string;
//     numberOfRepeat29?: string;
//     supplyInterval29?: string;
//     drNotes?: string;
// }

// Add these helper functions for SafeScript link
const getSafeScriptLink = (province: string | undefined): string | undefined => {
    if (!province) return undefined;
    const smallCase = province.toLowerCase();
    return safeScriptLinksPerProvince[smallCase] ?? undefined;
};

const getProvinceAbbreviation = (province: string | undefined): string | undefined => {
    if (!province) return undefined;
    const smallCase = province.toLowerCase();
    return provinceAbbreviation[smallCase] ?? undefined;
};

// Add history type definitions similar to histories.tsx
type History = {
    [key: string]: {
        name: string
        element: React.ReactElement
    }
}

const HistoryTypes: History = {
    order: {
        name: 'Order',
        element: <Order />
    },
    questionnaire: {
        name: "Questionnaire",
        element: <QuestionnaireForm />
    },
    treatmentplan: {
        name: 'Treatment Plan',
        element: <TreatmentPlan />
    },
    healthCheck: {
        name: 'Health Check - 2 weeks',
        element: <HealthCheck />
    }
}

// Reusable Patient History Button component
const PatientHistoryButton = ({
  marginLeft = 1,
  marginTop = 0,
  patientId,
  patientEmail
}: {
  marginLeft?: number;
  marginTop?: number;
  patientId: string;
  patientEmail?: string;
}) => {
  const { openPatientHistoryDialog } = usePatient();

  const handleClick = () => {
    // Pass both patientId and patientEmail to the dialog handler
    openPatientHistoryDialog(patientId, patientEmail);
  };

  return (
    <Button
      size="small"
      startIcon={<HistoryIcon sx={{ fontSize: '0.9rem' }} />}
      onClick={handleClick}
      sx={{
        fontSize: '0.7rem',
        backgroundColor: 'green',
        color: 'white',
        '&:hover': { backgroundColor: 'darkgreen' },
        marginLeft,
        py: 0.3,
        px: 1,
        minWidth: 'auto',
        ...(marginTop > 0 ? { mt: marginTop } : {})
      }}
    >
      Patient History
    </Button>
  );
};

export const ChatTreatmentPlanForm: React.FC<ChatTreatmentPlanFormProps> = ({
    patientId,
    patientEmail,
    onSubmit,
    isReturningPatient = false,
    patientName,
    patientDOB,
    patientState,
    existingTreatmentPlans = [],
    isLoadingPlans = false,
    onComplete,
    onNotification,
    onPlanSubmitted,
    onReturnToChannelList
}): JSX.Element => {
    // Remove localStorage-based dialog state - now managed by context

    const [treatmentPlan, setTreatmentPlan] = useState<TreatmentPlanWithPatient>({
        22: {
            dosePerDay: '1.0',
            maxDosePerDay: '2.0',
            totalQuantity: '28',
            numberOfRepeat: '6',
            supplyInterval: '28'
        },
        29: {
            dosePerDay: '0.1',
            maxDosePerDay: '1.0',
            totalQuantity: '28',
            numberOfRepeat: '6',  // Always 6 for 29% as well
            supplyInterval: '28'
        }
    });
    const [selectedPlan, setSelectedPlan] = useState<ConsultationOutCome>(TREATMENT_PLANS.unrestricted);
    const [selectedForms, setSelectedForms] = useState<('22' | '29')[]>(['22']); // Default to 22% selected
    const [activeStrength, setActiveStrength] = useState<'22' | '29'>('22');
    const [formError, setFormError] = useState<string | undefined>(undefined);
    const [isLoading, setIsLoading] = useState(false);
    const [_checkedMentalHealthDocument, setCheckedMentalHealthDocument] = useState(false);
    //const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const [openRejectDialog, setOpenRejectDialog] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [_showEmailConfirmation, _setShowEmailConfirmation] = useState(false);
    const [_isDirectConfirmation, _setIsDirectConfirmation] = useState(false);
    const [isRejecting, setIsRejecting] = useState(false);
    const [submissionSuccess, setSubmissionSuccess] = useState(false);
    const { doctor } = useAuth();
    const {
        patientTreatmentPlan,
        patientHistory,
        selectedPatientHistory,
        setSelectedPatientHistory,
        isPatientHistoryDialogOpen,
        isPatientHistoryLoading,
        patientHistoryError,
        closePatientHistoryDialog
    } = usePatient();

    // State for previous treatment plans
    const [previousTreatmentPlans, setPreviousTreatmentPlans] = useState<TreatmentPlanHistory[]>([]);
    const [loadingPreviousPlans, setLoadingPreviousPlans] = useState(false);
    const [showPreviousPlan, setShowPreviousPlan] = useState<boolean | null>(null);
    const [showFullForm, setShowFullForm] = useState<boolean | null>(null);
    const [expandedChat, setExpandedChat] = useState<boolean>(true);
    const [expandedTreatmentPlan, setExpandedTreatmentPlan] = useState<boolean>(true);

    // Use provided treatment plans instead of fetching them again
    useEffect(() => {
        // Create a mounted ref to track if component is still mounted
        const isMounted = { current: true };

        // Set loading state based on the isLoadingPlans prop
        setLoadingPreviousPlans(isLoadingPlans);

        // Use the provided treatment plans
        if (isMounted.current) {
            setPreviousTreatmentPlans(existingTreatmentPlans || []);

            // Always show the previous plan view first, even if there are no previous plans
            // This will allow us to show the "No previous plans" message with the "Create New Treatment Plan" button
            setShowPreviousPlan(true);
            setShowFullForm(false);
        }

        // Cleanup function to set mounted flag to false when component unmounts
        return () => {
            isMounted.current = false;
        };
    }, [patientId, existingTreatmentPlans, isLoadingPlans]);

    // Clear directSubmission flag when component mounts for a new patient
    // This ensures that the workflow state is clean for each new patient
    useEffect(() => {
        // Clear the directSubmission flag when starting treatment plan for a new patient
        // This prevents the flag from persisting between different patients
        localStorage.removeItem('directSubmission');
    }, [patientId]);

    // Dialog handlers now use context instead of local state
    const handleCloseHistoryDialog = () => {
        closePatientHistoryDialog();
    };

    const handleHistoryClicked = (history: any): void => {
        setSelectedPatientHistory(history);
    };

    const getHistoryTypeName = (type: string | undefined): string => {
        if (!type) return 'General';

        if (HistoryTypes[type]) {
            return HistoryTypes[type].name;
        }

        // Handle chat-treatmentplan case
        if (type?.includes('treatmentplan')) {
            return HistoryTypes['treatmentplan'].name;
        }

        return 'General';
    };

    const getHistoryTypeElement = (type: string | undefined): React.ReactElement | null => {
        if (!type) return null;

        if (HistoryTypes[type]) {
            return HistoryTypes[type].element;
        }

        // Handle chat-treatmentplan case
        if (type?.includes('treatmentplan')) {
            return HistoryTypes['treatmentplan'].element;
        }

        return null;
    };

    // Function to directly submit a previous treatment plan without showing the form
    const confirmSamePlan = async (plan: TreatmentPlanHistory) => {
        try {
            // Check if plan is valid
            if (!plan) {
                console.error('Cannot confirm plan: plan is undefined');
                if (onNotification) {
                    onNotification('Failed to confirm previous treatment plan: Invalid plan data', 'error');
                } else {
                    setFormError('Failed to confirm previous treatment plan: Invalid plan data');
                }
                return;
            }

            setIsSubmitting(true);

            // First create a prescription lead
            const leadResponse = await ApiClient.createPrescriptionLead(patientId);

            // Get doctor ID with fallback to localStorage
            const drIdLocal = localStorage.getItem('xdr');
            const doctorID = doctor?.accessID || drIdLocal || '';

            // Create a new treatment plan based on the previous one
            const newPlan: TreatmentPlanWithPatient = {
                drNotes: plan.drNotes || '',
                mentalHealthSupportingDocumentation: plan.mentalHealthSupprtingDocument === 'Yes' ? 'Yes' : 'No',
                date: new Date().toISOString(),
                patient: {
                    patientID: patientId,
                    fullName: patientName || '',
                    returningPatient: true
                },
                drId: doctorID,
                drName: doctor?.username || doctor?.name || 'Doctor',
                drAphraNumber: doctor?.aphraNumber,
                zohoID: leadResponse?.data?.leadId
            };

            // Set outcome based on previous plan
            if (plan.outcome) {
                const outcomeValue = Object.values(TREATMENT_PLANS).find(value => value === plan.outcome);
                if (outcomeValue) {
                    newPlan.outcome = outcomeValue;
                }
            }

            // Only add 22% strength if it existed in the previous plan
            if (plan.dosePerDay22) {
                newPlan['22'] = {
                    dosePerDay: plan.dosePerDay22,
                    maxDosePerDay: plan.maxDosePerDay22 || '2.0',
                    totalQuantity: plan.totalQuantity22 || '28',
                    numberOfRepeat: '6', // Always use 6 repeats regardless of previous plan value
                    supplyInterval: plan.supplyInterval22 || '28'
                };
            }

            // Only add 29% strength if it existed in the previous plan
            if (plan.dosePerDay29) {
                newPlan['29'] = {
                    dosePerDay: plan.dosePerDay29,
                    maxDosePerDay: plan.maxDosePerDay29 || '1.0',
                    totalQuantity: plan.totalQuantity29 || '28',
                    numberOfRepeat: '6', // Always use 6 repeats regardless of previous plan value
                    supplyInterval: plan.supplyInterval29 || '28'
                };
            }

            // Save the plan to localStorage (this will be used by the submit API)
            localStorage.setItem('TpPlan', JSON.stringify(newPlan));

            // DIRECT SUBMISSION: Instead of navigating to strain advice step, submit the treatment plan directly
            try {
                // Extract strain advice from the plan if available
                const strainAdviceSativa: string[] = [];
                const strainAdviceIndica: string[] = [];
                const strainAdviceHybrid: string[] = [];

                // Check if plan has treatmentPlan.strainAdvice data from Zoho
                if (plan.treatmentPlan?.strainAdvice) {
                    // Convert any string values to arrays as needed
                    if (plan.treatmentPlan.strainAdvice.sativa) {
                        strainAdviceSativa.push(...(Array.isArray(plan.treatmentPlan.strainAdvice.sativa)
                            ? plan.treatmentPlan.strainAdvice.sativa
                            : [plan.treatmentPlan.strainAdvice.sativa]));
                    }

                    if (plan.treatmentPlan.strainAdvice.indica) {
                        strainAdviceIndica.push(...(Array.isArray(plan.treatmentPlan.strainAdvice.indica)
                            ? plan.treatmentPlan.strainAdvice.indica
                            : [plan.treatmentPlan.strainAdvice.indica]));
                    }

                    if (plan.treatmentPlan.strainAdvice.hybrid) {
                        strainAdviceHybrid.push(...(Array.isArray(plan.treatmentPlan.strainAdvice.hybrid)
                            ? plan.treatmentPlan.strainAdvice.hybrid
                            : [plan.treatmentPlan.strainAdvice.hybrid]));
                    }
                }

                // Safely access optional properties
                const patientEmail = (plan as any).email || '';
                const patientZohoId = (plan as any).zohoId || leadResponse?.data?.leadId;

                // Prepare the treatment plan with proper typing for API submission
                const submissionPlan: PatientTreatmentPlan = {
                    // Patient information
                    patient: {
                        patientID: patientId, // This is required and non-optional in the type
                        fullName: patientName || '',
                        returningPatient: true,
                        // Only include email if it has value
                        ...(patientEmail ? { email: patientEmail } : {}),
                        // Add zohoID to patient object (this is allowed by the type)
                        ...(patientZohoId ? { zohoID: patientZohoId } : {})
                    },
                    // Treatment plan details
                    outcome: newPlan.outcome as ConsultationOutCome,
                    mentalHealthSupportingDocumentation: newPlan.mentalHealthSupportingDocumentation as "Yes" | "No", // Properly type this as literal union
                    date: newPlan.date as string,
                    drId: newPlan.drId as string,
                    drName: newPlan.drName as string,
                    drNotes: newPlan.drNotes as string,
                    ...(newPlan.drAphraNumber ? { drAphraNumber: newPlan.drAphraNumber as string } : {}),
                    // Casting to any to bypass type checking since API expects zohoID at top level
                    ...(patientZohoId ? { zohoID: patientZohoId } as any : {}),
                    // Strength details (only include what's in the plan)
                    ...(newPlan['22'] ? {
                        '22': {
                            dosePerDay: String(newPlan['22'].dosePerDay || '1.0'),
                            maxDosePerDay: String(newPlan['22'].maxDosePerDay || '2.0'),
                            totalQuantity: String(newPlan['22'].totalQuantity || '28'),
                            numberOfRepeat: String(newPlan['22'].numberOfRepeat || '6'),
                            supplyInterval: String(newPlan['22'].supplyInterval || '28')
                        }
                    } : {}),
                    ...(newPlan['29'] ? {
                        '29': {
                            dosePerDay: String(newPlan['29'].dosePerDay || '0.1'),
                            maxDosePerDay: String(newPlan['29'].maxDosePerDay || '1.0'),
                            totalQuantity: String(newPlan['29'].totalQuantity || '28'),
                            numberOfRepeat: String(newPlan['29'].numberOfRepeat || '6'),
                            supplyInterval: String(newPlan['29'].supplyInterval || '28')
                        }
                    } : {}),
                    // Email details with strain advice
                    email: {
                        introMessage: {
                            intro: 'As discussed, your Treatment Plan has now been approved. Below, you\'ll find all the necessary details, including the steps to obtain your treatment',
                            conclusion: 'You now have access to the Harvest Members Only shop, where you can purchase the therapeutic products outlined in your Treatment Plan. Please note that only the products included in your plan will be available for purchase.'
                        },
                        listTitle: {
                            title1: 'Possible Side Effects',
                            title2: 'Contraindications',
                            title3: 'Important Information',
                        },
                        listItemText: {
                            item1: 'Drowsiness, dizziness, dry mouth, euphoria, anxiety, or paranoia',
                            item2: 'Not recommended for pregnant or breastfeeding women, individuals with psychotic disorders, heart conditions, or those operating heavy machinery',
                            item3: 'Start with a low dose and gradually increase as needed. Avoid alcohol and consult with your healthcare provider about potential drug interactions',
                        },
                        // Use extracted strain advice arrays
                        checkedSativa: strainAdviceSativa,
                        checkedIndica: strainAdviceIndica,
                        checkedHybrid: strainAdviceHybrid,
                        otherTreatment: {
                            otreat1: 'Lifestyle changes (exercise, diet, sleep hygiene, stress management)',
                            otreat2: 'Physical therapy',
                            otreat3: 'Cognitive Behavioral Therapy (CBT)',
                            otreat4: 'Over-the-counter medications (paracetamol, ibuprofen)',
                            otreat5: 'Prescription medications (antidepressants, anti-anxiety meds, pain relievers)',
                            otreat6: 'Acupuncture',
                            otreat7: 'Herbal supplements (turmeric, valerian root)',
                            otreat8: 'Specialist care (pain management, rheumatologist)'
                        }
                    }
                };

                // Submit the treatment plan with cast to bypass type checking
                // The API expects zohoID at the top level even though our type definition doesn't include it
                await ApiClient.postChatTreatmentPlan(submissionPlan as any);

                // ONLY AFTER successful treatment plan submission, send the chat termination signal
                try {
                    // Use the patientId prop directly
                    const currentPatientId = patientId;

                    if (!currentPatientId) {
                        console.warn("No patient ID available for chat termination signal");
                    } else {
                        // First check if the patient has a chat channel
                        const patientChannels = await chatService.getPatientChannels(currentPatientId);

                        if (patientChannels && patientChannels.length > 0) {
                            const channelId = patientChannels[0];

                            // Create custom event data instead of a visible message
                            const eventData = {
                                type: "treatment.plan.submitted",
                                patientId: currentPatientId,
                                timestamp: new Date().toISOString()
                            };

                            // Send a hidden message to signal treatment plan submission
                            try {
                                // Send a hidden message via the event endpoint (which now sends a hidden message)
                                await fetch(`${config.apiUrl}/chat/v1.0/channel/${channelId}/event`, {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                    },
                                    credentials: 'include',
                                    body: JSON.stringify({
                                        event: {
                                            type: eventData.type,
                                            patient_id: eventData.patientId,
                                            timestamp: eventData.timestamp
                                        }
                                    })
                                });
                                console.log("Treatment plan termination signal sent after confirm same");
                            } catch (error) {
                                console.error("Error sending treatment plan termination signal:", error);
                            }
                        } else {
                            console.log("No chat channels found for patient");
                        }
                    }
                } catch (messageError) {
                    console.error("Error in chat termination process:", messageError);
                    // Continue with submission even if this fails
                }

                // Only notify parent component - remove local snackbar
                if (onNotification) {
                    onNotification('Treatment plan submitted successfully', 'success');
                }

                // Also set success state in the component
                setIsSubmitting(false);
                // Show a visually clear success state
                setFormError(undefined);
                setSubmissionSuccess(true);

                // After a delay, call onPlanSubmitted to trigger parent re-fetch, then onSubmit/onComplete
                setTimeout(() => {
                    // Set a flag in localStorage to indicate direct submission
                    localStorage.setItem('directSubmission', 'true');

                    // Notify parent to re-fetch plans
                    if (typeof onPlanSubmitted === 'function') {
                        onPlanSubmitted();
                    }

                    // Return to channel list if the callback is provided
                    if (onReturnToChannelList) {
                        onReturnToChannelList();
                        return; // Don't continue with other callbacks if we're returning to channel list
                    }

                    // If onComplete is provided, call it directly to reset the workflow
                    // This bypasses the strain advice and email confirmation steps
                    if (onComplete) {
                        onComplete();
                    } else {
                        onSubmit();
                    }
                }, 3000);
            } catch (submitError) {
                console.error('Error submitting treatment plan:', submitError);

                // Only notify parent component about the error
                if (onNotification) {
                    onNotification('Failed to submit treatment plan', 'error');
                } else {
                    // Fallback to form error if parent notification isn't available
                    setFormError('Failed to submit treatment plan');
                }
            }
        } catch (error) {
            console.error('Error in treatment plan preparation:', error);

            // Only notify parent component about the error
            if (onNotification) {
                onNotification('Failed to confirm previous treatment plan', 'error');
            } else {
                // Fallback to form error if parent notification isn't available
                setFormError('Failed to confirm previous treatment plan');
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    // Function to apply a previous treatment plan for editing
    const editPreviousPlan = (plan: TreatmentPlanHistory) => {
        // Check if plan is valid
        if (!plan) {
            console.error('Cannot edit plan: plan is undefined');
            if (onNotification) {
                onNotification('Failed to edit previous treatment plan: Invalid plan data', 'error');
            } else {
                setFormError('Failed to edit previous treatment plan: Invalid plan data');
            }
            return;
        }

        // Create a new treatment plan based on the previous one
        const newPlan: TreatmentPlanWithPatient = {
            drNotes: plan.drNotes || '',
            mentalHealthSupportingDocumentation: plan.mentalHealthSupprtingDocument === 'Yes' ? 'Yes' : 'No'
        };

        // Set outcome based on previous plan
        if (plan.outcome) {
            const outcomeValue = Object.values(TREATMENT_PLANS).find(value => value === plan.outcome);
            if (outcomeValue) {
                setSelectedPlan(outcomeValue);
                newPlan.outcome = outcomeValue;
            }
        }

        // Add 22% strength if it exists in the previous plan
        if (plan.dosePerDay22) {
            newPlan['22'] = {
                dosePerDay: plan.dosePerDay22,
                maxDosePerDay: plan.maxDosePerDay22 || '2.0',
                totalQuantity: plan.totalQuantity22 || '28',
                numberOfRepeat: plan.numberOfRepeat22 || '6',
                supplyInterval: String(plan.supplyInterval22 || '28')
            };

            if (!selectedForms.includes('22')) {
                setSelectedForms(prev => [...prev, '22']);
            }
        }

        // Add 29% strength if it exists in the previous plan
                    if (plan.dosePerDay29) {
                newPlan['29'] = {
                    dosePerDay: plan.dosePerDay29,
                    maxDosePerDay: plan.maxDosePerDay29 || '1.0',
                    totalQuantity: plan.totalQuantity29 || '28',
                    numberOfRepeat: plan.numberOfRepeat29 || '6',
                    supplyInterval: String(plan.supplyInterval29 || '28')
                };

            if (!selectedForms.includes('29')) {
                setSelectedForms(prev => [...prev, '29']);
            }
        }

        // Always ensure 22% and 29% are initialized as objects if they don't exist
        if (!newPlan['22']) {
            newPlan['22'] = {
                dosePerDay: '1.0',
                maxDosePerDay: '2.0',
                totalQuantity: '28',
                numberOfRepeat: '6',
                supplyInterval: '28'
            };

            if (!selectedForms.includes('22')) {
                setSelectedForms(prev => [...prev, '22']);
            }
        }

        if (!newPlan['29']) {
            newPlan['29'] = {
                dosePerDay: '0.1',
                maxDosePerDay: '1.0',
                totalQuantity: '28',
                numberOfRepeat: isReturningPatient ? '6' : '3',
                supplyInterval: '28'
            };
        }

        // Update the treatment plan state
        setTreatmentPlan(newPlan);

        // Set mental health supporting document checkbox
        setCheckedMentalHealthDocument(plan.mentalHealthSupprtingDocument === 'Yes');

        // Show the full form for editing
        setShowFullForm(true);
        setShowPreviousPlan(false);
    };

    useEffect(() => {
        // Synchronize notes from context to local state if needed
        if (patientTreatmentPlan?.drNotes && !treatmentPlan?.drNotes) {
            setTreatmentPlan((prev: TreatmentPlanWithPatient) => ({
                ...prev,
                drNotes: patientTreatmentPlan.drNotes
            }));
        }
    }, [patientTreatmentPlan, treatmentPlan]);

    // We want to show the "No previous plans" view when there are no previous plans
    // So we don't need to auto-switch to the full form

    const requiredFields: RequiredFields[] = ['dosePerDay', 'maxDosePerDay', 'totalQuantity', 'numberOfRepeat', 'supplyInterval'];

    const validateForm = () => {
        let status = true;


        if (selectedPlan === TREATMENT_PLANS.combined) {
            if (selectedForms.length < 2) {
                setFormError('Both 22% and 29% must be filled for Pre-approve 29% approve 22%');
                return false;
            }
        }

        if (!selectedPlan) {
            setFormError(`No Treatment Plan Selected`);
            status = false;
            return false;
        }

        if (selectedForms.length <= 0) {
            setFormError(`No Strength Selected`);
            return false;
        }

        selectedForms.forEach(form => {
            requiredFields.forEach(field => {
                if (!treatmentPlan?.[`${form}`]?.[`${field}`]) {
                    setFormError(`Field '${formFields[field]}' is mandatory for the ${form}% strength.`);
                    status = false;
                    return;
                }
            })
        });

        return status;
    }

    const handleFormChange = (e: SelectChangeEvent) => {
        const { name, value } = e.target;

        // If trying to set numberOfRepeat for either strength, always force it to "6"
        if (name === "numberOfRepeat" && value === "3") {
            setTreatmentPlan((prev) => ({
                ...prev,
                [activeStrength]: {
                    ...(prev[activeStrength] as FormValue),
                    [name]: "6"
                }
            }));
            return;
        }

        setTreatmentPlan((prev) => ({
            ...prev,
            [activeStrength]: {
                ...(prev[activeStrength] as FormValue),
                [name]: value === "" ? undefined : value
            }
        }));
    }

    // const handlePlanChange = (plan: ConsultationOutCome) => {
    //     setSelectedPlan(plan);
    //     setTreatmentPlan((prev) => ({
    //         ...prev,
    //         outcome: plan
    //     }));
    // }

    const handleStrengthClick = (strength: '22' | '29') => {
        // Only set the active strength for viewing/editing the form
        // Do not automatically add to selectedForms - that should only happen on double-click
        setActiveStrength(strength);
    };

    const handleStrengthDoubleClick = (strength: '22' | '29') => {
        setSelectedForms((prevValue) => {
            const currentIndex = prevValue.indexOf(strength);
            if (currentIndex === -1) {
                return [...prevValue, strength];
            } else {
                return prevValue.filter((item) => item !== strength);
            }
        });
    };

    // const handleSupportingDocumentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    //     setCheckedMentalHealthDocument(event.target.checked);
    //     const value = event.target.checked ? 'Yes' : 'No';
    //     setTreatmentPlan((prev) => ({
    //         ...prev,
    //         mentalHealthSupportingDocumentation: value
    //     }));
    // };

    const generateDosePerDayValues = () => {
        const options = [];
        for (let i = 0.1; i <= 1; i += 0.1) {
            options.push(
                <MenuItem value={`${i.toFixed(1)}`} key={`${i.toFixed(1)}`} sx={{ fontSize: '12px' }}>
                    {i.toFixed(1)}g
                </MenuItem>
            )
        }
        options.push(
            <MenuItem value='1.5' key={'1.5'} sx={{ fontSize: '12px' }}>
                1.5g
            </MenuItem>
        );
        return options;
    }

    const handleSubmit = async () => {
        setIsLoading(true);



        // Small delay to ensure state update completes
        await new Promise(resolve => setTimeout(resolve, 50));

        if (validateForm()) {
            await handleConfirmSubmit();
        }
        setIsLoading(false);
    };

    const handleReject = async () => {
        setOpenRejectDialog(true);
    };

    // Flag to track if a rejection is in progress
    const [isRejectingInProgress, setIsRejectingInProgress] = useState(false);

    const handleConfirmReject = async () => {
        // Prevent duplicate submissions
        if (isRejecting || isRejectingInProgress) {
            console.log('Preventing duplicate rejection - already processing');
            return;
        }

        try {
            setIsRejecting(true);
            setIsRejectingInProgress(true);

            // First create a prescription lead in Zoho
            const leadResponse = await ApiClient.createPrescriptionLead(patientId, 'rejected');
            const zohoID = leadResponse?.data?.leadId;

            // Create a treatment plan with outcome "Rejected"
            const rejectionPlan = {
                patient: {
                    patientID: patientId,
                    fullName: patientName,
                    returningPatient: isReturningPatient
                },
                outcome: 'Reject' as ConsultationOutCome,
                date: new Date().toISOString(),
                drId: doctor?.accessID || localStorage.getItem('xdr') || '',
                drName: doctor?.name || '',
                drAphraNumber: doctor?.aphraNumber,
                zohoID
            };

            // Submit the treatment plan to create a record in the database
            await ApiClient.postChatTreatmentPlan(rejectionPlan);

            // Create a prescription lead in Zoho marked as rejected
            await ApiClient.createPrescriptionLead(patientId, 'rejected');

            // Update patient queue status
            await ApiClient.confirmedPatientWaitingQueue(patientId);
            // const onlineQueuePatient = await ApiClient.fetchOnlineQueue();
            // if (onlineQueuePatient.length <= 0) {
            //     await ApiClient.notifyNextPatient();
            // }

            // Close dialog and show success message
            setOpenRejectDialog(false);

            // Only notify parent component
            if (onNotification) {
                onNotification('Treatment has been rejected successfully', 'success');
            }

            // Go back to the previous treatment plan view
            setShowPreviousPlan(true);
            setShowFullForm(false);

        } catch (error) {
            console.error('Error rejecting treatment plan:', error);
            const axiosError = error as AxiosError;
            if (axiosError.response?.status === 404) {
                setFormError('Patient not found');
            } else if (axiosError.response?.status === 400) {
                setFormError('Invalid data');
            } else {
                setFormError('Failed to reject treatment. Please try again.');
            }

            // Show error using parent notification
            if (onNotification) {
                onNotification('Failed to reject treatment plan', 'error');
            }
        } finally {
            setIsRejecting(false);
            setIsRejectingInProgress(false);
        }
    };

    const handleConfirmSubmit = async () => {
        try {
            setIsSubmitting(true);
            // First create a prescription lead
            const leadResponse = await ApiClient.createPrescriptionLead(patientId);
            // Get doctor ID with fallback to localStorage
            const drIdLocal = localStorage.getItem('xdr');
            const doctorID = doctor?.accessID || drIdLocal || '';
            // Get doctor notes from patientTreatmentPlan context if available
            const doctorNotes = patientTreatmentPlan?.drNotes !== undefined
                ? patientTreatmentPlan.drNotes
                : treatmentPlan.drNotes || '';



            // Update the treatment plan with lead ID and save to localStorage
            const updatedPlan: TreatmentPlanWithPatient = {
                ...treatmentPlan,
                date: new Date().toISOString(),
                patient: {
                    patientID: patientId,
                    fullName: patientName,
                    returningPatient: isReturningPatient
                },
                // Add doctor information to ensure it's available in history views
                drId: doctorID,
                drName: doctor?.name || 'Dr. Hussain Anjum',
                drAphraNumber: doctor?.aphraNumber,
                zohoID: leadResponse.data.leadId,
                // Use the doctor notes from context if available
                drNotes: doctorNotes
            };
            // Remove unselected strength forms
            if (!selectedForms.includes('22')) delete updatedPlan[22];
            if (!selectedForms.includes('29')) delete updatedPlan[29];
            localStorage.setItem('TpPlan', JSON.stringify(updatedPlan));
            // Update local state and close dialog
            setTreatmentPlan(updatedPlan);

            // Update patient queue status
            await ApiClient.confirmedPatientWaitingQueue(patientId);
            // const onlineQueuePatient = await ApiClient.fetchOnlineQueue();
            // if (onlineQueuePatient.length <= 0) {
            //     await ApiClient.notifyNextPatient();
            // }

            // Instead of showing email confirmation, call onPlanSubmitted to move to next step and trigger parent re-fetch
            if (typeof onPlanSubmitted === 'function') {
                onPlanSubmitted();
            }
            onSubmit();
        } catch (error) {
            console.error('Error in treatment plan submission:', error);
            const axiosError = error as AxiosError;
            if (axiosError.response?.status === 404) {
                setFormError('Patient not found');
            } else if (axiosError.response?.status === 400) {
                setFormError('Invalid treatment plan data');
            } else {
                setFormError('Failed to submit treatment plan. Please try again.');
            }
        } finally {
            setIsSubmitting(false);
        }
    };

    // Render the main content based on the current view state
    const renderContent = () => {
        if (showPreviousPlan && !showFullForm) {
            const latestPlan = previousTreatmentPlans[0];

            // Show message if no previous plans are found
            if (previousTreatmentPlans.length === 0 && !loadingPreviousPlans) {
                return (
                    <>
                        <Box sx={{ bgcolor: '#f5f5f5', p: 2, mb: 2 }}>
                            <Typography variant="h6" sx={{ color: '#3B3B3B', mb: 1 }}>
                                Previous Treatment Plan
                            </Typography>
                            <Grid container spacing={1} direction="column">
                                <Grid item>
                                    <Typography variant="body1" sx={{ color: '#3B3B3B',fontSize: '12px' }}>
                                        DOB: {patientDOB || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item>
                                    <Typography variant="body1" sx={{ color: '#3B3B3B',fontSize: '12px' }}>
                                        State: {patientState || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item container alignItems="center" spacing={1}>
                                    {patientState && getSafeScriptLink(patientState) && (
                                        <Grid item>
                                            <Button
                                                size="small"
                                                sx={{
                                                    backgroundColor: 'grey',
                                                    color: 'white',
                                                    textTransform: 'none',
                                                    py: 0.3,
                                                    px: 1,
                                                    fontSize: '0.7rem',
                                                    mt: 0.5,
                                                    minWidth: 'auto'
                                                }}
                                                onClick={() => {
                                                    window.open(
                                                        `${getSafeScriptLink(patientState)}`,
                                                        "_blank"
                                                    );
                                                }}
                                            >
                                                {getProvinceAbbreviation(patientState)} SafeScript
                                            </Button>
                                        </Grid>
                                    )}
                                    {/* First view - No previous plans */}
                                    <Grid item>
                                        <PatientHistoryButton
                                            marginTop={0.5}
                                            patientId={patientId}
                                            patientEmail={patientEmail}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Box>

                        <Card sx={{ mb: 2, p: 3 }}>
                            <Typography variant="body1" align="center">
                                No previous treatment plans found for this patient.
                            </Typography>
                        </Card>

                        <Button
                            variant="contained"
                            fullWidth
                            sx={{
                                bgcolor: 'green',
                                '&:hover': { bgcolor: 'darkgreen' },
                                borderRadius: '8px',
                                textTransform: 'uppercase'
                            }}
                            onClick={() => {
                                setShowFullForm(true);
                                setShowPreviousPlan(false);
                            }}
                        >
                            Create New Treatment Plan
                        </Button>
                    </>
                );
            }

            // Guard: If latestPlan is undefined, show the 'no previous plans' UI
            if (!latestPlan) {
                return (
                    <>
                        <Box sx={{ bgcolor: '#f5f5f5', p: 2, mb: 2 }}>
                            <Typography variant="h6" sx={{ color: '#3B3B3B', mb: 1 }}>
                                Previous Treatment Plan
                            </Typography>
                            <Grid container spacing={1} direction="column">
                                <Grid item>
                                    <Typography variant="body1" sx={{ color: '#3B3B3B',fontSize: '12px' }}>
                                        DOB: {patientDOB || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item>
                                    <Typography variant="body1" sx={{ color: '#3B3B3B',fontSize: '12px' }}>
                                        State: {patientState || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item container alignItems="center" spacing={1}>
                                    {patientState && getSafeScriptLink(patientState) && (
                                        <Grid item>
                                            <Button
                                                size="small"
                                                sx={{
                                                    backgroundColor: 'grey',
                                                    color: 'white',
                                                    textTransform: 'none',
                                                    py: 0.3,
                                                    px: 1,
                                                    fontSize: '0.7rem',
                                                    mt: 0.5,
                                                    minWidth: 'auto'
                                                }}
                                                onClick={() => {
                                                    window.open(
                                                        `${getSafeScriptLink(patientState)}`,
                                                        "_blank"
                                                    );
                                                }}
                                            >
                                                {getProvinceAbbreviation(patientState)} SafeScript
                                            </Button>
                                        </Grid>
                                    )}
                                    {/* First view - No previous plans */}
                                    <Grid item>
                                        <PatientHistoryButton
                                            marginTop={0.5}
                                            patientId={patientId}
                                            patientEmail={patientEmail}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Box>

                        <Card sx={{ mb: 2, p: 3 }}>
                            <Typography variant="body1" align="center">
                                No previous treatment plans found for this patient.
                            </Typography>
                        </Card>

                        <Button
                            variant="contained"
                            fullWidth
                            sx={{
                                bgcolor: 'green',
                                '&:hover': { bgcolor: 'darkgreen' },
                                borderRadius: '8px',
                                textTransform: 'uppercase'
                            }}
                            onClick={() => {
                                setShowFullForm(true);
                                setShowPreviousPlan(false);
                            }}
                        >
                            Create New Treatment Plan
                        </Button>
                    </>
                );
            }

            // If we have previous plans, render the previous plan view
            return (
                <>
                    <Box sx={{ bgcolor: '#f5f5f5', p: 2, mb: 2 }}>
                        <Typography variant="h6" sx={{ color: '#3B3B3B', mb: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                            <span>Previous Treatment Plan</span>
                            <Button
                                onClick={() => setExpandedTreatmentPlan(!expandedTreatmentPlan)}
                                sx={{ minWidth: 'auto', p: 0 }}
                            >
                                {expandedTreatmentPlan ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                            </Button>
                        </Typography>
                        {expandedTreatmentPlan && (
                            <Grid container spacing={1} direction="column">
                                <Grid item>
                                    <Typography variant="body1" sx={{ color: '#3B3B3B', fontSize: '12px' }}>
                                        <strong>DOB:</strong> {patientDOB || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item>
                                    <Typography variant="body1" sx={{ color: '#3B3B3B',fontSize: '12px'  }}>
                                        <strong>State:</strong> {patientState || 'N/A'}
                                    </Typography>
                                </Grid>
                                <Grid item container alignItems="center" spacing={1}>
                                    {patientState && getSafeScriptLink(patientState) && (
                                        <Grid item>
                                            <Button
                                                size="small"
                                                sx={{
                                                    backgroundColor: 'grey',
                                                    color: 'white',
                                                    textTransform: 'none',
                                                    py: 0.3,
                                                    px: 1,
                                                    fontSize: '0.7rem',
                                                    mt: 0.5,
                                                    minWidth: 'auto'
                                                }}
                                                onClick={() => {
                                                    window.open(
                                                        `${getSafeScriptLink(patientState)}`,
                                                        "_blank"
                                                    );
                                                }}
                                            >
                                                {getProvinceAbbreviation(patientState)} SafeScript
                                            </Button>
                                        </Grid>
                                    )}
                                    {/* First view - No previous plans */}
                                    <Grid item>
                                        <PatientHistoryButton
                                            marginTop={0.5}
                                            patientId={patientId}
                                            patientEmail={patientEmail}
                                        />
                                    </Grid>
                                </Grid>
                            </Grid>
                        )}
                    </Box>

                    {expandedTreatmentPlan && (
                        <Card sx={{ mb: 2 }}>
                            <CardContent>
                                <Typography variant="subtitle1" fontWeight="bold">
                                    Last Treatment Plan ({latestPlan.createdAt ? new Date(latestPlan.createdAt).toLocaleDateString('en-AU', { day: '2-digit', month: '2-digit', year: '2-digit' }) : 'Date not available'})
                                </Typography>


                                <Divider sx={{ my: 1 }} />

                                <Typography variant="body2" sx={{ mb: 1 }}>
                                    <strong>Outcome:</strong> {latestPlan.outcome || 'Not specified'}
                                </Typography>

                                {latestPlan.dosePerDay22 && (
                                    <Box sx={{ mb: 1 }}>
                                        <Typography variant="body2" fontWeight="bold">22% Strength:</Typography>
                                        <Typography variant="body2">
                                            Dose Per Day: {latestPlan.dosePerDay22}g
                                        </Typography>
                                        <Typography variant="body2">
                                            Max Dose Per Day: {latestPlan.maxDosePerDay22}g
                                        </Typography>
                                        <Typography variant="body2">
                                            Total Quantity: {latestPlan.totalQuantity22}g
                                        </Typography>
                                        <Typography variant="body2">
                                            Number of Repeats: {latestPlan.numberOfRepeat22}
                                        </Typography>
                                        <Typography variant="body2">
                                            Supply Interval: {latestPlan.supplyInterval22} days
                                        </Typography>
                                    </Box>
                                )}

                                {latestPlan.dosePerDay29 && (
                                    <Box sx={{ mb: 1 }}>
                                        <Typography variant="body2" fontWeight="bold">29% Strength:</Typography>
                                        <Typography variant="body2">
                                            Dose Per Day: {latestPlan.dosePerDay29}g
                                        </Typography>
                                        <Typography variant="body2">
                                            Max Dose Per Day: {latestPlan.maxDosePerDay29}g
                                        </Typography>
                                        <Typography variant="body2">
                                            Total Quantity: {latestPlan.totalQuantity29}g
                                        </Typography>
                                        <Typography variant="body2">
                                            Number of Repeats: {latestPlan.numberOfRepeat29}
                                        </Typography>
                                        <Typography variant="body2">
                                            Supply Interval: {latestPlan.supplyInterval29} days
                                        </Typography>
                                    </Box>
                                )}

                                {latestPlan.drNotes && (
                                    <Box sx={{ mt: 2 }}>
                                        <Typography variant="body2" fontWeight="bold">Doctor Notes:</Typography>
                                        <Typography variant="body2">{latestPlan.drNotes}</Typography>
                                    </Box>
                                )}
                            </CardContent>
                        </Card>
                    )}

                    <Grid container spacing={2}>
                        <Grid item xs={6}>
                            <Button
                                variant="contained"
                                fullWidth
                                sx={{
                                    bgcolor: isSubmitting ? '#ccc' : (submissionSuccess ? 'green' : 'green'),
                                    '&:hover': { bgcolor: submissionSuccess ? 'green' : 'darkgreen' },
                                    borderRadius: '8px',
                                    textTransform: 'uppercase'
                                }}
                                onClick={() => confirmSamePlan(latestPlan)}
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? 'Submitting...' : (submissionSuccess ? 'Submitted!' : 'Confirm Same')}
                            </Button>
                        </Grid>
                        <Grid item xs={6}>
                            <Button
                                variant="outlined"
                                fullWidth
                                sx={{
                                    color: '#676767',
                                    borderColor: '#676767',
                                    '&:hover': { borderColor: '#444', color: '#444' },
                                    borderRadius: '8px',
                                    textTransform: 'uppercase'
                                }}
                                onClick={() => editPreviousPlan(latestPlan)}
                            >
                                Edit
                            </Button>
                        </Grid>
                    </Grid>
                </>
            );
        }

        // Full form view content
        return (
            <>
                <Box sx={{ bgcolor: '#f5f5f5', p: 2, mb: 2 }}>
                    <Typography variant="h6" sx={{ color: '#3B3B3B', mb: 1, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                        <span>Treatment plan</span>                                {previousTreatmentPlans.length > 0 && !showPreviousPlan && (
                                    <Grid item>
                                        <Button
                                            size="small"
                                            startIcon={<ArrowBackIcon />}
                                            onClick={() => {
                                                setShowPreviousPlan(true);
                                                setShowFullForm(false);
                                            }}
                                            sx={{ fontSize: '0.75rem' }}
                                        >
                                            View Previous
                                        </Button>
                                    </Grid>
                                )}
                        <Button
                            onClick={() => setExpandedChat(!expandedChat)}
                            sx={{ minWidth: 'auto', p: 0 }}
                        >
                            {expandedChat ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </Button>
                    </Typography>
                    {expandedChat && (
                        <Grid container spacing={1} direction="column">
                            <Grid item>
                                <Typography variant="body1" sx={{ color: '#3B3B3B', fontSize: '12px' }}>
                                    DOB: {patientDOB || 'N/A'}
                                </Typography>
                            </Grid>
                            <Grid item>
                                <Typography variant="body1" sx={{ color: '#3B3B3B', fontSize: '12px' }}>
                                    State: {patientState || 'N/A'}
                                </Typography>
                            </Grid>
                            <Grid item container alignItems="center" spacing={1}>
                                {patientState && getSafeScriptLink(patientState) && (
                                    <Grid item>
                                        <Button
                                                size="small"
                                                sx={{
                                                    backgroundColor: 'grey',
                                                    color: 'white',
                                                    textTransform: 'none',
                                                    py: 0.3,
                                                    px: 1,
                                                    fontSize: '0.7rem',
                                                    mt: 0.5,
                                                    minWidth: 'auto'
                                                }}
                                                onClick={() => {
                                                    window.open(
                                                        `${getSafeScriptLink(patientState)}`,
                                                        "_blank"
                                                    );
                                                }}
                                            >
                                                {getProvinceAbbreviation(patientState)} SafeScript
                                            </Button>
                                    </Grid>
                                )}

                                <Grid item>
                                    <PatientHistoryButton
                                        marginTop={0}
                                        patientId={patientId}
                                        patientEmail={patientEmail}
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                    )}
                </Box>

                {expandedChat && (
                    <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
                        <Grid container direction="column" spacing={1}>
                            <Grid item xs={12}>
                                <Typography sx={{ fontWeight: 'light', fontSize: '12px', mb: 1 }}>
                                    Switch Forms <span style={{ fontWeight: 'bold', color: 'green' }}>( {activeStrength}% )</span>
                                </Typography>
                                <Grid container spacing={1}>
                                    <Button
                                        variant={activeStrength === '22' ? 'outlined' : 'text'}
                                        endIcon={selectedForms.includes('22') ?
                                            <CheckCircleIcon sx={{ width: '12px' }} /> :
                                            <CircleOutlinedIcon sx={{ width: '12px' }} />
                                        }
                                        sx={{
                                            height: '20px',
                                            fontSize: '12px',
                                            color: selectedForms.includes('22') ? 'green' : '#676767',
                                            borderColor: 'green',
                                            minWidth: '80px'
                                        }}
                                        onClick={() => handleStrengthClick('22')}
                                        onDoubleClick={() => handleStrengthDoubleClick('22')}
                                    >
                                        22%
                                    </Button>
                                    {selectedPlan !== TREATMENT_PLANS.cbd && (
                                        <Button
                                            variant={activeStrength === '29' ? 'outlined' : 'text'}
                                            endIcon={selectedForms.includes('29') ?
                                                <CheckCircleIcon sx={{ width: '12px' }} /> :
                                                <CircleOutlinedIcon sx={{ width: '12px' }} />
                                            }
                                            sx={{
                                                height: '20px',
                                                color: selectedForms.includes('29') ? 'green' : '#676767',
                                                fontSize: '12px',
                                                borderColor: 'green',
                                                minWidth: '80px'
                                            }}
                                            onClick={() => handleStrengthClick('29')}
                                            onDoubleClick={() => handleStrengthDoubleClick('29')}
                                        >
                                            29%
                                        </Button>
                                    )}
                                </Grid>
                            </Grid>

                            <Grid container item xs={12} spacing={2}>
                                <Grid item xs={6}>
                                    <FormControl
                                        fullWidth
                                        size="small"
                                        sx={{ ...inputStyle }}
                                    >
                                        <InputLabel id="doseparday">Dose Per Day</InputLabel>
                                        <Select
                                            labelId="doseparday"
                                            id="dosepardaySelect"
                                            value={treatmentPlan?.[activeStrength]?.dosePerDay || ""}
                                            label="Dose Per Day"
                                            name="dosePerDay"
                                            onChange={handleFormChange}
                                        >
                                            <MenuItem value="">None</MenuItem>
                                            {generateDosePerDayValues()}
                                        </Select>
                                        <FormHelperText sx={{ fontSize: '12px', color: 'text.secondary' }}>
                                            Dose per day {activeStrength === '22' ? '29' : '22'}% : None
                                        </FormHelperText>
                                    </FormControl>
                                </Grid>

                                <Grid item xs={6}>
                                    <FormControl
                                        fullWidth
                                        size="small"
                                        sx={{ ...inputStyle }}
                                    >
                                        <InputLabel id="maximumDosePerDay">Max Dose Per Day</InputLabel>
                                        <Select
                                            labelId="maximumDosePerDay"
                                            id="maximumDosePerDaySelect"
                                            value={treatmentPlan?.[activeStrength]?.maxDosePerDay || ""}
                                            label="Max Dose Per Day"
                                            name="maxDosePerDay"
                                            onChange={handleFormChange}
                                        >
                                            <MenuItem value="">None</MenuItem>
                                            {generateMaxDosePerDay()}
                                        </Select>
                                        <FormHelperText sx={{ fontSize: '12px', color: 'text.secondary' }}>
                                            Max Dose Per Day {activeStrength === '22' ? '29' : '22'}% : None
                                        </FormHelperText>
                                    </FormControl>
                                </Grid>

                                <Grid item xs={12}>
                                    <FormControl
                                        fullWidth
                                        size="small"
                                        sx={{ ...inputStyle }}
                                    >
                                        <InputLabel id="totalQuantity">Total Quantity (g)</InputLabel>
                                        <Select
                                            labelId="totalQuantity"
                                            id="totalQuantitySelect"
                                            value={treatmentPlan?.[activeStrength]?.totalQuantity || ""}
                                            label="Total Quantity (g)"
                                            name="totalQuantity"
                                            onChange={handleFormChange}
                                        >
                                            <MenuItem value="">None</MenuItem>
                                            {generateTotalQuantity()}
                                        </Select>
                                        <FormHelperText sx={{ fontSize: '12px', color: 'text.secondary' }}>
                                            Total Quantity (g) {activeStrength === '22' ? '29' : '22'}% : None
                                        </FormHelperText>
                                    </FormControl>
                                </Grid>

                                <Grid item xs={6}>
                                    <FormControl
                                        fullWidth
                                        size="small"
                                        sx={{ ...inputStyle }}
                                    >
                                        <InputLabel id="numberOfRepeat">Number of Repeat</InputLabel>
                                        <Select
                                            labelId="numberOfRepeat"
                                            id="numberOfRepeatSelect"
                                            value={treatmentPlan?.[activeStrength]?.numberOfRepeat || "6"}
                                            label="Number of Repeat"
                                            name="numberOfRepeat"
                                            onChange={handleFormChange}
                                        >
                                            <MenuItem value="6" sx={{ fontSize: '12px' }}>6</MenuItem>
                                            <MenuItem
                                                value="3"
                                                sx={{
                                                    fontSize: '12px',
                                                    display: 'none' // Hide for both 22% and 29%
                                                }}
                                            >
                                                3
                                            </MenuItem>
                                        </Select>
                                        <FormHelperText sx={{ fontSize: '12px', color: 'text.secondary' }}>
                                            Number of repeat {activeStrength === '22' ? '29' : '22'}% : 6
                                        </FormHelperText>
                                    </FormControl>
                                </Grid>

                                <Grid item xs={6}>
                                    <FormControl
                                        fullWidth
                                        size="small"
                                        sx={{ ...inputStyle }}
                                    >
                                        <InputLabel id="supplyInterval">Supply Interval (Days)</InputLabel>
                                        <Select
                                            labelId="supplyInterval"
                                            id="supplyIntervalSelect"
                                            value={treatmentPlan?.[activeStrength]?.supplyInterval || "28"}
                                            label="Supply Interval (Days)"
                                            name="supplyInterval"
                                            onChange={handleFormChange}
                                        >
                                            <MenuItem value="28" sx={{ fontSize: '12px' }}>28</MenuItem>
                                        </Select>
                                        <FormHelperText sx={{ fontSize: '12px', color: 'text.secondary' }}>
                                            Supply Interval (Days) {activeStrength === '22' ? '29' : '22'}% : 28
                                        </FormHelperText>
                                    </FormControl>
                                </Grid>
                            </Grid>

                            <Grid item xs={12} sx={{ mt: 2, mb: 2 }}>
                                <DoctorNotes
                                    onNoteChange={(notes) => {
                                        setTreatmentPlan(prev => ({
                                            ...prev,
                                            drNotes: notes
                                        }));
                                    }}
                                />
                            </Grid>

                            {formError && (
                                <Typography color="error" variant="body2" sx={{ mt: 2 }}>
                                    {formError}
                                </Typography>
                            )}

                            <Grid item xs={12} sx={{ mt: 2 }}>
                                <Grid container spacing={2}>
                                    <Grid item xs={6}>
                                        <Button
                                            variant="contained"
                                            sx={{
                                                bgcolor: 'green',
                                                '&:hover': {
                                                    bgcolor: 'darkgreen',
                                                },
                                                width: '100%',
                                                borderRadius: '8px',
                                                textTransform: 'uppercase'
                                            }}
                                            onClick={handleSubmit}
                                            disabled={isLoading || isRejecting}
                                        >
                                            Confirm
                                        </Button>
                                    </Grid>
                                    <Grid item xs={6}>
                                        <Button
                                            variant="outlined"
                                            sx={{
                                                color: '#d32f2f',
                                                borderColor: '#d32f2f',
                                                '&:hover': {
                                                    borderColor: '#b71c1c',
                                                    color: '#b71c1c',
                                                    bgcolor: 'rgba(211, 47, 47, 0.04)'
                                                },
                                                width: '100%',
                                                borderRadius: '8px',
                                                textTransform: 'uppercase'
                                            }}
                                            onClick={handleReject}
                                            disabled={isLoading || isRejecting}
                                        >
                                            {isRejecting ? 'Rejecting...' : 'Reject'}
                                        </Button>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    </Box>
                )}
            </>
        );
    };

    // Always render a single return with all dialogs at the top level

    return (
        <Box>
            {/* Always render dialogs at the top level */}
            <Dialog open={isPatientHistoryDialogOpen} fullWidth maxWidth='md' onClose={handleCloseHistoryDialog}>
                <Box sx={{ pt: 5, pl: 5, pr: 5 }}>
                    <Grid container justifyContent={'center'} alignItems='center'>
                        <Grid sx={{ flexGrow: 1 }}>
                            <Typography sx={{ fontSize: '24px', fontWeight: 'bold', color: 'green' }}>
                                {selectedPatientHistory ? getHistoryTypeName(selectedPatientHistory?.type) : "Patient History"}
                            </Typography>
                        </Grid>
                        <Grid>
                            <IconButton onClick={handleCloseHistoryDialog}>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                    </Grid>
                </Box>
                <Box sx={{ padding: 5, overflow: 'auto' }}>
                    {/* Loading State */}
                    {isPatientHistoryLoading && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
                            <CircularProgress />
                            <Typography sx={{ ml: 2 }}>Loading patient history...</Typography>
                        </Box>
                    )}

                    {/* Error State */}
                    {patientHistoryError && !isPatientHistoryLoading && (
                        <Alert severity="error" sx={{ mb: 2 }}>
                            {patientHistoryError}
                        </Alert>
                    )}

                    {/* Content - only show when not loading */}
                    {!isPatientHistoryLoading && (
                        <>
                            {selectedPatientHistory && (
                                <Button
                                    startIcon={<ArrowBackIcon />}
                                    onClick={() => setSelectedPatientHistory(undefined)}
                                    sx={{ mb: 2 }}
                                >
                                    Back to History List
                                </Button>
                            )}
                            <Grid sx={{ mt: 2 }}>
                                {getHistoryTypeElement(selectedPatientHistory?.type)}
                            </Grid>
                            {!selectedPatientHistory && (
                        <Grid sx={{
                            overflowY: 'auto',
                            maxHeight: '400px',
                            '&::-webkit-scrollbar': {
                                width: '6px',
                            },
                            '&::-webkit-scrollbar-track': {
                                backgroundColor: '#cbf5dd',
                                borderRadius: '10px',
                                mt: 1,
                                mb: 1
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: 'green',
                                borderRadius: '10px',
                                p: 2
                            },
                            '&::-webkit-scrollbar-button': {
                                backgroundColor: 'green',
                                height: '7px',
                                borderRadius: '10px'
                            },
                        }}>
                            {patientHistory && patientHistory.length > 0 ? (
                                patientHistory.map((history: any, index: number) => (
                                    <div key={`history-${index}`} onClick={() => handleHistoryClicked(history)} style={{
                                        cursor: 'pointer',
                                        width: '100%',
                                    }}>
                                        <Grid container
                                            sx={{
                                                boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
                                                p: 1,
                                                mr: '4px',
                                                border: '1px solid rgba(0, 0, 0, 0.2)',
                                                borderRadius: 2,
                                                mb: 1,
                                            }}
                                            direction={"column"}
                                        >
                                            <Grid container sx={{ width: '100%' }}>
                                                <Grid sx={{ flexGrow: 1, width: '100%' }} container direction={'column'}>
                                                    <Grid container alignItems={'center'}>
                                                        <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.80rem', fontWeight: '500' }}>
                                                            {getHistoryTypeName(history?.type)}
                                                        </Typography>
                                                    </Grid>
                                                    <Typography display="inline" sx={{ whiteSpace: 'nowrap', fontSize: '0.65rem', mt: 0.5 }}>
                                                        {history?.updatedAt && convertTimeStampToReadableFormat(history.updatedAt)}
                                                    </Typography>
                                                </Grid>
                                            </Grid>
                                        </Grid>
                                    </div>
                                ))
                            ) : (
                                <Typography sx={{ fontSize: '12px' }} align='center'>No previous history</Typography>
                            )}
                        </Grid>
                    )}
                        </>
                    )}
                </Box>
            </Dialog>

            {/* Reject Dialog - Always rendered regardless of view */}
            <Dialog
                open={openRejectDialog}
                onClose={() => setOpenRejectDialog(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogContent>
                    <Grid container direction="column" alignItems="center" spacing={2}>
                        <Grid item>
                            <Typography variant="h6" color="error">
                                Reject Treatment
                            </Typography>
                        </Grid>
                        <Grid item>
                            <Typography>
                                Are you sure you want to reject treatment for {patientName}?.
                            </Typography>
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button
                        onClick={() => setOpenRejectDialog(false)}
                        disabled={isRejecting}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmReject}
                        variant="outlined"
                        sx={{
                            color: '#d32f2f',
                            borderColor: '#d32f2f',
                            '&:hover': {
                                borderColor: '#b71c1c',
                                color: '#b71c1c',
                                bgcolor: 'rgba(211, 47, 47, 0.04)'
                            }
                        }}
                        disabled={isRejecting}
                    >
                        {isRejecting ? 'Rejecting...' : 'Reject'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Render the appropriate content based on current view */}
            {renderContent()}
        </Box>
    );
};

export default ChatTreatmentPlanForm;