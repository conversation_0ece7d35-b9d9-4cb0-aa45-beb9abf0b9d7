import { Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { PatientOrder } from "../../../../types";
import { usePatient } from "../../../../hooks/patient-provider";
import { convertTimeStampToReadableFormat } from "../../../../utils";
import OrderDetailsTable from "./order-details-table";

const AuthorizationCollect: React.FC = () => {
    const { selectedPatientHistory } = usePatient()
    const orderSelected = selectedPatientHistory as PatientOrder

    return <>
        <Grid container direction='column' sx={{ width: '100%' }}>
            <Grid>
                <Typography>
                    Date: {convertTimeStampToReadableFormat(orderSelected?.updatedAt)}
                </Typography>
            </Grid>
            <Grid>
                <Typography>
                    <br />
                    Medicinal Cannabis Wholesale Australia Pty Ltd<br />
                    114/545-553 Pacific Highway<br />
                    St Leonards  NSW  2065<br /><br />
                    Re:Authorization to Collect Medicinal Cannabis on Behalf of {orderSelected?.drName}<br /><br />
                    This certificate authorizes the bearer, {orderSelected?.collector}, an employee of Zenith Clinics Pty Ltd, to collect the prescribed medicinal cannabis from Medicinal Cannabis Wholesale Australia Pty Ltd on behalf of {orderSelected?.drName}, who is an authorised prescriber under the Therapeutic Goods Administration (TGA) and the Poisons and Therapeutic Goods Act 1966 (NSW).<br /><br />

                    Details of Authorised Person: <br/>

                    Name: {orderSelected?.collector}<br/>

                    Position: Stock Controller<br/><br/>

                    Details of Medicinal Cannabis to be Collected:<br/>

                </Typography>
            </Grid>
            <OrderDetailsTable />
            <Grid>
                <Typography>
                    <br />
                    NOTE: Only provide supply if the Quantity field contains an amount.<br /><br />
                    Intended Use: Treatment of Patient<br /><br />

                    By signing below, I, {orderSelected?.drName}, confirm that {orderSelected?.collector} is authorised to collect the above-specified medicinal cannabis on my behalf and for the purpose of patient care at Zenith Clinics Pty Ltd.

                    Signature of Authorised Prescriber:<br /><br />

                    <span style={{ fontStyle: 'italic', fontSize: '14px' }}>Your signature</span><br /><br />

                    {orderSelected?.drName} <br />

                    Zenith Clinics Pty Ltd <br />

                    114A/545-553 Pacific Highway <br />

                    St Leonards  NSW  2065 <br />
                </Typography>
            </Grid>

            <Grid>
                <Typography sx={{ fontSize: '12px' }}>
                    <br /><br />
                    Disclaimer:

                    This email and any attachments are intended solely for the designated recipient and may contain confidential or privileged information. If you are not the intended recipient:

                    <br />(a) Please notify us immediately by phone and ensure the permanent deletion of this email from your system;

                    <br />(b) Be advised that any unauthorized use, sharing, distribution, or reproduction of this email’s content is strictly forbidden.

                    <br /><br />Opinions expressed herein are those of the author and do not necessarily represent the views of Zenith Clinics unless explicitly stated. Zenith Clinics and its directors disclaim any liability for potential harm from computer viruses transmitted through this email.
                </Typography>
            </Grid>
        </Grid>
    </>
}

export default AuthorizationCollect