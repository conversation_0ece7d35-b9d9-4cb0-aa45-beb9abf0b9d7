import WebSocket from 'ws';
import { IncomingMessage } from 'http';
import { logger } from '../config/logger';

interface CustomWebSocket extends WebSocket {
  isAlive?: boolean;
}

class WebSocketManagerClient {
  private clients: CustomWebSocket[] = [];
  private wss: WebSocket.Server<typeof WebSocket, typeof IncomingMessage>;

  constructor() {
    this.wss = new WebSocket.Server({ port: 8080 });
  }

  setUp() {
    this.wss.on('connection', (ws: CustomWebSocket) => {
      this.clients.push(ws);
      // logger.info(`Doctor connected ${new Date()}`);

      const heartbeatInterval = setInterval(() => {
        this.clients.forEach((client) => {
          if (client.isAlive === false) {
            // logger.warn('Terminating unresponsive client.');
            return client.terminate();
          }

          client.isAlive = false;

          client.send(JSON.stringify({ type: 'ping', data: {} }));
        });
      }, 20000);

      ws.on('message', (message: string) => {
        try {
          const parsedMessage = JSON.parse(message);

          if (parsedMessage.type === 'pong') {
            ws.isAlive = true;
          }
        } catch (error) {
          logger.error('Error parsing WebSocket message:', error);
        }
      });

      ws.on('close', () => {
        this.clients = this.clients.filter((client) => client !== ws);
        clearInterval(heartbeatInterval);
      });
    });
  }

  dispatch(type: string, data: unknown) {
    this.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify({ type: type, data: data }));
      }
    });
  }
}

export const WebSocketManager = new WebSocketManagerClient();
