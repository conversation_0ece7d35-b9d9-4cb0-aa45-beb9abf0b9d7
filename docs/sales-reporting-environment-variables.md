# Sales Reporting Environment Variables

This document outlines the environment variables required for the Stripe Sales Reporting feature with Amazon S3 file storage.

## Required Environment Variables

### Slack Configuration
```bash
# Slack channel for sales reports (required)
SLACK_SALES_REPORT_CHANNEL=C1234567890

# Existing Slack token (already configured)
SLACK_TOKEN=xoxb-your-slack-bot-token
```

### Sales Reporting Feature Control
```bash
# Enable/disable sales reporting (default: false)
SALES_REPORTING_ENABLED=true

# Data retention period in days (default: 365)
SALES_DATA_RETENTION_DAYS=365
```

### AWS S3 Configuration (New - Required for File Storage)
```bash
# AWS region for S3 bucket (default: ap-southeast-2)
AWS_REGION=ap-southeast-2

# AWS access credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# S3 bucket name for storing sales report files (default: zenith-sales-reports)
AWS_BUCKET_NAME=zenith-sales-reports
```

### Stripe Configuration (Already Configured)
```bash
# Production Stripe API key
STRIPE_API_KEY=sk_live_your_stripe_key

# Test Stripe API key (hardcoded in config for development)
# STRIPE_TEST_API_KEY is handled automatically based on customer name
```

## Environment-Specific Configuration

### Development Environment
```bash
SALES_REPORTING_ENABLED=true
SLACK_SALES_REPORT_CHANNEL=C_DEV_CHANNEL_ID
SALES_DATA_RETENTION_DAYS=30

# AWS S3 Configuration (Development)
AWS_REGION=ap-southeast-2
AWS_ACCESS_KEY_ID=your_dev_access_key
AWS_SECRET_ACCESS_KEY=your_dev_secret_key
AWS_BUCKET_NAME=zenith-sales-reports-dev
```

### Production Environment
```bash
SALES_REPORTING_ENABLED=true
SLACK_SALES_REPORT_CHANNEL=C_PROD_CHANNEL_ID
SALES_DATA_RETENTION_DAYS=365

# AWS S3 Configuration (Production)
AWS_REGION=ap-southeast-2
AWS_ACCESS_KEY_ID=your_prod_access_key
AWS_SECRET_ACCESS_KEY=your_prod_secret_key
AWS_BUCKET_NAME=zenith-sales-reports
```

## Validation

The application will validate these environment variables at startup:

1. **SLACK_TOKEN** - Must be present for Slack integration
2. **SLACK_SALES_REPORT_CHANNEL** - Must be present if SALES_REPORTING_ENABLED=true
3. **STRIPE_API_KEY** - Must be present for production environment
4. **SALES_DATA_RETENTION_DAYS** - Must be a valid positive integer
5. **AWS_ACCESS_KEY_ID** - Must be present for S3 file storage
6. **AWS_SECRET_ACCESS_KEY** - Must be present for S3 file storage
7. **AWS_BUCKET_NAME** - Must be present for S3 file storage
8. **AWS_REGION** - Must be a valid AWS region

## Setup Instructions

1. **Configure AWS S3 Bucket:**
   - Create an S3 bucket in your AWS account
   - Configure bucket permissions for your application
   - Set up IAM user with S3 access permissions
   - Note the bucket name and region

2. **Add Environment Variables:**
   - Add all required environment variables to your `.env` file
   - Ensure AWS credentials have proper S3 permissions
   - Verify bucket name and region are correct

3. **Run Database Migration:**
   ```bash
   psql -h your-db-host -U your-db-user -d your-db-name -f migrations/create_sales_report_files_table.sql
   ```

4. **Restart Application:**
   - Restart the application to load new configuration
   - Check logs for S3 client initialization messages

5. **Verify Configuration:**
   - Call the status endpoint: `GET /api/sales/v1/status`
   - Test the integration with: `POST /api/sales/v1/test-report`
   - Check that files are being uploaded to S3 bucket

## Troubleshooting

### Common Issues

**Sales reporting not working:**
- Check that `SALES_REPORTING_ENABLED=true`
- Verify `SLACK_SALES_REPORT_CHANNEL` is set to a valid channel ID
- Ensure the Slack bot has permission to post in the specified channel

**Database connection issues:**
- Verify existing database configuration (DB_HOST, DB_USER, etc.)
- Check that the stripe_payments table migration has been run

**AWS S3 integration issues:**
- Verify AWS credentials are correct and have S3 permissions
- Check that the S3 bucket exists and is accessible
- Ensure bucket region matches `AWS_REGION` environment variable
- Verify IAM permissions include `s3:PutObject`, `s3:GetObject`, and `s3:DeleteObject`

**Stripe integration issues:**
- Verify `STRIPE_API_KEY` is set correctly for your environment
- Check that webhook endpoints are properly configured in Stripe dashboard

## AWS IAM Permissions

Your AWS IAM user/role needs the following permissions for the S3 bucket:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:GetObject",
        "s3:DeleteObject"
      ],
      "Resource": "arn:aws:s3:::your-bucket-name/*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "s3:ListBucket"
      ],
      "Resource": "arn:aws:s3:::your-bucket-name"
    }
  ]
}
```

## File Storage Structure

Sales report files are organized in S3 with the following structure:

```
bucket-name/
├── sales-reports/
│   ├── daily/
│   │   ├── 2025/
│   │   │   ├── 01/
│   │   │   │   ├── report-2025-01-24.png
│   │   │   │   └── report-2025-01-25.png
│   │   │   └── 02/
│   │   └── 2024/
│   ├── weekly/
│   │   └── 2025/
│   └── monthly/
│       └── 2025/
```