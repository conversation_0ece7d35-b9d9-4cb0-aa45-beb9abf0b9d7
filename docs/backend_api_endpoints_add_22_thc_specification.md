# Add 22% THC Questionnaire API Endpoints Specification

## Overview
This document specifies the backend API endpoints needed to support the 22% THC addition questionnaire functionality. The frontend is already implemented and expects these endpoints to be available.

## Database Table
The `add_22_thc_questionnaire` table should be created using the provided migration script (`database_migration_add_22_thc_questionnaire.sql`).

## Required API Endpoints

### 1. Submit Add 22% THC Questionnaire
**Endpoint:** `POST /funnel/v1.0/patient/add-22-thc-questionnaire`

**Purpose:** Submit a completed 22% THC addition questionnaire with scoring

**Authentication:** Required (cookie-based, following existing patterns)

**Request Body:**
```json
{
  "questionsAndAnswers": [
    {
      "questionKey": "reasonSideEffects",
      "questionText": "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)",
      "answerValue": true,
      "answerText": "Selected",
      "score": 4
    },
    {
      "questionKey": "symptomImprovement",
      "questionText": "How well has your current 29% THC treatment worked for you?",
      "answerValue": "8",
      "answerText": "8 - Very effective",
      "score": 3
    },
    {
      "questionKey": "healthChanges",
      "questionText": "Have there been any changes in your health, medications, or lifestyle since your last consultation?",
      "answerValue": "no-changes",
      "answerText": "No changes",
      "score": 3
    },
    {
      "questionKey": "usagePlan",
      "questionText": "How do you plan to use the 22% THC product alongside your 29%?",
      "answerValue": "alternative-situations",
      "answerText": "As an alternative for specific times/situations",
      "score": 4
    },
    {
      "questionKey": "consent",
      "questionText": "Do you consent to your doctor reviewing this information?",
      "answerValue": "yes",
      "answerText": "Yes",
      "score": 5
    }
  ],
  "totalScore": 39,
  "maxScore": 50,
  "isEligible": true,
  "submittedAt": "2025-01-17T10:30:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "22% THC addition questionnaire submitted successfully",
  "data": {
    "id": "uuid-here",
    "totalScore": 39,
    "isEligible": true,
    "status": "submitted"
  }
}
```

**Implementation Notes:**
- Extract user email from authentication session/cookie
- Look up patient_id from the `patient` table using email
- Store the form data in the `questionnaire_data` JSONB column
- Set `total_score`, `is_eligible`, and `status` fields
- Include IP address and user agent in metadata
- Return success response with questionnaire ID and eligibility status

### 2. Get Add 22% THC Questionnaire Status
**Endpoint:** `GET /funnel/v1.0/patient/add-22-thc-questionnaire/status?email={email}`

**Purpose:** Check if user has completed the 22% THC addition questionnaire and get their status

**Authentication:** Required (cookie-based)

**Query Parameters:**
- `email` (required): Patient email address

**Response (Completed):**
```json
{
  "success": true,
  "questionnaire": {
    "id": "uuid-here",
    "totalScore": 39,
    "maxScore": 50,
    "isEligible": true,
    "status": "submitted",
    "submittedAt": "2025-01-17T10:30:00Z",
    "reviewedAt": null,
    "approvedAt": null
  }
}
```

**Response (Not Completed):**
```json
{
  "success": false,
  "message": "No 22% THC addition questionnaire found for this patient"
}
```

**Implementation Notes:**
- Query the `add_22_thc_questionnaire` table by email
- Return the most recent submission if multiple exist
- Include all relevant status and scoring information

## Database Operations

### Insert New Questionnaire
```sql
INSERT INTO add_22_thc_questionnaire (
    patient_id, 
    email, 
    zoho_id,
    questionnaire_data, 
    total_score, 
    max_score, 
    is_eligible,
    status
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, 'submitted'
);
```

### Query Questionnaire Status
```sql
SELECT 
    id,
    total_score,
    max_score,
    is_eligible,
    status,
    created_at as submitted_at,
    reviewed_at,
    approved_at
FROM add_22_thc_questionnaire 
WHERE email = $1 
ORDER BY created_at DESC 
LIMIT 1;
```

## Scoring Logic
- **Total Possible Score:** 50 points (5 main questions with varying point values)
- **Eligibility Threshold:** 35+ points (70% of total)
- **Outcome:** Score ≥ 35 = Eligible for 22% THC addition to treatment plan
- **Outcome:** Score < 35 = Requires additional consultation before approval

## Eligibility Requirements
Before allowing questionnaire submission, verify:
1. Patient has an active treatment plan
2. Patient has 29% THC allowance > 0
3. Patient does NOT have 22% THC allowance (or it's 0)
4. Treatment plan is currently active (within start/end dates)

## Error Handling

### Common Error Responses
```json
{
  "success": false,
  "error": "Authentication required",
  "code": 401
}
```

```json
{
  "success": false,
  "error": "Patient not eligible for 22% THC addition questionnaire",
  "code": 403
}
```

```json
{
  "success": false,
  "error": "Invalid questionnaire data",
  "code": 400
}
```

```json
{
  "success": false,
  "error": "Patient not found",
  "code": 404
}
```

## Security Considerations
- Validate all input data before storing
- Ensure user can only access their own questionnaire data
- Verify patient eligibility before allowing questionnaire submission
- Log all questionnaire submissions for audit purposes
- Rate limit submissions to prevent abuse

## Integration Points
- **Patient Authentication:** Use existing cookie-based auth system
- **Patient Lookup:** Query `patient` table to get `patientID` and `zohoID`
- **Treatment Plan Validation:** Query Zoho API to verify 29% THC only status
- **Logging:** Follow existing logging patterns for API requests
- **Error Handling:** Use consistent error response format

## Testing
- Test with valid questionnaire data
- Test with invalid/missing data
- Test authentication requirements
- Test eligibility validation (29% THC only patients)
- Test duplicate submissions (should update existing or create new?)
- Test edge cases (missing patient, invalid email, etc.)

## Doctor Review Workflow
- Submitted questionnaires appear in doctor review dashboard
- Doctors can approve/reject based on questionnaire responses
- Status updates: submitted → under_review → approved/rejected
- Approved questionnaires trigger treatment plan updates in Zoho CRM
