export type TreatmentPlanHistory = {
  drName: string;
  treatmentPlan: string;
  date: string;
  22?: {
    dosePerDay: string;
    maxDosePerDay: string;
    totalQuantity: string;
    numberOfRepeat: string;
    supplyInterval: string;
  };
  29?: {
    dosePerDay: string;
    maxDosePerDay: string;
    totalQuantity: string;
    numberOfRepeat: string;
    supplyInterval: string;
  };
  drNotes: string;
};

export type OrderHistory = {
  orderNumber: string;
  date: string;
  22?: {
    quantity: string;
    remainingQuantity: string;
    initialQuantity: string;
    remainingRepeat: string;
    initialRepeat: string;
  };
  29?: {
    quantity: string;
    remainingQuantity: string;
    initialQuantity: string;
    remainingRepeat: string;
    initialRepeat: string;
  };
};
type Questionaire = {
  question: string;
  answer: string;
};

export type PatientHistory = {
  id: string;
  eventDate: string;
  type: 'order' | 'questionnaire' | 'treatmentPlan' | 'healthCheck';
  treatmentPlan?: TreatmentPlanHistory;
  healthCheck?: Questionaire[];
  questionnaire?: Questionaire[];
  order?: OrderHistory;
};

export type PatientConsultation = {
  id?: string;
  patientID?: string;
  joinedAt?: number;
  consultationDate?: string;
  consultationDateAndTime?: string;
  consultationDuration?: number;
  consultationURL?: string;
  meetingOngoing?: boolean;
  drJoined?: boolean;
  consultationStart?: string;
  consultationEnd?: string;
  notificationSent?: boolean;
  notificationSentDateTime?: string;
};

export type PatientData = {
  firstName?: string;
  lastName?: string;
  fullName?: string;
  email?: string;
  phone?: string;
  consultation?: PatientConsultation;
  returningPatient?: boolean;
  patientID: string;
  zohoID?: string;
  locked?: boolean;
  drLocked?: string;
  drDecision?: string;
  drNotes?: string;
  history?: PatientHistory[];
  userJourney?: 'waitingRoom' | 'landedWebsite' | 'joined';
};

export type TreatmentPlan = {
  strengthAndConcentration?: string;
  dosePerDay?: string;
  maxDosePerDay?: string;
  totalQuantity?: string;
  numberOfRepeat?: string;
  supplyInterval?: string;
};

export type ZohoTreatmentPlan = {
  consultingDoctor: string;
  treatmentPlanStartDate: string;
  treatmentPlanEndDate: string;
  thcContent: string;
  totalAllowance: {
    thc22: string;
    thc29: string;
  };
  totalAllowanceUsed: {
    thc22: string;
    thc29: string;
  };
  repeatAllowance: {
    thc22: string;
    thc29: string;
  };
  numberOfRepeats: number;
  repeatsRemaining: {
    thc22: number;
    thc29: number;
  };
  nextRepeatDate: {
    thc22: string;
    thc29: string;
  };
  supplyRemainingForRepeat: {
    thc22: string;
    thc29: string;
  };
};

export type ConsultationOutCome =
  | 'Approve Unrestricted'
  | 'Approve 29% Subject To 22% Trial'
  | 'Approve Subject To Discharge Form'
  | 'Approve 22% Subject To CBD Trial'
  | 'Approve Subject To GP Referral'
  | 'No Show'
  | 'Rejected';

export interface PatientTreatmentPlan {
  patient?: {
    patientID?: string;
    email?: string;
    consultation?: {
      id: string;
    };
    zohoID?: string;
  };
  zohoID?: string;
  drId?: string;
  drName?: string;
  drAphraNumber?: string;
  outcome?: string;
  drNotes?: string;
  diagnosis?: string;
  date?: string;
  mentalHealthSupportingDocumentation?: string;
  idVerified?: string;
  email?: {
    introMessage?: {
      intro?: string;
      conclusion?: string;
    };
    listTitle?: {
      title1?: string;
      title2?: string;
      title3?: string;
    };
    listItemText?: {
      item1?: string;
      item2?: string;
      item3?: string;
    };
    checkedSativa?: string[];
    checkedIndica?: string[];
    checkedHybrid?: string[];
    otherTreatment?: {
      [key: string]: string;
    };
  };
  [key: number]: {
    dosePerDay?: string;
    maxDosePerDay?: string;
    totalQuantity?: string;
    numberOfRepeat?: string;
    supplyInterval?: string;
  };
}

export type ListItemTextTpProps = {
  item1: string;
  item2: string;
  item3: string;
};

export type OtherTreatmentTpProps = {
  [key: string]: string;
};

export type ListTitleTpProps = {
  title1: string;
  title2: string;
  title3: string;
};

export type Dr = {
  email?: string;
  name?: string;
  username?: string;
  accessID: string;
  status?: string;
  role?: 'doctor' | 'admin';
  password?: string;
  confirmPassword?: string;
  aphraNumber?: string;
  consultationDurationMinutes?: number;
};

export interface ZohoPatientDb {
  Email: string | null; // Email address of the patient
  Auto_Number_Member_ID: string; // Unique ID for the patient
  Doctor_Notes: string | null; // Notes added by the doctor
  Is_Stored_In_DB: string | null; // Whether the patient data is stored in the database
  Consult_Date_Time: string; // Date and time of the consultation in ISO format
  Existing_Patient: string | null; // Indicates if the patient is an existing patient
  Mobile: string | null; // Mobile phone number of the patient
  Date_of_Birth_2: string | null; // Date of birth in DD/MM/YYYY format
  Dr_Trigger: string | null; // Doctor's trigger, if applicable
  Full_Name: string | null; // Full name of the patient
  Dr_Rejection_Date_Time: string | null; // Date and time of doctor's rejection in ISO format
  WP_User_ID: string | null; // WordPress User ID associated with the patient
  Dr_Approve_Date_Time: string | null; // Date and time of doctor's approval in ISO format
  State: string | null; // State or province of the patient
  Consult_Range_End: string | null; // End time of the consultation in ISO format
  id: string; // Unique identifier for the patient in Zoho
  Consult_Range_Start: string | null; // Start time of the consultation in ISO format
  Have_you_used_Alternative_Medicines_before_whethe: string | null; // Information about alternative medicine usage
  Risk_Rating: string | null;
  Condition: string | null;
  What_condition_or_symptom_are_you_having_issues_wi: string | null;
  Are_you_planning_to_have_children_in_the_near_futu: string | null;
  Do_you_suffer_from_any_cardiovascular_diseases_in: string | null;
  Do_you_have_an_addiction_to_any_psychoactive_subst: string | null;
  What_was_your_gender_at_birth: string | null;
  Please_add_the_first_medication_treatment_or_ther: string | null;
  Please_add_the_second_medication_treatment_or_the: string | null;
  Have_you_discussed_other_treatment_options_with_yo: string | null;
  Knowing_the_alternative_management_options_do_you: string | null;
  Do_you_suffer_from_psychosis_bipolar_disorder_or: string | null;
  Queue_Tags: string | null;
}

export interface ZohoPatientInComing extends Omit<ZohoPatientDb, 'Existing_Patient'> {
  Existing_Patient: string | null;
}

export type Admission = {
  id: string;
  patientID: string;
  createdAt: string;
  admitted: boolean;
};

export type Timer = {
  id: string;
  timerId: number;
  reassignmentTimerId?: number;
  drID: string;
};

export type NoShows = {
  fullName: string;
  mobile: string;
  zohoID: string;
  patientID: string;
  queueTag: string;
  consultationID: string
}

export type OrderItem = {
  sku: string;
  trade_name: string;
  quantity: number;
  strength: string;
}

export type PatientOrder = {
  order_id: string;
  wp_user_id: string;
  zoho_contact_id: string;
  allowanceLeft: {
    22: number
    29: number
  }
  email: string;
  treatmentPlanDate: string;
  drName: string;
  orderDateAndTime: string;
  repeatLeft: {
    22: number
    29: number
  };
  items: OrderItem[];
}

export type DoctorSession = {
  id: string;
  doctorId: string;
  sessionId?: string;
  action: 'LOGIN' | 'LOGOUT';
  timestamp: string;
  sessionDuration?: number; // in seconds, only for logout events
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  updatedAt: string;
}

export type DoctorLoginRequest = {
  doctorId: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export type DoctorLogoutRequest = {
  doctorId: string;
  sessionId?: string;
  sessionDuration?: number;
  ipAddress?: string;
  userAgent?: string;
}

export type PatientConsultationData = {
  id: string;
  patientID: string;
  drId: string | null;
  email: string;
  joinedAt: string | null;
  consultationDate: string;
  meetingOngoing: boolean;
  drJoined: boolean;
  consultationStart: string;
  consultationEnd: string;
  notificationSent: boolean;
  notificationSentDateTime: string | null;
  completed: boolean;
  warningSent: boolean;
  warningSentAt: string | null;
  createdAt: string;
  updatedAt: string;
  queueTag: string;
  drUsername: string;
  zohoID: string;
  assignedDoctorID: string;
  noShowNumbers: number;
};

export type PatientQueueDetailsData = {
  id: string;
  patientID: string;
  createdAt: string;
  updatedAt: string;
  joinedAt: string | null;
  email: string;
  status: 'ONLINE' | 'ADMITTED' | 'JOINED' | 'CONFIRMED' | 'COMPLETED' | 'AWAY' | 'ENDED';
  notificationSent: boolean;
  notificationSentDateTime: string | null;
  leftAt: string | null;
  joinedCallAt: string | null;
  admittedAt: string | null;
  completedAt: string | null;
  confirmedAt: string | null;
  callEndedAt: string | null;
};

export type PatientQueueAggregate = {
  waitingTime?: string;
  consultationTime?: string;
  waitingFromLastOnline?: string;
  doctor: string;
  email: string;
  zohoID: string;
};

export type PatientFormData = {
  dob: string;
  condition: string;
  first_medication: string;
  second_medication: string;
  children: string;
  disorder: string;
  diseases: string;
  addiction: string;
  treatment: string;
  alternative_medecine: string;
  trial: string;
};
export type QueueResult = Record<string, PatientQueueAggregate>;

export type DoctorQueueResult = Record<string, PatientQueueAggregate[]>;

export type TimeLine = Record<string, PatientQueueDetailsData[]>;
