import globals from "globals";
import pluginJs from "@eslint/js";
import tseslint from "typescript-eslint";


export default [
  {files: ["**/*.{js,mjs,cjs,ts}"]},
  {files: ["**/*.js"], languageOptions: {sourceType: "esnext"}},
  {languageOptions: { globals: globals.browser }},
  pluginJs.configs.recommended,
  {ignores: [".node_modules/*", "./build/*"]},
  {files: ["**/*.ts", "**/*.js"]},
  ...tseslint.configs.recommended,
];