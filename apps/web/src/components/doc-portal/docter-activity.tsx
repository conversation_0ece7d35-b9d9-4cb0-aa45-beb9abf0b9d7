/* eslint-disable @typescript-eslint/no-explicit-any */
import { Menu<PERSON>ist, Ty<PERSON>graphy, TextField, InputAdornment, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, TableSortLabel, FormControl, InputLabel, Box, Pagination } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useEffect, useState } from "react";
import { usePatient } from "../../hooks/patient-provider";
import { ApiClient } from "../../services";
import LoadingScreen from "../../utils/loading-screen";
import { AxiosError } from "axios";
import { useHistory } from "../../hooks/history-provider";
import SearchIcon from "@mui/icons-material/Search";
import { DrActivity } from "../../types";
import TimezoneSelect, { type ITimezone } from "react-timezone-select";
import { formatInTimeZone } from 'date-fns-tz';

const inputStyle = {
    '& .MuiInputBase-root': {
        borderRadius: '5px',
    }, 
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

const timezoneSelectStyles = {
    container: (base: any) => ({
        ...base,
        width: '100%',
        marginTop: '16px',
    }),
    control: (base: any) => ({
        ...base,
        minHeight: '40px',
        borderRadius: '5px',
        borderColor: '#ddd',
        '&:hover': {
            borderColor: 'green',
        },
        boxShadow: 'none',
        padding: '2px 8px',
    }),
    menu: (base: any) => ({
        ...base,
        zIndex: 9999,
    }),
    option: (base: any, state: { isSelected: boolean }) => ({
        ...base,
        backgroundColor: state.isSelected ? 'green' : base.backgroundColor,
        '&:hover': {
            backgroundColor: state.isSelected ? 'green' : '#e8f5e9',
        },
        padding: '8px 12px',
    }),
    placeholder: (base: any) => ({
        ...base,
        color: '#666',
    }),
    singleValue: (base: any) => ({
        ...base,
        color: '#000',
    }),
}

const paginationStyle = {
    '& .MuiPaginationItem-root': {
        color: '#3B3B3B',
        '&:hover': {
            backgroundColor: '#e8f5e9',
        },
        '&.Mui-selected': {
            backgroundColor: 'green',
            color: 'white',
            '&:hover': {
                backgroundColor: 'green',
            },
        },
    },
}

const DocterActivity: React.FC = () => {
    const {searchTerm, setSearchTerm} = useHistory();
    const [localError] = useState<string | undefined>(undefined);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedTimezone, setSelectedTimezone] = useState<ITimezone>('Australia/Sydney');

    const {setError} = usePatient()
    const [orderDirection, setOrderDirection] = useState<'asc' | 'desc'>('asc');
    const [orderBy, setOrderBy] = useState<string>('name');
    const [currentPage, setCurrentPage] = useState(0);
    const [originalList, setOriginalList] = useState<DrActivity[]>([]);
    const [docterList, setDocterList] = useState<DrActivity[]>([]);
    const itemsPerPage = 5;

    const handleSearchTermChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const value = event.target.value;
        setSearchTerm(value);
    }

    const paginatedData = docterList.slice(
        currentPage * itemsPerPage,
        (currentPage + 1) * itemsPerPage
    );

    const handleSort = (column: string) => {
        setOrderBy((prevOrderBy) => {
            const isSameColumn = prevOrderBy === column;
            setOrderDirection((prevDirection) => (isSameColumn && prevDirection === "asc" ? "desc" : "asc"));
            return column;
        });
    };

    const formatDate = (value: string | number | Date | undefined): string => {
        if (!value) return "";
        try {
            return formatInTimeZone(
                new Date(value), 
                typeof selectedTimezone === 'string' ? selectedTimezone : selectedTimezone.value, 
                'yyyy-MM-dd HH:mm:ss'
            );
        } catch {
            return "";
        }
    };
    
    useEffect(() => {
        const fetchdoctersData = async () => {
            setIsLoading(true);
            setError(null);

            try {
                const result = await ApiClient.doctersActivity({});
                
              
                setOriginalList(result);
                setDocterList(result);
            } catch (error) {
                setError(error as AxiosError<unknown, any>);
            } finally {
                setIsLoading(false);
            }
        };

        fetchdoctersData();
    }, []);

    useEffect(() => {
        let filtered = [...originalList];
    
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            filtered = filtered.filter(item =>
                (item.name ?? '').toLowerCase().includes(term)
            );
        }
    
        filtered.sort((a, b) => {
            const aValue = a[orderBy as keyof DrActivity];
            const bValue = b[orderBy as keyof DrActivity];
        
            let result = 0;
        
            if (orderBy === "createdAt") {
                const aTime = new Date(aValue as string | number | Date).getTime();
                const bTime = new Date(bValue as string | number | Date).getTime();
                result = aTime - bTime;
            } else {
                // fallback to string comparison
                const aStr = (aValue ?? "").toString();
                const bStr = (bValue ?? "").toString();
                result = aStr.localeCompare(bStr);
            }
        
            return orderDirection === "asc" ? result : -result;
        });
        setDocterList(filtered);
        setCurrentPage(0); // reset to page 1 when data changes
    }, [searchTerm, orderBy, orderDirection, originalList]);
    

    const handlePageChange = (_event: React.ChangeEvent<unknown>, value: number) => {
        setCurrentPage(value - 1);
    };

    return (
        <>
            {isLoading && <LoadingScreen />}
            <Grid container sx={{ width: "100%" }}>
                {/* Sidebar Menu */}
                <Grid size={{ md: 3 }} container direction="column">
                    <Typography sx={{ fontWeight: "bold", color: "white", backgroundColor:"green", textAlign: "center", padding: "8px" }}>Doctor Name</Typography>

                    <MenuList sx={{ mt: 3 }}>
                        <Grid container sx={{ width: { lg: "100%", xs: "100%" }, mb: 3 }} spacing={2}>
                            <TextField
                                label="Search"
                                size="small"
                                value={searchTerm}
                                fullWidth
                                sx={{ ...inputStyle }}
                                onChange={handleSearchTermChange}
                                helperText={localError}
                                error={!!localError}
                                slotProps={{
                                    input: {
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <SearchIcon />
                                            </InputAdornment>
                                        ),
                                    },
                                }}
                            />
                        </Grid>
                    </MenuList>
                </Grid>

                {/* Main Content */}
                <Grid container sx={{marginLeft: "3rem", width: "70%"}}>
                    {/* Add Timezone Selector */}
                    <FormControl fullWidth sx={{ mb: 3, mt: 1 }}>
                        <InputLabel 
                            shrink
                            sx={{ 
                                color: '#3B3B3B',
                                backgroundColor: 'white',
                                padding: '0 4px',
                                '&.Mui-focused': {
                                    color: 'green'
                                }
                            }}
                        >
                            Timezone
                        </InputLabel>
                        <TimezoneSelect
                            value={selectedTimezone}
                            onChange={setSelectedTimezone}
                            styles={timezoneSelectStyles}
                        />
                    </FormControl>

                <TableContainer component={Grid}>
                    <Table>
                        <TableHead>
                            <TableRow sx={{ backgroundColor: "green"}}>
                                <TableCell sx={{ color: "white" }} sortDirection={orderBy === 'name' ? orderDirection : false}>
                                    <TableSortLabel
                                        active={orderBy === 'name'}
                                        direction={orderBy === 'name' ? orderDirection : 'asc'}
                                        onClick={() => handleSort('name')}
                                    >
                                        Name
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell sx={{ color: "white" }} sortDirection={orderBy === 'email' ? orderDirection : false}>
                                    <TableSortLabel
                                        active={orderBy === 'email'}
                                        direction={orderBy === 'email' ? orderDirection : 'asc'}
                                        onClick={() => handleSort('email')}
                                    >
                                        Email
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell sx={{ color: "white" }} sortDirection={orderBy === 'action' ? orderDirection : false}>
                                    <TableSortLabel
                                        active={orderBy === 'action'}
                                        direction={orderBy === 'action' ? orderDirection : 'asc'}
                                        onClick={() => handleSort('action')}
                                    >
                                        Action
                                    </TableSortLabel>
                                </TableCell>
                                <TableCell sx={{ color: "white" }} sortDirection={orderBy === 'createdAt' ? orderDirection : false}>
                                    <TableSortLabel
                                        active={orderBy === 'createdAt'}
                                        direction={orderBy === 'createdAt' ? orderDirection : 'asc'}
                                        onClick={() => handleSort('createdAt')}
                                    >
                                        Date Created
                                    </TableSortLabel>
                                </TableCell>
                                
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {paginatedData.length > 0 ? (
                                paginatedData.map((row, index) => (
                                    <TableRow 
                                        key={`${row.id}-${row.createdAt}`}
                                        sx={{
                                            backgroundColor: index % 2 === 0 ? "#f9f9f9" : "#eafaf1",
                                            "&:hover": {
                                                backgroundColor: "green",
                                            },
                                        }}> 
                                        <TableCell sx={{ 
                                            borderRight: "1px solid #ddd", 
                                            padding: "12px 16px",
                                            '&:hover': {
                                                color: "white"
                                            }
                                        }}>{row.name}</TableCell>
                                        <TableCell sx={{ 
                                            borderRight: "1px solid #ddd",
                                            padding: "12px 16px",
                                            '&:hover': {
                                                color: "white"
                                            }
                                        }}>{row.email}</TableCell>
                                        <TableCell sx={{ 
                                            padding: "12px 16px",
                                            '&:hover': {
                                                color: "white"
                                            }
                                        }}>{row.action}</TableCell>
                                        <TableCell sx={{ 
                                            borderRight: "1px solid #ddd",
                                            padding: "12px 16px",
                                            '&:hover': {
                                                color: "white"
                                            }
                                        }}>{formatDate(row.createdAt)}</TableCell>
                                      
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell colSpan={6} align="center">
                                        <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                            NO DATA FOUND
                                        </Typography>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </TableContainer>

                <Grid container sx={{ width: "100%", mt: 4, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <Box sx={{ color: 'text.secondary', fontSize: '0.875rem' }}>
                        Showing {Math.min(currentPage * itemsPerPage + 1, docterList.length)}-{Math.min((currentPage + 1) * itemsPerPage, docterList.length)} of {docterList.length} items | Page {currentPage + 1} of {Math.ceil(docterList.length / itemsPerPage)}
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                        <Pagination 
                            count={Math.ceil(docterList.length / itemsPerPage)}
                            page={currentPage + 1}
                            onChange={handlePageChange}
                            variant="outlined"
                            shape="rounded"
                            sx={paginationStyle}
                        />
                    </Box>
                </Grid>
                </Grid>
            </Grid>
        </>
    );
};

export default DocterActivity
