// File: src/utils/addMissingDays.ts
import { v4 as uuid } from 'uuid';

export type Slot = {
  slot?: string;
  remaining?: number;
  noShowRemaining?: number;
  id?: string;
  range_id?: string;
  bookedByAdminId?: string;
  bookingType?: 'patient' | 'admin';
};

export type DaySchedule = {
  day: string;
  date: string;
  start: string;
  end: string;
  availability: number;
  noShowAvailability?: number;
  interval: number;
  slots?: Slot[];
  status?: 'active' | 'pending' | 'editing' | 'deleted';
  range_id: string;
  doctorID?: string;
  doctorName?: string;
};

export type AvailabilityEntry = {
  day?: string;
  date?: string;
  start?: string;
  end?: string;
  availability?: number;
  noShowAvailability?: number;
  interval?: number;
  id?: string;
  status?: 'active' | 'pending' | 'editing' | 'deleted';
  doctorID?: string;
  doctorName?: string;
};

const getDateObject = (date: string) => {
  const [year, month, day] = date.split('-').map(Number);
  return new Date(year, month - 1, day);
};

const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export const addMissingDays = (schedule: DaySchedule[]): DaySchedule[] => {
  const DAYS_OF_WEEK = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const WEEKDAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
  const defaultSlots: Slot[] = [];
  const defaultStart = '13:00';
  const defaultEnd = '16:00';
  const defaultAvailability = 0;
  const defaultIntervals = 30

  const groupedSchedules: Record<string, DaySchedule[]> = schedule.reduce(
    (groups, entry) => {
      const weekStartDate = formatDate(
        new Date(
          getDateObject(entry.date).setDate(getDateObject(entry.date).getDate() - getDateObject(entry.date).getDay()),
        ),
      );
      groups[weekStartDate] = groups[weekStartDate] || [];
      groups[weekStartDate].push(entry);
      return groups;
    },
    {} as Record<string, DaySchedule[]>,
  );

  const result: DaySchedule[] = [];

  for (const weekStartDate in groupedSchedules) {
    const weekSchedule = groupedSchedules[weekStartDate];

    const existingDays = new Set(weekSchedule.map((entry) => entry.day));

    for (const day of WEEKDAYS) {
      if (!existingDays.has(day)) {
        const missingDate = formatDate(
          new Date(
            getDateObject(weekStartDate).setDate(getDateObject(weekStartDate).getDate() + DAYS_OF_WEEK.indexOf(day)),
          ),
        );

        const id = uuid();
        weekSchedule.push({
          day,
          date: missingDate,
          start: defaultStart,
          end: defaultEnd,
          availability: defaultAvailability,
          noShowAvailability: defaultAvailability,
          slots: defaultSlots,
          interval: defaultIntervals,
          range_id: id,
          doctorID: undefined,
          doctorName: undefined,
        });
      }
    }

    weekSchedule.sort((a, b) => getDateObject(a.date).getTime() - getDateObject(b.date).getTime());
    result.push(...weekSchedule);
  }

  return result.sort((a, b) => getDateObject(a.date).getTime() - getDateObject(b.date).getTime());
};

export const createTimeSlots = (start: string, end: string, range: DaySchedule): Slot[] => {
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const minutesToTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60)
      .toString()
      .padStart(2, '0');
    const mins = (minutes % 60).toString().padStart(2, '0');
    return `${hours}:${mins}`;
  };

  const startMinutes = timeToMinutes(start);
  const endMinutes = timeToMinutes(end);

  if (startMinutes >= endMinutes) {
    throw new Error('Start time must be earlier than end time');
  }

  const slots: Slot[] = [];
  const interval = Number(range.interval) || 30
  for (let current = startMinutes; current < endMinutes; current += interval) {
    const slotStart = minutesToTime(current);
    const slotEnd = minutesToTime(current + interval);
    if (current + interval <= endMinutes) {
      slots.push({
        slot: `${slotStart} - ${slotEnd}`,
        range_id: range.range_id,
        remaining: range.availability,
        noShowRemaining: range.noShowAvailability || range.availability,
      });
    }
  }
  return slots;
};
