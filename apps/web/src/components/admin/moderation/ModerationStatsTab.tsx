import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  LinearProgress,
  Divider
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Pending as PendingIcon,
  Assessment as StatsIcon
} from '@mui/icons-material';
import { AdminRequestStats } from '../../../services/admin-requests.service';

interface RequestStatsTabProps {
  stats: AdminRequestStats | null;
}

const RequestStatsTab: React.FC<RequestStatsTabProps> = ({ stats }) => {
  if (!stats) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="h6" color="textSecondary">
          No statistics available
        </Typography>
      </Box>
    );
  }

  // Calculate rates based on total requests (including pending)
  const approvalRate = stats.total > 0 ? (stats.approved / stats.total) * 100 : 0;
  const rejectionRate = stats.total > 0 ? (stats.rejected / stats.total) * 100 : 0;
  const pendingRate = stats.total > 0 ? (stats.pending / stats.total) * 100 : 0;

  // Calculate rates based on processed requests only (excluding pending)
  const processedTotal = stats.approved + stats.rejected;
  const processedApprovalRate = processedTotal > 0 ? (stats.approved / processedTotal) * 100 : 0;
  const processedRejectionRate = processedTotal > 0 ? (stats.rejected / processedTotal) * 100 : 0;

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Treatment Plan Request Statistics
      </Typography>

      <Grid container spacing={3}>
        {/* Summary Cards */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Request Breakdown
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ApproveIcon color="success" fontSize="small" />
                    <Typography variant="body2">Approved</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.approved} ({approvalRate.toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={approvalRate}
                  color="success"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <RejectIcon color="error" fontSize="small" />
                    <Typography variant="body2">Rejected</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.rejected} ({rejectionRate.toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={rejectionRate}
                  color="error"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PendingIcon color="warning" fontSize="small" />
                    <Typography variant="body2">Pending</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.pending} ({pendingRate.toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={pendingRate}
                  color="warning"
                  sx={{ height: 8, borderRadius: 4 }}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <StatsIcon color="primary" fontSize="small" />
                  <Typography variant="body2" fontWeight="bold">Total Requests</Typography>
                </Box>
                <Typography variant="h6" color="primary">
                  {stats.total}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Stats */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12 }}>
              <Card sx={{ bgcolor: '#008000', color: 'white' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {processedApprovalRate.toFixed(1)}%
                      </Typography>
                      <Typography variant="body2">
                        Approval Rate
                      </Typography>
                      <Typography variant="caption" sx={{ opacity: 0.8 }}>
                        (of processed)
                      </Typography>
                    </Box>
                    <ApproveIcon sx={{ fontSize: 48, opacity: 0.8 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ xs: 12 }}>
              <Card sx={{ bgcolor: 'error.light', color: 'error.contrastText' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {processedRejectionRate.toFixed(1)}%
                      </Typography>
                      <Typography variant="body2">
                        Rejection Rate
                      </Typography>
                      <Typography variant="caption" sx={{ opacity: 0.8 }}>
                        (of processed)
                      </Typography>
                    </Box>
                    <RejectIcon sx={{ fontSize: 48, opacity: 0.8 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid size={{ xs: 12 }}>
              <Card sx={{ bgcolor: 'warning.light', color: 'warning.contrastText' }}>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {stats.pending}
                      </Typography>
                      <Typography variant="body2">
                        Awaiting Review
                      </Typography>
                    </Box>
                    <PendingIcon sx={{ fontSize: 48, opacity: 0.8 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>

        {/* Additional Insights */}
        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Request Processing Insights
              </Typography>

              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 4 }}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h3" color="primary" fontWeight="bold">
                      {processedTotal}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Requests Processed
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      ({stats.total > 0 ? (processedTotal / stats.total * 100).toFixed(1) : 0}% of total)
                    </Typography>
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 4 }}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h3" color="warning.main" fontWeight="bold">
                      {stats.pending}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Pending Review
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      ({pendingRate.toFixed(1)}% of total)
                    </Typography>
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 4 }}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h3" sx={{ color: '#008000' }} fontWeight="bold">
                      {processedApprovalRate.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Approval Rate
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      (of processed requests)
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default RequestStatsTab;
