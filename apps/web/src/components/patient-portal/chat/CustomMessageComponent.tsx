import {
  MessageSimple,
  useMessageContext,
  MessageUIComponentProps,
  DefaultStreamChatGenerics
} from 'stream-chat-react';

interface CustomMessageComponentProps extends MessageUIComponentProps<DefaultStreamChatGenerics> {
  doctorName?: string;
}

// This component wraps MessageSimple to only modify username display
const CustomMessageComponent = ({ doctorName = 'Doctor', ...props }: CustomMessageComponentProps) => {
  const messageContext = useMessageContext();
  const { message } = messageContext;
  
  // If no message, return null
  if (!message || !message.user) {
    return <MessageSimple {...props} />;
  }
  
  // Determine if the message is from a doctor
  const isDoctorMessage = message.user.id?.startsWith('d_');
  
  // Only modify the user name for doctor messages
  if (isDoctorMessage) {
    // Create a new message object with the modified name
    const modifiedMessage = { 
      ...message,
      user: {
        ...message.user,
        name: doctorName // Use the passed doctorName instead of hardcoded value
      }
    };
    
    // Return MessageSimple with our modified message
    return (
      <MessageSimple
        {...messageContext}
        {...props}
        message={modifiedMessage}
      />
    );
  }
  
  // For non-doctor messages, use the default rendering
  return <MessageSimple {...messageContext} {...props} />;
};

export default CustomMessageComponent; 