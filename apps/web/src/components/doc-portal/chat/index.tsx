import {
    Chat,
    Channel,
    //ChannelList,
    Window,
    ChannelHeader,
    //MessageInput,
    Thread,
    //ChannelPreviewUIComponentProps,
    DefaultStreamChatGenerics,
    useChannelStateContext,
    useChatContext,
    Streami18n
} from "stream-chat-react";
import { Channel as StreamChannel, Event } from 'stream-chat';
import Grid from "@mui/material/Grid2";
import "stream-chat-react/dist/css/v2/index.css";
import { Button, Typography, CircularProgress, Box, Paper, Badge, IconButton, SxProps, Alert, Snackbar } from "@mui/material";
import CustomChannelPreview from "./channelList";
import { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { useStreamChat } from "../../../hooks/useStreamChat";
import NotificationsIcon from '@mui/icons-material/Notifications';
import RefreshIcon from '@mui/icons-material/Refresh';
import { ApiClient } from "../../../services";
import ChatTreatmentPlanForm from "./ChatTreatmentPlanForm";
import ChatEmailConfirmation from "./ChatEmailConfirmation";
import { ChatEmailProvider } from "../../../hooks/chat-email-provider";
import ChatStrainAdvice from "./ChatStrainAdvice";
import { useAuth } from '../../../hooks/auth-provider';
import CustomMessageInput from './CustomMessageInput';
import { EmailProvider } from "../../../hooks/email-provider";
import { PatientTreatmentPlan } from "../../../types";
import CustomMessageList from '../../patient-portal/chat/CustomMessageList';
import chatService from '../../../services/chat.service';
import './layout.css'


// Default options for channel list
const options = { presence: true, state: true, watch: true, limit: 20 };



// Component to handle marking messages as read
export const MessageReadHandler = ({ isDisconnected }: { isDisconnected: boolean }) => {
    const { channel } = useChannelStateContext();
    const { client } = useChatContext();
    const userSelectedRef = useRef(false);
    const lastMarkReadRef = useRef<number>(0);
    const markReadTimeoutRef = useRef<NodeJS.Timeout>();
    const retryCountRef = useRef(0);
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1000; // Base delay in ms
    const RATE_LIMIT_WINDOW = 2000; // Minimum time between markRead calls

    // This component now only marks messages as read when:
    // 1. The channel is manually selected AND
    // 2. The component is mounted (indicating the channel view is active)

    // It no longer automatically marks messages as read when new messages arrive

    const markReadWithRetry = useCallback(async () => {
        // First check if we can proceed with marking as read
        if (!channel || !client || !userSelectedRef.current || isDisconnected) return;

        // Check if client is still connected
        if (!client.userID) {
            //console.log('Cannot mark as read: client is disconnected');
            return;
        }

        // Check if channel is still active
        if (!channel.cid) {
            //console.log('Cannot mark as read: channel is no longer active');
            return;
        }

        const now = Date.now();
        // Check if we're within the rate limit window
        if (now - lastMarkReadRef.current < RATE_LIMIT_WINDOW) {
            // If a timeout is already scheduled, don't schedule another one
            if (markReadTimeoutRef.current) return;

            // Schedule the markRead call for after the rate limit window
            const delay = RATE_LIMIT_WINDOW - (now - lastMarkReadRef.current);
            markReadTimeoutRef.current = setTimeout(() => {
                // Check again if client is still connected before proceeding
                if (client && client.userID && !isDisconnected) {
                    markReadWithRetry();
                }
                markReadTimeoutRef.current = undefined;
            }, delay);
            return;
        }

        try {
            // Final check before making the API call
            if (!client.userID || isDisconnected) {
                console.log('Aborting markRead: client disconnected before API call');
                return;
            }

            await channel.markRead();
            lastMarkReadRef.current = Date.now();
            retryCountRef.current = 0; // Reset retry count on success
        } catch (error: any) {
            console.error('Error marking channel as read:', error);

            // Check if it's a disconnection error
            if (error?.message?.includes('disconnect') || error?.message?.includes('after client.disconnect()')) {
                console.log('Client disconnected, aborting markRead retries');
                return;
            }

            // Check if it's a rate limit error (code 9)
            if (error?.code === 9 && retryCountRef.current < MAX_RETRIES) {
                retryCountRef.current++;
                const delay = RETRY_DELAY * Math.pow(2, retryCountRef.current - 1); // Exponential backoff

                // Schedule retry with exponential backoff
                setTimeout(() => {
                    // Check again if client is still connected before retrying
                    if (client && client.userID && !isDisconnected) {
                        markReadWithRetry();
                    }
                }, delay);
            }
        }
    }, [channel, client, isDisconnected]);

    useEffect(() => {
        // Check if we have a valid channel, client, and the client is connected
        if (!channel || !client || !client.userID || isDisconnected) return;

        // We only want to mark the channel as read when it's explicitly selected by the user
        // Get this information from the channel's metadata or a ref
        const selectedChannel = localStorage.getItem('lastSelectedChannel');
        userSelectedRef.current = selectedChannel === channel.id;

        // Only mark as read if explicitly selected by user action and client is connected
        if (client.userID && !isDisconnected) {
            markReadWithRetry();
        }

        // Set up a mounted ref to track component lifecycle
        const isMounted = { current: true };

        return () => {
            // Mark component as unmounted
            isMounted.current = false;

            // Clear any pending timeouts
            if (markReadTimeoutRef.current) {
                clearTimeout(markReadTimeoutRef.current);
                markReadTimeoutRef.current = undefined;
            }
        };
    }, [channel, client, markReadWithRetry, isDisconnected]);

    return null;
};

type ChatWindowProps = {
    showTestControls?: boolean;
    showApproveReject?: boolean;
    containerStyles?: SxProps;
    channelListHeight?: string;
    messageListHeight?: string;
    onPrescribe?: () => void;
    onReject?: () => void;
    reloadForm?: boolean;
    setReloadForm?: (value: boolean) => void;
    isDisabled?: boolean;
}

// Define a proper workflow state type for clarity
type PrescriptionWorkflowState = 'treatment-form' | 'strain-advice' | 'email-confirmation' | 'completed';

const ChatWindow: React.FC<ChatWindowProps> = ({
    showTestControls = false,
    showApproveReject = false,
    containerStyles,
    channelListHeight = '600px',
    messageListHeight = '60vh',
    onPrescribe,
    isDisabled = false
}) => {

    // Early return if chat is disabled
    if (isDisabled) {
        return null;
    }

    // Add state to control channel list visibility
    const [showChannelList, setShowChannelList] = useState(true);
    const [selectedChannel, setSelectedChannel] = useState<StreamChannel<DefaultStreamChatGenerics> | undefined>(undefined);
    const [testResult, setTestResult] = useState<string>("");
    const [_totalUnread, setTotalUnread] = useState(0);
    const [isLoading] = useState(false);
    const [chatPatient, setChatPatient] = useState<any>(null);
    const [previousPlans, setPreviousPlans] = useState<any[]>([]);
    const [treatmentPlansLoading, setTreatmentPlansLoading] = useState(false);

    // Replace showEmailConfirmation with a more explicit workflow state
    const [workflowState, setWorkflowState] = useState<PrescriptionWorkflowState>('treatment-form');

    // Use a single component instance key that changes whenever we need to reset
    const [componentKey, setComponentKey] = useState(0);

    // Add a ref to track if we're currently in the process of resetting
    const isResettingRef = useRef(false);

    // Snackbar state
    const [showSnackbar, setShowSnackbar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

    // Use our custom hooks
    const { loading, error, client, userId, originalUserId, createChannel, isDisconnected } = useStreamChat();

    // Add a ref to track the last selected channel ID
    const lastChannelIdRef = useRef<string | undefined>(undefined);

    // Add a ref to cache patient info
    const patientCacheRef = useRef<{ [patientId: string]: any }>({});

    // Add after other useState hooks in ChatWindow
    const [allChannels, setAllChannels] = useState<StreamChannel<DefaultStreamChatGenerics>[]>([]);
    const [_seenChannelIds, setSeenChannelIds] = useState<string[]>([]);

    // Add state for message filter - 'all' or 'unread'
    const [messageFilter, setMessageFilter] = useState<'all' | 'unread'>('unread');

    // Add state for unread messages in selected channel
    const [_unreadCount, setUnreadCount] = useState(0);

    // Add state for hidden message IDs (unapproved patient messages)
    const [hiddenMessageIds, setHiddenMessageIds] = useState<Set<string>>(new Set());

    // State for conversation visibility (approved conversations)
    const [conversationVisibility, setConversationVisibility] = useState<Record<string, boolean>>({});

    // Get auth data at component level
    const { doctor } = useAuth();

    // Function to check conversation visibility
    const checkConversationVisibility = async (channels: StreamChannel<DefaultStreamChatGenerics>[]) => {
        try {
            const channelIds = channels.map(channel => channel.id).filter((id): id is string => !!id);

            if (channelIds.length === 0) {
                setConversationVisibility({});
                return;
            }

            // Check conversation visibility
            const response = await chatService.checkConversationVisibility(channelIds);
            setConversationVisibility(response.data);
        } catch (error) {
            console.error('Error checking conversation visibility:', error);
            // On error, assume all conversations are hidden for safety
            const hiddenMap: Record<string, boolean> = {};
            channels.forEach(channel => {
                if (channel.id) {
                    hiddenMap[channel.id] = false;
                }
            });
            setConversationVisibility(hiddenMap);
        }
    };

    // Function to check message visibility for channel previews
    const checkChannelMessageVisibility = async (channels: StreamChannel<DefaultStreamChatGenerics>[]) => {
        try {
            // First check conversation visibility
            await checkConversationVisibility(channels);

            // Collect all patient message IDs from all channels
            const allPatientMessageIds: string[] = [];

            channels.forEach(channel => {
                const messages = channel.state.messages || [];
                messages.forEach(msg => {
                    if (msg.user?.id?.startsWith('p_') && msg.id) {
                        allPatientMessageIds.push(msg.id);
                    }
                });
            });

            if (allPatientMessageIds.length > 0) {
                const visibilityMap = await chatService.checkMessageVisibility(allPatientMessageIds);

                const newHiddenIds = new Set<string>();
                Object.entries(visibilityMap).forEach(([messageId, isVisible]) => {
                    if (!isVisible) {
                        newHiddenIds.add(messageId);
                    }
                });

                setHiddenMessageIds(newHiddenIds);
            }
        } catch (error) {
            console.error('Error checking channel message visibility:', error);
        }
    };

    // Add state for WebSocket restart mechanism
    const [wsRestart, setWsRestart] = useState(false);

    // Listen for moderation events via WebSocket to refresh message visibility
    useEffect(() => {
        const handleModerationUpdate = () => {
            // Force a visibility check when moderation status changes
            if (allChannels.length > 0) {
                checkChannelMessageVisibility(allChannels);
            }
        };

        // Connect to WebSocket for real-time moderation updates
        const ws = new WebSocket(`${import.meta.env.VITE_WSS}`);

        ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);

                if (message.type === 'ping') {
                    ws.send(JSON.stringify({ type: 'pong' }));
                    return;
                }

                if (message.type === 'moderationStatusChanged') {
                    handleModerationUpdate();
                }
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        ws.onclose = () => {
            // Trigger restart to reconnect
            setWsRestart(!wsRestart);
        };

       

        return () => {
            // if (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN) {
            //     ws.close();
            // }
        };
    }, [wsRestart, allChannels.length]);

    // Track unread messages for selected channel
    useEffect(() => {
        if (!selectedChannel) {
            setUnreadCount(0);
            return;
        }
        const unread = selectedChannel.countUnread();
        setUnreadCount(unread);
        // Listen for new messages
        const handleEvent = () => {
            setUnreadCount(selectedChannel.countUnread());
        };
        selectedChannel.on('message.new', handleEvent);
        return () => {
            selectedChannel.off('message.new', handleEvent);
        };
    }, [selectedChannel]);

    // Extract patient information from channel
    const extractPatientFromChannel = useCallback(async (channel: StreamChannel<DefaultStreamChatGenerics>) => {
        if (!channel || !client) {
            return null;
        }

        let patientMember: any = undefined;

        try {
            const members = Object.values(channel.state.members);

            // Filter out the current user (doctor) to get the patient
            patientMember = members.find(member => {
                return member?.user_id && member.user_id.startsWith('p_');
            });

            if (!patientMember?.user_id) {
                return null;
            }

            // Remove 'p_' prefix from patient ID before making API call
            const patientId = patientMember.user_id.replace('p_', '');

            // Check cache first - if we have the patient data already, return it
            if (patientCacheRef.current[patientId]) {
                return patientCacheRef.current[patientId];
            }

            // Only fetch patient data here - treatment plans will be fetched separately when needed
            const patientData = await ApiClient.getPatientByIdFromDB(patientId).catch(() => null);

            // Fallback name logic
            let patientName = patientData?.fullName || `Patient ${patientId}`;

            const result = {
                ...(patientData || {}),
                patientID: patientId,
                fullName: patientName,
                // We'll set these properties later when we fetch treatment plans
                returningPatient: false,
                previousPlan: null,
            };

            // Cache for future use
            patientCacheRef.current[patientId] = result;
            return result;
        } catch (error) {
            console.error('Error extracting patient from channel:', error);
            return null;
        }
    }, [client, userId]);

    // Update UI when a channel is selected
    useEffect(() => {
        if (selectedChannel) {
            // Only reset if the channel actually changed
            if (
                lastChannelIdRef.current !== selectedChannel.id &&
                workflowState !== 'treatment-form' &&
                !isResettingRef.current
            ) {
                setWorkflowState('treatment-form');
                // Clear directSubmission flag when switching to a new patient
                // This ensures clean workflow state for each patient
                localStorage.removeItem('directSubmission');
            }
            lastChannelIdRef.current = selectedChannel.id;

            // Extract patient info from the selected channel
            extractPatientFromChannel(selectedChannel).then(async patient => {
                if (patient) {
                    setChatPatient(patient);

                    // Fetch treatment plans only once per channel selection
                    setTreatmentPlansLoading(true);
                    try {
                        // Check if we already have treatment plans for this patient in the cache
                        const patientId = patient.patientID;

                        // Use the single patient API to get detailed data including Zoho
                        const latestPlan = await ApiClient.fetchLatestTreatmentPlanByPatientId(patientId);

                        // Get the plans from the response
                        const plans = latestPlan ? [latestPlan] : [];

                        // Update patient with treatment plan info
                        const isReturningPatient = plans.length > 0;
                        const latestPreviousPlan = isReturningPatient ? plans[0] : null;

                        // Update the patient cache with treatment plan info
                        patientCacheRef.current[patientId] = {
                            ...patient,
                            returningPatient: isReturningPatient,
                            previousPlan: latestPreviousPlan
                        };

                        // Update state
                        setChatPatient(patientCacheRef.current[patientId]);
                        setPreviousPlans(plans);
                    } catch (error) {
                        console.error('Error fetching treatment plans:', error);
                    } finally {
                        setTreatmentPlansLoading(false);
                    }
                } else {
                    setPreviousPlans([]);
                }
            });
        } else {
            setChatPatient(null);
            setPreviousPlans([]);
            lastChannelIdRef.current = undefined;
        }
    }, [selectedChannel, extractPatientFromChannel, workflowState]);

    // Improved reset workflow with better logging and state management
    const resetWorkflow = useCallback(() => {
        if (isResettingRef.current) {

            return;
        }


        isResettingRef.current = true;

        // First mark as completed to prevent any unwanted state transitions
        setWorkflowState('completed');

        // Clear all form-related localStorage items
        try {
            // List of specific keys we want to ensure are removed
            const specificKeys = [
                'TpPlan',
                'chatEmailData',
                'treatmentPlanFormData',
                'emailStep',
                'strainAdvice',
                'directSubmission',
                'bypassed_email'
            ];

            // Remove specific keys first
            specificKeys.forEach(key => {
                if (localStorage.getItem(key) !== null) {
                    localStorage.removeItem(key);

                }
            });

            // Then look for any other related keys
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (
                    key.toLowerCase().includes('treatment') ||
                    key.toLowerCase().includes('email') ||
                    key.toLowerCase().includes('plan') ||
                    key.toLowerCase().includes('strain') ||
                    key.toLowerCase().includes('chat')
                )) {
                    keysToRemove.push(key);
                }
            }

            // Remove them after collecting to avoid index issues
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);

            });


        } catch (err) {
            console.error('Error clearing localStorage:', err);
        }

        // Force a complete remount by changing the component key
        setComponentKey(prev => prev + 1);

        // After a delay, reset to the initial form state
        setTimeout(() => {
            setWorkflowState('treatment-form');

            // Reset our flag after everything is done
            setTimeout(() => {
                isResettingRef.current = false;
            }, 100);
        }, 500);
    }, []);

    // When back is pressed, show the channel list and clear selected channel
    const handleBackToChannelList = () => {
        setSelectedChannel(undefined);
        setShowChannelList(true);

        // Refresh channels to update unread counts after reading messages
        if (fetchChannels) {
            fetchChannels();
        }
    };

    // Specific handler for returning to channel list after treatment plan completion
    const handleReturnToChannelListAfterTreatment = () => {
        setSelectedChannel(undefined);
        setShowChannelList(true);
        // Reset filter to unread when returning after treatment plan completion
        setMessageFilter('unread');

        // Refresh channels to update unread counts
        if (fetchChannels) {
            fetchChannels();
        }
    };

    const handleTreatmentFormSubmit = useCallback(() => {
        // Check if this is a direct submission from "Confirm Same"
        const isDirectSubmission = localStorage.getItem('directSubmission') === 'true';

        // If it's a direct submission, skip strain-advice and go to email-confirmation
        if (isDirectSubmission) {
            // Clear the flag
            localStorage.removeItem('directSubmission');

            // Skip to email-confirmation since the plan has already been submitted
            setWorkflowState('email-confirmation');
            return;
        }

        // Regular flow for non-direct submissions
        // Save the current treatment plan data
        const savedPlan = localStorage.getItem('TpPlan');
        if (savedPlan) {
            try {
                const plan = JSON.parse(savedPlan) as PatientTreatmentPlan;
                // If we have Zoho data, include it in the plan
                if (chatPatient?.previousPlan?.treatmentPlan) {
                    plan.treatmentPlan = chatPatient.previousPlan.treatmentPlan;
                }
                localStorage.setItem('TpPlan', JSON.stringify(plan));
            } catch (err) {
                console.error('Error updating treatment plan with Zoho data:', err);
            }
        }
        setWorkflowState('strain-advice');
    }, [chatPatient]);

    const handlePrescribeSuccess = useCallback(() => {
        // Show success message
        setSnackbarMessage('Treatment plan created successfully');
        setSnackbarSeverity('success');
        setShowSnackbar(true);

        // Reset the workflow with a delay to ensure proper message display
        setTimeout(() => {
            resetWorkflow();
            // Return to channel list after successful prescription
            handleBackToChannelList();
            // Reset filter to unread to focus on messages requiring attention
            setMessageFilter('unread');
        }, 500);

        // Call the callback if provided
        if (onPrescribe) {
            onPrescribe();
        }
    }, [onPrescribe, resetWorkflow, handleBackToChannelList]);

    // Function to test channel creation
    const handleTestCreateChannel = async () => {
        try {
            setTestResult("Creating channel...");

            // Always use "harvest" as the doctor ID
            const doctorId = "harvest";  // Fixed doctor ID
            const patientId = "test-patient-123"; // This would normally come from your patient selection

            // Try to fetch patient data for the name
            let patientName = "Test Patient";
            try {
                // For a real patient, we would use:
                // const patientData = await ApiClient.getPatientById(patientId);
                // if (patientData?.fullName) {
                //     patientName = patientData.fullName;
                // } else if (patientData?.firstName) {
                //     patientName = patientData.firstName + (patientData.lastName ? ` ${patientData.lastName}` : '');
                // }

                // For test channel, just use test name
                patientName = "Test Patient";
            } catch (error) {
                console.error('Error fetching patient data:', error);
                // Proceed with default name if fetch fails
            }

            const channel = await createChannel(
                doctorId,
                patientId,
                `Chat with ${patientName}`,
                'doctor',  // First user is a doctor
                'patient' // Second user is a patient
            );

            if (channel) {
                setTestResult(`Channel created successfully: ${channel.id}`);
                setSelectedChannel(channel);
            } else {
                setTestResult("Failed to create channel");
            }
        } catch (err) {
            console.error("Error creating test channel:", err);
            let errorMessage = err instanceof Error ? err.message : String(err);

            // If it's an Axios error, try to extract more details
            if (err && typeof err === 'object' && 'response' in err) {
                const axiosError = err as any;
                if (axiosError.response?.data) {
                    errorMessage += ` - Server response: ${JSON.stringify(axiosError.response.data)}`;
                }
            }

            setTestResult(`Error: ${errorMessage}`);
        }
    };

    // Function to mark all channels as read
    const handleMarkAllRead = useCallback(async () => {
        // Check if client exists and is connected
        if (!client || !client.userID || isDisconnected) return;

        try {
            // Get all channels for the current user
            const filter = { type: 'messaging', members: { $in: [userId || ''] } };
            const sort = { last_message_at: -1 } as const;

            // Check again if client is still connected before querying channels
            if (!client.userID || isDisconnected) {
                //console.log('Client disconnected before querying channels');
                return;
            }

            const channels = await client.queryChannels(filter, sort);

            // Check again if client is still connected before marking channels as read
            if (!client.userID || isDisconnected) {
                //console.log('Client disconnected before marking channels as read');
                return;
            }

            // Mark each channel as read individually (markAllRead is deprecated)
            // Use a more resilient approach with individual try/catch blocks
            // Only mark approved conversations as read
            const markReadPromises = channels.map(async (channel) => {
                try {
                    // Check if client is still connected before marking this channel as read
                    if (!client.userID || isDisconnected) return;

                    // Only mark approved conversations as read
                    if (!channel.id) return; // Skip channels without ID
                    const isVisible = conversationVisibility[channel.id];
                    if (isVisible === false) {
                        return; // Skip unapproved conversations
                    }

                    await channel.markRead();
                } catch (err) {
                    // Log but don't fail the entire operation
                    console.error(`Error marking channel ${channel.id} as read:`, err);
                }
            });

            await Promise.all(markReadPromises);

            // Only update UI if we're still connected
            if (!isDisconnected) {
                setTotalUnread(0);
            }
        } catch (error) {
            // Check if it's a disconnection error
            if (error instanceof Error &&
                (error.message.includes('disconnect') ||
                 error.message.includes('after client.disconnect()'))) {
                console.log('Client disconnected during markAllRead operation');
            } else {
                console.error('Error marking all channels as read:', error);
            }
        }
    }, [client, userId, isDisconnected, conversationVisibility]);

    // Clear selectedChannel if client disconnects
    useEffect(() => {
        if (!client || !client.userID) {
            setSelectedChannel(undefined);
        }
    }, [client]);

    // Update handle for selecting a channel to mark as seen
    const handleSelectChannel = (channel: StreamChannel<DefaultStreamChatGenerics>) => {
        setSelectedChannel(channel);
        setShowChannelList(false);
        if (channel.id) {
            setSeenChannelIds((prev) =>
                prev.includes(channel.id!) ? prev : [...prev, channel.id!]
            );
        }
    };

    // Add state for channel refresh loading
    const [refreshingChannels, setRefreshingChannels] = useState(false);

    // Move fetchChannels to useCallback so it can be reused
    const fetchChannels = useCallback(async () => {
        if (!client) return;
        try {
            setRefreshingChannels(true);
            const filter = { type: 'messaging', members: { $in: [userId] } };
            const sort = { last_message_at: -1 } as const;
            const channels = await client.queryChannels(filter, sort, options);

            // Extract all patient IDs first
            const patientIds: string[] = [];
            const channelsByPatientId: Record<string, StreamChannel<DefaultStreamChatGenerics>> = {};
            const patientDataPromises: Promise<any>[] = [];

            for (const channel of channels) {
                const members = Object.values(channel.state.members || {});
                const patientMember = members.find(m => m.user?.id?.startsWith('p_'));
                if (!patientMember?.user?.id) continue;

                const patientId = patientMember.user.id.replace('p_', '');
                patientIds.push(patientId);
                channelsByPatientId[patientId] = channel;

                // If we don't have this patient in our cache yet, fetch their data
                if (!patientCacheRef.current[patientId]) {
                    const promise = ApiClient.getPatientByIdFromDB(patientId)
                        .then(patientData => {
                            if (patientData) {
                                // Store in cache with proper name
                                patientCacheRef.current[patientId] = {
                                    ...patientData,
                                    patientID: patientId,
                                    fullName: patientData.fullName || `Patient ${patientId}`,
                                    returningPatient: false,
                                    previousPlan: null
                                };
                            }
                        })
                        .catch(err => {
                            console.error(`Error fetching patient data for ${patientId}:`, err);
                        });

                    patientDataPromises.push(promise);
                }
            }

            // Wait for all patient data to be fetched
            await Promise.all(patientDataPromises);

            // If no patients found, return early
            if (patientIds.length === 0) {
                setAllChannels([]);
                return;
            }

            // Fetch all treatment plans in a single batch request
            const drIdLocal = typeof window !== 'undefined' ? localStorage.getItem('xdr') : undefined;
            const currentDoctorId = doctor?.accessID || drIdLocal || '';

            // Import chatService from services
            const { chatService } = await import('../../../services');

            const treatmentPlansMap = await chatService.fetchLatestTreatmentPlansForPatients(patientIds);

            // Filter channels by latest treatment plan's drId
            const filteredChannels: StreamChannel<DefaultStreamChatGenerics>[] = [];

            for (const patientId of patientIds) {
                const latestPlan = treatmentPlansMap[patientId];
                const channel = channelsByPatientId[patientId];

                // Only include if drId matches current doctor
                if (latestPlan && latestPlan.drId === currentDoctorId) {
                    filteredChannels.push(channel);
                }
            }

            setAllChannels(filteredChannels);

            // Check message visibility for channel previews
            await checkChannelMessageVisibility(filteredChannels);
        } catch (err) {
            console.error('Error fetching channels:', err);
        } finally {
            setRefreshingChannels(false);
        }
    }, [client, userId, doctor]);

    // Update useEffect to use fetchChannels and call it on relevant events
    useEffect(() => {
        if (!client || !userId) return;

        // Initial fetch of channels
        fetchChannels();

        // Listen for new messages and channel updates
        const handleEvent = (event: Event<DefaultStreamChatGenerics>) => {
            // If a new message is from a patient, remove the channel from seenChannelIds
            if (event.type === 'message.new' && event.user?.id?.startsWith('p_')) {
                const channelId = event.cid?.split(':')[1];
                if (channelId) {
                    setSeenChannelIds((prev) => prev.filter(id => id !== channelId));
                }
            }
            // Always re-fetch channels to update counts and lists
            fetchChannels();
        };

        // Handle when doctor is added to a new channel
        const handleAddedToChannel = (_event: Event<DefaultStreamChatGenerics>) => {

            // Fetch channels immediately when added to a new one
            fetchChannels();
        };

        // Register event listeners
        client.on('message.new', handleEvent);
        client.on('channel.updated', handleEvent);
        client.on('message.updated', handleEvent);
        client.on('message.deleted', handleEvent);

        // This is the critical event for new channels
        client.on('notification.added_to_channel', handleAddedToChannel);

        // Set up a periodic refresh as a fallback mechanism
        // This ensures we catch any channels that might have been missed by event listeners
        const refreshInterval = setInterval(() => {
            if (client && client.userID && !isDisconnected) {
                fetchChannels();
            }
        }, 120000); // Refresh every 2 minutes

        // Set up periodic message visibility check
        const visibilityCheckInterval = setInterval(() => {
            if (client && client.userID && !isDisconnected && allChannels.length > 0) {
                checkChannelMessageVisibility(allChannels);
            }
        }, 10000); // Check message visibility every 10 seconds

        // Clean up event listeners and interval on unmount
        return () => {
            client.off('message.new', handleEvent);
            client.off('channel.updated', handleEvent);
            client.off('message.updated', handleEvent);
            client.off('message.deleted', handleEvent);
            client.off('notification.added_to_channel', handleAddedToChannel);
            clearInterval(refreshInterval);
            clearInterval(visibilityCheckInterval);
        };
    }, [client, userId, fetchChannels]);

    // Calculate unread count from allChannels, considering hidden messages
    const calculateChannelUnreadCount = useCallback((channel: StreamChannel<DefaultStreamChatGenerics>) => {
        if (!userId) return 0;

        try {
            const messages = channel.state.messages || [];
            const lastReadMessageId = channel.state.read[userId]?.last_read_message_id;

            // Filter function to exclude hidden patient messages
            const isVisibleMessage = (msg: any) => {
                // Always count doctor messages
                if (!msg.user?.id?.startsWith('p_')) {
                    return true;
                }
                // For patient messages, exclude if they're hidden (unapproved)
                return !msg.id || !hiddenMessageIds.has(msg.id);
            };

            // If no messages have been read, count only visible messages not from current user
            if (!lastReadMessageId) {
                return messages.filter(msg =>
                    msg.user?.id !== userId && isVisibleMessage(msg)
                ).length;
            }

            // Find the index of the last read message
            const lastReadIndex = messages.findIndex(msg => msg.id === lastReadMessageId);

            // If the last read message is found, count unread visible messages not from current user
            if (lastReadIndex !== -1) {
                const unreadMessages = messages.slice(lastReadIndex + 1);
                return unreadMessages.filter(msg =>
                    msg.user?.id !== userId && isVisibleMessage(msg)
                ).length;
            }

            // If last read message not found, count all visible messages not from current user
            return messages.filter(msg =>
                msg.user?.id !== userId && isVisibleMessage(msg)
            ).length;
        } catch (err) {
            console.error('Error calculating channel unread count:', err);
            return 0;
        }
    }, [userId, hiddenMessageIds]);

    const unreadCountDisplay = useMemo(() => {
        return allChannels.filter(channel => {
            // Only count unread messages from approved conversations
            if (!channel.id) return false; // Skip channels without ID
            const isVisible = conversationVisibility[channel.id];
            if (isVisible === false) {
                return false; // Skip unapproved conversations
            }
            return calculateChannelUnreadCount(channel) > 0;
        }).length;
    }, [allChannels, calculateChannelUnreadCount, conversationVisibility]);

    // Add i18n instance with timezone
    const i18n = new Streami18n({
        language: 'en',
        timezone: 'Australia/Sydney',
    });

    // Show loading state
    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 4 }}>
                <CircularProgress />
            </Box>
        );
    }

    // Show error state or disconnected state
    if (isDisconnected) {
        return (
            <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="error">Chat disconnected. Please reload the chat or log in again.</Typography>
                <Button
                    variant="contained"
                    sx={{ mt: 2 }}
                    onClick={() => setComponentKey(prev => prev + 1)}
                >
                    Reload Chat
                </Button>
            </Box>
        );
    }
    if (error || !client) {
        return (
            <Box sx={{ p: 3, textAlign: 'center' }}>
                <Typography color="error">{error || "Unable to initialize chat"}</Typography>
                <Button
                    variant="contained"
                    sx={{ mt: 2 }}
                    onClick={() => window.location.reload()}
                >
                    Retry
                </Button>
            </Box>
        );
    }

    // Render chat interface when client is ready
    return (
        <Box sx={containerStyles}>
            {showTestControls && (
                <Box sx={{ mb: 2, p: 2, border: '1px solid #eee', borderRadius: 2 }}>
                    <Typography variant="h6">Test Controls</Typography>
                    <Typography variant="body2">Connected as: {userId}</Typography>
                    {originalUserId && userId !== originalUserId && (
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                            Original ID: {originalUserId}
                        </Typography>
                    )}
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={handleTestCreateChannel}
                        sx={{ mt: 1 }}
                    >
                        Create Test Channel
                    </Button>
                    {testResult && (
                        <Typography
                            variant="body2"
                            sx={{ mt: 1, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}
                        >
                            {testResult}
                        </Typography>
                    )}
                </Box>
            )}

            {isLoading && (
                <Box sx={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    zIndex: 9999,
                }}>
                    <CircularProgress />
                </Box>
            )}

            <Snackbar
                open={showSnackbar}
                autoHideDuration={8000}
                onClose={() => setShowSnackbar(false)}
                anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            >
                <Alert
                    onClose={() => setShowSnackbar(false)}
                    severity={snackbarSeverity}
                    sx={{ width: '100%' }}
                >
                    {snackbarMessage}
                </Alert>
            </Snackbar>

            <Chat client={client} i18nInstance={i18n}>
                <Grid container direction="column" spacing={2}>
                    <Grid size={12}>
                        {showChannelList && (
                            <>
                                <Grid sx={{ width: '100%', mb: 2 }} container alignItems="center">
                                    <Grid sx={{ flexGrow: 1 }}>
                                        <Typography align='left' sx={{ fontWeight: "700", fontSize: '24px', ml: 1, mt: 2, display: 'flex', alignItems: 'center' }}>
                                            Chats
                                            <span style={{ fontWeight: 400, fontSize: '16px', marginLeft: 12, color: '#666' }}>
                                                (All: {allChannels.filter(channel => channel.id && conversationVisibility[channel.id] !== false).length} | Unread: {unreadCountDisplay})
                                            </span>
                                        </Typography>
                                    </Grid>
                                    <Grid>
                                        {/* Refresh button */}
                                        <IconButton
                                            size="small"
                                            onClick={() => fetchChannels()}
                                            title="Refresh channels"
                                            sx={{ mr: 1 }}
                                            disabled={refreshingChannels}
                                        >
                                            {refreshingChannels ?
                                                <CircularProgress size={20} /> :
                                                <RefreshIcon />
                                            }
                                        </IconButton>

                                        {/* Mark all as read button */}
                                        <Badge badgeContent={unreadCountDisplay} color="error" sx={{ mr: 1, '& .MuiBadge-badge': { top: 8, right: 8 } }}>
                                            <IconButton
                                                size="small"
                                                onClick={handleMarkAllRead}
                                                disabled={unreadCountDisplay === 0}
                                                title="Mark all as read"
                                            >
                                                <NotificationsIcon color="action" />
                                            </IconButton>
                                        </Badge>
                                    </Grid>
                                </Grid>
                                <Box sx={{ mb: 1, display: 'flex', justifyContent: 'flex-end', mt: -2, gap: 1 }}>
                                    <Button
                                        variant={messageFilter === 'all' ? 'contained' : 'outlined'}
                                        color="primary"
                                        size="small"
                                        onClick={() => setMessageFilter('all')}
                                    >
                                        All
                                    </Button>
                                    <Button
                                        variant={messageFilter === 'unread' ? 'contained' : 'outlined'}
                                        color="primary"
                                        size="small"
                                        onClick={() => setMessageFilter('unread')}
                                    >
                                        Unread
                                    </Button>
                                </Box>
                                <Grid sx={{ height: channelListHeight, minHeight: '600px', overflow: 'auto' }}>
                                    <Paper elevation={1} sx={{ p: 1, height: '100%' }}>
                                        {/* Custom channel list rendering */}
                                        {(() => {
                                            // Filter channels based on messageFilter and conversation visibility
                                            let filteredChannels = allChannels.filter(channel => {
                                                // First filter by conversation visibility (only show approved conversations)
                                                if (!channel.id) return false; // Skip channels without ID
                                                const isVisible = conversationVisibility[channel.id];
                                                if (isVisible === false) {
                                                    return false; // Hide unapproved conversations
                                                }

                                                // Then filter by message filter
                                                if (messageFilter === 'unread') {
                                                    return calculateChannelUnreadCount(channel) > 0;
                                                }

                                                return true; // Show for 'all' filter
                                            });

                                            if (filteredChannels.length === 0) {
                                                return (
                                                    <Typography sx={{ p: 2, color: 'text.secondary' }}>
                                                        {messageFilter === 'unread' ? 'No unread messages.' : 'No messages.'}
                                                    </Typography>
                                                );
                                            }

                                            const list = filteredChannels;
                                            return list.map((channel: StreamChannel<DefaultStreamChatGenerics>) => {
                                                // Compute displayTitle and latestMessagePreview as ChannelList would
                                                const members = Object.values(channel.state.members || {});
                                                const patientMember = members.find(m => m.user?.id?.startsWith('p_'));

                                                // Get patient ID without prefix for database lookup
                                                const patientId = patientMember?.user?.id?.replace('p_', '');

                                                // Check if we have this patient in our cache
                                                const cachedPatient = patientId ? patientCacheRef.current[patientId] : null;

                                                // Use cached patient name if available, otherwise use GetStream name
                                                const displayTitle =
                                                    (cachedPatient?.fullName) ||
                                                    patientMember?.user?.name ||
                                                    patientMember?.user?.id ||
                                                    'Unknown';

                                                const messages = channel.state.messages || [];

                                                // Filter out unapproved patient messages for doctors
                                                const visibleMessages = messages.filter(msg => {
                                                    // Always show doctor messages
                                                    if (!msg.user?.id?.startsWith('p_')) {
                                                        return true;
                                                    }

                                                    // For patient messages, check if they're hidden (unapproved)
                                                    if (msg.id && hiddenMessageIds.has(msg.id)) {
                                                        return false; // Hide unapproved patient messages
                                                    }

                                                    return true; // Show approved patient messages
                                                });

                                                const lastMessage = visibleMessages[visibleMessages.length - 1];
                                                const latestMessagePreview = lastMessage?.text || '';

                                                return (
                                                    <CustomChannelPreview
                                                        key={channel.id}
                                                        channel={channel}
                                                        activeChannel={selectedChannel}
                                                        setActiveChannel={setSelectedChannel}
                                                        setSelectedChannel={handleSelectChannel}
                                                        displayTitle={displayTitle}
                                                        latestMessagePreview={latestMessagePreview}
                                                        user={patientMember?.user}
                                                        hiddenMessageIds={hiddenMessageIds}
                                                    />
                                                );
                                            });
                                        })()}
                                    </Paper>
                                </Grid>
                            </>
                        )}
                        {selectedChannel && client.userID ? (
                            <Channel
                                channel={selectedChannel}
                                markReadOnMount={false}
                                doMarkReadRequest={(_channel) => undefined}
                            >
                                <MessageReadHandler isDisconnected={isDisconnected} />
                                <Window>
                                    {/* Back button and notification badge in chat header */}
                                    <Box sx={{ display: 'flex', alignItems: 'center', p: 1 }}>
                                        <Button variant="outlined" size="small" onClick={handleBackToChannelList} sx={{ mr: 2 }}>
                                            Back
                                        </Button>
                                        <ChannelHeader />
                                        {/* Notification badge for unread messages, now at the right */}
                                        <Box sx={{ flexGrow: 1 }} />
                                        <Badge color="error" badgeContent={unreadCountDisplay} invisible={unreadCountDisplay === 0} sx={{ ml: 2 }}>
                                            <NotificationsIcon color="action" />
                                        </Badge>
                                    </Box>
                                    <Box sx={{ height: messageListHeight, overflow: 'auto' }}>
                                        <CustomMessageList userRole="doctor" />
                                    </Box>
                                    <CustomMessageInput
                                        onMessageSend={async (text) => {
                                            if (chatPatient && chatPatient.zohoID && text) {
                                                try {
                                                    await ApiClient.updateLastDoctorMessage(chatPatient.zohoID, text.slice(0, 50), false);
                                                } catch (err) {
                                                    console.error('Error updating doctor message:', err);
                                                }
                                            }
                                        }}
                                    />
                                    {showApproveReject && chatPatient && (
                                        <Box sx={{ p: 2, borderTop: '1px solid #eee' }}>
                                            <div key={`prescription-flow-${componentKey}`}>
                                                {workflowState === 'treatment-form' && (
                                                    <ChatTreatmentPlanForm
                                                        patientId={chatPatient.patientID}
                                                        patientName={chatPatient.fullName}
                                                        patientEmail={chatPatient.email}
                                                        patientDOB={chatPatient.dob}
                                                        patientState={chatPatient.state}
                                                        isReturningPatient={chatPatient.returningPatient}
                                                        onSubmit={handleTreatmentFormSubmit}
                                                        onCancel={() => {}}
                                                        existingTreatmentPlans={previousPlans}
                                                        isLoadingPlans={treatmentPlansLoading}
                                                        onComplete={resetWorkflow}
                                                        onNotification={(message, severity) => {
                                                            setSnackbarMessage(message);
                                                            setSnackbarSeverity(severity);
                                                            setShowSnackbar(true);
                                                        }}
                                                        onReturnToChannelList={handleReturnToChannelListAfterTreatment}
                                                    />
                                                )}

                                                {workflowState === 'strain-advice' && (
                                                    <EmailProvider>
                                                        <ChatEmailProvider>
                                                            <ChatStrainAdvice onComplete={() => {
                                                                // Check if email confirmation was bypassed by the strain advice component
                                                                if (localStorage.getItem('bypassed_email') === 'true') {
                                                                    // Clear the flag
                                                                    localStorage.removeItem('bypassed_email');
                                                                    // Show success notification
                                                                    setSnackbarMessage('Treatment plan submitted successfully');
                                                                    setSnackbarSeverity('success');
                                                                    setShowSnackbar(true);
                                                                    // Reset workflow to go back to form
                                                                    resetWorkflow();
                                                                    // Return to channel list
                                                                    handleBackToChannelList();
                                                                    // Reset filter to unread to focus on messages requiring attention
                                                                    setMessageFilter('unread');
                                                                } else {
                                                                    // Normal flow to email confirmation
                                                                    setWorkflowState('email-confirmation');
                                                                }
                                                            }} />
                                                        </ChatEmailProvider>
                                                    </EmailProvider>
                                                )}

                                                {workflowState === 'email-confirmation' && (
                                                    <EmailProvider>
                                                        <ChatEmailProvider>
                                                            <ChatEmailConfirmation
                                                                onComplete={handlePrescribeSuccess}
                                                                isDirectConfirmation={localStorage.getItem('directSubmission') === 'true'}
                                                            />
                                                        </ChatEmailProvider>
                                                    </EmailProvider>
                                                )}

                                                {workflowState === 'completed' && (
                                                    <Box sx={{
                                                        display: 'flex',
                                                        justifyContent: 'center',
                                                        alignItems: 'center',
                                                        p: 2,
                                                        height: '100px'
                                                    }}>
                                                        <CircularProgress size={30} />
                                                    </Box>
                                                )}
                                            </div>
                                        </Box>
                                    )}
                                </Window>
                                <Thread />
                            </Channel>
                        ) : (
                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 4, border: '1px dashed #ccc', borderRadius: 2 }}>
                                <Typography variant="body1" color="textSecondary">
                                    Select a conversation to start chatting
                                </Typography>
                            </Box>
                        )}
                    </Grid>
                </Grid>
            </Chat>
        </Box>
    );
};

export default ChatWindow;
