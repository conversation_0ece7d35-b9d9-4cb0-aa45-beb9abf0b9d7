/// <reference types="vite-plugin-svgr/client" />

import { AppBar, Toolbar } from "@mui/material"
import ZenithLogo from '../../assets/Zenith.svg?react'

const NavigationBar: React.FC = () => {

    return (
        <>
            <AppBar position="fixed" elevation={0} sx={{
                backgroundColor: 'green',
                zIndex: 1
            }}>
                <Toolbar>
                    <ZenithLogo width={'200'} />
                    <Toolbar sx={{ flexGrow: 1 }} />
                </Toolbar>
            </AppBar>
        </>
    )
}

export default NavigationBar