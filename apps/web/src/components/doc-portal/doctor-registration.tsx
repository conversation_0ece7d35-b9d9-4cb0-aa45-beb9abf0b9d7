import { Button, FormControl, FormHelperText, InputLabel, MenuItem, Select, SelectChangeEvent, TextField, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useEffect, useState } from "react";
import { Dr } from "../../types";
import { ApiClient } from "../../services";
import { SupabaseClient } from "../../services/supabase-client";
import LoadingScreen from "../../utils/loading-screen";

const inputStyle = {
    '& .MuiOutlinedInput-root': {
        '&:hover': {
            borderColor: 'green',
        },
        '&.Mui-focused fieldset': {
            borderColor: 'green',
        },
    },
    '& .MuiInputLabel-root': {
        color: '#3B3B3B',
    },
    '& .MuiInputLabel-root.Mui-focused': {
        color: 'green',
    },
}

const DoctorRegistration: React.FC = () => {
    const [allDoctors, setAllDoctors] = useState<Dr[]>([])
    const [newDoctor, setNewDoctor] = useState<Partial<Dr>>({})
    const [errorMessage, setErrorMessage] = useState<Partial<Dr>>({})
    const [isLoading, setIsLoading] = useState(false)

    const handleNewDoctorFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent) => {
        const { name, value } = e.target
        setNewDoctor((prev) => {
            return {
                ...prev,
                [name]: value
            }
        })

        setErrorMessage({})
    }

    const submitNewDoctor = async () => {

        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

        if (!newDoctor.email || newDoctor.email.trim().length <= 0) {
            setErrorMessage((prev) => {
                return {
                    ...prev,
                    email: 'Email is Required'
                }

            })

            return
        }

        if (newDoctor.email && !emailRegex.test(newDoctor.email)) {
            setErrorMessage((prev) => {
                return {
                    ...prev,
                    email: 'Wrong email pattern'
                }

            })

            return
        }

        if (!newDoctor.password || newDoctor.password.trim().length <= 0) {
            setErrorMessage((prev) => {
                return {
                    ...prev,
                    password: 'Password is Required'
                }

            })
            return
        }

        if (newDoctor.password.trim().length <= 3) {
            setErrorMessage((prev) => {
                return {
                    ...prev,
                    password: 'Password too short. Provide a password with more than 3 characters'
                }

            })
            return
        }

        if (newDoctor.password !== newDoctor.confirmPassword) {
            setErrorMessage((prev) => {
                return {
                    ...prev,
                    password: 'Password do not match',
                    confirmPassword: 'Password do not match'
                }

            })
            return
        }

        if (!newDoctor.role) {
            setErrorMessage((prev) => {
                return {
                    ...prev,
                    role: 'admin'
                }

            })
            return
        }

        if (newDoctor.email && newDoctor.confirmPassword) {
            setIsLoading(true)
            try {
                const supabaseRegistration = await SupabaseClient.signUpWithEmail(newDoctor.email, newDoctor.confirmPassword)
                if (supabaseRegistration?.user?.id) {
                    const doctorInfoWithAccessId = { ...newDoctor, accessID: supabaseRegistration.user.id }
                    await ApiClient.postDoctorData(doctorInfoWithAccessId)
                    const result = await ApiClient.getDoctors()
                    setAllDoctors(() => {
                        return [...result]
                    })

                    setNewDoctor({})
                }
            }
            finally {
                setIsLoading(false)
            }
        }

    }

    useEffect(() => {
        const init = async () => {
            const result = await ApiClient.getDoctors()
            setAllDoctors(() => {
                return [...result]
            })
        }

        init()

    }, [allDoctors])

    return (
        <>
            {isLoading && <LoadingScreen />}

            <Grid container sx={{ width: '100%' }}>
                <Grid size={{ xs: 12, lg: 4 }} container sx={{ width: '100%' }} alignItems={'center'} direction={'column'}>
                    <Grid container direction={'column'} spacing={2} sx={{ width: '90%', mt: 4 }}>
                        <TextField sx={{ ...inputStyle }} value={newDoctor.email ?? ''} size="small" name="email" label='Email' onChange={handleNewDoctorFormChange} error={errorMessage['email'] ? true : false} helperText={errorMessage['email'] ? errorMessage['email'] : undefined} />
                        <TextField sx={{ ...inputStyle }} value={newDoctor.aphraNumber ?? ''} size="small" name="aphraNumber" label='Aphra Number' onChange={handleNewDoctorFormChange} error={errorMessage['aphraNumber'] ? true : false} helperText={errorMessage['aphraNumber'] ? errorMessage['aphraNumber'] : undefined} />
                        <TextField sx={{ ...inputStyle }} value={newDoctor.name ?? ''} size="small" name="name" label='Name' onChange={handleNewDoctorFormChange} />
                        <TextField sx={{ ...inputStyle }} value={newDoctor.username ?? ''} size="small" name="username" label='Username' onChange={handleNewDoctorFormChange} />
                        <TextField sx={{ ...inputStyle }} value={newDoctor.password ?? ''} size="small" name="password" type="password" label='Password' onChange={handleNewDoctorFormChange} error={errorMessage['password'] ? true : false} helperText={errorMessage['password'] ? errorMessage['password'] : undefined} />
                        <TextField sx={{ ...inputStyle }} value={newDoctor.confirmPassword ?? ''} size="small" name="confirmPassword" type="password" label='Confirm Password' onChange={handleNewDoctorFormChange} error={errorMessage['confirmPassword'] ? true : false} helperText={errorMessage['confirmPassword'] ? errorMessage['confirmPassword'] : undefined} />

                        <FormControl sx={{ ...inputStyle }} size="small">
                            <InputLabel>Role</InputLabel>
                            <Select value={newDoctor.role ?? ''} name="role" onChange={handleNewDoctorFormChange}>
                                <MenuItem key="doctor" value='doctor'>Doctor</MenuItem>
                                <MenuItem key="admin" value='admin'>Admin</MenuItem>
                            </Select>
                            <FormHelperText sx={{ color: errorMessage['role'] ? 'red' : 'grey' }}>{errorMessage['role'] ? 'User role is required' : undefined}</FormHelperText>
                        </FormControl>
                        <Button variant="contained" sx={{ backgroundColor: 'green' }} onClick={submitNewDoctor}>
                            Register A New User
                        </Button>
                    </Grid>
                </Grid>
                <Grid size={{ xs: 12, lg: 8 }} container sx={{ width: '100%' }} alignItems={'center'} direction={'column'}>
                    <Grid>
                        <Typography sx={{ fontSize: '24px', fontWeight: 'bold' }}>
                            User List
                        </Typography>
                    </Grid>
                    <Grid container sx={{ width: '100%', mt: 3, ml: 2 }} alignItems={'center'}>
                        <Grid sx={{ width: '100%', mt: 2, mb: 2 }} container alignItems={'center'} justifyContent={'center'}>
                            <Grid size={{ lg: 3 }} container justifyContent={'start'}>
                                <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                    Email
                                </Typography>
                            </Grid>

                            <Grid size={{ lg: 3 }} container justifyContent={'start'}>
                                <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                    APHRA Number
                                </Typography>
                            </Grid>

                            <Grid size={{ lg: 2 }} container justifyContent={'start'}>
                                <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                    Name
                                </Typography>
                            </Grid>
                            <Grid size={{ lg: 2 }} container justifyContent={'start'}>
                                <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                    UserName
                                </Typography>
                            </Grid>
                            <Grid size={{ lg: 1 }} container justifyContent={'start'}>
                                <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                    Status
                                </Typography>
                            </Grid>
                            <Grid size={{ lg: 1 }} container justifyContent={'start'}>
                                <Typography sx={{ fontSize: '12px', fontWeight: 'bold' }}>
                                    Role
                                </Typography>
                            </Grid>
                        </Grid>
                        <Grid sx={{
                            width: '100%',
                            overflow: 'auto',
                            height: '68dvh',
                            '&::-webkit-scrollbar': {
                                width: '6px',
                            },
                            '&::-webkit-scrollbar-track': {
                                backgroundColor: '#cbf5dd',
                                borderRadius: '10px',
                                mt: 1,
                                mb: 1
                            },
                            '&::-webkit-scrollbar-thumb': {
                                backgroundColor: 'green',
                                borderRadius: '10px',
                                p: 2
                            },
                            '&::-webkit-scrollbar-button': {
                                backgroundColor: 'green',
                                height: '7px',
                                borderRadius: '10px'
                            }
                        }}
                        >
                            {allDoctors.map((doctor, index) => {
                                return (
                                    <Grid key={`${index}`} sx={{ width: '100%' }} className='testin'>
                                        <Grid sx={{ width: '100%', mb: 1 }} container alignItems={'start'} justifyContent={'center'}>
                                            <Grid size={{ lg: 3 }} container justifyContent={'start'}>
                                                <Typography sx={{ fontSize: '12px' }} align='center'>
                                                    {doctor.email}
                                                </Typography>
                                            </Grid>
                                            <Grid size={{ lg: 3 }} container justifyContent={'start'}>
                                                <Typography sx={{ fontSize: '12px' }} align='center'>
                                                    {doctor.aphraNumber}
                                                </Typography>
                                            </Grid>
                                            <Grid size={{ lg: 2 }} container justifyContent={'start'}>
                                                <Typography sx={{ fontSize: '12px' }} align='center'>
                                                    {doctor.name}
                                                </Typography>
                                            </Grid>
                                            <Grid size={{ lg: 2 }} container justifyContent={'start'}>
                                                <Typography sx={{ fontSize: '12px' }} align='center'>
                                                    {doctor.username}
                                                </Typography>
                                            </Grid>
                                            <Grid size={{ lg: 1 }} container justifyContent={'start'}>
                                                <Typography sx={{ fontSize: '12px' }} align='center'>
                                                    {doctor.status}
                                                </Typography>
                                            </Grid>
                                            <Grid size={{ lg: 1 }} container justifyContent={'start'}>
                                                <Typography sx={{ fontSize: '12px' }} align='center'>
                                                    {doctor.role}
                                                </Typography>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                )
                            })}

                        </Grid>

                    </Grid>
                </Grid>
            </Grid>
        </>
    )
}

export default DoctorRegistration