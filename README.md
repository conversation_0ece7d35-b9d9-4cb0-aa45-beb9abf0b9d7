# Doc-App-Mono

**A Monorepo for Doctor Consultation Application**

Welcome to the **Doc-App-Mono!** This monorepo houses all the components of a comprehensive Doctor Consultation Application, designed to streamline healthcare consultations and improve patient outcomes. 

---

## Table of Contents
- [Getting Started](#getting-started)
- [Project Structure](#project-structure)
  - [API Package](#api-package)
  - [Web Package](#web-package)
- [Contributing](#contributing)
- [License](#license)

---

## Getting Started

### Prerequisites
Before you begin, ensure you have **Pnpm** installed. If you haven’t installed it yet, you can do so by running:

```bash
`npm install -g pnpm`
```

### Installation

Once Pnpm is installed, navigate to the project directory and install the dependencies:

```bash
pnpm install
```

### Running the Application

To run the application in development mode, execute the following command:

```bash 
pnpm run dev
```

### Pre-Commit Hooks

Before committing your changes, a pre-commit hook will automatically execute tests for all modified packages. This ensures code quality and functionality are maintained throughout development.

# Project Structure

## API Package

### Config Folder: 
This directory contains configuration variables that initialize our application, including environment variables and the Express entry point.

### Controllers: 
The core business logic resides here. Controllers handle operations such as fetching data from the database, posting data, and applying algorithms.

### Helpers: 
This directory holds utility functions, classes, and generic helpers designed to support your code. 

Note: Avoid placing business logic or database operations here; those belong in the controllers.

### Middleware: 
Middleware functions act as intermediaries for your routes, facilitating operations like authentication, verification, and hashing.

### Models: 
If you are using an ORM, this is where you define your schema programmatically. For instance, when utilizing Sequelize or Mongoose, you'll initialize your database models here.

### Routes: 
All routes are centralized in this directory, aiding in grouping and scaling your application while maintaining version control.

### Utils: 
This folder contains utility functions and small packages that can be reused throughout your application. For example, a Patient Deal Converter, which could potentially be a standalone package.

## Web Package

Doc App UI

UI Design can be found [here](https://www.figma.com/design/aoc00hZRSkJ6JtM5FGIJ0l/Harvest-Master-V2?node-id=3359-47&node-type=canvas&t=wJ0a8wN90p7uZUtd-0)

UI is using MUI for styling component, typescript for fast development and type-safe components. 

Run `pnpm run dev` for local development

Run  `pnpm run build` for production deployment.


# Contact
For any inquiries or feedback, feel free to reach out to the dev squade.
