import React, { useState, useEffect, useRef } from 'react';
import {
  Chat,
  Channel,
  Window,
  Thread,
  useChannelStateContext,
  useChatContext,
  DefaultStreamChatGenerics,
  Streami18n
} from "stream-chat-react";
import { Channel as StreamChannel, StreamChat } from 'stream-chat';
import "stream-chat-react/dist/css/v2/index.css";
import { Box, CircularProgress, Alert, Typography } from "@mui/material";
import { config } from "../../../config";
import chatService, { formatStreamChatId } from '../../../services/chat.service';
import { useAuth } from '../../../hooks/auth-provider';
import { ApiClient } from '../../../services';
import CustomChannelHeader from '../../patient-portal/chat/CustomChannelHeader';
import CustomMessageComponent from '../../patient-portal/chat/CustomMessageComponent';
import CustomMessageInput from '../chat/CustomMessageInput';
import CustomMessageList from '../../patient-portal/chat/CustomMessageList';
import RequestCard from './RequestCard';
import '../chat/layout.css';

// API key for Stream Chat
const API_KEY = config.streamChat.apiKey;

// Keep a reference to active clients across hot reloads
const activeClients = new Map<string, StreamChat<DefaultStreamChatGenerics>>();

// Add i18n instance with timezone
const i18n = new Streami18n({
  language: 'en',
  timezone: 'Australia/Sydney',
});

interface Request {
  id: string;
  type: 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase';
  patient_id: string;
  patient_name: string;
  patient_dob?: string;
  email: string;
  questionnaire_data: any[];
  total_score: number;
  max_score: number;
  is_eligible: boolean;
  status: string;
  created_at: string;
  approved_at?: string;
  approved_by?: string;
  review_notes?: string;
  strength_requests?: Array<{
    strength: string;
    currentQuantity: number;
    requestedQuantity: number;
    increaseAmount: number;
  }>;
}

interface PatientChatViewProps {
  patientId: string;
  patientName: string;
  channelId?: string;
  request?: Request;
  onRequestUpdate?: () => void;
}

// Component to handle marking messages as read and clearing doctor notifications
const MessageReadHandler = ({ patientId }: { patientId: string }) => {
  const { channel } = useChannelStateContext();
  const { client } = useChatContext();
  const markReadTimeoutRef = useRef<NodeJS.Timeout>();
  const isMountedRef = useRef(true);
  const notificationClearedRef = useRef(false);

  useEffect(() => {
    isMountedRef.current = true;

    if (!channel || !client || !client.userID) return;

    const markRead = async () => {
      if (!client.userID) return;

      try {
        if (!channel.cid) return;
        await channel.markRead();

        // Clear doctor notification when doctor reads messages (only once per session)
        if (!notificationClearedRef.current) {
          try {
            // Get patient email from patientId
            const patients = await ApiClient.getPatientsRedis();
            const patient = patients?.find(p => p.patientID === patientId);

            if (patient?.email) {
              await chatService.clearDoctorNotificationForPatient(patient.email);
              notificationClearedRef.current = true;
              console.log(`Doctor notification cleared for patient ${patient.email}`);
            }
          } catch (error) {
            console.error('Error clearing doctor notification:', error);
          }
        }
      } catch (error) {
        if (error instanceof Error &&
            (error.message.includes('disconnect') ||
             error.message.includes('after client.disconnect()'))) {
          console.log('Client disconnected, cannot mark as read');
        } else {
          console.error('Error marking channel as read:', error);
        }
      }
    };

    markReadTimeoutRef.current = setTimeout(() => {
      if (client.userID && isMountedRef.current) {
        markRead();
      }
    }, 100);

    return () => {
      isMountedRef.current = false;
      if (markReadTimeoutRef.current) {
        clearTimeout(markReadTimeoutRef.current);
        markReadTimeoutRef.current = undefined;
      }
    };
  }, [channel, client]);

  return null;
};

const PatientChatView: React.FC<PatientChatViewProps> = ({
  patientId,
  patientName,
  channelId,
  request,
  onRequestUpdate
}) => {
  const [selectedChannel, setSelectedChannel] = useState<StreamChannel<DefaultStreamChatGenerics> | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [client, setClient] = useState<StreamChat<DefaultStreamChatGenerics> | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const [requestExpanded, setRequestExpanded] = useState(false);
  const clientRef = useRef<StreamChat<DefaultStreamChatGenerics> | null>(null);
  const isInitializingRef = useRef(false);
  const shouldCleanupRef = useRef(true);
  const mountedRef = useRef(true);
  const { doctor } = useAuth();

  // On component mount/unmount
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Initialize chat client for doctor
  useEffect(() => {
    if (isInitializingRef.current) return;

    const initializeDoctorChat = async () => {
      isInitializingRef.current = true;
      shouldCleanupRef.current = false;

      try {
        setLoading(true);
        setError(null);

        if (!API_KEY) {
          throw new Error("Stream Chat API key is missing. Please contact support.");
        }

        // Get doctor ID
        const doctorId = doctor?.accessID || 'harvest';
        const formattedDoctorId = formatStreamChatId(doctorId, 'd');

        // Check if we already have an active client for this doctor
        const existingClient = activeClients.get(formattedDoctorId);
        if (existingClient && existingClient.userID) {
          clientRef.current = existingClient;
          setClient(existingClient);
          setUserId(formattedDoctorId);
          setLoading(false);
          return;
        }

        // Create Stream Chat client
        const chatClient = new StreamChat<DefaultStreamChatGenerics>(API_KEY);

        // Connect doctor user
        const userData = {
          id: formattedDoctorId,
          name: doctor?.name || doctor?.username || 'Doctor',
          role: 'doctor'
        };

        // Get a token from backend for this doctor
        const tokenResponse = await chatService.generateToken(formattedDoctorId);

        if (!tokenResponse || !tokenResponse.token) {
          throw new Error("No token received from server");
        }

        await chatClient.connectUser(userData, tokenResponse.token);

        // Store the client
        clientRef.current = chatClient;
        activeClients.set(formattedDoctorId, chatClient);

        if (mountedRef.current) {
          setClient(chatClient);
          setUserId(formattedDoctorId);
        }
      } catch (err) {
        console.error("Error initializing doctor chat:", err);
        if (mountedRef.current) {
          setError(err instanceof Error ? 
            `Failed to initialize chat: ${err.message}` :
            "Failed to initialize chat. Please try again later.");
        }
        clientRef.current = null;
      } finally {
        isInitializingRef.current = false;
        shouldCleanupRef.current = true;
        if (mountedRef.current) {
          setLoading(false);
        }
      }
    };

    initializeDoctorChat();

    return () => {
      if (!shouldCleanupRef.current || isInitializingRef.current) {
        return;
      }
      clientRef.current = null;
    };
  }, [doctor]);

  // Find or create channel with patient
  useEffect(() => {
    if (!client || !userId || selectedChannel) return;
    if (!client.userID) return;

    let isMounted = true;

    const findOrCreateChannel = async () => {
      try {
        if (!isMounted) return;
        setLoading(true);

        if (!client.userID) return;

        const doctorId = doctor?.accessID || 'harvest';
        
        // If we have a specific channelId, try to get that channel
        if (channelId) {
          try {
            const channel = client.channel('messaging', channelId);
            await channel.watch();
            
            if (isMounted && client.userID) {
              setSelectedChannel(channel);
            }
            return;
          } catch (error) {
            console.error('Error loading specific channel:', error);
            // Fall through to create new channel
          }
        }

        // Create or find channel with patient
        try {
          const channelResponse = await chatService.createChannel(
            doctorId,
            patientId,
            `Chat with ${patientName}`,
            'doctor',
            'patient'
          );

          if (!client.userID || !isMounted) return;

          // Get the channel
          const channel = client.channel('messaging', channelResponse.channelId);
          await channel.watch();

          if (!client.userID || !isMounted) return;

          setSelectedChannel(channel);
        } catch (error) {
          console.error('Error creating/finding channel:', error);
          setError("Could not create chat channel. Please try again later.");
        }
      } catch (err) {
        if (!isMounted) return;
        setError("Could not load chat. Please try again later.");
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    findOrCreateChannel();

    return () => {
      isMounted = false;
    };
  }, [client, userId, selectedChannel, patientId, patientName, channelId, doctor]);

  // Show loading state
  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        width: '100%'
      }}>
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  if (error || !client) {
    return (
      <Box sx={{
        p: 4,
        textAlign: 'center',
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || "Unable to initialize chat"}
        </Alert>
        <Typography variant="body2">
          If this issue persists, please contact support.
        </Typography>
      </Box>
    );
  }

  // Render chat interface when client is ready and channel is found
  return (
    <Box sx={{
      height: '100%',
      width: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <Chat client={client} i18nInstance={i18n}>
        {selectedChannel ? (
          <Channel
            channel={selectedChannel}
            markReadOnMount={true}
            Message={(messageProps) => (
              <CustomMessageComponent {...messageProps} doctorName={doctor?.name || 'Doctor'} />
            )}
          >
            <MessageReadHandler patientId={patientId} />
            <Window>
              <CustomChannelHeader
                doctorName={doctor?.name || 'Doctor'}
                patientName={patientName}
                userRole="doctor"
              />
              {/* Request Card - only show if request data is available */}
              {request && (
                <Box sx={{ p: 1, borderBottom: '1px solid #eee', bgcolor: '#f8f9fa' }}>
                  <RequestCard
                    request={request}
                    onUpdate={onRequestUpdate || (() => {})}
                    onPatientMessage={() => {}} // No-op since we're already in chat
                    isExpanded={requestExpanded}
                    onExpand={() => setRequestExpanded(true)}
                    onCollapse={() => setRequestExpanded(false)}
                    inChatContext={true}
                  />
                </Box>
              )}
              <Box sx={{
                height: request
                  ? (requestExpanded ? 'calc(100% - 400px)' : 'calc(100% - 180px)')
                  : 'calc(100% - 120px)',
                overflow: 'auto'
              }}>
                <CustomMessageList userRole="doctor" />
              </Box>
              <CustomMessageInput patientEmail={request?.email} />
            </Window>
            <Thread
              Message={(messageProps) => (
                <CustomMessageComponent {...messageProps} doctorName={doctor?.name || 'Doctor'} />
              )}
            />
          </Channel>
        ) : (
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%',
            p: 4
          }}>
            <CircularProgress />
          </Box>
        )}
      </Chat>
    </Box>
  );
};

export default PatientChatView;
