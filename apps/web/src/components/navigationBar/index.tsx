/// <reference types="vite-plugin-svgr/client" />

import { AppBar, Button, IconButton, Menu, MenuItem, Toolbar, useMediaQuery } from "@mui/material";
import ZenithLogo from "../../assets/Zenith.svg?react";
import { useNavigate } from "@tanstack/react-location";
import { useTheme } from "@mui/material/styles";
import MenuIcon from "@mui/icons-material/Menu";
import { useEffect, useState } from "react";
import { useAuth } from "../../hooks/auth-provider";
import LoadingScreen from "../../utils/loading-screen";
import { usePatient } from "../../hooks/patient-provider";
import { UserActions } from "../../utils";
import { useTracker } from "../../hooks/activity-tracker-provider";

const NavigationBar: React.FC = () => {
	const navigate = useNavigate();
	const theme = useTheme();
	const isDesktopOrTablet = useMediaQuery(theme.breakpoints.up("md"));
	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
	const [isLoading, setIsLoading] = useState(false);
	const { setNotificationInterval } = usePatient();

	const { logout, doctor } = useAuth();
	const { trackActivity, disableTracking, flushEventsToBackend } = useTracker();

	const handleMenuOpen = (e: React.MouseEvent<HTMLElement>) => {
		setAnchorEl(e.currentTarget);
	};

	const handleMenuClose = () => {
		setAnchorEl(null);
	};
	const handleLogOut: () => Promise<void> = async () => {
		setIsLoading(true);
		trackActivity("Doctor", "", UserActions.LOGOUT, "Doctor logged out", doctor?.id || "");
		if (doctor?.role === "doctor") {
			await flushEventsToBackend();
		}
		await logout();
		setNotificationInterval(undefined);
		setAnchorEl(null);
		setIsLoading(false);
	};
	// const handleHistory = () => {
	//     navigate({ to: '/history' })
	//     setAnchorEl(null);
	// }

	useEffect(() => {
		if (doctor?.role !== "doctor") {
			disableTracking();
		}
	}, [doctor]);

	return (
		<>
			{isLoading && <LoadingScreen />}

			<AppBar
				position="fixed"
				elevation={0}
				sx={{
					backgroundColor: "green",
					zIndex: 1,
				}}
			>
				{/*TODO: Refactor*/}
				<Toolbar>
					<ZenithLogo width={"200"} onClick={() => navigate({ to: "/home" })} style={{ cursor: "pointer" }} />
					<Toolbar sx={{ flexGrow: 1 }} />
					{isDesktopOrTablet ? (
						<>
							<Button
								sx={{
									color: "white",
									outline: "none",
									"&:focus": { outline: "none" },
									"&:focusVisible": { outline: "none" },
									mr: 1,
									textTransform: "none",
									fontSize: "12px",
								}}
								onClick={() => {
									if (doctor?.role === "doctor") {
										trackActivity(
											"Doctor",
											"",
											UserActions.CLICKED,
											"Doctor clicked on Online Patients in navigation bar",
											doctor?.id || ""
										);
									}
									navigate({ to: "/online-patients" });
								}}
							>
								Online Patients
							</Button>
							<span key="separator-0" style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}>
								|
							</span>
							<Button
								sx={{
									color: "white",
									outline: "none",
									"&:focus": { outline: "none" },
									"&:focusVisible": { outline: "none" },
									mr: 1,
									textTransform: "none",
									fontSize: "12px",
								}}
								onClick={() => {
									if (doctor?.role === "doctor") {
										trackActivity(
											"Doctor",
											"",
											UserActions.CLICKED,
											"Doctor clicked on View All Consultation in navigation bar",
											doctor?.id || ""
										);
									}
									navigate({ to: "/doc" });
								}}
							>
								View All Consultations
							</Button>
							{doctor &&
								(doctor.role === "admin" || doctor.role === "superAdmin") && [
									<span
										key="separator-1"
										style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}
									>
										|
									</span>,
									<Button
										key="patients-queue"
										sx={{
											color: "white",
											outline: "none",
											"&:focus": { outline: "none" },
											"&:focusVisible": { outline: "none" },
											mr: 1,
											textTransform: "none",
											fontSize: "12px",
										}}
										onClick={() => navigate({ to: "/patient-queue" })}
									>
										Patients Queue
									</Button>,
									<span
										key="separator-2"
										style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}
									>
										|
									</span>,
									<Button
										key="doctor-registration"
										sx={{
											color: "white",
											outline: "none",
											"&:focus": { outline: "none" },
											"&:focusVisible": { outline: "none" },
											mr: 1,
											textTransform: "none",
											fontSize: "12px",
										}}
										onClick={() => navigate({ to: "/doctor-registration" })}
									>
										Doctor Registration
									</Button>,
									<span
										key="separator-3"
										style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}
									>
										|
									</span>,
									<Button
										key="calendar"
										sx={{
											color: "white",
											outline: "none",
											"&:focus": { outline: "none" },
											"&:focusVisible": { outline: "none" },
											mr: 1,
											textTransform: "none",
											fontSize: "12px",
										}}
										onClick={() => navigate({ to: "/schedule-admin" })}
									>
										Calendar
									</Button>,
									<span
										key="separator-4"
										style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}
									>
										|
									</span>,
									<Button
										key="moderation"
										sx={{
											color: "white",
											outline: "none",
											"&:focus": { outline: "none" },
											"&:focusVisible": { outline: "none" },
											mr: 1,
											textTransform: "none",
											fontSize: "12px",
										}}
										onClick={() => navigate({ to: "/admin/moderation" })}
									>
										Moderation
									</Button>,
									<span
										key="separator-5"
										style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}
									>
										|
									</span>,
									<Button
										key="admin-bookings"
										sx={{
											color: "white",
											outline: "none",
											"&:focus": { outline: "none" },
											"&:focusVisible": { outline: "none" },
											mr: 1,
											textTransform: "none",
											fontSize: "12px",
										}}
										onClick={() => navigate({ to: "/admin/bookings" })}
									>
										Admin Bookings
									</Button>,
								]}
							<span key="separator-6" style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}>
								|
							</span>
							<Button
								sx={{
									color: "white",
									outline: "none",
									"&:focus": { outline: "none" },
									"&:focusVisible": { outline: "none" },
									mr: 1,
									textTransform: "none",
									fontSize: "12px",
								}}
								onClick={() => {
									if (doctor?.role === "doctor") {
										trackActivity(
											"Doctor",
											"",
											UserActions.CLICKED,
											"Doctor clicked on History in navigation bar",
											doctor?.id || ""
										);
									}
									navigate({ to: "/patient/history" });
								}}
							>
								History
							</Button>
							<span key="separator-8" style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}>
								|
							</span>
							<Button
								sx={{
									color: "white",
									outline: "none",
									"&:focus": { outline: "none" },
									"&:focusVisible": { outline: "none" },
									mr: 1,
									textTransform: "none",
									fontSize: "12px",
								}}
								onClick={() => {
									if (doctor?.role === "doctor") {
										trackActivity(
											"Doctor",
											"",
											UserActions.CLICKED,
											"Doctor clicked on Patient Reports in navigation bar",
											doctor?.id || ""
										);
									}
									navigate({ to: "/patient/reports" });
								}}
							>
								Patient Reports
							</Button>
							{doctor && (doctor.role === "admin" || doctor.role === "superAdmin") && (
								<>
									<Button
										sx={{
											color: "white",
											outline: "none",
											"&:focus": { outline: "none" },
											"&:focusVisible": { outline: "none" },
											mr: 1,
											textTransform: "none",
											fontSize: "12px",
										}}
										onClick={() => navigate({ to: "/drActivity" })}
									>
										Doctors Activity
									</Button>

									<span
										key="separator-7"
										style={{ fontSize: "1.5rem", color: "rgba(255, 255, 255, 0.3)" }}
									>
										|
									</span>
								</>
							)}

							<Button
								variant="contained"
								sx={{
									color: "white",
									outline: "none",
									"&:focus": { outline: "none" },
									"&:focusVisible": { outline: "none" },
									ml: 1,
									textTransform: "none",
									backgroundColor: "black",
								}}
								onClick={handleLogOut}
							>
								Logout
							</Button>
						</>
					) : (
						<>
							<IconButton
								onClick={handleMenuOpen}
								sx={{
									color: "white",
									outline: "none",
									"&:focus": { outline: "none" },
									"&:focusVisible": { outline: "none" },
								}}
							>
								<MenuIcon />
							</IconButton>
							<Menu
								id="menu-appbar"
								anchorEl={anchorEl}
								anchorOrigin={{
									vertical: "top",
									horizontal: "right",
								}}
								keepMounted
								transformOrigin={{
									vertical: "top",
									horizontal: "right",
								}}
								open={Boolean(anchorEl)}
								onClose={handleMenuClose}
							>
								<MenuItem
									onClick={() => {
										if (doctor?.role === "doctor") {
											trackActivity(
												"Doctor",
												"",
												UserActions.CLICKED,
												"Doctor clicked on Online Patients in navigation bar",
												doctor?.id || ""
											);
										}
										navigate({ to: "/online-patients" });
										handleMenuClose();
									}}
								>
									Online Patients
								</MenuItem>

								<MenuItem
									onClick={() => {
										if (doctor?.role === "doctor") {
											trackActivity(
												"Doctor",
												"",
												UserActions.CLICKED,
												"Doctor clicked on View all consultation in navigation bar",
												doctor?.id || ""
											);
										}
										navigate({ to: "/doc" });
										handleMenuClose();
									}}
								>
									{" "}
									View All Consultations
								</MenuItem>
								{doctor &&
									(doctor.role === "admin" || doctor.role === "superAdmin") && [
										<MenuItem
											key={"queue"}
											onClick={() => {
												navigate({ to: "/patient-queue" });
												handleMenuClose();
											}}
										>
											Patients Queue
										</MenuItem>,
										<MenuItem
											key={"doctor-registration"}
											onClick={() => {
												navigate({ to: "/doctor-registration" });
												handleMenuClose();
											}}
										>
											Doctor Registration
										</MenuItem>,
										<MenuItem
											key="calendar"
											onClick={() => {
												navigate({ to: "/schedule-admin" });
												handleMenuClose();
											}}
										>
											Calendar
										</MenuItem>,
										<MenuItem
											key="moderation"
											onClick={() => {
												navigate({ to: "/admin/moderation" });
												handleMenuClose();
											}}
										>
											Message Moderation
										</MenuItem>,
										<MenuItem
											key="admin-bookings"
											onClick={() => {
												navigate({ to: "/admin/bookings" });
												handleMenuClose();
											}}
										>
											Admin Bookings
										</MenuItem>,
									]}

								<MenuItem
									onClick={() => {
										navigate({ to: "/patient/history" });
										handleMenuClose();
									}}
								>
									History
								</MenuItem>

								{doctor && (doctor.role === "admin" || doctor.role === "superAdmin") && (
									<MenuItem
										onClick={() => {
											navigate({ to: "/drActivity" });
											handleMenuClose();
										}}
									>
										Doctors Activity
									</MenuItem>
								)}
								<MenuItem onClick={handleLogOut}>Logout</MenuItem>
							</Menu>
						</>
					)}
				</Toolbar>
			</AppBar>
		</>
	);
};

export default NavigationBar;
