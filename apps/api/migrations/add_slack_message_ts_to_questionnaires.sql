-- Migration: Add slack_message_ts column to questionnaire tables for threaded Slack replies
-- Date: 2025-01-09
-- Purpose: Enable threaded Slack notifications for request moderation (similar to chat moderation)

-- Add slack_message_ts column to thc_increase_questionnaire table
ALTER TABLE thc_increase_questionnaire 
ADD COLUMN slack_message_ts TEXT;

-- Add slack_message_ts column to extend_tp_questionnaire table  
ALTER TABLE extend_tp_questionnaire 
ADD COLUMN slack_message_ts TEXT;

-- Add comments to document the purpose of these columns
COMMENT ON COLUMN thc_increase_questionnaire.slack_message_ts IS 'Slack message timestamp for threaded replies when moderating THC increase requests';
COMMENT ON COLUMN extend_tp_questionnaire.slack_message_ts IS 'Slack message timestamp for threaded replies when moderating treatment plan extension requests';

-- <PERSON><PERSON> indexes for better performance when looking up by slack_message_ts (optional but recommended)
CREATE INDEX IF NOT EXISTS idx_thc_increase_questionnaire_slack_message_ts 
ON thc_increase_questionnaire(slack_message_ts) 
WHERE slack_message_ts IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_extend_tp_questionnaire_slack_message_ts 
ON extend_tp_questionnaire(slack_message_ts) 
WHERE slack_message_ts IS NOT NULL;
