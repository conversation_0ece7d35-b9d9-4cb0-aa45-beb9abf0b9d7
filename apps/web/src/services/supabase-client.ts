import { supabase } from "./supabase";
import { Session } from "@supabase/supabase-js";

class Supabase {
  async signInWithPassword(email: string, password: string) {
    const result = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (result.error) throw result.error;

    if (result.data) {
      return result.data;
    }

    return undefined;
  }

  async signUpWithEmail(email: string, password: string) {
    const result = await supabase.auth.signUp({
      email,
      password,
    });
  
    if (result.error) throw result.error;
  
    if (result.data) {
      return result.data;
    }
  
    return undefined;
  }

  async getSession() {
    return supabase.auth.getSession().then(({ data: { session } }) => {
      if (session) {
        return session;
      } else {
        return undefined;
      }
    });
  }

  onAuthStateChange() {
    let UserSession: Session | undefined = undefined;
    const data = supabase.auth.onAuthStateChange((_event, session) => {
      if (session) {
        UserSession = session;
      }
    });

    return {
      data: data.data,
      session: UserSession,
    };
  }
  async signOut() {
    const result = await supabase.auth.signOut();
    return result;
  }
}

export const SupabaseClient = new Supabase();
