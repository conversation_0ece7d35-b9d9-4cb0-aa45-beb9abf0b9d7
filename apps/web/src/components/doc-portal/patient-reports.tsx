import React, { useState, useEffect, useCallback } from "react";
import {
	Box,
	Button,
	Typography,
	Table,
	TableBody,
	TableCell,
	TableRow,
	TableHead,
	TableContainer,
	Paper,
	tableCellClasses,
	TablePagination,
	CircularProgress,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import Grid from "@mui/material/Grid2";
import { ApiClient } from "../../services";
import { ReportData } from "../../types";
import moment from "moment";
import PatientReport from "./PatientReportModal";
import { useTracker } from "../../hooks/activity-tracker-provider";
import { useAuth } from "../../hooks/auth-provider";
import { UserActions } from "../../utils";

const StyledTableCell = styled(TableCell)(({ theme }) => ({
	[`&.${tableCellClasses.head}`]: {
		backgroundColor: "green",
		color: theme.palette.common.white,
	},
	[`&.${tableCellClasses.body}`]: {
		fontSize: 14,
	},
}));

const StyledTableRow = styled(TableRow)(() => ({
	"&:nth-of-type(odd)": {
		backgroundColor: "#EBEBEB",
	},
	// hide last border
	"&:last-child td, &:last-child th": {
		border: 0,
	},
}));

const PatientReports: React.FC = () => {
	const [reports, setReports] = useState<Partial<ReportData[]>>([]);
	const [page, setPage] = useState(0);
	const [rowsPerPage, setRowsPerPage] = useState(10);
	const [totalCount, setTotalCount] = useState(0);
	const [openReport, setOpenReport] = useState(false);
	const [reportId, setReportId] = useState<string | null>(null);
	const [loading, setLoading] = useState(true);
	const { trackActivity } = useTracker();
	const { doctor } = useAuth();

	const handlePageChange = (_event: unknown, newPage: number) => {
		setPage(newPage);
	};

	const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setRowsPerPage(parseInt(event.target.value, 10));
		setPage(0); // Reset to first page when rows per page changes
	};

	const handleOpenReport = (reportId: string) => {
		setReportId(reportId);
		setOpenReport(true);
	};

	const fetchReports = useCallback(async () => {
		try {
			const response = await ApiClient.getAICheckResponses(rowsPerPage, page * rowsPerPage);

			if (response && response.data) {
				console.log("Fetched reports:", response.data.data);
				setReports(response.data.data || []);
				setTotalCount(response.data.pagination.total || 0);
			}
		} catch (error) {
			console.error("Error fetching reports:", error);
		} finally {
			setLoading(false);
		}
	}, [rowsPerPage, page]);

	useEffect(() => {
		fetchReports();
	}, [fetchReports]);

	useEffect(() => {
		if (doctor) {
			trackActivity(
				"Doctor",
				"",
				UserActions.REACHED,
				"Doctor accessed the patient reports page",
				doctor?.id as string
			);
		}
	}, [doctor, trackActivity]);

	return (
		<Box sx={{ p: 2, width: "100%", maxWidth: 1920, margin: "auto" }}>
			<Grid container spacing={2}>
				<Grid size={12}>
					<Typography variant="h5" gutterBottom fontWeight="bold" color="green">
						Patient Reports
					</Typography>
				</Grid>
			</Grid>
			{loading ? (
				<Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
					<CircularProgress />
				</Box>
			) : reports.length > 0 ? (
				<TableContainer component={Paper}>
					<Table sx={{ minWidth: 700 }} aria-label="patient reports table">
						<TableHead>
							<TableRow>
								<StyledTableCell>Patient Name</StyledTableCell>
								<StyledTableCell>Consult Date</StyledTableCell>
								<StyledTableCell>Condition</StyledTableCell>
								<StyledTableCell>Risk Score</StyledTableCell>
								<StyledTableCell>Response</StyledTableCell>
								<StyledTableCell>Report</StyledTableCell>
							</TableRow>
						</TableHead>
						<TableBody>
							{reports.map((report, index) => (
								<StyledTableRow key={index}>
									<StyledTableCell>{report?.patientName}</StyledTableCell>
									<StyledTableCell>
										{moment(report?.createdAt).format("YYYY-MM-DD HH:mm:ss")}
									</StyledTableCell>
									<StyledTableCell>{report?.condition}</StyledTableCell>
									<StyledTableCell>{report?.risk_score}</StyledTableCell>
									<StyledTableCell>{report?.response}</StyledTableCell>
									<StyledTableCell>
										<Button
											onClick={() => handleOpenReport(report?.patientID as string)}
											variant="contained"
											disableElevation
											color="primary"
										>
											View
										</Button>
									</StyledTableCell>
								</StyledTableRow>
							))}
						</TableBody>
					</Table>
					<TablePagination
						rowsPerPageOptions={[5, 10, 25]}
						component="div"
						count={Number(totalCount)}
						rowsPerPage={rowsPerPage}
						page={page}
						onPageChange={handlePageChange}
						onRowsPerPageChange={handleRowsPerPageChange}
					/>
				</TableContainer>
			) : (
				<Typography variant="body1" color="textSecondary" align="center">
					No reports available.
				</Typography>
			)}
			<PatientReport open={openReport} onClose={() => setOpenReport(false)} id={reportId ?? ""} />
		</Box>
	);
};

export default PatientReports;
