import moment from "moment-timezone";

export function getClientTimeZoneId(): string {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
}


export function convertAustralianRangeToUtc(
  rangeString: string,
  referenceDate: Date,
  australianTimeZone: string = "Australia/Sydney"
): { startUTC: Date; endUTC: Date } {
  const [startStr, endStr] = rangeString.split("-");

  if (!startStr || !endStr) {
    throw new Error(
      `Invalid range string format: ${rangeString}. Expected "HH:MM-HH:MM".`
    );
  }

  const cleanedStartStr = startStr.trim();
  const cleanedEndStr = endStr.trim();

  const baseMomentInAustralianTimeZone = moment.tz(
    referenceDate,
    australianTimeZone
  );

  const startUTC = baseMomentInAustralianTimeZone
    .clone()
    .hour(parseInt(cleanedStartStr.split(":")[0], 10))
    .minute(parseInt(cleanedStartStr.split(":")[1], 10))
    .second(0)
    .millisecond(0)
    .utc()
    .toDate();

  const endUTC = baseMomentInAustralianTimeZone
    .clone()
    .hour(parseInt(cleanedEndStr.split(":")[0], 10))
    .minute(parseInt(cleanedEndStr.split(":")[1], 10))
    .second(0)
    .millisecond(0)
    .utc()
    .toDate();
  return { startUTC, endUTC };
}

export function convertUtcToClientTime(
  utcDate: Date,
  clientTimeZoneId: string,
  outputFormat: string = "HH:mm"
): string {
  if (isNaN(utcDate.getTime())) {
    console.error(`Invalid Date object provided to convertUtcToClientTime`);
    return "--:--";
  }
  const clientMoment = moment.utc(utcDate).tz(clientTimeZoneId);
  return clientMoment.format(outputFormat);
}
