import { Pool } from 'pg';
import { logger } from '../../config/logger';
import { WebSocketManager } from '../../helpers/webSocketManager';
import { TOPICS } from '../../helpers/topics';
import { timerMap } from './index';
import { Queries } from '../../helpers/queries';


/**
 * Gets the doctor's ID from accessID
 * @param db Database connection pool
 * @param accessID The doctor's accessID
 * @returns The doctor's ID or null if not found
 */
export const getDoctorIdFromAccessId = async (db: Pool, accessID: string): Promise<string | null> => {
  const client = await db.connect();
  
  try {
    const query = `
      SELECT id
      FROM dr
      WHERE "accessID" = $1
    `;
    
    const result = await client.query(query, [accessID]);
    
    if (result.rows.length === 0) {
      logger.error(`Doctor with accessID ${accessID} not found`);
      return null;
    }
    
    return result.rows[0].id;
  } catch (error) {
    logger.error(`Error getting doctor ID from accessID ${accessID}:`, error);
    return null;
  } finally {
    client.release();
  }
};

/**
 * Calculates the dynamic patient limit for a specific doctor based on their unnotified patients
 * Since each doctor manages their own independent queue, this function focuses solely on
 * the individual doctor's workload without considering other doctors.
 *
 * @param db Database connection pool
 * @param doctorAccessID The doctor's accessID (required in independent queue architecture)
 * @returns The number of patients this doctor can notify in the next batch
 */
export const calculateDynamicPatientLimit = async (db: Pool, doctorAccessID?: string): Promise<number> => {
  // Since each doctor manages their own independent queue,
  // doctorAccessID should always be provided
  if (!doctorAccessID) {
    logger.warn('calculateDynamicPatientLimit called without doctorAccessID - this should not happen in independent queue architecture');
    return 1; // Default fallback
  }

  // Convert accessID to ID
  const doctorID = await getDoctorIdFromAccessId(db, doctorAccessID);
  if (!doctorID) {
    logger.error(`Doctor with accessID ${doctorAccessID} not found`);
    return 1; // Default minimum
  }

  const client = await db.connect();

  try {
    // Count UNNOTIFIED patients assigned to this specific doctor for today
    // This is the key fix: we count unnotified patients, not total assigned patients
    // Using DISTINCT to avoid counting duplicate consultation records for the same patient
    const unnotifiedPatientsQuery = `
      SELECT COUNT(DISTINCT c."patientID") as unnotified_patients
      FROM Consultation c
      JOIN Patient p ON c."patientID" = p."patientID"
      JOIN patientslot ps ON p."zohoID" = ps.patient_id
      JOIN range r ON ps.range_id = r.id
      WHERE r."doctorID" = $1
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      AND (c."notificationSent" = false OR c."notificationSent" IS NULL)
      AND c.completed = FALSE
    `;

    const result = await client.query(unnotifiedPatientsQuery, [doctorID]);
    const unnotifiedPatients = parseInt(result.rows[0].unnotified_patients, 10);

    // If no unnotified patients, return 0 (doctor shouldn't notify anyone)
    if (unnotifiedPatients === 0) {
      return 0;
    }

    // Apply batch sizing logic based on unnotified patients for this doctor
    // This logic is consistent with the notification middleware but focuses on unnotified patients
    if (unnotifiedPatients <= 30) {
      return 2;
    } else if (unnotifiedPatients > 30 && unnotifiedPatients < 40) {
      return 2;
    } else if (unnotifiedPatients >= 40 && unnotifiedPatients <= 60) {
      return 3;
    } else {
      return 1 + Math.floor(unnotifiedPatients / 20);
    }

  } catch (error) {
    logger.error(`Error calculating dynamic patient limit for doctor ${doctorAccessID}:`, error);
    return 1; // Default fallback value
  } finally {
    client.release();
  }
};

/**
 * Gets the number of patients a doctor has already notified
 * @param db Database connection pool
 * @param doctorID The doctor's ID
 * @returns The count of patients assigned to this doctor for today's consultations
 */
export const getDoctorPatientCount = async (db: Pool, doctorID: string): Promise<number> => {
  const client = await db.connect();

  try {
    // Join with Consultation and PatientSlot tables to ensure we're only counting patients with today's consultations and valid bookings
    const query = `
      SELECT COUNT(*) as patient_count
      FROM PatientQueue pq
      JOIN Consultation c ON pq."patientID" = c."patientID"
      WHERE pq."assignedDoctorID" = $1
      AND pq."completedAt" IS NULL
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      AND c.completed = FALSE
    `;

    const result = await client.query(query, [doctorID]);
    const count = parseInt(result.rows[0].patient_count, 10);

    // Log the count for debugging
   // logger.info(`Doctor ${doctorID} - Patient count for today's consultations: ${count}`);

    return count;
  } catch (error) {
    logger.error(`Error getting patient count for doctor ${doctorID}:`, error);
    return 0;
  } finally {
    client.release();
  }
};

export const assignPatientToDoctorForInDoctorQueue = async (
  db: Pool,
  patientID: string,
  doctorID: string,
  existingClient?: import('pg').PoolClient
): Promise<boolean> => {
  // Use provided client or get a new one
  const client = existingClient || await db.connect();
  const shouldManageTransaction = !existingClient;

  try {
    // Only begin a transaction if we're managing it
    if (shouldManageTransaction) {
      await client.query('BEGIN');
    }

    // Create entry in DoctorQueue
    const createDoctorQueueQuery = `
      INSERT INTO DoctorQueue ("patientID", "doctorID", status)
      VALUES ($1, $2, 'ASSIGNED')
      ON CONFLICT ("patientID", "doctorID")
      DO UPDATE SET
        status = 'ASSIGNED',
        "updatedAt" = CURRENT_TIMESTAMP
      RETURNING *
    `;

    await client.query(createDoctorQueueQuery, [patientID, doctorID]);

    // Only commit if we're managing the transaction
    if (shouldManageTransaction) {
      await client.query('COMMIT');
    }

    return true;
  } catch (error) {
    // Only rollback if we're managing the transaction
    if (shouldManageTransaction) {
      await client.query('ROLLBACK');
    }
    logger.error(`Error assigning patient ${patientID} to doctor ${doctorID}:`, error);
    return false;
  } finally {
    // Only release if we created the client
    if (shouldManageTransaction && client) {
      client.release();
    }
  }
};

/**
 * Assigns a patient to a doctor
 * @param db Database connection pool
 * @param patientID The patient's ID
 * @param doctorID The doctor's ID
 * @returns True if assignment was successful
 */
export const assignPatientToDoctor = async (
  db: Pool,
  patientID: string,
  doctorID: string,
  existingClient?: import('pg').PoolClient
): Promise<boolean> => {
  // Use provided client or get a new one
  const client = existingClient || await db.connect();
  const shouldManageTransaction = !existingClient;

  try {
    // Only begin a transaction if we're managing it
    if (shouldManageTransaction) {
      await client.query('BEGIN');
    }
    
    // Update PatientQueue with assigned doctor
    const updatePatientQueueQuery = `
      UPDATE PatientQueue
      SET "assignedDoctorID" = $1,
          "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $2
      RETURNING *
    `;

    const patientQueueResult = await client.query(updatePatientQueueQuery, [doctorID, patientID]);
    
    if (patientQueueResult.rows.length === 0) {
      if (shouldManageTransaction) {
        await client.query('ROLLBACK');
      }
      return false;
    }

    // Create entry in DoctorQueue
    const createDoctorQueueQuery = `
      INSERT INTO DoctorQueue ("patientID", "doctorID", status)
      VALUES ($1, $2, 'ASSIGNED')
      ON CONFLICT ("patientID", "doctorID")
      DO UPDATE SET
        status = 'ASSIGNED',
        "updatedAt" = CURRENT_TIMESTAMP
      RETURNING *
    `;


    await client.query(createDoctorQueueQuery, [patientID, doctorID]);

    // Only commit if we're managing the transaction
    if (shouldManageTransaction) {
      await client.query('COMMIT');
    }

    // Notify via WebSocket
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, patientQueueResult.rows[0]);

    logger.info(`Patient ${patientID} assigned to doctor ${doctorID}`);
    return true;
  } catch (error) {
    // Only rollback if we're managing the transaction
    if (shouldManageTransaction) {
      await client.query('ROLLBACK');
    }
    logger.error(`Error assigning patient ${patientID} to doctor ${doctorID}:`, error);
    return false;
  } finally {
    // Only release if we created the client
    if (shouldManageTransaction && client) {
      client.release();
    }
  }
};

/**
 * Reassigns a patient to a different doctor
 * @param db Database connection pool
 * @param patientID The patient's ID
 * @param newDoctorID The new doctor's ID
 * @returns True if reassignment was successful
 */
export const reassignPatient = async (
  db: Pool,
  patientID: string,
  newDoctorID: string
): Promise<boolean> => {
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Final admission check: has this patient been admitted today?
    const admissionCheckQuery = `
      SELECT 1 FROM Admission a
      WHERE a."patientID" = $1
        AND a.admitted = true
        AND a."createdAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND a."createdAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      LIMIT 1
    `;
    const admissionCheckResult = await client.query(admissionCheckQuery, [patientID]);
    if (admissionCheckResult.rows.length > 0) {
      await client.query('ROLLBACK');
      //logger.info(`Patient ${patientID} was just admitted and will not be reassigned.`);
      return false;
    }

    // Get current assigned doctor
    const getCurrentDoctorQuery = `
      SELECT "assignedDoctorID"
      FROM PatientQueue
      WHERE "patientID" = $1
    `;

    const currentDoctorResult = await client.query(getCurrentDoctorQuery, [patientID]);

    if (currentDoctorResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return false;
    }

    const currentDoctorID = currentDoctorResult.rows[0].assignedDoctorID;

    // Update PatientQueue with new doctor
    const updatePatientQueueQuery = `
      UPDATE PatientQueue
      SET "assignedDoctorID" = $1,
          "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $2
      RETURNING *
    `;

    const patientQueueResult = await client.query(updatePatientQueueQuery, [newDoctorID, patientID]);

    // Update status in DoctorQueue for old doctor
    if (currentDoctorID) {
      const updateOldDoctorQueueQuery = `
        UPDATE DoctorQueue
        SET status = 'REASSIGNED',
            "updatedAt" = CURRENT_TIMESTAMP
        WHERE "patientID" = $1 AND "doctorID" = $2
      `;

      await client.query(updateOldDoctorQueueQuery, [patientID, currentDoctorID]);
    }

    // Create entry in DoctorQueue for new doctor
    const createDoctorQueueQuery = `
      INSERT INTO DoctorQueue ("patientID", "doctorID", status)
      VALUES ($1, $2, 'ASSIGNED')
      ON CONFLICT ("patientID", "doctorID")
      DO UPDATE SET
        status = 'ASSIGNED',
        "updatedAt" = CURRENT_TIMESTAMP
      RETURNING *
    `;

    await client.query(createDoctorQueueQuery, [patientID, newDoctorID]);

    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [newDoctorID]);
    const doctorName = doctorResult.rows[0].username;

    await client.query('COMMIT');


    // Notify via WebSocket
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, patientQueueResult.rows[0]);



    logger.info(`Patient ${patientID} reassigned from doctor ${currentDoctorID} to doctor ${doctorName}`);
    return true;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error(`Error reassigning patient ${patientID} to doctor ${newDoctorID}:`, error);
    return false;
  } finally {
    client.release();
  }
};

/**
 * Marks a patient as consulted by a doctor
 * @param db Database connection pool
 * @param patientID The patient's ID
 * @param doctorID The doctor's ID
 * @returns True if update was successful
 */
export const markPatientAsConsulted = async (
  db: Pool,
  patientID: string,
  doctorID: string
): Promise<boolean> => {
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Update PatientQueue with consulted doctor
    const updatePatientQueueQuery = `
      UPDATE PatientQueue
      SET "consultedDoctorID" = $1,
          "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $2
      RETURNING *
    `;

    const patientQueueResult = await client.query(updatePatientQueueQuery, [doctorID, patientID]);

    if (patientQueueResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return false;
    }

    // Update status in DoctorQueue
    const updateDoctorQueueQuery = `
      UPDATE DoctorQueue
      SET status = 'CONSULTED',
          "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $1 AND "doctorID" = $2
      RETURNING *
    `;

    await client.query(updateDoctorQueueQuery, [patientID, doctorID]);

    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [doctorID]);
    const doctorName = doctorResult.rows[0].username;

    await client.query('COMMIT');

    // Notify via WebSocket
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, patientQueueResult.rows[0]);

    logger.info(`Patient ${patientID} marked as consulted by doctor ${doctorName}`);
    return true;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error(`Error marking patient ${patientID} as consulted by doctor ${doctorID}:`, error);
    return false;
  } finally {
    client.release();
  }
};

/**
 * Finds patients who have been waiting for more than 30 seconds and can be reassigned
 * @param db Database connection pool
 * @returns Array of patient IDs eligible for reassignment
 */
export const findPatientsForReassignment = async (db: Pool): Promise<string[]> => {
  const client = await db.connect();

  try {
    // Find patients who have been waiting for more than 30 seconds and have NOT been admitted today
    const query = `
      SELECT "patientID"
      FROM PatientQueue
      WHERE "notificationSentDateTime" < (CURRENT_TIMESTAMP - INTERVAL '30 seconds')
      AND "completedAt" IS NULL
      AND (status = 'ONLINE' OR status = 'JOINED')
      AND "noShow" IS NULL
      AND NOT EXISTS (
        SELECT 1 FROM Admission a
        WHERE a."patientID" = PatientQueue."patientID"
          AND a.admitted = true
          AND a."createdAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
          AND a."createdAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      )
    `;

    const result = await client.query(query);
    return result.rows.map(row => row.patientID);
  } catch (error) {
    logger.error('Error finding patients for reassignment:', error);
    return [];
  } finally {
    client.release();
  }
};

/**
 * Finds the least busy doctor (with the fewest assigned patients)
 * @param db Database connection pool
 * @returns The ID of the least busy doctor, or null if no doctors are available
 */
export const findLeastBusyDoctor = async (db: Pool): Promise<string | null> => {
  const client = await db.connect();

  try {
    // Get active doctors from timer map
    const activeDoctorIDs = Array.from(timerMap.keys()).filter(key => key !== 'patientInterval');

    if (activeDoctorIDs.length === 0) {
      return null;
    }

    // Count patients per doctor
    const doctorPatientCounts: Record<string, number> = {};

    for (const doctorID of activeDoctorIDs) {
      doctorPatientCounts[doctorID] = await getDoctorPatientCount(db, doctorID);
    }

    // Find doctor with fewest patients
    const leastBusyDoctorID = Object.entries(doctorPatientCounts).sort(([, countA], [, countB]) => countA - countB)[0][0];
    return leastBusyDoctorID;
  } catch (error) {
    logger.error('Error finding least busy doctor:', error);
    return null;
  } finally {
    client.release();
  }
};

/**
 * Determines if a doctor can notify more patients based on unnotified patients
 * @param db Database connection pool
 * @param doctorAccessID The doctor's accessID
 * @returns True if the doctor has unnotified patients
 */
export const canDoctorNotifyMorePatients = async (db: Pool, doctorAccessID: string): Promise<boolean> => {
  const client = await db.connect();
  
  try {
    // Convert accessID to ID
    const doctorID = await getDoctorIdFromAccessId(db, doctorAccessID);
    if (!doctorID) {
      logger.error(`Doctor with accessID ${doctorAccessID} not found`);
      return false;
    }
    
    // Count unnotified patients for this doctor
    // Using DISTINCT to avoid counting duplicate consultation records for the same patient
    const unnotifiedPatientsQuery = `
      SELECT COUNT(DISTINCT c."patientID") as unnotified_count
      FROM Consultation c
      JOIN Patient p ON c."patientID" = p."patientID"
      JOIN patientslot ps ON p."zohoID" = ps.patient_id
      JOIN range r ON ps.range_id = r.id
      WHERE r."doctorID" = $1
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      AND (c."notificationSent" = false OR c."notificationSent" IS NULL)
      AND c.completed = FALSE
    `;
    
    const unnotifiedResult = await client.query(unnotifiedPatientsQuery, [doctorID]);
    const unnotifiedCount = parseInt(unnotifiedResult.rows[0].unnotified_count, 10);
    
    //get doctor name for logging
    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [doctorAccessID]);
    const doctorName = doctorResult.rows[0].username;
    
    // Log for debugging
    logger.info(`Doctor ${doctorName} - Unnotified patient count: ${unnotifiedCount}`);
    
    // Doctor can notify more if they have unnotified patients
    return unnotifiedCount > 0;
  } catch (error) {
    logger.error(`Error checking if doctor ${doctorAccessID} can notify more patients:`, error);
    return false;
  } finally {
    client.release();
  }
};

/**
 * Gets the next batch of patients to notify for a specific doctor
 * @param db Database connection pool
 * @param doctorAccessID The doctor's accessID
 * @param batchSize Number of patients to retrieve
 * @returns Array of patient IDs to notify
 */
export const getNextPatientsForDoctor = async (
  db: Pool,
  doctorAccessID: string,
  batchSize: number
): Promise<string[]> => {
  const client = await db.connect();

  try {
    // Convert accessID to ID
    const doctorID = await getDoctorIdFromAccessId(db, doctorAccessID);
    if (!doctorID) {
      logger.error(`Doctor with accessID ${doctorAccessID} not found`);
      return [];
    }
    //get doctor name from the query helper
    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [doctorAccessID]);
    const doctorName = doctorResult.rows[0].username;
    
    // Check if the doctor can notify more patients
    const canNotifyMore = await canDoctorNotifyMorePatients(db, doctorAccessID);
    if (!canNotifyMore) {
      logger.info(`Doctor ${doctorName} cannot notify more patients`);
      return [];
    }

    // Query for next patients to notify - now filtering by doctor's assigned patients
    // and joining with zohoID instead of patientID
    const query = `
      WITH LatestConsultation AS (
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
        FROM Consultation c
        WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND (c."notificationSent" = false OR c."notificationSent" IS NULL)
        ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC
      ),
      PatientsWithQueue AS (
        SELECT
          p."patientID",
          p."fullName",
          p."returningPatient",
          p."riskRating",
          lc."notificationSent",
          lc."consultationDate",
          CASE
            WHEN pq."patientID" IS NULL THEN false
            WHEN pq."completedAt" IS NOT NULL THEN false
            ELSE true
          END as in_active_queue
        FROM Patient p
        JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
        LEFT JOIN PatientQueue pq ON p."patientID" = pq."patientID"
        JOIN patientslot ps ON p."zohoID" = ps.patient_id
        JOIN range r ON ps.range_id = r.id
        WHERE lc.completed = false
        AND r."doctorID" = $2
      )
      SELECT pwq."patientID"
      FROM PatientsWithQueue pwq
      ORDER BY pwq."consultationDate" ASC NULLS LAST, pwq."returningPatient" DESC, pwq."riskRating" ASC NULLS LAST
      LIMIT $1
    `;

    const result = await client.query(query, [batchSize, doctorID]);
    const selectedPatientIDs = result.rows.map(row => row.patientID);

    
    if (selectedPatientIDs.length > 0) {
      logger.info(`Selected ${selectedPatientIDs.length} patients for doctor ${doctorName}`);
    } else {
      logger.info(`No available patients found for doctor ${doctorName}`);
    }
    
    return selectedPatientIDs;
  } catch (error) {
    logger.error(`Error getting next patients for doctor ${doctorAccessID}:`, error);
    return [];
  } finally {
    client.release();
  }
};