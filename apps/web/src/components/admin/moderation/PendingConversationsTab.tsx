import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Pagination
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  AttachFile as AttachFileIcon,
  Visibility as ViewIcon,
  MedicalServices as TreatmentIcon,
  Chat as ChatIcon
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import moderationService, { ConversationModeration, PaginationInfo } from '../../../services/moderation.service';
import ConversationDetailsModal from './ConversationDetailsModal';

interface PendingConversationsTabProps {
  onStatsUpdate: () => void;
  onShowSnackbar: (message: string, severity: 'success' | 'error' | 'info') => void;
}

const PendingConversationsTab: React.FC<PendingConversationsTabProps> = ({ onStatsUpdate, onShowSnackbar }) => {
  const [conversations, setConversations] = useState<ConversationModeration[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedConversations, setSelectedConversations] = useState<Set<string>>(new Set());
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 5,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  const [moderationDialog, setModerationDialog] = useState<{
    open: boolean;
    channelId: string | null;
    action: 'approve' | 'reject' | null;
    isBulk: boolean;
  }>({
    open: false,
    channelId: null,
    action: null,
    isBulk: false
  });
  const [reason, setReason] = useState('');
  const [processing, setProcessing] = useState(false);
  const [detailsModal, setDetailsModal] = useState<{
    open: boolean;
    conversation: ConversationModeration | null;
  }>({
    open: false,
    conversation: null
  });

  useEffect(() => {
    loadPendingConversations();
  }, []);

  const loadPendingConversations = async (page: number = pagination.page) => {
    try {
      setLoading(true);
      const response = await moderationService.getPendingConversations(pagination.limit, page);

      // Ensure we always have an array, even if the API response is unexpected
      setConversations(response?.conversations || []);
      setPagination(response?.pagination || {
        page: 1,
        limit: 5,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      });
    } catch (error) {
      console.error('Error loading pending conversations:', error);
      console.error('Full error details:', error);
      onShowSnackbar('Failed to load pending conversations', 'error');
      // Set empty array on error to prevent undefined errors
      setConversations([]);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    loadPendingConversations(page);
  };

  const handleSelectConversation = (channelId: string) => {
    const newSelected = new Set(selectedConversations);
    if (newSelected.has(channelId)) {
      newSelected.delete(channelId);
    } else {
      newSelected.add(channelId);
    }
    setSelectedConversations(newSelected);
  };

  const handleSelectAll = () => {
    if (!conversations || conversations.length === 0) return;

    if (selectedConversations.size === conversations.length) {
      setSelectedConversations(new Set());
    } else {
      setSelectedConversations(new Set(conversations.map(c => c.channelId)));
    }
  };

  const openModerationDialog = (action: 'approve' | 'reject', channelId?: string) => {
    setModerationDialog({
      open: true,
      channelId: channelId || null,
      action,
      isBulk: !channelId
    });
    setReason('');
  };

  const closeModerationDialog = () => {
    setModerationDialog({
      open: false,
      channelId: null,
      action: null,
      isBulk: false
    });
    setReason('');
  };

  // Handle immediate approval without dialog
  const handleImmediateApproval = async (channelId?: string, isBulk: boolean = false) => {
    try {
      setProcessing(true);

      if (isBulk) {
        // Bulk approval
        const channelIds = Array.from(selectedConversations);
        if (channelIds.length === 0) {
          onShowSnackbar('No conversations selected', 'error');
          return;
        }

        // Approve each conversation individually
        for (const id of channelIds) {
          await moderationService.moderateConversation({
            channelId: id,
            action: 'approve',
            reason: undefined
          });
        }

        onShowSnackbar(`Successfully approved ${channelIds.length} conversations`, 'success');
        setSelectedConversations(new Set());
      } else {
        // Single conversation approval
        if (!channelId) return;

        const result = await moderationService.moderateConversation({
          channelId,
          action: 'approve',
          reason: undefined
        });

        onShowSnackbar(result.message, 'success');
      }

      // Refresh data
      await loadPendingConversations();
      onStatsUpdate();
    } catch (error) {
      console.error('Error approving conversation:', error);
      onShowSnackbar('Failed to approve conversation', 'error');
    } finally {
      setProcessing(false);
    }
  };

  const handleDetailsModeration = (channelId: string, action: 'approve' | 'reject') => {
    // Close details modal first
    setDetailsModal({ open: false, conversation: null });

    if (action === 'approve') {
      // Approve immediately without dialog
      handleImmediateApproval(channelId);
    } else {
      // Open moderation dialog for rejection
      openModerationDialog(action, channelId);
    }
  };

  const handleModeration = async () => {
    if (!moderationDialog.action) return;

    try {
      setProcessing(true);

      if (moderationDialog.isBulk) {
        // Bulk moderation - would need to implement bulk conversation moderation
        const channelIds = Array.from(selectedConversations);
        if (channelIds.length === 0) {
          onShowSnackbar('No conversations selected', 'error');
          return;
        }

        // For now, moderate each conversation individually
        for (const channelId of channelIds) {
          await moderationService.moderateConversation({
            channelId,
            action: moderationDialog.action,
            reason: reason || undefined
          });
        }

        onShowSnackbar(`Successfully ${moderationDialog.action}d ${channelIds.length} conversations`, 'success');
        setSelectedConversations(new Set());
      } else {
        // Single conversation moderation
        if (!moderationDialog.channelId) return;

        const result = await moderationService.moderateConversation({
          channelId: moderationDialog.channelId,
          action: moderationDialog.action,
          reason: reason || undefined
        });

        onShowSnackbar(result.message, 'success');
      }

      // Refresh data
      await loadPendingConversations();
      onStatsUpdate();

      closeModerationDialog();
    } catch (error) {
      console.error('Error moderating conversation:', error);
      onShowSnackbar('Failed to moderate conversation', 'error');
    } finally {
      setProcessing(false);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('dd/MM/yyyy HH:mm');
  };

  const formatDuration = (start: string, end: string) => {
    const startTime = DateTime.fromISO(start);
    const endTime = DateTime.fromISO(end);
    const diff = endTime.diff(startTime, ['hours', 'minutes']);
    
    if (diff.hours > 0) {
      return `${Math.floor(diff.hours)}h ${Math.floor(diff.minutes)}m`;
    }
    return `${Math.floor(diff.minutes)}m`;
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!conversations || conversations.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="h6" color="textSecondary">
          No pending conversations
        </Typography>
        <Typography variant="body2" color="textSecondary">
          All conversations have been moderated
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Bulk Actions */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={conversations && selectedConversations.size === conversations.length && conversations.length > 0}
                indeterminate={conversations && selectedConversations.size > 0 && selectedConversations.size < conversations.length}
                onChange={handleSelectAll}
              />
            }
            label={`Select All (${selectedConversations.size} selected)`}
          />
        </Box>

        {selectedConversations.size > 0 && (
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<ApproveIcon />}
              onClick={() => handleImmediateApproval(undefined, true)}
              disabled={processing}
              sx={{
                backgroundColor: '#008000',
                color: 'white',
                '&:hover': {
                  backgroundColor: '#006600'
                }
              }}
            >
              Approve Selected ({selectedConversations.size})
            </Button>
            <Button
              variant="contained"
              color="error"
              startIcon={<RejectIcon />}
              onClick={() => openModerationDialog('reject')}
            >
              Reject Selected ({selectedConversations.size})
            </Button>
          </Box>
        )}
      </Box>

      {/* Conversations Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">Select</TableCell>
              <TableCell>Patient</TableCell>
              <TableCell>Conversation</TableCell>
              <TableCell>Treatment</TableCell>
              <TableCell>Started</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {conversations && conversations.map((conversation) => (
              <TableRow key={conversation.channelId} hover>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedConversations.has(conversation.channelId)}
                    onChange={() => handleSelectConversation(conversation.channelId)}
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="bold">
                      {conversation.patientName}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      ID: {conversation.patientId}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ maxWidth: 300 }}>
                    {/* Message count and duration */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <ChatIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                      <Typography variant="body2" fontWeight="bold">
                        {conversation.messageCount} message{conversation.messageCount !== 1 ? 's' : ''}
                      </Typography>
                      {conversation.firstMessageAt && conversation.lastMessageAt && 
                       conversation.firstMessageAt !== conversation.lastMessageAt && (
                        <Typography variant="caption" color="textSecondary">
                          • {formatDuration(conversation.firstMessageAt, conversation.lastMessageAt)}
                        </Typography>
                      )}
                    </Box>

                    {/* Message preview */}
                    {conversation.messagePreview && (
                      <Typography
                        variant="body2"
                        sx={{
                          wordBreak: 'break-word',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          mb: 1,
                          fontStyle: 'italic',
                          color: 'text.secondary'
                        }}
                      >
                        {conversation.messagePreview}
                      </Typography>
                    )}

                    {/* Attachments indicator */}
                    {conversation.hasAttachments && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <AttachFileIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                        <Typography variant="body2" color="primary.main">
                          Has attachments
                        </Typography>
                      </Box>
                    )}

                    {/* View Details Button */}
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => setDetailsModal({ open: true, conversation })}
                      sx={{ mt: 1 }}
                    >
                      View Conversation
                    </Button>
                  </Box>
                </TableCell>
                <TableCell>
                  {conversation.treatmentOutcome ? (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TreatmentIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                      <Typography variant="body2">
                        {conversation.treatmentOutcome}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="textSecondary" fontStyle="italic">
                      No treatment plan
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatTimestamp(conversation.firstMessageAt || conversation.createdAt)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Approve Conversation">
                      <IconButton
                        onClick={() => handleImmediateApproval(conversation.channelId)}
                        disabled={processing}
                        sx={{
                          color: '#008000',
                          '&:hover': {
                            backgroundColor: 'rgba(0, 128, 0, 0.1)'
                          }
                        }}
                      >
                        <ApproveIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Reject Conversation">
                      <IconButton
                        color="error"
                        onClick={() => openModerationDialog('reject', conversation.channelId)}
                      >
                        <RejectIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination Controls */}
      {!loading && conversations && conversations.length > 0 && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
          <Typography variant="body2" color="textSecondary">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
            {pagination.total} conversations
          </Typography>
          <Pagination
            count={Math.max(pagination.totalPages, 1)}
            page={pagination.page}
            onChange={handlePageChange}
            color="primary"
            showFirstButton
            showLastButton
            size="medium"
            disabled={pagination.totalPages <= 1}
          />
        </Box>
      )}

      {/* Moderation Dialog - Only for rejections */}
      <Dialog open={moderationDialog.open && moderationDialog.action === 'reject'} onClose={closeModerationDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          Reject Conversation{moderationDialog.isBulk ? 's' : ''}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2 }}>
            {moderationDialog.isBulk
              ? `Are you sure you want to reject ${selectedConversations.size} selected conversations?`
              : `Are you sure you want to reject this conversation?`
            }
          </Typography>
          <TextField
            fullWidth
            label="Reason (optional)"
            multiline
            rows={3}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter a reason for this rejection..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={closeModerationDialog} disabled={processing}>
            Cancel
          </Button>
          <Button
            onClick={handleModeration}
            variant="contained"
            color="error"
            disabled={processing}
            startIcon={processing ? <CircularProgress size={20} /> : null}
          >
            {processing ? 'Processing...' : 'Reject Conversation'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Conversation Details Modal */}
      <ConversationDetailsModal
        open={detailsModal.open}
        conversation={detailsModal.conversation}
        onClose={() => setDetailsModal({ open: false, conversation: null })}
        onModerate={handleDetailsModeration}
      />
    </Box>
  );
};

export default PendingConversationsTab;
