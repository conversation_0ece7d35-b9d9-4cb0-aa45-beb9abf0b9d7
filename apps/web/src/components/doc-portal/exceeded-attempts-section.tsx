import { Typography, Grid } from "@mui/material";
import { PatientQueue } from "../../types";
//import { ApiClient } from "../../services";
import { useState } from "react";
// import RefreshIcon from '@mui/icons-material/Refresh';
//import { useSnackbar } from 'notistack'; // Added import
import { formatTimeWithMeridian } from "../../utils";

interface ExceededAttemptsProps {
  patients: PatientQueue[];
  onReset: (patient: PatientQueue) => void;
}

const ExceededAttemptsSection: React.FC<ExceededAttemptsProps> = ({ patients }) => {
  const [_resetting, _setResetting] = useState<Record<string, boolean>>({});
  // const { _enqueueSnackbar } = useSnackbar(); // Get enqueueSnackbar function

  // const handleResetAttempts = async (patient: PatientQueue) => {
  //   if (!patient.patientID) return;

  //   setResetting(prev => ({ ...prev, [patient.patientID]: true }));
  //   try {
  //     const updatedPatient = await ApiClient.resetPatientAttempts(patient.patientID);
  //     onReset(updatedPatient);
  //     enqueueSnackbar(`Attempts reset successfully for ${patient.fullName}`, { variant: 'success' }); // Add success snackbar
  //   } catch (error) {
  //     console.error("Failed to reset attempts:", error);
  //     const errorMessage = error instanceof Error ? error.message : 'Failed to reset attempts';
  //     enqueueSnackbar(errorMessage, { variant: 'error' }); // Add error snackbar
  //   } finally {
  //     setResetting(prev => ({ ...prev, [patient.patientID]: false }));
  //   }
  // };

  if (patients.length === 0) return null;

  return (
    <Grid container direction="column" sx={{ mt: 3, mb: 2, width: '100%', border: '1px solid #ffcccc', borderRadius: 2, p: 2, bgcolor: '#fff9f9' }}>
      <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 'bold', mb: 1, color: '#d32f2f' }}>
        Patients with Exceeded Attempts
      </Typography>

      {patients.map((patient) => (
        <Grid
          container
          key={patient.patientID}
          sx={{
            boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.1)",
            p: 1,
            mr: '4px',
            border: '1px solid rgba(0, 0, 0, 0.1)',
            borderRadius: 2,
            mb: 1,
            backgroundColor: 'white'
          }}
        >
          <Grid item xs={8}>
            <Typography sx={{ fontSize: '0.85rem', fontWeight: '500' }}>
              {patient.fullName}
            </Typography>
            <Typography sx={{ fontSize: '0.7rem' }}>
              Attempts: {patient.attempt || 0}
            </Typography>
            <Typography sx={{ fontSize: '0.7rem' }}>
              Status: {patient.status}
            </Typography>
            <Typography sx={{ fontSize: '0.7rem' }}>
              Last Notified: {patient.notificationSentDateTime ? formatTimeWithMeridian(patient.notificationSentDateTime) : 'N/A'}
            </Typography>
            <Typography sx={{ fontSize: '0.7rem' }}>
              Last Admitted: {patient.admittedAt ? formatTimeWithMeridian(patient.admittedAt) : 'N/A'}
            </Typography>
          </Grid>
          <Grid item xs={4} container justifyContent="flex-end" alignItems="center">
            {/* <Button
              variant="contained"
              size="small"
              startIcon={<RefreshIcon />}
              disabled={resetting[patient.patientID]}
              onClick={() => handleResetAttempts(patient)}
              sx={{
                textTransform: 'none',
                backgroundColor: 'green',
                fontSize: '0.7rem',
                '&:hover': { backgroundColor: 'darkgreen' }
              }}
            >
              Reset Attempts
            </Button> */}
          </Grid>
        </Grid>
      ))}
    </Grid>
  );
};

export default ExceededAttemptsSection;
