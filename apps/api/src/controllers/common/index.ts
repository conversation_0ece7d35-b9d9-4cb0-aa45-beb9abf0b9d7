import { AxiosError } from 'axios';
import { db } from '../../utils/db';
import httpStatus from 'http-status';
import { ApiResponse } from '../../helpers/response';

export type NoShow = {
  id: string;
  queueTag: string;
  has_previous_consultation: boolean;
};

export const verifyNoShowPatient = async (email: string, limit = 4, getAllConsultation = false): Promise<NoShow[]> => {
  const client = await db.connect();
  // Select 4 consecutive no-shows

  const query = `
    WITH LatestConsultation AS (
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.*
        FROM Consultation c
        WHERE c."email" = $1
        ORDER BY c."createdAt" DESC,c."consultationDate", c."patientID" DESC  -- Get the most recent consultation per patient
        ${limit === 0 ? "" : `LIMIT ${limit}`}
        )
    SELECT lc.*
    FROM LatestConsultation lc
    ${getAllConsultation ? '' : `WHERE "queueTag" = 'no-show'`} 
    `;
  try {
    await client.query('BEGIN');
    const result = await client.query(query, [email]);
    await client.query('COMMIT');
    return result.rows as NoShow[];
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiResponse(httpStatus.BAD_REQUEST, 'ERROR', error, undefined, true);
  } finally {
    client.release();
  }
};

export const verifyPreviousConsultation = async (email: string) => {
  const client = await db.connect();

  const query = `SELECT c.*
    FROM consultation c
    WHERE 
      c."email" = $1 and
      c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      AND EXISTS (
        SELECT 1
        FROM consultation c2
        WHERE c2."email" = c."email"
          AND c2."consultationDate" AT TIME ZONE 'Australia/Sydney' < (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      );`;

  try {
    await client.query('BEGIN');
    const result = await client.query(query, [email]);
    await client.query('COMMIT');
    return result.rows as NoShow[];
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiResponse(httpStatus.BAD_REQUEST, 'ERROR', error, undefined, true);
  } finally {
    client.release();
  }
};

export const verifyPreviousNoShowConsultation = async (email: string) => {
  const client = await db.connect();

  const query = `WITH RankedConsultations AS (
        SELECT 
          c.*,
          ROW_NUMBER() OVER (PARTITION BY c."email" ORDER BY c."consultationDate" DESC) AS rn
        FROM consultation c
        WHERE c."email" = $1
      ),
      LastConsultation AS (
        SELECT * FROM RankedConsultations WHERE rn = 1
      ),
      PreviousConsultation AS (
        SELECT * FROM RankedConsultations WHERE rn = 2
      )
      SELECT l.*
      FROM LastConsultation l
      JOIN PreviousConsultation p ON l."email" = p."email"
      WHERE 
        l."queueTag" IS DISTINCT FROM 'no-show' -- patient showed up in the latest
        AND p."queueTag" = 'no-show'`;

  try {
    await client.query('BEGIN');
    const result = await client.query(query, [email]);
    await client.query('COMMIT');
    return result.rows as NoShow[];
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiResponse(httpStatus.BAD_REQUEST, 'ERROR', error, undefined, true);
  } finally {
    client.release();
  }
};

export const verifyPreviousIfPatientHasAnyPreviousBooking = async (email: string): Promise<boolean> => {
  const client = await db.connect();

  const query = `SELECT EXISTS (
  SELECT 1
  FROM consultation
  WHERE "email" = $1
    AND "consultationDate" < (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
) AS has_previous_consultation;`;

  try {
    await client.query('BEGIN');
    const result = await client.query(query, [email]);
    await client.query('COMMIT');
    return result.rows[0].has_previous_consultation ? result.rows[0].has_previous_consultation : false as boolean;
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiResponse(httpStatus.BAD_REQUEST, 'ERROR', error, undefined, true);
  } finally {
    client.release();
  }
};
