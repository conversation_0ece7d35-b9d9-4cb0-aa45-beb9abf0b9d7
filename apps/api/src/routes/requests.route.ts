import express from 'express';
import { validateDr } from '../middlewares/validationMiddleware';
import { getPendingRequests, approveRequest, rejectRequest } from '../controllers/requests';

const router = express.Router();
const currentVersion = 'v1.0';

// Get all pending requests for doctor
router.get(`/${currentVersion}/pending-requests`, validateDr, getPendingRequests);

// Approve a request
router.put(`/${currentVersion}/request/:id/approve`, validateDr, approveRequest);

// Reject a request
router.put(`/${currentVersion}/request/:id/reject`, validateDr, rejectRequest);

export default router;
