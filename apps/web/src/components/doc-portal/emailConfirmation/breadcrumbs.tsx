import { Button, Divider } from "@mui/material";
import Grid from "@mui/material/Grid2";
import CircleOutlinedIcon from '@mui/icons-material/CircleOutlined';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { StepsType } from "../../../types";

type CustomBreadCrumbsProps = {
    isDesktop: boolean,
    step: StepsType,
    handleSelectionChange: (step: StepsType) => void

}

const CustomBreadCrumbs: React.FC<CustomBreadCrumbsProps> = ({ isDesktop, step, handleSelectionChange }) => {
    return (
        <>
            {isDesktop ?
                <Grid container justifyContent={'center'} alignItems={'center'} sx={{ width: '100%' }} spacing={5}>
                    <Grid>
                        <Button
                            variant={step === 'step1' ? 'outlined' : "text"}
                            endIcon={step === 'step1' ? <CheckCircleIcon /> : <CircleOutlinedIcon />}
                            sx={{
                                textTransform: 'none',
                                color: step === 'step1' ? 'green' : '#676767',
                                borderColor: 'green',
                                width: '200px'
                            }}
                            onClick={() => handleSelectionChange('step1')}>
                            Introduction
                        </Button>
                    </Grid>
                    <Grid>
                        <Button variant={step === 'step2' ? 'outlined' : "text"} endIcon={step === 'step2' ? <CheckCircleIcon /> : <CircleOutlinedIcon />} sx={{ textTransform: 'none', color: step === 'step2' ? 'green' : '#676767', borderColor: 'green', width: '200px' }} onClick={() => handleSelectionChange('step2')}>
                            Treatment Plan
                        </Button>
                    </Grid>
                    <Grid>
                        <Button variant={step === 'step3' ? 'outlined' : "text"} endIcon={step === 'step3' ? <CheckCircleIcon /> : <CircleOutlinedIcon />} sx={{ textTransform: 'none', color: step === 'step3' ? 'green' : '#676767', borderColor: 'green', width: '200px' }} onClick={() => handleSelectionChange('step3')}>
                            Strain Advice
                        </Button>
                    </Grid>
                    <Grid>
                        <Button variant={step === 'step4' ? 'outlined' : "text"} endIcon={step === 'step4' ? <CheckCircleIcon /> : <CircleOutlinedIcon />} sx={{ textTransform: 'none', color: step === 'step4' ? 'green' : '#676767', borderColor: 'green', width: '200px' }} onClick={() => handleSelectionChange('step4')}>
                            Review Patient Email
                        </Button>
                    </Grid>
                </Grid> : <Grid container justifyContent={'start'} alignItems={'center'} sx={{ width: '100%' }} spacing={0}>
                    <Grid>
                        <Button
                            endIcon={step === 'step1' ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />}
                            sx={{
                                textTransform: 'none',
                                color: step === 'step1' ? 'green' : '#676767',
                                borderColor: 'green',
                                fontSize: '10px'
                            }}
                            onClick={() => handleSelectionChange('step1')}>
                            Introduction
                        </Button>
                    </Grid>
                    <Grid>
                        <Button endIcon={step === 'step2' ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />} sx={{ textTransform: 'none', color: step === 'step2' ? 'green' : '#676767', borderColor: 'green', fontSize: '10px' }} onClick={() => handleSelectionChange('step2')}>
                            Treatment Plan
                        </Button>
                    </Grid>
                    <Grid>
                        <Button endIcon={step === 'step3' ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />} sx={{ textTransform: 'none', color: step === 'step3' ? 'green' : '#676767', borderColor: 'green', fontSize: '10px' }} onClick={() => handleSelectionChange('step3')}>
                            Strain Advice
                        </Button>
                    </Grid>
                    <Grid>
                        <Button endIcon={step === 'step4' ? <CheckCircleIcon sx={{ width: '12px' }} /> : <CircleOutlinedIcon sx={{ width: '12px' }} />} sx={{ textTransform: 'none', color: step === 'step4' ? 'green' : '#676767', borderColor: 'green', fontSize: '10px' }} onClick={() => handleSelectionChange('step4')}>
                            Patient Email Preview
                        </Button>
                    </Grid>
                    <Divider style={{ width: '100%', marginTop: '10px', marginBottom: '10px' }} />
                </Grid>}
        </>
    )
}

export default CustomBreadCrumbs