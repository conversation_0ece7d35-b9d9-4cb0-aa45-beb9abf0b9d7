import { <PERSON><PERSON>, Divider, Typography, Dialog, DialogContent } from "@mui/material";
import Grid from "@mui/material/Grid2";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import VideoCallIcon from "@mui/icons-material/VideoCall";
import CalendarMonthOutlinedIcon from "@mui/icons-material/CalendarMonthOutlined";
import { usePatient } from "../../hooks/patient-provider";
import { useEffect, useState } from "react";
import { PatientData } from "../../types";
import { Link as LocationLink, useNavigate } from "@tanstack/react-location";
import { getConsultationDate, UserActions } from "../../utils/index";
import { ApiClient } from "../../services";
import { AxiosError } from "axios";
import LoadingScreen from "../../utils/loading-screen";
import { useAuth } from "../../hooks/auth-provider";
import { useTracker } from "../../hooks/activity-tracker-provider";

const PatientOnlineQueue: React.FC = () => {
	const {
		patients,
		updateLockStatus,
		selectedPatient,
		setError,
		setSelectedPatient,
		drId,
		setDrId,
		setPatient,
		timerKey,
		setTimerKey,
	} = usePatient();

	const [isLoading, setIsLoading] = useState(true);
	const navigate = useNavigate();
	const { doctor } = useAuth();
	const [showInfoPopup, setShowInfoPopup] = useState(false);
	const [patientStatusMap, setPatientStatusMap] = useState<Record<string, boolean>>({});
	const { trackActivity, setDocId } = useTracker();
	let intervalsFetchPatient: NodeJS.Timeout | undefined = undefined;

	// Helper function to get doctor's consultation duration from localStorage
	const getDoctorConsultationDuration = (): number => {
		if (doctor?.accessID) {
			const storageKey = `doctorConsultationDuration_${doctor.accessID}`;
			const storedDuration = localStorage.getItem(storageKey);

			if (storedDuration) {
				const duration = parseInt(storedDuration, 10);
				return isNaN(duration) ? 6 : duration;
			}
		}
		return 6; // Default to 6 minutes if no doctor or no stored duration
	};

	// Helper function to determine if a patient should be visible to doctors
	// Now uses real-time status from PatientQueue table and checks doctor assignment
	const shouldShowPatientToDoctor = (patient: PatientData, realTimeStatus: boolean): boolean => {
		// Use real-time status
		const isOnline = realTimeStatus;
		const hasExceededAttempts = patient.attempt && patient.attempt > 0;

		// Admin users can see all online patients without assignment filtering
		if (doctor?.role === "admin" || doctor?.role === "superAdmin") {
			return isOnline && !hasExceededAttempts;
		}

		// Regular doctors only see patients assigned to them
		const currentDoctorID = doctor?.accessID;

		// Handle both null and string "null" cases
		const assignedDoctorID = patient.assignedDoctorID;
		const isValidAssignment =
			assignedDoctorID && assignedDoctorID !== null && assignedDoctorID !== "null" && assignedDoctorID !== "";

		const isAssignedToCurrentDoctor = isValidAssignment && assignedDoctorID === currentDoctorID;
		const canDoctorSeePatient = Boolean(isAssignedToCurrentDoctor);

		return isOnline && !hasExceededAttempts && canDoctorSeePatient;
	};

	const getStatusMessage = (patient: PatientData, realTimeStatus?: boolean) => {
		// Check doctor assignment using consistent logic
		const currentDoctorID = doctor?.accessID || drId;

		if (patient.assignedDoctorID && currentDoctorID !== patient.assignedDoctorID) {
			return "Selected. You cannot join";
		}

		if (patient.assignedDoctorID && currentDoctorID === patient.assignedDoctorID) {
			return "Selected by you.";
		}

		// Use real-time status
		const isOnline = realTimeStatus;

		if (!isOnline) {
			return "Offline";
		}

		if (isOnline) {
			return "Online";
		}

		return "";
	};

	// Wrapper function for filtering patients with real-time status and doctor assignment
	const filterPatientsForDisplay = (patientList: PatientData[]): PatientData[] => {
		return patientList.filter((patient) => {
			const realTimeStatus = patientStatusMap[patient.patientID];
			// Only show patients if we have real-time status data and they're online
			if (realTimeStatus === undefined) {
				return false; // Don't show patients until we have real-time status
			}
			return shouldShowPatientToDoctor(patient, realTimeStatus);
		});
	};

	// Calculate real-time online patient count (all patients for admin, assigned patients for doctors)
	const getOnlinePatientCount = (): number => {
		if (!patients) return 0;

		return patients.filter((patient) => {
			const realTimeStatus = patientStatusMap[patient.patientID];
			// Only count patients if we have real-time status data
			if (realTimeStatus === undefined) {
				return false;
			}
			return shouldShowPatientToDoctor(patient, realTimeStatus);
		}).length;
	};

	// Effect to fetch real-time patient status when patients change
	useEffect(() => {
		if (patients && patients.length > 0) {
			const patientIDs = patients.map((p) => p.patientID);
			ApiClient.getBatchPatientStatus(patientIDs)
				.then((statusMap) => {
					setPatientStatusMap(statusMap);
				})
				.catch((error) => {
					console.error("Error fetching batch patient status:", error);
					// Keep existing cached status on error
				});
		}
	}, [patients]);

	const handleAdmitClicked = async (patient: PatientData) => {
		// Check if patient has exceeded attempts (business rule: attempt > 0 AND noShow = true)
		if (patient.noShow === true && (patient.attempt ?? 0) >= 1) {
			trackActivity(
				"Doctor",
				patient.patientID || "",
				UserActions.TIMEOUT,
				`Manual admission blocked - patient has noShow: true, attempts: ${patient.attempt}`,
				doctor?.id || ""
			);
			return; // Block admission
		}

		const drIdLocal = localStorage.getItem("xdr");
		const doctorID = drIdLocal && drIdLocal !== "" ? drIdLocal : doctor && doctor.accessID ? doctor.accessID : "";
		setSelectedPatient(patient);

		// Update the patient's assignedDoctorID in the PatientQueue table
		// We're still using updateLockStatus but it should be updated to use assignedDoctorID
		updateLockStatus(patient, true, doctorID);

		// Update the patient object with the assignedDoctorID
		patient.assignedDoctorID = doctorID;

		try {
			await ApiClient.alertAwayPatientOnAdmit(patient);
			await ApiClient.postRedirect(patient);
			await ApiClient.postPatientAdmission(patient.patientID, doctorID);
			trackActivity(
				"Doctor",
				patient.patientID || "",
				UserActions.ADMITTED,
				"Patient admitted from Online List",
				doctor?.id || ""
			);
			navigate({ to: "/doctor-consultation", search: { token: patient.patientID } });
		} catch (error: any) {
			if (error?.response?.data?.message === "PATIENT_EXCEEDED_ADMISSION_LIMIT") {
				trackActivity(
					"Doctor",
					patient.patientID || "",
					UserActions.TIMEOUT,
					"Manual admission blocked - patient exceeded admission limit",
					doctorID
				);
			} else {
				throw error; // Re-throw other errors
			}
		}
	};

	useEffect(() => {
		const fetchPatient = async () => {
			try {
				const patients = await ApiClient.getPatientsRedis();
				setPatient(() => {
					return patients?.sort((a, b) => {
						if (a.consultation?.meetingOngoing !== b.consultation?.meetingOngoing) {
							return a.consultation?.meetingOngoing ? -1 : 1;
						}

						if (a.consultation?.consultationDate && b.consultation?.consultationDate) {
							return (
								new Date(a.consultation.consultationDate).getTime() -
								new Date(b.consultation.consultationDate).getTime()
							);
						}

						return 0;
					});
				});
				const drIdLocal = localStorage.getItem("xdr");
				if (drIdLocal) {
					setDrId(drIdLocal);
				}
				setIsLoading(false);
			} catch (error) {
				setIsLoading(false);
				setError(error as AxiosError<unknown, Error>);
			}
		};
		fetchPatient();

		// ✅ Set up periodic refresh to ensure fresh patient data
		// This prevents stale data issues when patients are marked as no-show
		const refreshInterval = setInterval(fetchPatient, 10000); // Refresh every 10 seconds

		return () => {
			clearInterval(refreshInterval);
		};
	}, []);

	useEffect(() => {
		if (doctor) {
			trackActivity(
				"Doctor",
				"",
				UserActions.REACHED,
				`${doctor?.username} is looking at the online list`,
				doctor?.id as string
			);
			setDocId(doctor?.id as string);
		}
	}, [doctor]);

	useEffect(() => {
		const init = async () => {
			try {
				if (selectedPatient) {
					localStorage.removeItem("TpPlan");
					let drIdLocal = null;
					if (!drId) {
						drIdLocal = localStorage.getItem("xdr");
					} else if (!drIdLocal) {
						drIdLocal = doctor?.accessID;
					}

					if (drIdLocal) {
						// Update the patient's assignedDoctorID in the PatientQueue table
						// We're still using updateLockStatus but it should be updated to use assignedDoctorID
						updateLockStatus(selectedPatient, false, drIdLocal);

						// Clear the assignedDoctorID in the patient object
						if (selectedPatient.assignedDoctorID) {
							selectedPatient.assignedDoctorID = undefined;
						}

						await ApiClient.updateMeetingStatus(selectedPatient.patientID, false);
					}

					setSelectedPatient(undefined);
				}

				if (doctor && doctor.role !== "admin") {
					const timer = await ApiClient.fetchTimer(doctor.accessID);
					setTimerKey((prev) => {
						return {
							...prev,
							[doctor.accessID]: timer.timerKey,
						};
					});
				}
			} catch (error) {
				setError(error as AxiosError<unknown, Error>);
			}
		};
		init();
	}, []);

	useEffect(() => {
		const handleVisibilityChange = async () => {
			if (document.visibilityState === "visible") {
				trackActivity(
					"Doctor",
					"",
					UserActions.RETURNED,
					`${doctor?.username} returned to online list screen`,
					doctor?.id || ""
				);
			} else if (document.visibilityState === "hidden") {
				trackActivity(
					"Doctor",
					"",
					UserActions.AWAY,
					`${doctor?.username} left online list screen`,
					doctor?.id || ""
				);
			}
		};

		document.addEventListener("visibilitychange", handleVisibilityChange);

		return () => {
			document.removeEventListener("visibilitychange", handleVisibilityChange);
		};
	}, []);

	useEffect(() => {
		const autoAdmitDoc = async () => {
			if (!intervalsFetchPatient) {
				intervalsFetchPatient = setInterval(async () => {
					// Make sure the shift has started before redirecting people.
					if (doctor?.accessID && timerKey[doctor.accessID]) {
						const result = await ApiClient.fetchNextPatientToConsult(doctor.accessID);
						const nextPatient = result.length > 0 ? result[0] : undefined;
						if (nextPatient) {
							// Use fresh patient data from API instead of stale local state
							// This ensures we have the latest attempt and noShow values
							const freshPatientData: PatientData = {
								...nextPatient,
								assignedDoctorID: nextPatient.assignedDoctorID || undefined,
							};

							// Admit the patient with fresh data
							handleAdmitClicked(freshPatientData);
						}
					}
				}, 2000);
			}
		};

		autoAdmitDoc();

		return () => {
			clearInterval(intervalsFetchPatient);
		};
	}, [patients, timerKey]);

	// Check if the info popup has been shown before
	useEffect(() => {
		// Show the popup immediately when the component mounts
		const hasSeenInfoPopup = localStorage.getItem("hasSeenInfoPopup");
		if (!hasSeenInfoPopup) {
			setShowInfoPopup(true);
		}
	}, []);

	const handleCloseInfoPopup = () => {
		setShowInfoPopup(false);
		localStorage.setItem("hasSeenInfoPopup", "true");
	};

	return (
		<>
			<Dialog
				open={showInfoPopup}
				fullWidth={true}
				maxWidth={"xs"}
				onClose={handleCloseInfoPopup}
				slotProps={{
					paper: {
						sx: {
							borderRadius: "8px",
							boxShadow: "0 4px 20px rgba(0, 0, 0, 0.15)",
						},
					},
				}}
			>
				<DialogContent sx={{ p: 3 }}>
					<Grid
						container
						direction={"column"}
						sx={{ width: "100%" }}
						justifyContent={"center"}
						alignItems={"center"}
					>
						<Typography sx={{ fontSize: "20px", fontWeight: "bold", mb: 3, color: "#2e7d32" }}>
							Queue Management Update
						</Typography>
						{/* <Typography sx={{ fontSize: '15px', lineHeight: 1.5, mb: 2 }} align="center">
                            You have 6 minutes to confirm a Treatment Plan (TP) during consultations. If not confirmed, the consultation will be marked successful, and you'll move to the next patient. Please confirm the TP within 6 minutes.
                        </Typography> */}

						<Typography sx={{ fontSize: "15px", lineHeight: 1.5, mb: 2 }} align="center">
							Our Doctor UI now automatically submits a treatment plan after a{" "}
							{getDoctorConsultationDuration()}-minute consultation. Kindly review and confirm as needed.
							Thank you!
						</Typography>

						<Grid sx={{ mt: 2 }}>
							<Button
								variant="contained"
								onClick={handleCloseInfoPopup}
								sx={{
									backgroundColor: "green",
									px: 4,
									py: 1,
									borderRadius: "4px",
									textTransform: "none",
									fontWeight: "bold",
									"&:hover": {
										backgroundColor: "#1b5e20",
									},
								}}
							>
								I understand
							</Button>
						</Grid>
					</Grid>
				</DialogContent>
			</Dialog>

			<Grid container sx={{ mt: 1 }} justifyContent={"center"}>
				<Grid container size={{ xs: 12, lg: 6 }} direction={"column"} sx={{ mr: { xs: 0, md: 1, lg: 7 } }}>
					<>
						{isLoading && <LoadingScreen />}

						<Grid
							sx={{
								border: "1px solid green",
								borderRadius: 2,
								p: 1,
								flexGrow: 1,
								width: "100%",
							}}
							container
							direction={"column"}
						>
							<Grid
								container
								alignItems={"center"}
								justifyContent={"center"}
								sx={{ mb: 1, width: "100%" }}
							>
								<Grid size={{ lg: 3, xs: 3 }}>
									<Typography sx={{ fontSize: "0.90rem", fontWeight: "bold" }}>
										Total Online: {getOnlinePatientCount()}
									</Typography>
								</Grid>

								<Grid size={{ lg: 6, xs: 6 }} justifyContent="center" container alignItems="center">
									<Typography sx={{ fontSize: "0.90rem", fontWeight: "bold" }}>
										Online Patients
									</Typography>
								</Grid>

								<Grid size={{ lg: 3, xs: 3 }} container justifyContent={"end"}>
									{doctor?.role === "doctor" && (
										<>
											<Button
												variant="contained"
												disabled={doctor?.accessID && timerKey[doctor.accessID] ? true : false}
												sx={{ textTransform: "none", backgroundColor: "green" }}
												onClick={async () => {
													if (doctor) {
														const result = await ApiClient.startTimer(doctor.accessID);
														trackActivity(
															"Doctor",
															"",
															UserActions.SHIFT_STARTED,
															`Doctor ${doctor.username} has started his shift`,
															doctor.id || ""
														);
														setTimerKey((prev) => {
															return {
																...prev,
																[doctor.accessID]: result.timerKey,
															};
														});
													}
												}}
											>
												Start Shift
											</Button>
										</>
									)}
								</Grid>
							</Grid>

							<Divider sx={{ mb: 2 }} />
							<Grid
								sx={{ width: "100%" }}
								container
								direction="column"
								justifyContent="center"
								alignItems="center"
							>
								{getOnlinePatientCount() < 1 && (
									<>
										<Typography sx={{ fontSize: "18px", fontWeight: "bold" }}>
											There is no patient in the waiting room at the moment.
										</Typography>
										<Typography sx={{ fontSize: "14px" }}>
											Once a patient joins the waiting room you will be redirected.
										</Typography>
									</>
								)}
							</Grid>
							<Grid
								sx={{
									overflowY: "auto",
									height: "75dvh",
									"&::-webkit-scrollbar": {
										width: "6px",
									},
									"&::-webkit-scrollbar-track": {
										backgroundColor: "#cbf5dd",
										borderRadius: "10px",
										mt: 1,
										mb: 1,
									},
									"&::-webkit-scrollbar-thumb": {
										backgroundColor: "green",
										borderRadius: "10px",
										p: 2,
									},
									"&::-webkit-scrollbar-button": {
										backgroundColor: "green",
										height: "7px",
										borderRadius: "10px",
									},
								}}
							>
								<>
									{filterPatientsForDisplay(patients || []).map((patient, index) => {
										return (
											<Grid
												container
												sx={{
													boxShadow: "0px 2px 5px rgba(0, 0, 0, 0.2)",
													p: 1,
													mr: "4px",
													border:
														selectedPatient?.patientID === patient.patientID
															? "1px solid green"
															: "1px solid rgba(0, 0, 0, 0.2)",
													borderRadius: 2,
													mb: 1,
													"&:hover": {
														visibility: "visible",
														border: "1px solid green",
													},
												}}
												direction={"column"}
												key={`${index}`}
											>
												<Grid container>
													<Grid sx={{ flexGrow: 1 }} container direction={"column"}>
														<Grid container alignItems={"center"}>
															<Typography
																display="inline"
																sx={{
																	whiteSpace: "nowrap",
																	fontSize: "0.80rem",
																	fontWeight: "500",
																}}
															>
																{`${patient.fullName}`}
															</Typography>
														</Grid>
														<Typography
															display="inline"
															sx={{ whiteSpace: "nowrap", fontSize: "0.65rem", mt: 0.5 }}
														>
															{getStatusMessage(
																patient,
																patientStatusMap[patient.patientID]
															)}
														</Typography>
													</Grid>
													<Grid
														direction={"row"}
														alignItems={"center"}
														justifyContent={"center"}
														container
													>
														<Grid sx={{ mr: 1 }} container>
															<VideoCallIcon
																style={{
																	color: (
																		patientStatusMap[patient.patientID] !==
																		undefined
																			? patientStatusMap[patient.patientID]
																			: !!patient.consultation?.meetingOngoing
																	)
																		? "green"
																		: "grey",
																}}
															/>
														</Grid>
														{/* <Grid>
                                                                    <Button
                                                                        variant="contained"
                                                                        sx={{
                                                                            textTransform: 'none', transform: 'none', borderRadius: '0.50rem',
                                                                            width: '25px',
                                                                            height: '25px',
                                                                            outline: "none",
                                                                            "&:focus": { outline: "none" },
                                                                            "&:focusVisible": { outline: "none" },
                                                                            fontSize: '12px',
                                                                            backgroundColor: patient.consultation?.meetingOngoing ? 'green' : 'grey'
                                                                        }}
                                                                        onClick={() => handleAdmitClicked(patient)}
                                                                        disabled={doctor?.accessID && !timerKey[doctor.accessID] ? true : patient.locked && drId !== patient.drLocked ? true : false}
                                                                    >
                                                                        Admit
                                                                    </Button>
                                                                </Grid> */}
													</Grid>
												</Grid>
												<Grid
													sx={{ mt: 2 }}
													container
													justifyContent={"start"}
													alignItems={"center"}
												>
													<CalendarMonthOutlinedIcon
														sx={{ height: "20px", width: "20px", mr: 1 }}
													/>
													<Typography sx={{ fontSize: "0.70rem" }}>
														{getConsultationDate(
															patient.consultation?.consultationDate,
															patient.consultation?.consultationEnd
														)}
													</Typography>
												</Grid>
												<Grid
													container
													justifyContent={"start"}
													alignItems={"center"}
													sx={{ mt: 2 }}
												>
													<Grid
														sx={{ flexGrow: 1 }}
														container
														justifyContent={"start"}
														alignItems={"center"}
													>
														<Typography sx={{ fontSize: "12px" }} display={"inline"}>
															{patient.returningPatient
																? " Returning Patient"
																: "New Patient"}
														</Typography>
													</Grid>
													{patient.returningPatient && (
														<Grid container justifyContent={"center"} alignItems={"center"}>
															<LocationLink
																to={"/patient-history"}
																search={{ token: patient.patientID }}
															>
																<Button
																	sx={{
																		fontSize: "12px",
																		color: "green",
																		fontWeight: "bold",
																		mr: 1,
																		textTransform: "none",
																	}}
																	component="button"
																	endIcon={
																		<ArrowForwardIcon
																			sx={{ width: "12px", ml: 0.5 }}
																		/>
																	}
																>
																	View History
																</Button>
															</LocationLink>
														</Grid>
													)}
												</Grid>
											</Grid>
										);
									})}
								</>
							</Grid>
						</Grid>
					</>
				</Grid>
			</Grid>
		</>
	);
};

export default PatientOnlineQueue;
