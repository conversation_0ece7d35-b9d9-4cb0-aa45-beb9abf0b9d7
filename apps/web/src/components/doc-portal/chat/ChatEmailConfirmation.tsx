import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Grid,
  Dialog,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Snackbar,
  Container,
  Paper,
  Collapse
} from "@mui/material";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import { useNavigate } from "@tanstack/react-location";
import { useAuth } from "../../../hooks/auth-provider";
import { ApiClient } from "../../../services";
import { PatientTreatmentPlan } from "../../../types";
import { useChatEmail } from "../../../hooks/chat-email-provider";
import ChatBreadcrumbs, { StepsType } from "./ChatBreadcrumbs";
import ChatConsultationMessage from "./ChatConsultationMessage";
import ChatTreatmentPlanDetails from "./ChatTreatmentPlanDetails";
import ChatStrainAdvice from "./ChatStrainAdvice";
import ChatEmailPreview from "./ChatEmailPreview";
import { AxiosError } from "axios";
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { EmailProvider, useEmailContext } from "../../../hooks/email-provider";

export interface ChatEmailConfirmationProps {
    onBack?: () => void;
    onComplete?: () => void;
    isDirectConfirmation?: boolean;
}

const ChatEmailConfirmation: React.FC<ChatEmailConfirmationProps> = ({
    onComplete,
    isDirectConfirmation = false
}) => {
    // Component mounts with isDirectConfirmation flag
    const [step, setStep] = useState<StepsType>('step1');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | undefined>();
    const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
    const [showSnackbar, setShowSnackbar] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
    const [directConfirmationComplete, setDirectConfirmationComplete] = useState(false);
    const [completedSteps, setCompletedSteps] = useState<Record<StepsType, boolean>>({
        step1: false,
        step2: false,
        step3: false,
        step4: false
    });
    // Ref to track if submission has already been initiated
    const hasSubmittedRef = useRef(false);
    const navigate = useNavigate();
    const { doctor } = useAuth();
    const { strainAdvice } = useChatEmail();
    const { listValuesSativa, listValuesIndica, listValuesHybrid } = useEmailContext();

    const handleSubmit = async () => {
        setOpenConfirmDialog(true);
    };

    const handleConfirmSubmit = useCallback(async () => {
        // Prevent multiple submissions
        if (hasSubmittedRef.current || isSubmitting) {
            console.log('Submission already in progress, preventing duplicate submission');
            return;
        }

        hasSubmittedRef.current = true;

        try {
            setIsSubmitting(true);
            setError(undefined);

            const savedPlan = localStorage.getItem('TpPlan');
            if (!savedPlan) {
                throw new Error('No treatment plan found');
            }

            // Parse the saved plan and ensure all values are in the correct format
            const parsedPlan = JSON.parse(savedPlan);

            // Ensure numeric values are converted to strings for the API
            if (parsedPlan["22"]) {
                parsedPlan["22"].numberOfRepeat = String(parsedPlan["22"].numberOfRepeat);
                parsedPlan["22"].supplyInterval = String(parsedPlan["22"].supplyInterval);
            }

            if (parsedPlan["29"]) {
                parsedPlan["29"].numberOfRepeat = String(parsedPlan["29"].numberOfRepeat);
                parsedPlan["29"].supplyInterval = String(parsedPlan["29"].supplyInterval);
            }

            const treatmentPlan = parsedPlan as PatientTreatmentPlan;
            if (!treatmentPlan.patient?.patientID) {
                throw new Error('Invalid patient data');
            }

            // Check if zohoID already exists in treatment plan
            const zohoID = (treatmentPlan as any).zohoID;

            // Get doctor ID with fallback to localStorage
            const drIdLocal = localStorage.getItem('xdr');
            const doctorID = treatmentPlan.drId || doctor?.accessID || drIdLocal || '';

            // Use zohoID from the treatment plan (which was set during form submission)
            let finalZohoID = zohoID || treatmentPlan.patient?.zohoID;

            // If we still don't have a zohoID, try to extract it from the patientID
            if (!finalZohoID && treatmentPlan.patient?.patientID) {
                // Sometimes patientID is prefixed with 'p' followed by the zohoID
                if (treatmentPlan.patient.patientID.startsWith('p')) {
                    finalZohoID = treatmentPlan.patient.patientID.substring(1);
                    console.log('Extracted zohoID from patientID:', finalZohoID);
                } else {
                    // Otherwise, just use the patientID as a fallback
                    finalZohoID = treatmentPlan.patient.patientID;
                    console.log('Using patientID as zohoID fallback:', finalZohoID);
                }
            }

            if (!finalZohoID) {
                throw new Error('No zohoID found in treatment plan. The prescription lead should have been created during form submission.');
            }

            // Convert strain advice to array format
            const strainLines = strainAdvice.split('\n');
            const checkedSativa: string[] = [];
            const checkedIndica: string[] = [];
            const checkedHybrid: string[] = [];

            let currentType: 'sativa' | 'indica' | 'hybrid' | null = null;

            // Improved strain parsing with better logging and error handling
            for (const line of strainLines) {
                const trimmedLine = line.trim();

                // Skip empty lines
                if (!trimmedLine) continue;

                // Check for section headers
                if (trimmedLine.toLowerCase().includes('sativa strain')) {
                    currentType = 'sativa';
                } else if (trimmedLine.toLowerCase().includes('indica strain')) {
                    currentType = 'indica';
                } else if (trimmedLine.toLowerCase().includes('hybrid strain')) {
                    currentType = 'hybrid';
                } else if (trimmedLine.startsWith('-')) {
                    // Extract strain name, trim any whitespace
                    const strain = trimmedLine.substring(1).trim();

                    // Add to appropriate array
                    if (currentType === 'sativa') checkedSativa.push(strain);
                    if (currentType === 'indica') checkedIndica.push(strain);
                    if (currentType === 'hybrid') checkedHybrid.push(strain);
                }
            }

            // If arrays are still empty but strainAdvice exists, try alternative parsing
            if (strainAdvice && checkedSativa.length === 0 && checkedIndica.length === 0 && checkedHybrid.length === 0) {
                // Alternative parsing for differently formatted advice
                const sativaMatch = strainAdvice.match(/Sativa[^-]*(-[^-\n]*)+/i);
                const indicaMatch = strainAdvice.match(/Indica[^-]*(-[^-\n]*)+/i);
                const hybridMatch = strainAdvice.match(/Hybrid[^-]*(-[^-\n]*)+/i);

                if (sativaMatch) {
                    const strains = sativaMatch[0].match(/-\s*([^\n-]+)/g);
                    if (strains) {
                        checkedSativa.push(...strains.map(s => s.replace(/-\s*/, '').trim()));
                    }
                }

                if (indicaMatch) {
                    const strains = indicaMatch[0].match(/-\s*([^\n-]+)/g);
                    if (strains) {
                        checkedIndica.push(...strains.map(s => s.replace(/-\s*/, '').trim()));
                    }
                }

                if (hybridMatch) {
                    const strains = hybridMatch[0].match(/-\s*([^\n-]+)/g);
                    if (strains) {
                        checkedHybrid.push(...strains.map(s => s.replace(/-\s*/, '').trim()));
                    }
                }
            }

            // As a fallback, if the user selected any strain type but our parsing failed to extract it,
            // add the actual strain advice text to ensure the user's selection is represented
            if (strainAdvice.toLowerCase().includes('sativa') && checkedSativa.length === 0) {
                checkedSativa.push(listValuesSativa.option1);
            }
            if (strainAdvice.toLowerCase().includes('indica') && checkedIndica.length === 0) {
                checkedIndica.push(listValuesIndica.option1);
            }
            if (strainAdvice.toLowerCase().includes('hybrid') && checkedHybrid.length === 0) {
                checkedHybrid.push(listValuesHybrid.option1);
            }

            // Create a simplified plan with the exact structure expected by the API
            const planWithDoctor = {
                patient: {
                    patientID: treatmentPlan.patient?.patientID,
                    // Add email with fallback to empty string to ensure it's always present
                    email: treatmentPlan.patient?.email || '',
                    // Extract fullName from patient or create from patientID
                    fullName: treatmentPlan.patient?.fullName || `Patient ${treatmentPlan.patient?.patientID}`,
                    // Set returningPatient to true for direct confirmation
                    returningPatient: true,
                    // Only include consultation if it has an id
                    consultation: treatmentPlan.patient?.consultation?.id ? {
                        id: treatmentPlan.patient.consultation.id
                    } : undefined,
                    // Ensure zohoID is present
                    zohoID: treatmentPlan.patient?.zohoID || finalZohoID
                },
                outcome: treatmentPlan.outcome || 'Approve Unrestricted',
                drNotes: treatmentPlan.drNotes !== undefined ? treatmentPlan.drNotes : 'Treatment plan approved via chat',
                mentalHealthSupportingDocumentation: treatmentPlan.mentalHealthSupportingDocumentation || 'No',
                date: treatmentPlan.date || new Date().toISOString(),
                drId: doctorID,
                drName: treatmentPlan.drName || '',
                drAphraNumber: treatmentPlan.drAphraNumber || doctor?.aphraNumber,
                zohoID: finalZohoID,
                // Only include the strength data that was in the original plan
                ...(treatmentPlan["22"] ? {
                    "22": {
                        dosePerDay: String(treatmentPlan["22"].dosePerDay || '1.0'),
                        maxDosePerDay: String(treatmentPlan["22"].maxDosePerDay || '2.0'),
                        totalQuantity: String(treatmentPlan["22"].totalQuantity || '28'),
                        numberOfRepeat: String(treatmentPlan["22"].numberOfRepeat || '3'),
                        supplyInterval: String(treatmentPlan["22"].supplyInterval || '28')
                    }
                } : {}),
                // Only include 29% data if it was in the original plan
                ...(treatmentPlan["29"] ? {
                    "29": {
                        dosePerDay: String(treatmentPlan["29"].dosePerDay || '0.1'),
                        maxDosePerDay: String(treatmentPlan["29"].maxDosePerDay || '1.0'),
                        totalQuantity: String(treatmentPlan["29"].totalQuantity || '28'),
                        numberOfRepeat: String(treatmentPlan["29"].numberOfRepeat || '3'),
                        supplyInterval: String(treatmentPlan["29"].supplyInterval || '28')
                    }
                } : {}),
                email: {
                    introMessage: {
                        intro: 'As discussed, your Treatment Plan has now been approved. Below, you\'ll find all the necessary details, including the steps to obtain your treatment',
                        conclusion: 'You now have access to the Harvest Members Only shop, where you can purchase the therapeutic products outlined in your Treatment Plan. Please note that only the products included in your plan will be available for purchase.'
                    },
                    listTitle: {
                        title1: 'Possible Side Effects',
                        title2: 'Contraindications',
                        title3: 'Important Information',
                    },
                    listItemText: {
                        item1: 'Drowsiness, dizziness, dry mouth, euphoria, anxiety, or paranoia',
                        item2: 'Not recommended for pregnant or breastfeeding women, individuals with psychotic disorders, heart conditions, or those operating heavy machinery',
                        item3: 'Start with a low dose and gradually increase as needed. Avoid alcohol and consult with your healthcare provider about potential drug interactions',
                    },
                    checkedSativa,
                    checkedIndica,
                    checkedHybrid,
                    otherTreatment: {
                        otreat1: 'Lifestyle changes (exercise, diet, sleep hygiene, stress management)',
                        otreat2: 'Physical therapy',
                        otreat3: 'Cognitive Behavioral Therapy (CBT)',
                        otreat4: 'Over-the-counter medications (paracetamol, ibuprofen)',
                        otreat5: 'Prescription medications (antidepressants, anti-anxiety meds, pain relievers)',
                        otreat6: 'Acupuncture',
                        otreat7: 'Herbal supplements (turmeric, valerian root)',
                        otreat8: 'Specialist care (pain management, rheumatologist)'
                    }
                }
            };

            try {
                console.log('Sending treatment plan to API:', JSON.stringify(planWithDoctor, null, 2));

                // Check if we have all required fields before sending
                if (!planWithDoctor.patient?.patientID) {
                    throw new Error('Patient ID is required');
                }

                if (!planWithDoctor.zohoID) {
                    throw new Error('Zoho ID is required');
                }

                // Try using the regular treatment plan endpoint as a fallback if needed
                try {
                    // Make the API call with additional error handling
                    await ApiClient.postChatTreatmentPlan(planWithDoctor);
                } catch (chatApiError) {
                    console.error('Chat treatment plan API call failed, trying regular treatment plan endpoint');
                    // Try the regular treatment plan endpoint as a fallback
                    await ApiClient.postTreamentPlan(planWithDoctor);
                }

                // Clear localStorage immediately
                localStorage.removeItem('TpPlan');
                setOpenConfirmDialog(false);

                // Show success message
                setSnackbarMessage('Treatment plan submitted successfully');
                setSnackbarSeverity('success');
                setShowSnackbar(true);

                // If this is a direct confirmation, update the state to show success
                if (isDirectConfirmation) {
                    setDirectConfirmationComplete(true);
                }

                // Wait for snackbar to be visible before completing
                setTimeout(() => {
                    if (onComplete) {
                        onComplete();
                    } else {
                        navigate({ to: '/doc' });
                    }
                }, 2000);

            } catch (error: any) {
                const apiError = error as AxiosError<any>;
                let errorMessage = 'Failed to submit treatment plan';

                if (apiError.response?.data) {
                    const responseData = apiError.response.data;
                    errorMessage = `API error: ${responseData.message || JSON.stringify(responseData)}`;
                } else if (apiError.response) {
                    errorMessage = `API error: ${apiError.response.status} - ${apiError.response.statusText}`;
                } else if (apiError.message) {
                    errorMessage = `API error: ${apiError.message}`;
                }

                // Check specifically for zohoID issues
                if (errorMessage.includes('Zoho ID is required')) {
                    errorMessage = 'Zoho ID is missing or invalid. Please try again or contact support.';
                }

                setSnackbarMessage(errorMessage);
                setSnackbarSeverity('error');
                setShowSnackbar(true);

                // If this is a direct confirmation, update the state to show error
                if (isDirectConfirmation) {
                    // Keep directConfirmationComplete as false to show the error state
                    // The error will be shown in the snackbar
                }
            } finally {
                setIsSubmitting(false);
                // Reset submission state if there was an error
                if (snackbarSeverity === 'error') {
                    hasSubmittedRef.current = false;
                }
            }
        } catch (err) {
            console.error('Failed to submit treatment plan:', err);
            setError(err instanceof Error ? err.message : 'Failed to submit treatment plan');
            setIsSubmitting(false);
            hasSubmittedRef.current = false;
        }
    }, [navigate, doctor, strainAdvice]);

    // Effect to handle direct confirmation flow
    useEffect(() => {
        // Only run this effect once when the component mounts with isDirectConfirmation=true
        if (isDirectConfirmation && !hasSubmittedRef.current) {
            // Mark all steps as completed
            setCompletedSteps({
                step1: true,
                step2: true,
                step3: true,
                step4: true
            });

            // Set the step to the final step
            setStep('step4');

            // Automatically trigger submission after a short delay
            const timer = setTimeout(() => {
                // Skip the confirmation dialog and directly call handleConfirmSubmit
                handleConfirmSubmit();
            }, 1000);

            // Clean up the timer if the component unmounts
            return () => clearTimeout(timer);
        }
    }, [isDirectConfirmation, handleConfirmSubmit]);

    const handleStepChange = (newStep: StepsType) => {
        setStep(newStep);
    };

    const handleStepComplete = (stepName: StepsType) => {
        setCompletedSteps(prev => ({
            ...prev,
            [stepName]: true
        }));

        // Move to next step
        const steps: StepsType[] = ['step1', 'step2', 'step3', 'step4'];
        const currentIndex = steps.indexOf(stepName);
        if (currentIndex < steps.length - 1) {
            handleStepChange(steps[currentIndex + 1]);
        }
    };

    const getStepTitle = (stepName: StepsType) => {
        switch(stepName) {
            case 'step1':
                return 'Introduction';
            case 'step2':
                return 'Treatment Plan';
            case 'step3':
                return 'Strain Advice';
            case 'step4':
                return 'Review Patient Email';
            default:
                return 'Unknown Step';
        }
    };

    const renderStep = (stepName: StepsType) => {
        const isActive = step === stepName;
        const isCompleted = completedSteps[stepName];

        return (
            <Paper
                elevation={isActive ? 3 : 0}
                sx={{
                    mb: 2,
                    width: '100%',
                    overflow: 'hidden',
                    border: isActive ? '1px solid #e0e0e0' : 'none',
                    borderRadius: 2,
                    transition: 'all 0.3s ease'
                }}
            >
                <Box
                    onClick={() => handleStepChange(stepName)}
                    sx={{
                        p: 2,
                        bgcolor: isActive ? 'green' : isCompleted ? '#f0f7f0' : '#f5f5f5',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        cursor: 'pointer',
                        borderBottom: isActive ? '1px solid #e0e0e0' : 'none'
                    }}
                >
                    <Typography
                        variant="subtitle1"
                        sx={{
                            fontWeight: 'medium',
                            color: isActive ? 'white' : isCompleted ? 'green' : '#333'
                        }}
                    >
                        {getStepTitle(stepName)}
                    </Typography>
                    {isCompleted && !isActive && (
                        <CheckCircleIcon sx={{ color: 'green' }} />
                    )}
                    {isActive && (
                        <ExpandMoreIcon sx={{ color: 'white' }} />
                    )}
                </Box>

                <Collapse in={isActive}>
                    <Box sx={{ p: { xs: 0, sm: 1 }, bgcolor: 'white' }}>
                        {stepName === 'step1' && (
                            <ChatConsultationMessage />
                        )}
                        {stepName === 'step2' && (
                            <ChatTreatmentPlanDetails />
                        )}
                        {stepName === 'step3' && (
                            <EmailProvider>
                                <ChatStrainAdvice onComplete={() => handleStepComplete('step3')} />
                            </EmailProvider>
                        )}
                        {stepName === 'step4' && (
                            <ChatEmailPreview onSubmit={handleSubmit} />
                        )}

                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                            {stepName !== 'step4' ? (
                                <Button
                                    variant="contained"
                                    onClick={() => handleStepComplete(stepName)}
                                    sx={{
                                        bgcolor: 'green',
                                        '&:hover': {
                                            bgcolor: 'darkgreen',
                                        },
                                        fontSize: '14px',
                                        textTransform: 'none'
                                    }}
                                >
                                    Continue
                                </Button>
                            ) : null}
                        </Box>
                    </Box>
                </Collapse>
            </Paper>
        );
    };

    // Render a simplified view for direct confirmation
    if (isDirectConfirmation) {
        return (
            <Container
                maxWidth="lg"
                disableGutters
                sx={{
                    px: { xs: 0, sm: 1 },
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    width: '100%'
                }}
            >
                <Paper
                    elevation={0}
                    sx={{
                        width: '100%',
                        mb: 2,
                        bgcolor: '#f5f5f5',
                        p: { xs: 1, md: 2 },
                        borderRadius: 2,
                    }}
                >
                    <Typography variant="h5" sx={{
                        color: '#3B3B3B',
                        mb: 1,
                        fontWeight: 600,
                        fontSize: { xs: '1.2rem', sm: '1.5rem' }
                    }}>
                        {directConfirmationComplete ? 'Treatment Plan Confirmed' : 'Confirming Previous Treatment Plan'}
                    </Typography>
                </Paper>

                <Box
                    sx={{
                        width: '100%',
                        maxWidth: '800px',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        p: 4,
                        bgcolor: 'white',
                        borderRadius: 2,
                        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)'
                    }}
                >
                    {directConfirmationComplete ? (
                        <>
                            <CheckCircleIcon sx={{ fontSize: 60, mb: 3, color: 'green' }} />
                            <Typography variant="h6" sx={{ mb: 2, textAlign: 'center', color: '#3B3B3B' }}>
                                Treatment plan submitted successfully!
                            </Typography>
                            <Typography variant="body1" sx={{ textAlign: 'center', color: 'text.secondary', mb: 3 }}>
                                The previous treatment plan has been confirmed and submitted.
                            </Typography>
                            
                        </>
                    ) : (
                        <>
                            <CircularProgress size={60} sx={{ mb: 3, color: 'green' }} />
                            <Typography variant="h6" sx={{ mb: 2, textAlign: 'center', color: '#3B3B3B' }}>
                                Submitting the same treatment plan...
                            </Typography>
                            <Typography variant="body1" sx={{ textAlign: 'center', color: 'text.secondary', mb: 3 }}>
                                Please wait while we process your request.
                            </Typography>
                            <Typography variant="body2" sx={{ textAlign: 'center', color: 'green', fontStyle: 'italic' }}>
                                Using the previous treatment plan details without requiring manual confirmation.
                            </Typography>
                        </>
                    )}
                </Box>

                {/* Add Snackbar for direct confirmation notifications */}
                <Snackbar
                    open={showSnackbar}
                    autoHideDuration={5000}
                    onClose={() => setShowSnackbar(false)}
                >
                    <Alert
                        onClose={() => setShowSnackbar(false)}
                        severity={snackbarSeverity}
                        sx={{ width: '100%' }}
                    >
                        {snackbarMessage}
                    </Alert>
                </Snackbar>
            </Container>
        );
    }

    // Regular view with all steps
    return (
        <Container
            maxWidth="lg"
            disableGutters
            sx={{
                px: { xs: 0, sm: 1 },
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%'
            }}
        >
            <Paper
                elevation={0}
                sx={{
                    width: '100%',
                    mb: 2,
                    bgcolor: '#f5f5f5',
                    p: { xs: 1, md: 2 },
                    borderRadius: 2,
                }}
            >
                <Typography variant="h5" sx={{
                    color: '#3B3B3B',
                    mb: 1,
                    fontWeight: 600,
                    fontSize: { xs: '1.2rem', sm: '1.5rem' }
                }}>
                    Email Confirmation
                </Typography>
                <ChatBreadcrumbs step={step} onStepChange={handleStepChange} />
            </Paper>

            <Box sx={{ width: '100%', maxWidth: '800px' }}>
                {renderStep('step1')}
                {renderStep('step2')}
                {renderStep('step3')}
                {renderStep('step4')}
            </Box>

            {/* Confirmation Dialog */}
            <Dialog
                open={openConfirmDialog}
                onClose={() => !isSubmitting && setOpenConfirmDialog(false)}
                maxWidth="sm"
                fullWidth
            >
                <DialogContent>
                    <Grid container direction="column" alignItems="center" spacing={2}>
                        <Grid item>
                            <Typography variant="h6">
                                Confirm Treatment Plan
                            </Typography>
                        </Grid>
                        <Grid item>
                            <Typography>
                                Are you sure you want to submit this treatment plan?
                            </Typography>
                        </Grid>
                        {error && (
                            <Grid item>
                                <Typography color="error">
                                    {error}
                                </Typography>
                            </Grid>
                        )}
                    </Grid>
                </DialogContent>
                <DialogActions>
                    <Button
                        onClick={() => setOpenConfirmDialog(false)}
                        disabled={isSubmitting}
                        sx={{
                            color: '#676767',
                            fontSize: '14px',
                            textTransform: 'none'
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmSubmit}
                        variant="contained"
                        sx={{
                            bgcolor: 'green',
                            '&:hover': {
                                bgcolor: 'darkgreen',
                            },
                            fontSize: '14px',
                            textTransform: 'none'
                        }}
                        disabled={isSubmitting}
                        startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
                    >
                        {isSubmitting ? 'Submitting...' : 'Confirm'}
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Add Snackbar for notifications */}
            <Snackbar
                open={showSnackbar}
                autoHideDuration={5000}
                onClose={() => setShowSnackbar(false)}
            >
                <Alert
                    onClose={() => setShowSnackbar(false)}
                    severity={snackbarSeverity}
                    sx={{ width: '100%' }}
                >
                    {snackbarMessage}
                </Alert>
            </Snackbar>
        </Container>
    );
};

export default ChatEmailConfirmation;
