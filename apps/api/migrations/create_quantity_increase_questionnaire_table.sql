-- Migration: Create quantity_increase_questionnaire table
-- Date: 2025-01-17
-- Purpose: Store questionnaire responses for patients requesting quantity increases

-- Create the quantity_increase_questionnaire table with columns matching existing questionnaire tables
CREATE TABLE IF NOT EXISTS quantity_increase_questionnaire (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id VARCHAR(255),
    email TEXT NOT NULL,
    zoho_id TEXT,
    questionnaire_data JSONB NOT NULL,
    selected_strengths TEXT[] NOT NULL, -- Array of strengths (e.g., ['22', '29'])
    strength_requests JSONB NOT NULL, -- Array of strength request objects
    total_score INTEGER NOT NULL DEFAULT 0,
    max_score INTEGER NOT NULL DEFAULT 50,
    is_eligible BOOLEAN NOT NULL DEFAULT false,
    status TEXT NOT NULL DEFAULT 'submitted',
    reviewed_by TEXT,
    reviewed_at TIMESTAMPTZ,
    review_notes TEXT,
    approved_by TEXT,
    approved_at TIMESTAMPTZ,
    approval_notes TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    slack_message_ts TEXT,
    
    -- Foreign key constraints
    CONSTRAINT fk_quantity_increase_patient_id 
        FOREIGN KEY (patient_id) REFERENCES Patient("patientID") ON DELETE CASCADE,
    CONSTRAINT fk_quantity_increase_email 
        FOREIGN KEY (email) REFERENCES Patient(email) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- Check constraints
    CONSTRAINT chk_quantity_increase_status 
        CHECK (status IN ('submitted', 'pending', 'approved', 'rejected')),
    CONSTRAINT chk_quantity_increase_score_range 
        CHECK (total_score >= 0 AND total_score <= max_score)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quantity_increase_patient_id ON quantity_increase_questionnaire(patient_id);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_email ON quantity_increase_questionnaire(email);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_status ON quantity_increase_questionnaire(status);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_created_at ON quantity_increase_questionnaire(created_at);
CREATE INDEX IF NOT EXISTS idx_quantity_increase_slack_message_ts ON quantity_increase_questionnaire(slack_message_ts) 
    WHERE slack_message_ts IS NOT NULL;

-- Add comments to document the table and columns
COMMENT ON TABLE quantity_increase_questionnaire IS 'Stores questionnaire responses for patients requesting quantity increases for their existing THC strengths';
COMMENT ON COLUMN quantity_increase_questionnaire.id IS 'Primary key - UUID for the questionnaire submission';
COMMENT ON COLUMN quantity_increase_questionnaire.patient_id IS 'Patient ID from the Patient table';
COMMENT ON COLUMN quantity_increase_questionnaire.email IS 'Patient email address';
COMMENT ON COLUMN quantity_increase_questionnaire.zoho_id IS 'Zoho CRM ID for the patient';
COMMENT ON COLUMN quantity_increase_questionnaire.questionnaire_data IS 'JSONB containing the complete questionnaire responses and scoring details';
COMMENT ON COLUMN quantity_increase_questionnaire.selected_strengths IS 'Array of selected THC strengths (e.g., [22, 29])';
COMMENT ON COLUMN quantity_increase_questionnaire.strength_requests IS 'JSONB array containing strength request objects with current/requested quantities';
COMMENT ON COLUMN quantity_increase_questionnaire.total_score IS 'Total score achieved by the patient (0-50)';
COMMENT ON COLUMN quantity_increase_questionnaire.max_score IS 'Maximum possible score (default: 50)';
COMMENT ON COLUMN quantity_increase_questionnaire.is_eligible IS 'Boolean indicating if patient scored above threshold';
COMMENT ON COLUMN quantity_increase_questionnaire.status IS 'Current status: submitted, pending, approved, or rejected';
COMMENT ON COLUMN quantity_increase_questionnaire.created_at IS 'Timestamp when questionnaire was submitted';
COMMENT ON COLUMN quantity_increase_questionnaire.reviewed_at IS 'Timestamp when questionnaire was reviewed by doctor';
COMMENT ON COLUMN quantity_increase_questionnaire.approved_at IS 'Timestamp when questionnaire was approved';
COMMENT ON COLUMN quantity_increase_questionnaire.approved_by IS 'Doctor ID who approved the request';
COMMENT ON COLUMN quantity_increase_questionnaire.reviewed_by IS 'Doctor ID who reviewed/rejected the request';
COMMENT ON COLUMN quantity_increase_questionnaire.review_notes IS 'Doctor notes for approval/rejection';
COMMENT ON COLUMN quantity_increase_questionnaire.approval_notes IS 'Doctor notes for approval';
COMMENT ON COLUMN quantity_increase_questionnaire.ip_address IS 'IP address of patient when submitting questionnaire';
COMMENT ON COLUMN quantity_increase_questionnaire.user_agent IS 'User agent of patient when submitting questionnaire';
COMMENT ON COLUMN quantity_increase_questionnaire.slack_message_ts IS 'Slack message timestamp for threaded replies when moderating requests';

-- Verify the table was created successfully
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'quantity_increase_questionnaire' 
ORDER BY ordinal_position;

-- Verify indexes were created
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'quantity_increase_questionnaire'
ORDER BY indexname;

-- Verify constraints were created
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'quantity_increase_questionnaire'
ORDER BY constraint_name;
