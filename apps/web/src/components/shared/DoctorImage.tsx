import React, { useState } from 'react';
import { getDoctorImageUrl } from '../../utils/doctorImages';

interface DoctorImageProps {
  doctorName?: string;
  style?: React.CSSProperties;
  className?: string;
  alt?: string;
}

/**
 * Component that displays a doctor's image with fallback support for multiple formats
 */
const DoctorImage: React.FC<DoctorImageProps> = ({ 
  doctorName, 
  style, 
  className,
  alt 
}) => {
  const [currentFormat, setCurrentFormat] = useState<string>('jpg');
  const [fallbackTriggered, setFallbackTriggered] = useState(false);
  
  // Base filename without extension
  const baseFilename = doctorName ? 
    doctorName.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_\.]/g, '')
      .replace(/^dr_?/, 'dr_') : 
    '';
  
  // Get the initial image URL
  const initialImageUrl = getDoctorImageUrl(doctorName);
  
  // Handle image loading error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    // If we've already tried all formats, use the default image
    if (fallbackTriggered) {
      e.currentTarget.src = '/images/default_user.png';
      return;
    }
    
    // Try different formats in sequence
    switch (currentFormat) {
      case 'jpg':
        setCurrentFormat('webp');
        e.currentTarget.src = `/images/${baseFilename}.webp`;
        break;
      case 'webp':
        setCurrentFormat('png');
        e.currentTarget.src = `/images/${baseFilename}.png`;
        break;
      case 'png':
        setCurrentFormat('jpeg');
        e.currentTarget.src = `/images/${baseFilename}.jpeg`;
        break;
      default:
        // If all formats fail, use the default image
        setFallbackTriggered(true);
        e.currentTarget.src = '/images/default_user.png';
        break;
    }
  };
  
  return (
    <img
      src={initialImageUrl}
      alt={alt || doctorName || "Doctor"}
      style={style}
      className={className}
      onError={handleImageError}
    />
  );
};

export default DoctorImage; 