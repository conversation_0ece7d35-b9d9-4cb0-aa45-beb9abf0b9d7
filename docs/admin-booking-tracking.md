# Admin Booking Tracking Feature

## Overview

This feature enhances the booking system to track which admin user booked a patient appointment. This provides better accountability and audit trails for administrative bookings.

## Database Changes

### New Fields in PatientSlot Table

- `bookedByAdminId` (TEXT): Stores the admin's accessID who made the booking
- `bookingType` (VARCHAR): Indicates whether the booking was made by 'patient' or 'admin'

### Migration

Run the migration file: `apps/api/migrations/add_admin_booking_tracking.sql`

```sql
-- Add columns to track admin bookings
ALTER TABLE PatientSlot 
ADD COLUMN IF NOT EXISTS "bookedByAdminId" TEXT,
ADD COLUMN IF NOT EXISTS "bookingType" VARCHAR(20) DEFAULT 'patient';

-- Add foreign key constraint to link to Dr table
ALTER TABLE PatientSlot 
ADD CONSTRAINT fk_booked_by_admin
    FOREIGN KEY ("bookedByAdminId") 
    REFERENCES Dr("accessID")
    ON DELETE SET NULL;
```

## API Changes

### Updated Endpoints

#### POST `/zoho/v1.0/booking/:leadId/:flag`
- **New**: Accepts `bookedByAdminId` in request body
- **Behavior**: 
  - If `bookedByAdminId` is provided, sets `bookingType` to 'admin'
  - If `flag` is 'sales', automatically treats as admin booking
  - Otherwise, defaults to 'patient' booking

#### GET `/zoho/v1.0/booking-history/:patientId` (New)
- **Purpose**: Retrieve booking history for a patient with admin information
- **Returns**: Array of booking records with admin details

#### GET `/zoho/v1.0/admin-bookings` (New)
- **Purpose**: Retrieve all admin bookings with filtering options
- **Query Parameters**:
  - `adminId`: Filter by specific admin (optional)
  - `period`: 'today', 'week', or 'custom' (optional)
  - `startDate`: Start date for custom range (YYYY-MM-DD)
  - `endDate`: End date for custom range (YYYY-MM-DD)
- **Returns**: Array of admin booking records

### Request/Response Examples

#### Admin Booking Request
```typescript
// POST /zoho/v1.0/booking/PATIENT123/sales
{
  "slot": "14:00 - 14:30",
  "remaining": 5,
  "id": "slot123",
  "range_id": "range456",
  "bookedByAdminId": "admin789"
}
```

#### Booking History Response
```typescript
[
  {
    "patient_id": "PATIENT123",
    "range_date": "2024-01-15",
    "slot_details": "14:00 - 14:30",
    "booking_type": "admin",
    "admin_name": "John Smith",
    "admin_email": "<EMAIL>",
    "createdAt": "2024-01-10T10:30:00Z"
  }
]
```

## Frontend Integration

### Updated Service Methods

#### `postPatientBooking`
```typescript
async postPatientBooking(
  leadID: string, 
  slot: Slot & { doctorID?: string }, 
  rebookFlag?: string, 
  adminId?: string  // New parameter
)
```

#### `getPatientBookingHistory` (New)
```typescript
async getPatientBookingHistory(patientId: string)
```

### Admin Booking Viewer Component

A new component `AdminBookingInterface` has been created at:
`apps/web/src/components/admin/AdminBookingInterface.tsx`

**Features:**
- View all admin bookings in a table format
- Filter by specific admin user
- Filter by time period (today, this week, custom date range)
- Real-time data from the API
- Visual indicators for booking types
- Responsive design with Material-UI components

**Navigation:**
- Added to main navigation bar (admin users only)
- Available at route: `/admin/bookings`
- Accessible from both desktop and mobile menus

## Usage Examples

### Creating an Admin Booking

```typescript
import { ApiClient } from '../services';

// Admin creates a booking for a patient
const adminId = 'admin123';
const patientId = 'PATIENT456';
const selectedSlot = {
  id: 'slot789',
  slot: '14:00 - 14:30',
  remaining: 3,
  range_id: 'range123'
};

await ApiClient.postPatientBooking(
  patientId, 
  selectedSlot, 
  'sales',  // Flag indicating admin booking
  adminId   // Admin ID for tracking
);
```

### Viewing Booking History

```typescript
// Get booking history for a patient
const history = await ApiClient.getPatientBookingHistory('PATIENT456');

history.forEach(booking => {
  console.log(`${booking.booking_type} booking on ${booking.range_date}`);
  if (booking.booking_type === 'admin') {
    console.log(`Booked by admin: ${booking.admin_name}`);
  }
});
```

### Viewing Admin Bookings

```typescript
// Get all admin bookings for today
const todayBookings = await ApiClient.getAdminBookings({ period: 'today' });

// Get admin bookings for a specific admin
const adminBookings = await ApiClient.getAdminBookings({
  adminId: 'admin123',
  period: 'week'
});

// Get admin bookings for custom date range
const customBookings = await ApiClient.getAdminBookings({
  startDate: '2024-01-01',
  endDate: '2024-01-31'
});
```

## Benefits

1. **Accountability**: Track which admin made each booking
2. **Audit Trail**: Complete history of who booked what and when
3. **Analytics**: Understand booking patterns by admin vs patient
4. **Compliance**: Meet regulatory requirements for record keeping
5. **Support**: Better customer service with booking context

## Implementation Notes

- Backward compatible: Existing bookings default to 'patient' type
- Foreign key constraint ensures data integrity
- Indexes added for performance on admin lookups
- WebSocket notifications still work for real-time updates
- Admin bookings follow same availability rules as patient bookings

## Setup Steps

1. **Run the database migration** to add the new fields:
   ```sql
   -- Execute the migration file
   \i apps/api/migrations/add_admin_booking_tracking.sql
   ```

2. **Access the Admin Bookings interface**:
   - Navigate to `/admin/bookings` in your browser
   - Or use the "Admin Bookings" link in the navigation bar (admin users only)

3. **Test the filtering features**:
   - Filter by specific admin users
   - Try different time periods (today, week, custom range)
   - Verify the data displays correctly

4. **Update existing admin booking flows** to pass the admin ID when creating bookings

## Future Enhancements

- Admin booking reports and analytics
- Bulk booking capabilities for admins
- Admin booking approval workflows
- Integration with admin user permissions
- Booking modification tracking
