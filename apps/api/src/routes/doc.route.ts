import {
  getPatients,
  postPatientData,
  updatePatientLockStatus,
  postDoctorData,
  getDoctorById,
  updateNextPatient,
  updateMeetingStatus,
  notifyNextPatient,
  postRedirect,
  postPatientWaitingQueue,
  leftPatientWaitingQueue,
  getPatientById,
  fetchQueue,
  refreshPatientListFromZoho,
  emptyQueue,
  fetchOnlineQueue,
  joinedCallPatientWaitingQueue,
  completedPatientWaitingQueue,
  fetchLatestAdmission,
  postPatientAdmission,
  updateAdmission,
  fetchJoinedQueue,
  fetchQueueDetailsById,
  completePatientConsultation,
  getPatientsFullHistory,
  deleteTimer,
  startTimer,
  fetchTimer,
  fetchTimerAdmin,
  getDoctors,
  getActiveDoctors,
  postPatientOffline,
  fetchAllInbox,
  fetchInboxByEmail,
  getSearchedPatients,
  fetchConsultationByDate,
  tpConfirmedPatientWaitingQueue,
  callEndedPatientWaitingQueue,
  getPatientDoctorInfo,
  fetchTreatmentPlanById,
  verifyLockedPatient,
  alertAwayPatientOnAdmit,
  getNextPatientAutomatically,
  updatePatientToNoShowInQueue,
  updatePatientWithTechIssue,
  updatePatientEmail,
  testWarningPatient,
  postPatientOrder,
  getPatientOrdersByEmail,
  fetchTreatmentPlanByEmail,
  fetchQuestionnaireByEmail,
  getDoctorsActivity,
  checkNoShow,
  sendPatientToDoctor,
  getPatientByZohoId,
  userActions,
  fetchNextPatientToConsult,
  getPatientByIdFromDB,
  fetchPatientsWithExceededAttempts,
  resetPatientAttempts,
  updatePatientZohoIdByEmail,
  getPatientByEmail,
  fetchHealthcheckByPatientId,
  trackDoctorLogin,
  trackDoctorLogout,
  getDoctorSessionHistory,
  getDoctorOnlineStatus,
  fixZohoConsultations,
  analyzeZohoConsultations,
  getPatientsRedis,
  getPatientStatus,
  getBatchPatientStatus,
  getPatientDetailedStatus,
  assignUnassignedPatientsToDoctor,
  fetchAIResponses,
  fetchAIResponseById,
} from '../controllers/doctor';

import {
  postAvailabilities,
  deleteAvailability,
  getAvailabilitiesForPatient,
  getAvailabilitiesForAdmin,
  deleteAvailabilityForce,
  postDecryptedUrl
} from '../controllers/calendar';

import express from 'express';
import { validateDr } from '../middlewares/validationMiddleware';
import { sendDetailedReportToSlack, sendReportToSlack } from '../controllers/slack';
import { logInfo } from '../middlewares/llogMiddleware';

const router = express.Router();

const currentVersion = 'v1.0';

router.get(`/${currentVersion}/patients`, validateDr, getPatients);
router.get(`/${currentVersion}/patients/redis`, getPatientsRedis);
router.get(`/${currentVersion}/doc/:id`, getDoctorById);
router.get(`/${currentVersion}/active-docs/`, getActiveDoctors);
router.get(`/${currentVersion}/docs/`, logInfo(),  getDoctors);
router.get(`/${currentVersion}/next`, validateDr, updateNextPatient);
router.get(`/${currentVersion}/queue`, fetchQueue);
router.get(`/${currentVersion}/online/queue`, fetchOnlineQueue);
router.get(`/${currentVersion}/joined/queue`, fetchJoinedQueue);
router.get(`/${currentVersion}/patient/:id`, getPatientById);
router.get(`/${currentVersion}/patient-by-id/:id`, getPatientByZohoId);
router.get(`/${currentVersion}/patient-by-email/:email`, validateDr, getPatientByEmail);
router.get(`/${currentVersion}/refresh/zoho`, refreshPatientListFromZoho);
router.get(`/${currentVersion}/admission`, fetchLatestAdmission);
router.get(`/${currentVersion}/details/queue/:id`, validateDr, fetchQueueDetailsById);
router.get(`/${currentVersion}/timer/retrieve/:drId`, validateDr, fetchTimer);
router.get(`/${currentVersion}/timer/all/retrieve`, validateDr, fetchTimerAdmin);
router.get(`/${currentVersion}/inbox/retrieve`, validateDr, fetchAllInbox);
router.get(`/${currentVersion}/inbox/:email/retrieve`, validateDr, fetchInboxByEmail);
router.get(`/${currentVersion}/queue/eligibility/:id`, fetchTreatmentPlanById);
router.get(`/${currentVersion}/patient/:id/doctor-info`, getPatientDoctorInfo);
router.get(`/${currentVersion}/locked/:id`, validateDr, verifyLockedPatient);
router.get(`/${currentVersion}/automate/next-patient/:drId`, getNextPatientAutomatically);
router.get(`/${currentVersion}/order/retrieve/:email`, getPatientOrdersByEmail);
router.get(`/${currentVersion}/drActivity`, getDoctorsActivity);
router.get(`/${currentVersion}/doctor-sessions/:doctorId`, validateDr, getDoctorSessionHistory);
router.get(`/${currentVersion}/doctor-status`, getDoctorOnlineStatus);

// Patient's History
router.get(`/${currentVersion}/history/tp/:email`, fetchTreatmentPlanByEmail);
router.get(`/${currentVersion}/history/order/:email`, getPatientOrdersByEmail);
router.get(`/${currentVersion}/history/questionnaire/:id`, fetchQuestionnaireByEmail);
router.get(`/${currentVersion}/history/healthcheck/:id`, fetchHealthcheckByPatientId);
router.get(`/${currentVersion}/next/autoadmit`, fetchNextPatientToConsult);
router.get(`/${currentVersion}/patients/exceeded-attempts`,validateDr, fetchPatientsWithExceededAttempts);

router.get(`/${currentVersion}/test/thirdwarning`, testWarningPatient);

// Production Zoho consultation fix endpoints
router.post(`/${currentVersion}/fix-zoho-consultations`, fixZohoConsultations);
router.get(`/${currentVersion}/analyze-zoho-consultations`, analyzeZohoConsultations);

router.put(`/${currentVersion}/patient/reset-attempts/:id`, resetPatientAttempts);

router.post(`/${currentVersion}/patient`, validateDr, postPatientData);
router.post(`/${currentVersion}/doc`, postDoctorData);
router.post(`/${currentVersion}/notify/next`, notifyNextPatient);
router.post(`/${currentVersion}/redirect`, validateDr, postRedirect);
router.post(`/${currentVersion}/queue/`, postPatientWaitingQueue);
router.post(`/${currentVersion}/admission/`, postPatientAdmission);
router.post(`/${currentVersion}/complete/consultation`, completePatientConsultation);
router.post(`/${currentVersion}/alert/admit`, validateDr, alertAwayPatientOnAdmit);
router.post(`/${currentVersion}/noshow`, validateDr, updatePatientToNoShowInQueue);
router.post(`/${currentVersion}/techissue`, validateDr, updatePatientWithTechIssue);
router.post(`/${currentVersion}/offline`, postPatientOffline);
router.post(`/${currentVersion}/order/supplyDocument/submit`, postPatientOrder);
router.post(`/${currentVersion}/search`, validateDr, getPatientsFullHistory);
router.post(`/${currentVersion}/inbox/patient/retrieve`, validateDr, getSearchedPatients);
router.post(`/${currentVersion}/timer/start`, validateDr, startTimer);
router.post(`/${currentVersion}/consultation/filter`, validateDr, fetchConsultationByDate);
router.post(`/${currentVersion}/status-change/admin`, validateDr, sendPatientToDoctor);
router.post(`/${currentVersion}/doctor/login`, trackDoctorLogin);
router.post(`/${currentVersion}/doctor/logout`, trackDoctorLogout);
router.post(`/${currentVersion}/user/actions`, logInfo(), userActions);

router.post(`/${currentVersion}/noshow/check`, checkNoShow);
router.post(`/${currentVersion}/report/send`, sendReportToSlack)
router.post(`/${currentVersion}/report/detailed/send`, sendDetailedReportToSlack)

router.put(`/${currentVersion}/lock/:id`, validateDr, updatePatientLockStatus);
router.put(`/${currentVersion}/meeting/patient/:id`, updateMeetingStatus);
router.put(`/${currentVersion}/left/queue/:id`, leftPatientWaitingQueue);
router.post(`/${currentVersion}/left/queue/`, leftPatientWaitingQueue);
router.put(`/${currentVersion}/joinedCall/queue/:id`, joinedCallPatientWaitingQueue);
router.put(`/${currentVersion}/completed/queue/:id`, completedPatientWaitingQueue);
router.put(`/${currentVersion}/admission/:id`, updateAdmission);
router.put(`/${currentVersion}/ended/queue/:id`, callEndedPatientWaitingQueue);
router.put(`/${currentVersion}/confirmed/queue/:id`, tpConfirmedPatientWaitingQueue);
router.put(`/${currentVersion}/email/update`, updatePatientEmail);

router.delete(`/${currentVersion}/all/queue`, validateDr, emptyQueue);
router.delete(`/${currentVersion}/timer/delete/:drId`, validateDr, deleteTimer);

// CALENDAR ENDPOINTS
// TODO: Should go into Calendar.route.ts
router.get(`/${currentVersion}/availabilities`, getAvailabilitiesForPatient);
router.get(`/${currentVersion}/admin/availabilities/`, getAvailabilitiesForAdmin);

router.post(`/${currentVersion}/leadId/`, postDecryptedUrl);
router.post(`/${currentVersion}/availabilities`, postAvailabilities);

router.delete(`/${currentVersion}/availability/:id`, deleteAvailability);
router.delete(`/${currentVersion}/force/availability/:id`, deleteAvailabilityForce);

// Add this new route with other patient-related GET routes
router.get(`/${currentVersion}/patient-by-patient-id/:id`, getPatientByIdFromDB);
router.put(`/${currentVersion}/patient/update-zohoid`, updatePatientZohoIdByEmail);



// PATIENT STATUS ENDPOINTS - Real-time status checking
router.get(`/${currentVersion}/patient/:id/status`, getPatientStatus);
router.post(`/${currentVersion}/patients/status`, getBatchPatientStatus);
router.get(`/${currentVersion}/patient/:id/queue-status`, getPatientDetailedStatus);

// PATIENT ASSIGNMENT ENDPOINTS
router.post(`/${currentVersion}/assign-patients-to-doctor`, assignUnassignedPatientsToDoctor);

// AI ENDPOINTS
router.get(`/${currentVersion}/ai-check/responses`, fetchAIResponses);
router.get(`/${currentVersion}/ai-check/responses/:id`, fetchAIResponseById);

export default router;
