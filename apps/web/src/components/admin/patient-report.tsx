import React, { useEffect, useState } from "react";
import { ReportData } from "../../types";
import PatientReportTemplate from "../doc-portal/PatientReportTemplate";
import { MakeGenerics, useMatch } from "@tanstack/react-location";
import { ApiClient } from "../../services";

type UrlProps = MakeGenerics<{
	Params: {
		id: string; // This will be the zohoId
	};
}>;

const PatientReport: React.FC = () => {
	const {
		params: { id },
	} = useMatch<UrlProps>();
	const [reportData, setReportData] = useState<ReportData | null>(null);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchReportData = async () => {
			// Fetch report data from API or context
			try {
				const data = await ApiClient.getAICheckResponseByZohoId(id);
				setReportData(data);
			} catch (error) {
				console.error("Error fetching report data:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchReportData();
	}, []);

	return <PatientReportTemplate reportData={reportData ?? ({} as ReportData)} loading={loading} />;
};

export default PatientReport;