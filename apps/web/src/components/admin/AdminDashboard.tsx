import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import AdminBookingInterface from './AdminBookingInterface';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`admin-tabpanel-${index}`}
      aria-labelledby={`admin-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `admin-tab-${index}`,
    'aria-controls': `admin-tabpanel-${index}`,
  };
}

const AdminDashboard: React.FC = () => {
  const [value, setValue] = useState(0);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Typography variant="h4" gutterBottom sx={{ p: 3, pb: 0 }}>
        Admin Dashboard
      </Typography>
      
      <Paper sx={{ m: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={value} onChange={handleChange} aria-label="admin dashboard tabs">
            <Tab label="Admin Bookings" {...a11yProps(0)} />
            <Tab label="Patient Management" {...a11yProps(1)} />
            <Tab label="Reports" {...a11yProps(2)} />
            <Tab label="Settings" {...a11yProps(3)} />
          </Tabs>
        </Box>
        
        <TabPanel value={value} index={0}>
          <AdminBookingInterface />
        </TabPanel>
        
        <TabPanel value={value} index={1}>
          <Typography variant="h6">Patient Management</Typography>
          <Typography color="text.secondary">
            Patient management features will be implemented here.
          </Typography>
        </TabPanel>
        
        <TabPanel value={value} index={2}>
          <Typography variant="h6">Reports</Typography>
          <Typography color="text.secondary">
            Reporting features will be implemented here.
          </Typography>
        </TabPanel>
        
        <TabPanel value={value} index={3}>
          <Typography variant="h6">Settings</Typography>
          <Typography color="text.secondary">
            Admin settings will be implemented here.
          </Typography>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default AdminDashboard;
