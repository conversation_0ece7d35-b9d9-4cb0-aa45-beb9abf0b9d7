-- Migration: Create add_22_thc_questionnaire table
-- Date: 2025-01-17
-- Purpose: Store questionnaire responses for patients requesting to add 22% THC to their existing 29% THC treatment plan

-- Create the add_22_thc_questionnaire table with columns matching existing questionnaire tables
CREATE TABLE IF NOT EXISTS add_22_thc_questionnaire (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    patient_id VARCHAR(255),
    email TEXT NOT NULL,
    zoho_id TEXT,
    questionnaire_data JSONB NOT NULL,
    total_score INTEGER NOT NULL DEFAULT 0,
    max_score INTEGER NOT NULL DEFAULT 50,
    is_eligible BOOLEAN NOT NULL DEFAULT false,
    status TEXT NOT NULL DEFAULT 'submitted',
    reviewed_by TEX<PERSON>,
    reviewed_at TIMESTAMPTZ,
    review_notes TEXT,
    approved_by TEXT,
    approved_at TIMESTAMPTZ,
    approval_notes TEXT,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    slack_message_ts TEXT,
    
    -- Foreign key constraints
    CONSTRAINT fk_add_22_thc_patient_id 
        FOREIGN KEY (patient_id) REFERENCES Patient("patientID") ON DELETE CASCADE,
    CONSTRAINT fk_add_22_thc_email 
        FOREIGN KEY (email) REFERENCES Patient(email) ON DELETE CASCADE ON UPDATE CASCADE,
    
    -- Check constraints
    CONSTRAINT chk_add_22_thc_status
        CHECK (status IN ('submitted', 'pending', 'approved', 'rejected')),
    CONSTRAINT chk_add_22_thc_score_range
        CHECK (total_score >= 0 AND total_score <= max_score)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_add_22_thc_patient_id ON add_22_thc_questionnaire(patient_id);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_email ON add_22_thc_questionnaire(email);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_status ON add_22_thc_questionnaire(status);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_created_at ON add_22_thc_questionnaire(created_at);
CREATE INDEX IF NOT EXISTS idx_add_22_thc_slack_message_ts ON add_22_thc_questionnaire(slack_message_ts) 
    WHERE slack_message_ts IS NOT NULL;

-- Add comments to document the table and columns
COMMENT ON TABLE add_22_thc_questionnaire IS 'Stores questionnaire responses for patients requesting to add 22% THC to their existing 29% THC treatment plan';
COMMENT ON COLUMN add_22_thc_questionnaire.id IS 'Primary key - UUID for the questionnaire submission';
COMMENT ON COLUMN add_22_thc_questionnaire.patient_id IS 'Patient ID from the Patient table';
COMMENT ON COLUMN add_22_thc_questionnaire.email IS 'Patient email address';
COMMENT ON COLUMN add_22_thc_questionnaire.zoho_id IS 'Zoho CRM ID for the patient';
COMMENT ON COLUMN add_22_thc_questionnaire.questionnaire_data IS 'JSONB containing the complete questionnaire responses and scoring details';
COMMENT ON COLUMN add_22_thc_questionnaire.total_score IS 'Total score achieved by the patient (0-50)';
COMMENT ON COLUMN add_22_thc_questionnaire.max_score IS 'Maximum possible score (default: 50)';
COMMENT ON COLUMN add_22_thc_questionnaire.is_eligible IS 'Boolean indicating if patient scored above threshold (35+ points)';
COMMENT ON COLUMN add_22_thc_questionnaire.status IS 'Current status: submitted, pending, approved, or rejected';
COMMENT ON COLUMN add_22_thc_questionnaire.created_at IS 'Timestamp when questionnaire was submitted';
COMMENT ON COLUMN add_22_thc_questionnaire.reviewed_at IS 'Timestamp when questionnaire was reviewed by doctor';
COMMENT ON COLUMN add_22_thc_questionnaire.approved_at IS 'Timestamp when questionnaire was approved';
COMMENT ON COLUMN add_22_thc_questionnaire.approved_by IS 'Doctor ID who approved the request';
COMMENT ON COLUMN add_22_thc_questionnaire.reviewed_by IS 'Doctor ID who reviewed/rejected the request';
COMMENT ON COLUMN add_22_thc_questionnaire.review_notes IS 'Doctor notes for approval/rejection';
COMMENT ON COLUMN add_22_thc_questionnaire.slack_message_ts IS 'Slack message timestamp for threaded replies when moderating requests';

-- Verify the table was created successfully
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'add_22_thc_questionnaire' 
ORDER BY ordinal_position;

-- Verify indexes were created
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'add_22_thc_questionnaire'
ORDER BY indexname;

-- Verify constraints were created
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'add_22_thc_questionnaire'
ORDER BY constraint_name;
