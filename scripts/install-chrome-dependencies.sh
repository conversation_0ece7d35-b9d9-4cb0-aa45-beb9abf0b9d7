#!/bin/bash

# Script to install Chrome dependencies for Puppeteer on Ubuntu/Debian servers
# This fixes the "libatk-1.0.so.0: cannot open shared object file" error

set -e

echo "Installing Chrome dependencies for Puppeteer..."

# Update package list
sudo apt-get update

# Install required dependencies for Chrome/Chromium (updated for newer Ubuntu versions)
sudo apt-get install -y \
    ca-certificates \
    fonts-liberation \
    libappindicator3-1 \
    libasound2t64 \
    libatk-bridge2.0-0t64 \
    libatk1.0-0t64 \
    libc6 \
    libcairo2 \
    libcups2t64 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc-s1 \
    libglib2.0-0t64 \
    libgtk-3-0t64 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    wget \
    xdg-utils

# Install additional dependencies that might be missing
sudo apt-get install -y \
    libdrm2 \
    libxkbcommon0 \
    libatspi2.0-0 \
    libgtk-4-1 \
    libu2f-udev

echo "Chrome dependencies installed successfully!"

# Optional: Install Chrome browser (if you want to use system Chrome instead of Puppeteer's bundled version)
# Uncomment the following lines if you want to install Google Chrome
# wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
# echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google-chrome.list
# sudo apt-get update
# sudo apt-get install -y google-chrome-stable

echo "Setup complete! You can now restart your application."
echo "If you installed Google Chrome, you can set CHROME_BIN environment variable:"
echo "export CHROME_BIN=/usr/bin/google-chrome-stable"
