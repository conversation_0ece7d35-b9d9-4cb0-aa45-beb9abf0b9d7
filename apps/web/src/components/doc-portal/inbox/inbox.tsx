import Grid from "@mui/material/Grid2";
import TreatmentPlan from "./category/treatment-plan";
import { Fragment, useEffect } from "react";
import { TreatmentPlanHistory, OrderHistory, Questionnaire } from "../../../types";
import { ApiClient } from "../../../services";
import Order from "./category/order";
// import QuestionnaireForm from "./category/questionnaire";
import HealthCheck from "./category/healthcheck";
import { usePatient } from "../../../hooks/patient-provider";

const Inbox: React.FC = () => {

    const {inboxData, updateInboxData, currentSearchPage, setSelectedInboxItem} = usePatient()

    useEffect(() => {
        const init = async () => {
            const result = await ApiClient.fetchAllInbox(currentSearchPage)
            updateInboxData(result)
        }
        init()
    }, [])

    const handleSelectedTreatmentPlan = (data: TreatmentPlanHistory) => {
        setSelectedInboxItem(data)
    }

    return (
        <>
            <Grid container sx={{ width: '100%'}} direction={'column'}>
                {
                    inboxData.map((entry, index) => {
                        return (
                            <Fragment key={entry.type + index}>
                                {
                                    entry.type === 'treatmentplan' && < TreatmentPlan data={entry as TreatmentPlanHistory} onSelect={handleSelectedTreatmentPlan}/>
                                }
                                {
                                    entry.type === 'order' && < Order data={entry as OrderHistory} />
                                }
                                {/* {
                                    entry.type === 'questionnaire' && < QuestionnaireForm data={entry as Questionnaire} />
                                } */}
                                {
                                    entry.type === 'healthcheck' && < HealthCheck data={entry as Questionnaire} />
                                }
                            </Fragment>
                        )
                    })
                }
            </Grid></>
    )
}

export default Inbox