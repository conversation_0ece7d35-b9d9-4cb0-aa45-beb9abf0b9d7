# TODO: Persistent Consultation Timer Implementation (Revised)

This document outlines the plan for implementing a refresh-proof consultation timer based on the **existing logic** in `doctor-consultation.tsx`. This is a **frontend-only** change.

### Objective

To make the existing consultation timers and warnings persistent across page refreshes. The state will be stored in `localStorage` to provide a seamless experience.

---

### Key `localStorage` Object Structure

The implementation will use a single key in `localStorage`: `activeConsultationState`.

```json
{
  "consultationId": "some-unique-id-for-the-consultation",
  "startTime": "2025-07-03T10:00:00.000Z",
  "consultationDurationMinutes": 6,
  "warningsShown": ["2min_warning", "1min_countdown"]
}
```
-   `consultationDurationMinutes`: The total duration for this specific call, fetched via `getDoctorConsultationDuration()`.

---

### Warning Thresholds (Based on Existing Code)

The warnings are dynamic and relative to the `consultationDurationMinutes`.

-   **"2 Minutes Remaining" Warning:**
    -   **Trigger:** `consultationDurationMinutes - 2` minutes from `startTime`.
    -   **Action:** A dialog (`showWarningDialog`) is displayed.
    -   **`localStorage` ID:** `2min_warning`

-   **"1 Minute Remaining" Countdown:**
    -   **Trigger:** `consultationDurationMinutes - 1` minute from `startTime`.
    -   **Action:** A non-modal countdown UI (`showFinalCountdown`) appears.
    -   **`localStorage` ID:** `1min_countdown`

-   **End of Call:**
    -   **Trigger:** At `consultationDurationMinutes` from `startTime`.
    -   **Action:** The call is automatically ended via `endCall()`.

---

### Implementation Plan (TODO)

-   [ ] **Restore State on Load**
    -   On component mount, check for `activeConsultationState` in `localStorage`.
    -   **Crucially**, validate that its `consultationId` matches the current consultation.
    -   If valid, calculate `elapsedMs = Date.now() - new Date(startTime)`.
    -   Re-initialize the timers (`earlyWarningTimer`, `finalCountdownTimer`, `ongoingMeetingTimer`) with the remaining time.
    -   For each timer, first check if its corresponding ID exists in `warningsShown`. If it does, do not set the timer again.
    -   If the time for a warning has already passed, trigger its effect immediately (e.g., if 5.5 minutes have passed in a 6-minute call, immediately show the 1-minute countdown).

-   [ ] **Persist State on Start**
    -   In `handleDrJoinedMeeting`, when the consultation starts, create the `activeConsultationState` object in `localStorage`.
    -   Store the `consultationId`, `startTime`, and the dynamic `consultationDurationMinutes`.
    -   Initialize `warningsShown` as an empty array.

-   [ ] **Update State When Warnings Fire**
    -   When `earlyWarningTimer` fires, show the dialog and add `2min_warning` to the `warningsShown` array in `localStorage`.
    -   When `finalCountdownTimer` fires, start the countdown and add `1min_countdown` to the `warningsShown` array in `localStorage`.

-   [ ] **Implement State Cleanup**
    -   **This is the most critical step for preventing state leakage between patients.**
    -   The `activeConsultationState` object **must be removed** from `localStorage` in all scenarios that terminate the consultation.
    -   This cleanup should occur inside the `handleLeave()` and `endCall()` functions, and also in the `useEffect` cleanup function of the component (on unmount).

---

### Verification Steps

1.  Set consultation duration to 6 minutes. Start a call for Patient A. Verify `activeConsultationState` is created in `localStorage`.
2.  Refresh the page at the 2-minute mark. Verify the timer logic continues correctly.
3.  Wait until 4 minutes have passed. Verify the "2 minutes remaining" dialog appears and `warningsShown` is updated.
4.  Refresh the page. Verify the dialog does not appear again.
5.  Wait until 5 minutes have passed. Verify the "1 minute countdown" UI appears and `warningsShown` is updated.
6.  Refresh the page. Verify the countdown UI reappears and continues from the correct time.
7.  End the consultation for Patient A.
8.  **Verify** that `activeConsultationState` has been completely removed from `localStorage`.
9.  Start a new consultation for Patient B. Verify a fresh timer starts and no state from Patient A's session is present.
