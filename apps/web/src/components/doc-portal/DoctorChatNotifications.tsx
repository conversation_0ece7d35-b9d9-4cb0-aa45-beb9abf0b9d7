import React, { useState, useEffect } from 'react';
import { Badge, IconButton, Popover, Box, Typography, List, ListItem, ListItemText, Chip, CircularProgress } from '@mui/material';
import NotificationsIcon from '@mui/icons-material/Notifications';
import NotificationsActiveIcon from '@mui/icons-material/NotificationsActive';
import chatService from '../../services/chat.service';
import { useAuth } from '../../hooks/auth-provider';

interface DoctorNotification {
  patientEmail: string;
  doctorNotification: boolean;
  lastPatientMessageAt: string | null;
  unreadPatientMessages: number;
  doctorId: string | null;
  createdAt: string;
  updatedAt: string;
}

interface DoctorNotificationsData {
  notifications: DoctorNotification[];
  totalUnread: number;
  activeCount: number;
}

interface DoctorChatNotificationsProps {
  onNotificationClick?: (patientEmail: string) => void;
  variant?: 'navbar' | 'inline'; // navbar for navigation bar, inline for requests row
}

const DoctorChatNotifications: React.FC<DoctorChatNotificationsProps> = ({ onNotificationClick, variant = 'navbar' }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [notifications, setNotifications] = useState<DoctorNotificationsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [backgroundFetching, setBackgroundFetching] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const { doctor } = useAuth();

  const fetchNotifications = async (isManualRefresh = false) => {
    if (!doctor?.accessID) return;

    try {
      // Show different loading states for manual vs background refreshes
      if (isManualRefresh) {
        setLoading(true);
      } else {
        setBackgroundFetching(true);
      }

      const data = await chatService.getDoctorNotifications(doctor.accessID, false);
      setNotifications(data);
      setRetryCount(0); // Reset retry count on success
    } catch (error) {
      console.error('Error fetching doctor notifications:', error);
      setRetryCount(prev => prev + 1);

      // Don't show errors for background polling, only for manual refreshes
      if (isManualRefresh) {
        console.error('Failed to refresh notifications');
      }
    } finally {
      if (isManualRefresh) {
        setLoading(false);
      } else {
        setBackgroundFetching(false);
      }
    }
  };

  useEffect(() => {
    fetchNotifications(true); // Initial fetch with loading indicator

    // Smart polling with visibility detection and dropdown state awareness
    const startPolling = () => {
      return setInterval(() => {
        // Only poll if page is visible and dropdown is closed
        if (!document.hidden && !anchorEl) {
          // Use exponential backoff for retries (max 30 seconds)
          const delay = Math.min(5000 * Math.pow(2, retryCount), 30000);
          if (retryCount === 0 || Date.now() % delay < 5000) {
            fetchNotifications(false); // Background fetch without loading indicator
          }
        }
      }, 10000);
    };

    let interval = startPolling();

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Pause polling when page is not visible
        clearInterval(interval);
      } else {
        // Resume polling when page becomes visible
        clearInterval(interval);
        fetchNotifications(false); // Immediate fetch when page becomes visible
        interval = startPolling();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [doctor?.accessID, anchorEl, retryCount]);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    fetchNotifications(true); // Manual refresh with loading indicator when opening
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = async (patientEmail: string) => {
    try {
      // Clear the notification for this patient
      await chatService.clearDoctorNotificationForPatient(patientEmail);
      
      // Refresh notifications
      await fetchNotifications();
      
      // Call the callback if provided
      if (onNotificationClick) {
        onNotificationClick(patientEmail);
      }
      
      // Close the popover
      handleClose();
    } catch (error) {
      console.error('Error clearing notification:', error);
    }
  };

  const formatTime = (timestamp: string | null) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const getPatientName = (email: string) => {
    // Extract name from email or return email
    const namePart = email.split('@')[0];
    return namePart.replace(/[0-9]/g, '').replace(/\./g, ' ').trim() || email;
  };

  const open = Boolean(anchorEl);
  const hasNotifications = notifications && notifications.totalUnread > 0;

  // Define styles based on variant
  const iconButtonStyles = variant === 'navbar'
    ? {
        color: 'white',
        '&:hover': {
          backgroundColor: 'rgba(255, 255, 255, 0.1)'
        }
      }
    : {
        color: '#007F00',
        '&:hover': {
          backgroundColor: 'rgba(0, 127, 0, 0.1)'
        }
      };

  const iconColor = variant === 'navbar' ? 'white' : '#007F00';

  return (
    <>
      <IconButton
        onClick={handleClick}
        sx={{
          ...iconButtonStyles,
          // Add subtle pulse animation when background fetching
          ...(backgroundFetching && {
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.7 },
              '100%': { opacity: 1 }
            }
          })
        }}
      >
        <Badge 
          badgeContent={notifications?.totalUnread || 0} 
          color="error"
          max={99}
        >
          {hasNotifications ? (
            <NotificationsActiveIcon sx={{ color: '#FFD700' }} />
          ) : (
            <NotificationsIcon sx={{ color: iconColor }} />
          )}
        </Badge>
      </IconButton>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: { width: 350, maxHeight: 400 }
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            Chat Notifications
            {loading && <CircularProgress size={16} />}
          </Typography>

          {notifications && notifications.notifications.length > 0 ? (
            <List sx={{ maxHeight: 300, overflow: 'auto' }}>
              {notifications.notifications
                .filter(n => n.doctorNotification && n.unreadPatientMessages > 0)
                .map((notification, index) => (
                  <ListItem
                    key={index}
                    onClick={() => handleNotificationClick(notification.patientEmail)}
                    sx={{
                      border: '1px solid #eee',
                      borderRadius: 1,
                      mb: 1,
                      cursor: onNotificationClick ? 'pointer' : 'default',
                      '&:hover': {
                        backgroundColor: onNotificationClick ? '#f0f8f0' : '#f5f5f5',
                        borderColor: onNotificationClick ? '#007F00' : '#eee'
                      }
                    }}
                  >
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                            {getPatientName(notification.patientEmail)}
                          </Typography>
                          <Chip
                            label={`${notification.unreadPatientMessages} new`}
                            size="small"
                            color="primary"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        </Box>
                      }
                      secondary={
                        <>
                          <Typography variant="caption" color="text.secondary" component="span" sx={{ display: 'block' }}>
                            Last message: {formatTime(notification.lastPatientMessageAt)}
                          </Typography>
                          {onNotificationClick && (
                            <Typography variant="caption" color="primary" component="span" sx={{ display: 'block', fontStyle: 'italic' }}>
                              Click to open chat
                            </Typography>
                          )}
                        </>
                      }
                    />
                  </ListItem>
                ))}
            </List>
          ) : (
            <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 2 }}>
              {loading ? 'Loading notifications...' : 'No new messages'}
            </Typography>
          )}

          {notifications && notifications.totalUnread > 0 && (
            <Box sx={{ mt: 1, pt: 1, borderTop: '1px solid #eee' }}>
              <Typography variant="caption" color="text.secondary" align="center" display="block">
                {notifications.totalUnread} total unread message{notifications.totalUnread !== 1 ? 's' : ''}
              </Typography>
            </Box>
          )}
        </Box>
      </Popover>
    </>
  );
};

export default DoctorChatNotifications;
