import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './auth-provider';
import { useStreamChat } from './useStreamChat';
import chatService from '../services/chat.service';

/**
 * Custom hook to check if a doctor has any active chat channels with patients
 * This is used to determine whether to show the chat interface in the navigation
 * @param enabled - Whether the hook should run (default: true)
 */
export function useDoctorChatChannels(enabled: boolean = true) {
  const [hasChannels, setHasChannels] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [channelCount, setChannelCount] = useState<number>(0);

  const { doctor } = useAuth();

  // Call useStreamChat with enabled flag to prevent initialization when not needed
  const streamChatResult = useStreamChat(undefined, enabled && doctor?.role === 'doctor');
  const { client, userId, loading: streamLoading, error: streamError } = streamChatResult;

  // Early return with disabled state if not enabled or if there's an auth error
  useEffect(() => {
    if (!enabled || (streamError && streamError.includes('No user found'))) {
      setHasChannels(false);
      setChannelCount(0);
      setLoading(false);
      return;
    }
  }, [enabled, streamError]);

  const checkForChannels = useCallback(async () => {
    if (!enabled || !client || !userId || !doctor?.accessID || streamLoading || streamError) {
      setHasChannels(false);
      setChannelCount(0);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Query channels where the doctor is a member
      const filter = { type: 'messaging', members: { $in: [userId] } };
      const sort = { last_message_at: -1 } as const;
      const options = {
        watch: false,
        state: true,
        presence: false
      };

      const channels = await client.queryChannels(filter, sort, options);

      if (channels.length === 0) {
        setHasChannels(false);
        setChannelCount(0);
        setLoading(false);
        return;
      }

      // Extract patient IDs from channels
      const patientIds: string[] = [];
      for (const channel of channels) {
        const members = Object.values(channel.state.members || {});
        const patientMember = members.find(m => m.user?.id?.startsWith('p_'));
        if (patientMember?.user?.id) {
          const patientId = patientMember.user.id.replace('p_', '');
          patientIds.push(patientId);
        }
      }

      if (patientIds.length === 0) {
        setHasChannels(false);
        setChannelCount(0);
        setLoading(false);
        return;
      }

      // Check treatment plans to filter channels by current doctor
      const treatmentPlansMap = await chatService.fetchLatestTreatmentPlansForPatients(patientIds);

      // Filter channels by treatment plan first
      const doctorChannels = channels.filter(channel => {
        const members = Object.values(channel.state.members || {});
        const patientMember = members.find(m => m.user?.id?.startsWith('p_'));
        const patientId = patientMember?.user?.id?.replace('p_', '');

        if (!patientId) return false;

        const latestPlan = treatmentPlansMap[patientId];
        return latestPlan && latestPlan.drId === doctor.accessID;
      });

      // Check conversation visibility for approved conversations only
      const channelIds = doctorChannels.map(channel => channel.id).filter((id): id is string => !!id);
      let approvedChannelCount = 0;

      if (channelIds.length > 0) {
        const visibilityResponse = await chatService.checkConversationVisibility(channelIds);
        approvedChannelCount = Object.values(visibilityResponse.data).filter(isVisible => isVisible).length;
      }

      setHasChannels(approvedChannelCount > 0);
      setChannelCount(approvedChannelCount);

    } catch (error) {
      console.error('Error checking for doctor chat channels:', error);
      setHasChannels(false);
      setChannelCount(0);
    } finally {
      setLoading(false);
    }
  }, [enabled, client, userId, doctor?.accessID, streamLoading, streamError]);

  // Check for channels when dependencies change
  useEffect(() => {
    checkForChannels();
  }, [checkForChannels]);

  // Set up event listeners to re-check when channels are added/removed
  useEffect(() => {
    if (!enabled || !client || streamLoading || streamError) return;

    const handleChannelEvent = () => {
      // Debounce the check to avoid too many API calls
      const timeoutId = setTimeout(() => {
        checkForChannels();
      }, 1000);

      return () => clearTimeout(timeoutId);
    };

    // Listen for events that might affect channel membership
    client.on('notification.added_to_channel', handleChannelEvent);
    client.on('notification.removed_from_channel', handleChannelEvent);
    client.on('channel.deleted', handleChannelEvent);

    return () => {
      client.off('notification.added_to_channel', handleChannelEvent);
      client.off('notification.removed_from_channel', handleChannelEvent);
      client.off('channel.deleted', handleChannelEvent);
    };
  }, [enabled, client, checkForChannels, streamLoading, streamError]);

  // Set up WebSocket listener for moderation events
  useEffect(() => {
    if (!enabled || !doctor?.role) return;

    let ws: WebSocket | null = null;
    let reconnectTimeout: NodeJS.Timeout | null = null;

    const connectWebSocket = () => {
      try {
        ws = new WebSocket(`${import.meta.env.VITE_WSS}`);

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);

            if (message.type === 'ping') {
              ws?.send(JSON.stringify({ type: 'pong' }));
              return;
            }

            // Listen for moderation events that might affect channel visibility
            if (message.type === 'conversationModerationChanged' || message.type === 'moderationStatusChanged') {
              // Debounce the check to avoid too many API calls
              const timeoutId = setTimeout(() => {
                checkForChannels();
              }, 1000);

              return () => clearTimeout(timeoutId);
            }
          } catch (error) {
            console.error('Error parsing WebSocket message in useDoctorChatChannels:', error);
          }
        };

        ws.onclose = () => {
          // Attempt to reconnect after 3 seconds
          reconnectTimeout = setTimeout(() => {
            if (enabled && doctor?.role) {
              connectWebSocket();
            }
          }, 3000);
        };

        ws.onerror = (error) => {
          console.log('WebSocket error in useDoctorChatChannels:', error);
        };
      } catch (error) {
        console.error('Failed to create WebSocket connection in useDoctorChatChannels:', error);
      }
    };

    connectWebSocket();

    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (ws && (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN)) {
        ws.close();
      }
    };
  }, [enabled, doctor?.role, checkForChannels]);

  return {
    hasChannels,
    channelCount,
    loading,
    refresh: checkForChannels
  };
}
