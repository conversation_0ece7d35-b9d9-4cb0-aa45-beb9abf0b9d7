    limit_req_zone $binary_remote_addr zone=limit_per_ip:10m rate=10r/s;

    server {
        listen 80;
        server_name doctor.zenith.clinic;

        # Redirect all HTTP traffic to HTTPS
        return 301 https://$host$request_uri;
    }

    server {
        listen 443 ssl;
        server_name doctor.zenith.clinic;

        # SSL Configuration
        ssl_certificate /etc/letsencrypt/live/doctor.zenith.clinic/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/doctor.zenith.clinic/privkey.pem;
        include /etc/letsencrypt/options-ssl-nginx.conf;
        ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

        # API Reverse Proxy
        location /api/ {
            limit_req zone=limit_per_ip burst=5 nodelay;
            proxy_pass http://localhost:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket Reverse Proxy
        location /ws/ {
            proxy_pass http://localhost:8080;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }

        # Frontend App Reverse Proxy
        location / {
            proxy_pass http://localhost:5173;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
        }

        location /adminer/ {
            limit_req zone=limit_per_ip burst=5 nodelay;
            proxy_pass http://localhost:8081;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Additional security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    }
