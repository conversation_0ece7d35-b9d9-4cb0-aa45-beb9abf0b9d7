import winston from "winston";
import DailyRotateFile from "winston-daily-rotate-file";
import path from "path";

const isProduction = process.env.DEV_MODE === "true" ? false : true;

const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

const transports: winston.transport[] = [
  new DailyRotateFile({
    dirname: path.join(process.cwd(), "logs"),
    filename: "%DATE%.log",
    datePattern: "YYYY-MM-DD",
    zippedArchive: true,
    maxSize: "20m",
    maxFiles: "14d",
    level: "info",
  }),
];

// Add console logging in non-production
if (!isProduction) {
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
      level: "debug",
    })
  );
}

const llogger = winston.createLogger({
  level: isProduction ? "info" : "debug",
  format: logFormat,
  transports,
});

export default llogger;
