import React, { useEffect, useState } from 'react';
import { Box, Typography, Chip, Collapse, IconButton } from '@mui/material';
import { ExpandMore, ExpandLess } from '@mui/icons-material';
import Grid from '@mui/material/Grid2';
import { ApiClient } from '../../services';
import { formatTimeWithMeridian } from '../../utils';

interface DoctorStatus {
  doctorId: string;
  doctorName: string;
  isOnline: boolean;
  lastActivity: string | null;
  sessionId: string | null;
  loggedInToday: boolean;
}

interface DoctorSessionLog {
  id: string;
  doctorId: string;
  action: 'LOGIN' | 'LOGOUT';
  timestamp: string;
  sessionDuration?: number;
  ipAddress?: string;
  userAgent?: string;
}

interface WebSocketMessage {
  type: string;
  data: any;
}

const DoctorStatusIndicator: React.FC = () => {
  const [doctorStatuses, setDoctorStatuses] = useState<DoctorStatus[]>([]);
  const [doctorLogs, setDoctorLogs] = useState<Record<string, DoctorSessionLog[]>>({});
  const [expandedDoctors, setExpandedDoctors] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [loadingLogs, setLoadingLogs] = useState<Record<string, boolean>>({});

  // Fetch initial doctor statuses
  const fetchDoctorStatuses = async () => {
    try {
      const response = await ApiClient.getDoctorOnlineStatus();
      setDoctorStatuses(response.data);

      // Automatically fetch logs for all doctors on initial load
      response.data.forEach(doctor => {
        fetchDoctorLogs(doctor.doctorId);
      });
    } catch (error) {
      console.error('Error fetching doctor statuses:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch session logs for a specific doctor
  const fetchDoctorLogs = async (doctorId: string) => {
    try {
      // Set loading state for this doctor
      setLoadingLogs(prev => ({ ...prev, [doctorId]: true }));

      // Get logs for the current day only
      const today = new Date();
      const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0);
      const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);

      const response = await ApiClient.getDoctorSessionHistory(doctorId, {
        startDate: startOfDay.toISOString(),
        endDate: endOfDay.toISOString(),
        limit: 100,
        offset: 0
      });

      // Merge with existing logs and remove duplicates
      setDoctorLogs(prev => {
        const existingLogs = prev[doctorId] || [];
        const newLogs = response.data;

        // Combine all logs and remove duplicates based on timestamp and action
        const allLogs = [...existingLogs, ...newLogs];
        const uniqueLogs = allLogs.filter((log, index, array) => {
          return array.findIndex(l =>
            l.timestamp === log.timestamp &&
            l.action === log.action &&
            l.doctorId === log.doctorId
          ) === index;
        });

        // Sort by timestamp DESC (newest first)
        uniqueLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

        return {
          ...prev,
          [doctorId]: uniqueLogs
        };
      });
    } catch (error) {
      console.error(`Error fetching logs for doctor ${doctorId}:`, error);
    } finally {
      // Clear loading state for this doctor
      setLoadingLogs(prev => ({ ...prev, [doctorId]: false }));
    }
  };

  // Calculate logout duration between sessions
  const calculateLogoutDuration = (logs: DoctorSessionLog[], currentIndex: number): string | null => {
    if (logs[currentIndex].action !== 'LOGIN') return null;

    // Find the most recent logout before this login
    // Since logs are ordered by timestamp DESC (newest first), we need to look at entries AFTER currentIndex
    let previousLogout: DoctorSessionLog | null = null;

    for (let i = currentIndex + 1; i < logs.length; i++) {
      if (logs[i].action === 'LOGOUT') {
        previousLogout = logs[i];
        break;
      }
    }

    if (!previousLogout) return null;

    const logoutTime = new Date(previousLogout.timestamp);
    const loginTime = new Date(logs[currentIndex].timestamp);
    const durationMs = loginTime.getTime() - logoutTime.getTime();

    if (durationMs <= 0) return null;

    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m offline`;
    } else if (minutes > 0) {
      return `${minutes}m offline`;
    } else {
      const seconds = Math.floor(durationMs / 1000);
      return `${seconds}s offline`;
    }
  };

  // Format session duration for logout events
  const formatSessionDuration = (sessionDuration?: number): string => {
    if (!sessionDuration) return '';

    const hours = Math.floor(sessionDuration / 3600);
    const minutes = Math.floor((sessionDuration % 3600) / 60);

    if (hours > 0) {
      return `(${hours}h ${minutes}m session)`;
    } else {
      return `(${minutes}m session)`;
    }
  };

  // Toggle expanded state for doctor logs
  const toggleDoctorExpanded = (doctorId: string) => {
    setExpandedDoctors(prev => ({
      ...prev,
      [doctorId]: !prev[doctorId]
    }));

    // No need to fetch logs here since we fetch them on initial load
    // Logs are already available when user expands
  };

  // Update doctor status based on WebSocket events
  const updateDoctorStatus = (doctorId: string, isOnline: boolean, lastActivity: string, sessionId?: string) => {
    setDoctorStatuses(prev =>
      prev.map(doctor =>
        doctor.doctorId === doctorId
          ? { ...doctor, isOnline, lastActivity, sessionId: sessionId || doctor.sessionId }
          : doctor
      )
    );
  };

  // Add new log entry from WebSocket events
  const addLogEntry = (logEntry: Omit<DoctorSessionLog, 'id' | 'ipAddress' | 'userAgent'>) => {
    setDoctorLogs(prev => {
      const existingLogs = prev[logEntry.doctorId] || [];
      const newLog: DoctorSessionLog = {
        ...logEntry,
        id: `ws_${Date.now()}`, // Temporary ID for WebSocket entries
        ipAddress: undefined,
        userAgent: undefined
      };

      // Check if this log entry already exists (avoid duplicates)
      const isDuplicate = existingLogs.some(log =>
        log.timestamp === newLog.timestamp &&
        log.action === newLog.action &&
        log.doctorId === newLog.doctorId
      );

      if (isDuplicate) {
        return prev; // Don't add duplicate
      }

      // Add to the beginning and sort by timestamp DESC
      const updatedLogs = [newLog, ...existingLogs];
      updatedLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      return {
        ...prev,
        [logEntry.doctorId]: updatedLogs
      };
    });
  };

  useEffect(() => {
    // Initial fetch
    fetchDoctorStatuses();

    // Set up WebSocket connection for real-time updates
    let ws: WebSocket | null = null;
    let reconnectTimeout: NodeJS.Timeout | null = null;

    const connectWebSocket = () => {
      try {
        // Check if WebSocket URL is available
        const wsUrl = import.meta.env.VITE_WSS;
        if (!wsUrl) {

          return;
        }

        ws = new WebSocket(wsUrl);

        ws.onopen = () => {

        };

        ws.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data) as WebSocketMessage;

            if (message.type === 'ping') {
              ws?.send(JSON.stringify({ type: 'pong' }));
              return;
            }

            if (message.type === 'doctor_login') {
              const data = message.data;
              updateDoctorStatus(data.doctorId, true, data.timestamp, data.sessionId);
              addLogEntry({
                doctorId: data.doctorId,
                action: 'LOGIN',
                timestamp: data.timestamp,
                sessionDuration: undefined
              });
              return;
            }

            if (message.type === 'doctor_logout') {
              const data = message.data;
              updateDoctorStatus(data.doctorId, false, data.timestamp, data.sessionId);
              addLogEntry({
                doctorId: data.doctorId,
                action: 'LOGOUT',
                timestamp: data.timestamp,
                sessionDuration: data.sessionDuration
              });
              return;
            }
          } catch (error) {
            console.error('Error parsing WebSocket message:', error);
          }
        };

        ws.onerror = (error) => {
          console.warn('WebSocket connection error (this is normal if WebSocket server is not running):', error);
        };

        ws.onclose = (event) => {


          // Only attempt reconnection if it wasn't a manual close and we're still mounted
          if (event.code !== 1000 && !event.wasClean) {

            reconnectTimeout = setTimeout(() => {
              connectWebSocket();
            }, 5000);
          }
        };
      } catch (error) {
        console.warn('Failed to create WebSocket connection:', error);
      }
    };

    // Initial connection
    connectWebSocket();

    // Cleanup function
    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (ws) {
        ws.close(1000, 'Component unmounting');
      }
    };
  }, []);

  // No polling needed - WebSocket handles all real-time updates after initial load

  if (isLoading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Loading doctor statuses...
        </Typography>
      </Box>
    );
  }

  if (doctorStatuses.length === 0) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="h6" sx={{ mb: 1, fontSize: '14px', fontWeight: 'bold' }}>
          DOCTOR STATUS
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ fontSize: '12px' }}>
          No active doctors have logged in today
        </Typography>
        <Typography variant="caption" sx={{ fontSize: '10px', color: 'text.secondary', mt: 1, display: 'block' }}>
          Only active doctors who have logged in today are shown
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ mb: 1, fontSize: '14px', fontWeight: 'bold' }}>
        DOCTOR STATUS
      </Typography>
      <Typography variant="caption" sx={{ fontSize: '10px', color: 'text.secondary', mb: 2, display: 'block' }}>
        Showing active doctors who logged in today ({new Date().toLocaleDateString('en-AU', {
          timeZone: 'Australia/Sydney',
          weekday: 'short',
          month: 'short',
          day: 'numeric'
        })})
      </Typography>

      <Grid container spacing={1}>
        {doctorStatuses.map((doctor) => (
          <Grid key={doctor.doctorId} sx={{ width: '100%' }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                p: 1.5,
                border: '1px solid rgba(0, 0, 0, 0.1)',
                borderRadius: 1,
                backgroundColor: doctor.isOnline ? 'rgba(76, 175, 80, 0.05)' : 'rgba(244, 67, 54, 0.05)',
                mb: 1
              }}
            >
              {/* Status Dot */}
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: doctor.isOnline ? '#4CAF50' : '#F44336',
                  mr: 2,
                  flexShrink: 0,
                  boxShadow: doctor.isOnline
                    ? '0 0 8px rgba(76, 175, 80, 0.6)'
                    : '0 0 8px rgba(244, 67, 54, 0.6)'
                }}
              />

              {/* Doctor Info */}
              <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 'bold',
                    fontSize: '12px',
                    color: doctor.isOnline ? '#2E7D32' : '#C62828',
                    mb: 0.5
                  }}
                >
                  {doctor.doctorName}
                </Typography>

                {doctor.lastActivity && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '10px',
                      color: 'text.secondary'
                    }}
                  >
                    {doctor.isOnline ? 'Online' : `Last seen: ${formatTimeWithMeridian(doctor.lastActivity)}`}
                  </Typography>
                )}
              </Box>

              {/* Status Chip and Expand Button */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  label={doctor.isOnline ? 'Online' : 'Offline'}
                  size="small"
                  sx={{
                    fontSize: '10px',
                    height: 20,
                    backgroundColor: doctor.isOnline ? '#E8F5E8' : '#FFEBEE',
                    color: doctor.isOnline ? '#2E7D32' : '#C62828',
                    border: `1px solid ${doctor.isOnline ? '#4CAF50' : '#F44336'}`,
                    '& .MuiChip-label': {
                      px: 1
                    }
                  }}
                />

                <IconButton
                  size="small"
                  onClick={() => toggleDoctorExpanded(doctor.doctorId)}
                  sx={{
                    width: 20,
                    height: 20,
                    color: 'text.secondary'
                  }}
                >
                  {expandedDoctors[doctor.doctorId] ? <ExpandLess /> : <ExpandMore />}
                </IconButton>
              </Box>
            </Box>

            {/* Expandable Logs Section */}
            <Collapse in={expandedDoctors[doctor.doctorId]} timeout={300}>
              <Box sx={{
                mt: 1,
                pl: 3,
                borderLeft: '2px solid rgba(0, 0, 0, 0.1)',
                maxHeight: '200px',
                overflowY: 'auto'
              }}>
                {loadingLogs[doctor.doctorId] ? (
                  <Typography variant="caption" sx={{ fontSize: '9px', color: 'text.secondary' }}>
                    Loading today's activity...
                  </Typography>
                ) : doctorLogs[doctor.doctorId] ? (
                  doctorLogs[doctor.doctorId].length > 0 ? (
                    doctorLogs[doctor.doctorId].map((log, index) => {
                      const logoutDuration = calculateLogoutDuration(doctorLogs[doctor.doctorId], index);
                      const sessionDuration = formatSessionDuration(log.sessionDuration);

                      return (
                        <Box key={log.id} sx={{ mb: 1, p: 1, backgroundColor: 'rgba(0, 0, 0, 0.02)', borderRadius: 1 }}>
                          <Typography variant="caption" sx={{
                            fontSize: '9px',
                            fontWeight: 'bold',
                            color: log.action === 'LOGIN' ? '#2E7D32' : '#C62828'
                          }}>
                            {log.action === 'LOGIN' ? '🟢 LOGIN' : '🔴 LOGOUT'}
                          </Typography>

                          <Typography variant="caption" sx={{
                            fontSize: '9px',
                            color: 'text.secondary',
                            ml: 1
                          }}>
                            {formatTimeWithMeridian(log.timestamp)}
                          </Typography>

                          {logoutDuration && (
                            <Typography variant="caption" sx={{
                              fontSize: '8px',
                              color: '#FF9800',
                              display: 'block',
                              fontStyle: 'italic'
                            }}>
                              Was {logoutDuration}
                            </Typography>
                          )}

                          {sessionDuration && (
                            <Typography variant="caption" sx={{
                              fontSize: '8px',
                              color: '#1976D2',
                              display: 'block',
                              fontStyle: 'italic'
                            }}>
                              {sessionDuration}
                            </Typography>
                          )}
                        </Box>
                      );
                    })
                  ) : (
                    <Typography variant="caption" sx={{ fontSize: '9px', color: 'text.secondary' }}>
                      No activity today
                    </Typography>
                  )
                ) : (
                  <Typography variant="caption" sx={{ fontSize: '9px', color: 'text.secondary' }}>
                    Loading today's activity...
                  </Typography>
                )}
              </Box>
            </Collapse>
          </Grid>
        ))}
      </Grid>

      {/* Summary */}
      <Box sx={{ mt: 2, pt: 1, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
        <Typography variant="caption" sx={{ fontSize: '10px', color: 'text.secondary', display: 'block' }}>
          {doctorStatuses.filter(d => d.isOnline).length} of {doctorStatuses.length} doctors currently online
        </Typography>
        <Typography variant="caption" sx={{ fontSize: '9px', color: 'text.secondary', mt: 0.5, display: 'block', fontStyle: 'italic' }}>
          All {doctorStatuses.length} doctors shown have logged in today
        </Typography>
      </Box>
    </Box>
  );
};

export default DoctorStatusIndicator;
