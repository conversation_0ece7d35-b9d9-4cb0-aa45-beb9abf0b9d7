import React from 'react';
import { Box, Typography, Paper, useMediaQuery } from '@mui/material';
import { useTheme } from "@mui/material/styles";
import { useChatEmail } from '../../../hooks/chat-email-provider';

const ChatTreatmentPlanDetails: React.FC = () => {
    const { treatmentPlan } = useChatEmail();
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));

    // Extract patient info from treatment plan
    const patientName = treatmentPlan?.patient?.fullName || 'Patient';
    const isReturningPatient = treatmentPlan?.patient?.returningPatient || false;

    const emailTemplateValues = () => {
        const activeIngredient = treatmentPlan?.outcome === 'Approve 22% Subject To CBD Trial' ? 'CBD Oil' : treatmentPlan?.outcome ? 'Tetrahydrocannabinol (THC)' : 'None';
        const strength = treatmentPlan?.[22] && treatmentPlan[29] ? '22% and 29%' : treatmentPlan?.[22] ? '22%' : treatmentPlan?.[29] ? '29%' : 'none';

        let quantity;
        let numRepeat;
        let interval;
        let dosePerDay;
        let maxDosePerDay;
        if (treatmentPlan?.[22]) {
            quantity = treatmentPlan?.[22]?.totalQuantity || 'none';
            numRepeat = treatmentPlan?.[22]?.numberOfRepeat || 'none';
            interval = treatmentPlan?.[22]?.supplyInterval || 'none';
            dosePerDay = treatmentPlan?.[22]?.dosePerDay || 'none';
            maxDosePerDay = treatmentPlan?.[22]?.maxDosePerDay || 'none';
        }
        else if (treatmentPlan?.[29]) {
            quantity = treatmentPlan?.[29]?.totalQuantity || 'none';
            numRepeat = treatmentPlan?.[29]?.numberOfRepeat || 'none';
            interval = treatmentPlan?.[29]?.supplyInterval || 'none';
            dosePerDay = treatmentPlan?.[29]?.dosePerDay || 'none';
            maxDosePerDay = treatmentPlan?.[29]?.maxDosePerDay || 'none';
        }

        return {
            activeIngredient,
            strength,
            quantity,
            numRepeat,
            interval,
            dosePerDay,
            maxDosePerDay
        };
    };

    const getNextFollowUp = () => {
        const today = new Date();
        const futureDate = new Date(today);
        futureDate.setDate(today.getDate() + 28);
        return futureDate.toISOString().split('T')[0];
    };

    return (
        <Paper 
            elevation={0} 
            sx={{ 
                p: { xs: 2, sm: 3 }, 
                bgcolor: '#f8f9fa',
                borderRadius: '8px',
                width: '100%'
            }}
        >
            {/* Add patient information at the top */}
            <Box sx={{ mb: 2, pb: 2, borderBottom: '1px solid #e2e8f0' }}>
                <Typography variant="h6" sx={{ 
                    color: '#2d3748',
                    fontWeight: 'bold',
                    fontSize: { xs: '16px', sm: '18px' }
                }}>
                    Patient: {patientName}
                </Typography>
                <Typography variant="body2" sx={{ color: '#718096', fontSize: '13px' }}>
                    {isReturningPatient ? 'Returning Patient' : 'New Patient'}
                </Typography>
            </Box>

            <Typography variant="h6" sx={{ 
                color: '#2d3748',
                fontWeight: 'bold',
                mb: 1.5,
                fontSize: { xs: '18px', sm: '22px' }
            }}>
                Treatment Plan Overview:
            </Typography>

            <Typography variant="body1" sx={{ mb: 1.5, color: '#4a5568', fontSize: '14px' }}>
                Your treatment plan includes a script for medicinal cannabis as follows:
            </Typography>

            <Box sx={{ mb: 1.5 }}>
                <Box display="inline-flex" alignItems="center" sx={{ whiteSpace: 'nowrap', mb: 1 }}>
                    <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px' }}>
                        Active Ingredient: {emailTemplateValues().activeIngredient}
                    </Typography>
                </Box>

                <Box display="inline-flex" alignItems="center" sx={{ whiteSpace: 'nowrap', mb: 1 }}>
                    <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px' }}>
                        Strength/Concentration: {emailTemplateValues().strength}
                    </Typography>
                </Box>

                <Box display="inline-flex" sx={{ whiteSpace: 'nowrap', mb: 1 }}>
                    <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px' }}>
                        Quantity Prescribed (g): {emailTemplateValues().quantity} (g)
                    </Typography>
                </Box>

                <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px', mb: 1 }}>
                    Number of Repeats: {emailTemplateValues().numRepeat}
                </Typography>

                <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px', mb: 1 }}>
                    Repeat Interval (days): {emailTemplateValues().interval}
                </Typography>

                <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px', mb: 1 }}>
                    When and how often to use: {emailTemplateValues().dosePerDay} (g) daily max {emailTemplateValues().maxDosePerDay} (g)
                </Typography>
            </Box>

            <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px', mb: 1 }}>
                When to stop using:
            </Typography>

            <Box sx={{ ml: isDesktop ? 2 : 1, mb: 2 }}>
                <ul style={{ listStyleType: 'disc', marginLeft: isDesktop ? '10px' : '0px' }}>
                    <li>
                        <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px' }}>
                            If you experience any adverse effects
                        </Typography>
                    </li>
                    <li>
                        <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px' }}>
                            If the medication is not helping your condition
                        </Typography>
                    </li>
                    <li>
                        <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px' }}>
                            As advised by your healthcare provider
                        </Typography>
                    </li>
                </ul>
            </Box>

            <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px', mt: 2 }}>
                Follow-up appointment: {getNextFollowUp()}
            </Typography>

            {treatmentPlan?.drNotes && (
                <Box sx={{ mt: 2 }}>
                    <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px', fontWeight: 'bold' }}>
                        Doctor Notes:
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#4a5568', fontSize: '14px' }}>
                        {treatmentPlan.drNotes}
                    </Typography>
                </Box>
            )}
        </Paper>
    );
};

export default ChatTreatmentPlanDetails; 