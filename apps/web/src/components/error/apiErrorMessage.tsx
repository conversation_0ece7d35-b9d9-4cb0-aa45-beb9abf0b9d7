import { Al<PERSON>, AlertTitle, Container } from '@mui/material'
import { AuthError } from '@supabase/supabase-js'
import { AxiosError } from 'axios'
import { FC } from 'react'

const ApiErrorMessage: FC<{ error: Error | AuthError |  AxiosError}> = ({ error }) => {
    return (
        <Container>
            <Alert severity="error" sx={{zIndex: 999}}>
                <AlertTitle>Error</AlertTitle>
                {error.message}
            </Alert>
        </Container>
    )
}

export default ApiErrorMessage
