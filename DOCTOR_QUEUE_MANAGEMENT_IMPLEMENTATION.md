# Doctor Queue Management Implementation

This document outlines the implementation of the Doctor Queue Management feature, which efficiently manages patient notifications and consultations by tracking each doctor's list of notified patients and helping balance workload dynamically.

## Database Changes

1. Created a new `DoctorQueue` table to track patient-doctor assignments:
   - `id` (UUID, primary key)
   - `patientID` (VA<PERSON>HAR, references Patient)
   - `doctorID` (TEXT, references Dr.accessID)
   - `assignedAt` (TIMESTAMPTZ)
   - `status` (TEXT) - 'ASSIGNED', 'CONSULTED', 'REASSIGNED'
   - `createdAt` (TIMESTAMPTZ)
   - `updatedAt` (TIMESTAMPTZ)

2. Modified the `PatientQueue` table to add:
   - `assignedDoctorID` (TEXT, references Dr.accessID) - the doctor currently responsible for the patient
   - `consultedDoctorID` (TEXT, references Dr.accessID) - the doctor who ultimately consults with the patient
   - `processingAdmission` (BOOLEAN, default FALSE) - flag to indicate if a patient is currently being processed for admission

## Core Logic Implementation

### Timer Management

1. **Doctor Shift Tracking**
   - Uses a global `timerMap` to track active doctors (those who have started their shift)
   - Each doctor has their own timer that triggers patient notifications
   - The timer is added to the `timerMap` immediately when a doctor starts their shift
   - Initial patient notifications are delayed until the timer triggers (after 1 minute)

2. **Dynamic Patient Limit Calculation**
   - Calculates the number of patients each doctor can have assigned based on the number of active doctors
   - Formula: `Math.max(1, Math.ceil(totalWaitingPatients / activeDoctorCount))`
   - Example: 40 patients waiting with 2 doctors → each can notify up to 20 patients
   - Recalculated whenever doctors start or end their shifts

3. **Notification Batch Size**
   - Determines how many patients to notify per interval (every minute)
   - Based on the total number of consultations:
     - For 1-30 consultations → 1 notification per interval
     - For 31-39 consultations → 2 notifications per interval
     - For 40-60 consultations → 3 notifications per interval
     - For 60+ consultations → 1 + Math.floor(numberOfConsults / 20) notifications per interval
   - Limited by each doctor's remaining capacity (dynamic limit - current count)
   - Ensures different doctors notify different patients by excluding patients already assigned to other doctors

4. **Patient Assignment**
   - Assigns patients to doctors in both PatientQueue and DoctorQueue tables
   - Tracks which doctor is responsible for which patient
   - Prioritizes patients based on returning status and risk rating

5. **Patient Reassignment**
   - Identifies patients who have been waiting for more than 30 seconds
   - Reassigns them to the least busy doctor (doctor with fewest assigned patients)
   - Updates both PatientQueue and DoctorQueue tables during reassignment
   - Marks the patient's status as 'REASSIGNED' in the original doctor's queue
   - Notifies doctors via WebSocket when a patient is reassigned
   - Runs every minute as part of the no-show check process

6. **Consultation Tracking**
   - Marks patients as consulted by a specific doctor
   - Updates both PatientQueue and DoctorQueue tables

## WebSocket Notifications

Added WebSocket topics to notify doctors and update the UI:
1. `UPDATE_TIMER` - When a doctor starts their shift
2. `UPDATE_CONSULTATION` - When a consultation is updated
3. `ADD_PATIENT_QUEUE` - When a patient is added to the queue
4. `UPDATE_PATIENT_QUEUE` - When a patient's status is updated
5. `REMOVE_ID` - When a patient is removed from the queue

## Background Processes

1. **Patient Notification Process**
   - Runs every minute for each active doctor
   - Checks if there are any online patients
   - If not, notifies the next batch of patients based on the notification batch size
   - Includes one no-show patient in each batch

2. **Patient Reassignment Process**
   - Runs every minute as part of the no-show check process
   - Finds patients who have been waiting for more than 30 seconds since notification
   - Finds the least busy doctor based on current patient count
   - Reassigns patients to the least busy doctor
   - Updates both PatientQueue and DoctorQueue tables
   - Marks the patient's status as 'REASSIGNED' in the original doctor's queue
   - Notifies doctors via WebSocket with PATIENT_REASSIGNED topic
   - Implementation:
     - Main function: `startPatientReassignmentProcess` in `apps/api/src/controllers/doctor/patientReassignment.ts` (lines 12-51)
     - Called from: `startTimer` in `apps/api/src/controllers/doctor/index.ts` (around line 1000)
     - Supporting functions:
       - `findPatientsForReassignment` in `apps/api/src/controllers/doctor/doctorQueueManager.ts` (lines 321-343)
       - `findLeastBusyDoctor` in `apps/api/src/controllers/doctor/doctorQueueManager.ts` (lines 350-377)
       - `reassignPatient` in `apps/api/src/controllers/doctor/doctorQueueManager.ts` (lines 162-254)

3. **No-Show Check Process**
   - Runs every minute
   - Identifies patients who haven't responded to notifications
   - Marks them as no-show after a certain period
   - Allows them to be re-notified up to 3 times



## Edge Cases Handled

1. **Doctor Capacity Limits**: If a doctor has already notified their limit of patients and more doctors join later, the first doctor will not notify additional patients. (Implemented in `canDoctorNotifyMorePatients` function in `apps/api/src/controllers/doctor/doctorQueueManager.ts`)

2. **New Doctor Assignment**: If a doctor has notified all patients and new doctors join, the system will assign new doctors to any patients who have waited over 30 seconds. (Implemented in `startPatientReassignmentProcess` function in `apps/api/src/controllers/doctor/patientReassignment.ts`)

3. **No-Show Handling**: For no-show patients, only the doctor who initially attempted the consult can reattempt to prevent overlapping consultations. (Implemented in `callNoShowPatient` function in `apps/api/src/controllers/doctor/index.ts`)

4. **Timer Recognition**: The timer is added to the `timerMap` immediately when a doctor starts their shift, ensuring proper recognition for dynamic limit calculations. (Implemented in `startTimer` function in `apps/api/src/controllers/doctor/index.ts`)

5. **Immediate Initial Notification with Staggered Delay**: Initial patient notifications are sent immediately when a doctor starts their shift, with a small random delay (500-1500ms) to prevent race conditions when multiple doctors start their shifts simultaneously. (Implemented in `startTimer` function in `apps/api/src/controllers/doctor/index.ts`)

6. **Batch Size Limits**: The number of patients notified per interval is limited by both the notification batch size and each doctor's remaining capacity. (Implemented in `getNextPatientsForDoctor` function in `apps/api/src/controllers/doctor/doctorQueueManager.ts`)

7. **Duplicate Patient Notifications**: The system prevents multiple doctors from notifying the same patient by using SQL's `NOT EXISTS` to exclude patients already assigned to other doctors. (Implemented in `getNextPatientsForDoctor` function in `apps/api/src/controllers/doctor/doctorQueueManager.ts`)

8. **Patient Distribution Tracking**: The system logs detailed information about patient distribution across doctors to help diagnose any issues. (Implemented throughout the codebase with logger calls)

9. **NULL Value Handling**: The system properly handles consultations where the `notificationSent` field is `null`, ensuring these patients are included in the notification queue. (Implemented in SQL queries in `getNextPatientsForDoctor` function)

10. **Concurrent Patient Admission Prevention**: The system prevents multiple doctors from admitting the same patient simultaneously by using database locking mechanisms. (Implemented in `fetchNextPatientToConsult` and `postPatientAdmission` functions in `apps/api/src/controllers/doctor/index.ts`)

## Implementation Details

### Recent Improvements

1. **Fixed Patient Distribution**: Modified the patient selection logic to ensure patients are properly distributed between doctors:
   - Added SQL's `NOT EXISTS` to exclude patients already assigned to other doctors
   - Improved the query performance and reliability
   - Added detailed logging to track patient distribution

2. **Enhanced Notification Scaling**: Improved the notification batch size calculation:
   - Moved the calculation to the `notifyNextPatientMiddleWare` function
   - Updated the scaling logic to match requirements:
     - For 1-30 consultations → 1 notification per interval
     - For 31-39 consultations → 2 notifications per interval
     - For 40-60 consultations → 3 notifications per interval
     - For 60+ consultations → 1 + Math.floor(numberOfConsults / 20) notifications per interval
   - Ensured it's calculated dynamically for each notification interval
   - Added logging to show the calculated batch size

3. **Immediate Notifications with Staggered Delay**: Changed the notification timing:
   - Patients are now notified immediately when a doctor starts their shift
   - Added a small random delay (500-1500ms) to prevent race conditions when multiple doctors start simultaneously
   - Removed the 1-minute delay before the first notification
   - Subsequent notifications still occur at regular intervals

4. **Improved Logging**: Added detailed logging throughout the system:
   - Patient distribution across doctors
   - Notification batch sizes and adjustments
   - Patient selection and assignment

5. **Handling NULL Values**: Enhanced the patient selection queries:
   - Modified queries to handle `null` values in the `notificationSent` field
   - Changed `c."notificationSent" = false` to `(c."notificationSent" = false OR c."notificationSent" IS NULL)`
   - Ensures patients with `null` notification status are properly included in the queue
   - Added detailed logging to show consultation details for debugging

6. **Database Connection Pool Improvements**: Enhanced database transaction handling to prevent connection pool exhaustion:getting the same patient
   - Implemented proper transaction handling with explicit BEGIN/COMMIT/ROLLBACK
   - Added checks to prevent admitting patients who are already being processed
   - Implementation:
     - Updated functions:
       - `fetchNextPatientToConsult` in `apps/api/src/controllers/doctor/index.ts` (lines 3680-3739)
       - `postPatientAdmission` in `apps/api/src/controllers/doctor/index.ts` (lines 1914-2063)
       - `markPatientAsConsulted` in `apps/api/src/controllers/doctor/doctorQueueManager.ts` (lines 263-314)

### Key Functions

1. **calculateDynamicPatientLimit**
   - Calculates the dynamic patient limit based on active doctors
   - Uses the `timerMap` to count active doctors
   - Returns the maximum number of patients each doctor can have assigned
   - File: `apps/api/src/controllers/doctor/doctorQueueManager.ts` (lines 13-64)

2. **getNextPatientsForDoctor**
   - Gets the next batch of patients to notify for a specific doctor
   - Respects both the notification batch size and the doctor's remaining capacity
   - Prioritizes patients based on returning status and risk rating
   - Uses SQL's `NOT EXISTS` to exclude patients already assigned to other doctors
   - Handles `null` values in the `notificationSent` field
   - Includes detailed logging about available patients and selection criteria
   - Logs detailed information about patient selection for debugging
   - File: `apps/api/src/controllers/doctor/doctorQueueManager.ts` (lines 405-530)

3. **notifyNextPatientMiddleWare**
   - Notifies the next batch of patients for a specific doctor
   - Calculates the notification batch size based on total consultations
   - Updates the consultation and queue tables
   - Sends WebSocket notifications
   - Logs detailed information about notification process including:
     - Consultation details for debugging
     - Patient information being notified
     - Notification batch size calculations
     - Doctor capacity and patient limits
   - Tracks patient distribution across doctors
   - Handles `null` values in the `notificationSent` field
   - File: `apps/api/src/controllers/doctor/index.ts` (around line 1121)

4. **startTimer**
   - Starts a timer for a specific doctor
   - Adds the timer to the `timerMap` immediately
   - Sends initial patient notifications with a small random delay (500-1500ms)
   - Uses setTimeout to stagger notifications when multiple doctors start simultaneously
   - Sets up the interval for periodic notifications
   - File: `apps/api/src/controllers/doctor/index.ts` (around line 2713)

5. **fetchNextPatientToConsult**
   - Gets the next patient to be admitted by a doctor
   - Marks patients as being processed to prevent race conditions
   - Implements proper transaction handling with explicit BEGIN/COMMIT/ROLLBACK
   - File: `apps/api/src/controllers/doctor/index.ts` (lines 3691)

6. **postPatientAdmission**
   - Admits a patient for consultation with a doctor
   - Checks if a patient is already being processed by another doctor
   - Updates PatientQueue, PatientQueueDetails, and Admission tables
   - Implements proper transaction handling with explicit BEGIN/COMMIT/ROLLBACK
   - File: `apps/api/src/controllers/doctor/index.ts` (lines 1914-2063)

7. **markPatientAsConsulted**
   - Marks a patient as consulted by a specific doctor
   - Updates both PatientQueue and DoctorQueue tables
   - Uses locking to prevent concurrent updates to the same patient
   - Implements proper transaction handling with explicit BEGIN/COMMIT/ROLLBACK
   - File: `apps/api/src/controllers/doctor/doctorQueueManager.ts` (lines 263-314)
