import { DateTime } from 'luxon';
import { logger } from '../config/logger';
import StripeSyncService from './stripeSyncService';
import { db } from '../utils/db';

interface BackfillPlan {
  totalDays: number;
  chunks: Array<{
    startDate: DateTime;
    endDate: DateTime;
    chunkNumber: number;
    estimatedPayments: number;
  }>;
  estimatedDuration: number;
  estimatedTotalPayments: number;
}

interface BackfillProgress {
  planId: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  totalChunks: number;
  completedChunks: number;
  currentChunk?: number;
  startedAt: DateTime;
  estimatedCompletion?: DateTime;
  results: {
    totalPaymentsProcessed: number;
    totalPaymentsInserted: number;
    totalPaymentsUpdated: number;
    errors: string[];
  };
}

interface BackfillResult {
  success: boolean;
  planId: string;
  totalDuration: number;
  chunksCompleted: number;
  totalChunks: number;
  paymentsProcessed: number;
  paymentsInserted: number;
  paymentsUpdated: number;
  errors: string[];
}

class BackfillService {
  private stripeSyncService: StripeSyncService;
  private activeBackfills = new Map<string, BackfillProgress>();

  constructor() {
    this.stripeSyncService = new StripeSyncService();
  }

  /**
   * Create a backfill plan for a date range
   */
  async createBackfillPlan(startDate: DateTime, endDate: DateTime, chunkSizeDays: number = 7): Promise<BackfillPlan> {
    const totalDays = Math.ceil(endDate.diff(startDate, 'days').days);
    
    if (totalDays <= 0) {
      throw new Error('End date must be after start date');
    }

    if (totalDays > 365) {
      throw new Error('Cannot backfill more than 365 days at once');
    }

    const chunks: BackfillPlan['chunks'] = [];
    let currentStart = startDate;
    let chunkNumber = 1;

    while (currentStart < endDate) {
      const currentEnd = currentStart.plus({ days: chunkSizeDays });
      const chunkEnd = currentEnd > endDate ? endDate : currentEnd;
      
      // Estimate payments for this chunk (rough estimate based on typical volume)
      const chunkDays = Math.ceil(chunkEnd.diff(currentStart, 'days').days);
      const estimatedPayments = chunkDays * 10; // Assume ~10 payments per day

      chunks.push({
        startDate: currentStart,
        endDate: chunkEnd,
        chunkNumber,
        estimatedPayments,
      });

      currentStart = chunkEnd;
      chunkNumber++;
    }

    const estimatedTotalPayments = chunks.reduce((sum, chunk) => sum + chunk.estimatedPayments, 0);
    const estimatedDuration = chunks.length * 30000; // ~30 seconds per chunk

    return {
      totalDays,
      chunks,
      estimatedDuration,
      estimatedTotalPayments,
    };
  }

  /**
   * Execute backfill with progress tracking
   */
  async executeBackfill(plan: BackfillPlan): Promise<BackfillResult> {
    const planId = this.generatePlanId();
    const startTime = Date.now();

    const progress: BackfillProgress = {
      planId,
      status: 'running',
      totalChunks: plan.chunks.length,
      completedChunks: 0,
      startedAt: DateTime.now().setZone('Australia/Sydney'),
      results: {
        totalPaymentsProcessed: 0,
        totalPaymentsInserted: 0,
        totalPaymentsUpdated: 0,
        errors: [],
      },
    };

    this.activeBackfills.set(planId, progress);

    try {
      logger.info(`Starting backfill execution: ${planId}`, {
        totalChunks: plan.chunks.length,
        totalDays: plan.totalDays,
        estimatedPayments: plan.estimatedTotalPayments,
      });

      for (let i = 0; i < plan.chunks.length; i++) {
        const chunk = plan.chunks[i];
        progress.currentChunk = chunk.chunkNumber;

        try {
          logger.info(`Processing backfill chunk ${chunk.chunkNumber}/${plan.chunks.length}`, {
            startDate: chunk.startDate.toISO(),
            endDate: chunk.endDate.toISO(),
          });

          const chunkResult = await this.stripeSyncService.syncPaymentsForDateRange({
            startDate: chunk.startDate,
            endDate: chunk.endDate,
            syncType: 'backfill',
            batchSize: 50, // Smaller batches for backfill
          });

          // Update progress
          progress.completedChunks++;
          progress.results.totalPaymentsProcessed += chunkResult.paymentsProcessed;
          progress.results.totalPaymentsInserted += chunkResult.paymentsInserted;
          progress.results.totalPaymentsUpdated += chunkResult.paymentsUpdated;
          progress.results.errors.push(...chunkResult.errors);

          // Update estimated completion
          const completionRatio = progress.completedChunks / progress.totalChunks;
          const elapsedTime = Date.now() - startTime;
          const estimatedTotalTime = elapsedTime / completionRatio;
          progress.estimatedCompletion = progress.startedAt.plus({ milliseconds: estimatedTotalTime });

          logger.info(`Backfill chunk ${chunk.chunkNumber} completed`, {
            paymentsProcessed: chunkResult.paymentsProcessed,
            paymentsInserted: chunkResult.paymentsInserted,
            paymentsUpdated: chunkResult.paymentsUpdated,
            errors: chunkResult.errors.length,
          });

          // Small delay between chunks to avoid overwhelming the API
          if (i < plan.chunks.length - 1) {
            await this.delay(2000); // 2 second delay
          }

        } catch (chunkError) {
          const errorMessage = `Chunk ${chunk.chunkNumber} failed: ${chunkError instanceof Error ? chunkError.message : 'Unknown error'}`;
          progress.results.errors.push(errorMessage);
          logger.error(errorMessage, chunkError);

          // Continue with next chunk instead of failing entire backfill
          progress.completedChunks++;
        }
      }

      progress.status = 'completed';
      const totalDuration = Date.now() - startTime;

      const result: BackfillResult = {
        success: progress.results.errors.length < plan.chunks.length, // Success if not all chunks failed
        planId,
        totalDuration,
        chunksCompleted: progress.completedChunks,
        totalChunks: progress.totalChunks,
        paymentsProcessed: progress.results.totalPaymentsProcessed,
        paymentsInserted: progress.results.totalPaymentsInserted,
        paymentsUpdated: progress.results.totalPaymentsUpdated,
        errors: progress.results.errors,
      };

      logger.info(`Backfill execution completed: ${planId}`, result);
      return result;

    } catch (error) {
      progress.status = 'failed';
      const errorMessage = `Backfill execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      progress.results.errors.push(errorMessage);
      
      logger.error(`Backfill execution failed: ${planId}`, error);
      throw error;

    } finally {
      // Keep progress for 1 hour for status queries
      setTimeout(() => {
        this.activeBackfills.delete(planId);
      }, 60 * 60 * 1000);
    }
  }

  /**
   * Get backfill progress
   */
  getBackfillProgress(planId: string): BackfillProgress | null {
    return this.activeBackfills.get(planId) || null;
  }

  /**
   * List all active backfills
   */
  getActiveBackfills(): BackfillProgress[] {
    return Array.from(this.activeBackfills.values());
  }

  /**
   * Smart backfill - automatically determine optimal chunk size and missing data
   */
  async smartBackfill(startDate: DateTime, endDate: DateTime): Promise<BackfillResult> {
    logger.info('Starting smart backfill analysis', {
      startDate: startDate.toISO(),
      endDate: endDate.toISO(),
    });

    // Analyze existing data to find gaps
    const missingDateRanges = await this.findMissingDataRanges(startDate, endDate);
    
    if (missingDateRanges.length === 0) {
      logger.info('No missing data found in date range');
      return {
        success: true,
        planId: 'no-backfill-needed',
        totalDuration: 0,
        chunksCompleted: 0,
        totalChunks: 0,
        paymentsProcessed: 0,
        paymentsInserted: 0,
        paymentsUpdated: 0,
        errors: [],
      };
    }

    logger.info(`Found ${missingDateRanges.length} missing date ranges`, missingDateRanges);

    // Create optimized plan for missing ranges
    const chunks: BackfillPlan['chunks'] = [];
    let chunkNumber = 1;

    for (const range of missingDateRanges) {
      const rangeDays = Math.ceil(range.endDate.diff(range.startDate, 'days').days);
      const optimalChunkSize = rangeDays > 30 ? 7 : Math.max(1, rangeDays); // 7-day chunks for large ranges

      let currentStart = range.startDate;
      while (currentStart < range.endDate) {
        const currentEnd = currentStart.plus({ days: optimalChunkSize });
        const chunkEnd = currentEnd > range.endDate ? range.endDate : currentEnd;
        
        chunks.push({
          startDate: currentStart,
          endDate: chunkEnd,
          chunkNumber,
          estimatedPayments: Math.ceil(chunkEnd.diff(currentStart, 'days').days) * 10,
        });

        currentStart = chunkEnd;
        chunkNumber++;
      }
    }

    const plan: BackfillPlan = {
      totalDays: Math.ceil(endDate.diff(startDate, 'days').days),
      chunks,
      estimatedDuration: chunks.length * 30000,
      estimatedTotalPayments: chunks.reduce((sum, chunk) => sum + chunk.estimatedPayments, 0),
    };

    return this.executeBackfill(plan);
  }

  /**
   * Find missing data ranges by checking database
   */
  private async findMissingDataRanges(startDate: DateTime, endDate: DateTime): Promise<Array<{ startDate: DateTime; endDate: DateTime }>> {
    const client = await db.connect();
    
    try {
      // Get dates that have payments in the database
      const query = `
        SELECT DISTINCT DATE(created_at) as payment_date
        FROM stripe_payments
        WHERE created_at >= $1 AND created_at <= $2
        ORDER BY payment_date
      `;

      const result = await client.query(query, [startDate.toJSDate(), endDate.toJSDate()]);
      const existingDates = new Set(result.rows.map(row => row.payment_date.toISOString().split('T')[0]));

      // Find missing date ranges
      const missingRanges: Array<{ startDate: DateTime; endDate: DateTime }> = [];
      let currentStart: DateTime | null = null;

      let currentDate = startDate.startOf('day');
      while (currentDate <= endDate) {
        const dateString = currentDate.toFormat('yyyy-MM-dd');
        
        if (!existingDates.has(dateString)) {
          if (!currentStart) {
            currentStart = currentDate;
          }
        } else {
          if (currentStart) {
            missingRanges.push({
              startDate: currentStart,
              endDate: currentDate.minus({ days: 1 }),
            });
            currentStart = null;
          }
        }

        currentDate = currentDate.plus({ days: 1 });
      }

      // Handle case where missing range extends to the end
      if (currentStart) {
        missingRanges.push({
          startDate: currentStart,
          endDate: endDate,
        });
      }

      return missingRanges;

    } finally {
      client.release();
    }
  }

  /**
   * Generate unique plan ID
   */
  private generatePlanId(): string {
    return `backfill_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export default BackfillService;
export { BackfillPlan, BackfillProgress, BackfillResult };
