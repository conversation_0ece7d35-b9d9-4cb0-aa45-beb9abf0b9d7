# S3 File Upload Implementation Documentation

## Overview

This document describes the AWS S3 file upload implementation used in the medical consultation system. The implementation uses presigned URLs for secure, direct client-to-S3 uploads, reducing server load and improving performance.

## Architecture

### Components
- **AWS S3 Bucket**: Cloud storage for patient documents
- **Presigned URLs**: Temporary, secure upload URLs
- **Database Tracking**: PostgreSQL tables to track file metadata
- **API Endpoints**: Backend services for URL generation and file tracking

### Flow Diagram
```
Client Request → API (Presigned URL) → Direct S3 Upload → Database Update
```

## Configuration

### Environment Variables
```bash
# AWS Configuration
AWS_REGION=ap-southeast-2
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_BUCKET_NAME=your_bucket_name

# Application URLs
FUNNEL_URL=your_application_domain
```

### AWS S3 Client Setup

**File**: `apps/api/src/config/bucketClient.ts`
```typescript
import { S3Client } from '@aws-sdk/client-s3';
import config from './index';

const bucketClient = new S3Client({
  region: config.awsRegion,
  credentials: {
    accessKeyId: config.awsAccessKeyId,
    secretAccessKey: config.awsSecretAccessKey,
  },
});
export default bucketClient;
```

### Configuration Index

**File**: `apps/api/src/config/index.ts`
```typescript
export default {
  // ... other config
  awsRegion: process.env.AWS_REGION ?? 'ap-southeast-2',
  awsAccessKeyId: process.env.AWS_ACCESS_KEY_ID ?? '',
  awsSecretAccessKey: process.env.AWS_SECRET_ACCESS_KEY ?? '',
  awsBucketName: process.env.AWS_BUCKET_NAME ?? 'shopreviewsbucket',
};
```

## Database Schema

### Required Table: user_uploaded_documents

```sql
CREATE TABLE user_uploaded_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id VARCHAR(255) NOT NULL,
  question_key VARCHAR(255) NOT NULL,
  file_url TEXT,
  file_name TEXT,
  mime_type TEXT,
  file_size INTEGER,
  created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, question_key)
);

-- Indexes for performance
CREATE INDEX idx_user_uploaded_documents_user_id ON user_uploaded_documents(user_id);
CREATE INDEX idx_user_uploaded_documents_question_key ON user_uploaded_documents(question_key);
```

### Alternative: DischargeLetters Table (Database Storage)
```sql
CREATE TABLE DischargeLetters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "fileName" TEXT,
  "mimeType" TEXT,
  "email" TEXT REFERENCES Patient("email") ON DELETE CASCADE ON UPDATE CASCADE,
  "patientID" VARCHAR(255) NOT NULL,
  "zohoID" VARCHAR(255) NOT NULL,
  status TEXT,
  "fileData" BYTEA, -- For binary data storage
  "createdAt" TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

## API Implementation

### 1. Presigned URL Generation

**Endpoint**: `POST /api/v1.0/get-presigned-url`

**File**: `apps/api/src/controllers/funnel/index.ts`
```typescript
export const getPresignedUrl: RequestHandler = catchAll(async (req, res) => {
  const { fileName, fileType } = req.body;
  
  if (!fileName || !fileType) {
    res.status(400).json({ message: 'File name and type are required' });
    return;
  }
  
  // Generate unique key with timestamp
  const key = `form-docs/${Date.now()}-${fileName}`;

  const params = {
    Bucket: config.awsBucketName,
    Key: key,
    ContentType: fileType,
  };
  
  try {
    const command = new PutObjectCommand(params);
    const url = await getSignedUrl(bucketClient, command, { expiresIn: 3600 }); // 1 hour
    res.status(200).json({ url, key });
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    res.status(500).json({ message: 'Error generating presigned URL' });
  }
});
```

**Request Body**:
```json
{
  "fileName": "blood_pressure_results.pdf",
  "fileType": "application/pdf"
}
```

**Response**:
```json
{
  "url": "https://bucket.s3.region.amazonaws.com/form-docs/**********-blood_pressure_results.pdf?X-Amz-Algorithm=...",
  "key": "form-docs/**********-blood_pressure_results.pdf"
}
```

### 2. File Upload Completion

**Endpoint**: `POST /api/v1.0/patient/upload-form-doc`

```typescript
export const uploadFormDocument: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  try {
    const { file_url, question_key, user_id } = req.body;
    const response = await client.query(
      'UPDATE user_uploaded_documents SET file_url = $1 WHERE question_key = $2 AND user_id = $3 RETURNING id',
      [file_url, question_key, user_id],
    );
    res.status(201).json({ id: response.rows[0].id });
  } catch (error) {
    console.error('Error uploading form document:', error);
    res.status(500).json({ message: 'Error uploading form document' });
  } finally {
    client.release();
  }
});
```

**Request Body**:
```json
{
  "file_url": "https://bucket.s3.region.amazonaws.com/form-docs/**********-blood_pressure_results.pdf",
  "question_key": "blood_pressure",
  "user_id": "lead_12345"
}
```

## Route Configuration

**File**: `apps/api/src/routes/funnel.route.ts`
```typescript
import { getPresignedUrl, uploadFormDocument } from '../controllers/funnel';

// Add these routes
router.post(`/${currentVersion}/get-presigned-url`, getPresignedUrl);
router.post(`/${currentVersion}/patient/upload-form-doc`, uploadFormDocument);
```

## Integration with Patient Questionnaire

### Database Entry Creation
When patients indicate they have medical documents during questionnaire:

```typescript
if (data.blood_pressure.toLowerCase() === 'yes') {
  // Create database entry for file upload tracking
  await client.query(
    `INSERT INTO user_uploaded_documents ("user_id", "question_key")
     VALUES ($1, $2)`,
    [leadId, 'blood_pressure'],
  );
  
  // Set Zoho field with upload URL
  dataZoho.data[0].Do_you_have_any_history_of_high_blood_pressure = 'Yes';
  dataZoho.data[0].Blood_Pressure_Results_URL = 
    `https://${process.env.FUNNEL_URL}/patient/upload-blood-pressure?token=${leadId}`;
}
```

### Supported Document Types
- `blood_pressure` - Blood pressure readings
- `heart_rate` - Heart rate monitoring results
- `discharge_letters` - Hospital discharge documents
- `mental_health_documents` - Supporting mental health documentation

## File Organization

### S3 Bucket Structure
```
bucket-name/
├── form-docs/
│   ├── *************-blood_pressure_report.pdf
│   ├── 1640995300000-heart_rate_monitor.jpg
│   └── 1640995400000-discharge_letter.pdf
└── other-folders/
```

### Naming Convention
- **Pattern**: `{timestamp}-{original_filename}`
- **Purpose**: Prevents filename conflicts
- **Example**: `*************-patient_document.pdf`

## Security Considerations

### Presigned URL Security
- **Expiration**: URLs expire after 1 hour
- **Scope**: Limited to specific bucket and key
- **Method**: Only PUT operations allowed
- **Content-Type**: Enforced during upload

### Access Control
- **IAM Policies**: Restrict S3 access to application service account
- **CORS Configuration**: Configure bucket for web uploads
- **Encryption**: Enable S3 server-side encryption

### Recommended IAM Policy
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:PutObject",
        "s3:PutObjectAcl",
        "s3:GetObject"
      ],
      "Resource": "arn:aws:s3:::your-bucket-name/form-docs/*"
    }
  ]
}
```

## Frontend Implementation

### Client-Side Upload Flow

```typescript
// 1. Request presigned URL
const getPresignedUrl = async (fileName: string, fileType: string) => {
  const response = await fetch('/api/v1.0/get-presigned-url', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ fileName, fileType })
  });
  return response.json();
};

// 2. Upload file directly to S3
const uploadToS3 = async (file: File, presignedUrl: string) => {
  const response = await fetch(presignedUrl, {
    method: 'PUT',
    body: file,
    headers: { 'Content-Type': file.type }
  });
  return response.ok;
};

// 3. Update database with file URL
const updateDatabase = async (fileUrl: string, questionKey: string, userId: string) => {
  const response = await fetch('/api/v1.0/patient/upload-form-doc', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      file_url: fileUrl,
      question_key: questionKey,
      user_id: userId
    })
  });
  return response.json();
};

// Complete upload workflow
const handleFileUpload = async (file: File, questionKey: string, userId: string) => {
  try {
    // Step 1: Get presigned URL
    const { url, key } = await getPresignedUrl(file.name, file.type);

    // Step 2: Upload to S3
    const uploadSuccess = await uploadToS3(file, url);
    if (!uploadSuccess) throw new Error('S3 upload failed');

    // Step 3: Update database
    const fileUrl = url.split('?')[0]; // Remove query parameters
    const result = await updateDatabase(fileUrl, questionKey, userId);

    console.log('Upload completed:', result);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

### React Component Example

```tsx
import React, { useState } from 'react';

interface FileUploadProps {
  questionKey: string;
  userId: string;
  onUploadComplete?: (fileUrl: string) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ questionKey, userId, onUploadComplete }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      // Get presigned URL
      setUploadProgress(25);
      const { url, key } = await getPresignedUrl(file.name, file.type);

      // Upload to S3
      setUploadProgress(50);
      const uploadSuccess = await uploadToS3(file, url);
      if (!uploadSuccess) throw new Error('Upload failed');

      // Update database
      setUploadProgress(75);
      const fileUrl = url.split('?')[0];
      await updateDatabase(fileUrl, questionKey, userId);

      setUploadProgress(100);
      onUploadComplete?.(fileUrl);
    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed. Please try again.');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div>
      <input
        type="file"
        onChange={handleFileSelect}
        disabled={uploading}
        accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
      />
      {uploading && (
        <div>
          <progress value={uploadProgress} max={100} />
          <span>{uploadProgress}% uploaded</span>
        </div>
      )}
    </div>
  );
};
```

## Error Handling

### Common Error Scenarios

1. **Invalid File Type**
```typescript
if (!allowedTypes.includes(file.type)) {
  throw new Error('File type not supported');
}
```

2. **File Size Limits**
```typescript
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
if (file.size > MAX_FILE_SIZE) {
  throw new Error('File too large');
}
```

3. **Network Failures**
```typescript
const uploadWithRetry = async (file: File, url: string, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await uploadToS3(file, url);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};
```

4. **Presigned URL Expiration**
```typescript
const isUrlExpired = (url: string): boolean => {
  const urlObj = new URL(url);
  const expires = urlObj.searchParams.get('X-Amz-Expires');
  const date = urlObj.searchParams.get('X-Amz-Date');

  if (!expires || !date) return true;

  const expirationTime = new Date(date).getTime() + (parseInt(expires) * 1000);
  return Date.now() > expirationTime;
};
```

## Monitoring and Logging

### Backend Logging
```typescript
import { logger } from '../../config/logger';

export const getPresignedUrl: RequestHandler = catchAll(async (req, res) => {
  const { fileName, fileType } = req.body;

  logger.info('Presigned URL requested', {
    fileName,
    fileType,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  });

  try {
    // ... implementation
    logger.info('Presigned URL generated successfully', { key });
  } catch (error) {
    logger.error('Presigned URL generation failed', { error, fileName, fileType });
    throw error;
  }
});
```

### Metrics to Track
- Upload success/failure rates
- File sizes and types
- Upload duration
- Presigned URL generation frequency
- Storage costs

## Deployment Considerations

### Environment Setup
1. **AWS Credentials**: Use IAM roles in production, not access keys
2. **Bucket Configuration**: Enable versioning and lifecycle policies
3. **CDN**: Consider CloudFront for file delivery
4. **Backup**: Configure cross-region replication

### Production Checklist
- [ ] IAM policies configured with least privilege
- [ ] S3 bucket encryption enabled
- [ ] CORS policy configured for web uploads
- [ ] Lifecycle policies for cost optimization
- [ ] Monitoring and alerting setup
- [ ] Backup and disaster recovery plan
- [ ] File size and type validation
- [ ] Rate limiting on presigned URL generation

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Configure S3 bucket CORS policy
   - Ensure allowed origins include your domain

2. **403 Forbidden**
   - Check IAM permissions
   - Verify bucket policy
   - Ensure presigned URL hasn't expired

3. **File Not Found**
   - Verify S3 key format
   - Check bucket name configuration
   - Ensure file was uploaded successfully

4. **Database Sync Issues**
   - Implement retry logic for database updates
   - Add transaction handling
   - Monitor for orphaned S3 files

### Debug Commands
```bash
# Check S3 bucket contents
aws s3 ls s3://your-bucket-name/form-docs/ --recursive

# Test presigned URL generation
curl -X POST http://localhost:5000/api/v1.0/get-presigned-url \
  -H "Content-Type: application/json" \
  -d '{"fileName":"test.pdf","fileType":"application/pdf"}'

# Verify database entries
SELECT * FROM user_uploaded_documents WHERE user_id = 'test_user';
```

## Migration Guide

### From Database Storage to S3

1. **Create Migration Script**
```sql
-- Add file_url column to existing table
ALTER TABLE existing_file_table ADD COLUMN file_url TEXT;

-- Create user_uploaded_documents table
-- (Use schema from Database Schema section)
```

2. **Data Migration**
```typescript
const migrateFilesToS3 = async () => {
  const files = await db.query('SELECT * FROM existing_file_table WHERE file_data IS NOT NULL');

  for (const file of files.rows) {
    // Upload to S3
    const key = `migrated/${file.id}-${file.filename}`;
    await s3.putObject({
      Bucket: config.awsBucketName,
      Key: key,
      Body: file.file_data,
      ContentType: file.mime_type
    });

    // Update database
    const fileUrl = `https://${config.awsBucketName}.s3.${config.awsRegion}.amazonaws.com/${key}`;
    await db.query('UPDATE existing_file_table SET file_url = $1 WHERE id = $2', [fileUrl, file.id]);
  }
};
```

## Cost Optimization

### Storage Classes
- Use S3 Standard for frequently accessed files
- Implement lifecycle policies for archival
- Consider S3 Intelligent-Tiering for unknown access patterns

### Request Optimization
- Batch presigned URL requests when possible
- Implement client-side caching for repeated requests
- Use multipart uploads for large files

## Additional Resources

### AWS Documentation
- [S3 Presigned URLs](https://docs.aws.amazon.com/AmazonS3/latest/userguide/presigned-urls.html)
- [S3 CORS Configuration](https://docs.aws.amazon.com/AmazonS3/latest/userguide/cors.html)
- [IAM Best Practices](https://docs.aws.amazon.com/IAM/latest/UserGuide/best-practices.html)

### Implementation Notes
- This implementation provides a robust, scalable file upload solution
- Leverages AWS S3's capabilities while maintaining security and performance
- Suitable for medical document storage with proper compliance considerations
- Can be extended for additional file types and use cases

---

**Last Updated**: 2025-07-24
**Version**: 1.0
**Author**: System Documentation
