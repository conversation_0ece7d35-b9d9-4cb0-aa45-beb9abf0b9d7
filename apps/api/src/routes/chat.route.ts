import express from 'express';
//import { validateDr } from '../middlewares/validationMiddleware';
import {
  generateToken,
  refreshToken,
  createChannel,
  getUserChannels,
  addChannelMember,
  removeChannelMember,
  sendChannelMessage,
  sendChannelEvent,
  postChatTreatmentPlan,
  getPatientTreatmentPlans,
  getLatestTreatmentPlansForPatients,
  getLatestPatientTreatmentPlan,
  getLatestTreatmentPlanWithDoctor,
  handleStreamWebhook,
  checkMessageVisibility,
  checkConversationVisibility,
  getChatNotification,
  getAllChatNotifications,
  markChatNotificationRead,
  setChatNotificationForMessage,
  setDoctorNotificationForMessage,
  clearDoctorNotificationForPatient,
  getDoctorNotifications,
} from '../controllers/chat';
import { validateDr } from '../middlewares/validationMiddleware';

const router = express.Router();

const currentVersion = 'v1.0';

// Temporarily removing validateDr middleware for testing
// Generate token for Stream Chat
router.post(`/${currentVersion}/token`, generateToken);

// Refresh token for Stream Chat
router.post(`/${currentVersion}/refresh-token`, refreshToken);

// Create a new channel
router.post(`/${currentVersion}/channel`, createChannel);

// Get all channels for a user
router.get(`/${currentVersion}/channels/:userId`, getUserChannels);

// Add a member to a channel
router.post(`/${currentVersion}/channel/:channelId/members`, addChannelMember);

// Remove a member from a channel
router.delete(`/${currentVersion}/channel/:channelId/members/:userId`, removeChannelMember);

// Send a message to a channel
router.post(`/${currentVersion}/channel/:channelId/message`, sendChannelMessage);

// Send a custom event to a channel
router.post(`/${currentVersion}/channel/:channelId/event`, sendChannelEvent);

router.post(`/${currentVersion}/patient/chat-treatment-plan`, validateDr, postChatTreatmentPlan);

// Get treatment plans for a patient
router.get(`/${currentVersion}/patient/:patientId/treatment-plans`, validateDr, getPatientTreatmentPlans);

// Get the latest treatment plan for a patient
router.get(`/${currentVersion}/patient/:patientId/treatment-plan-latest`, validateDr, getLatestPatientTreatmentPlan);

// Get the latest treatment plan for a patient, including doctor info
router.get(`/${currentVersion}/patient/:patientId/latest-treatment-plan-with-doctor`, getLatestTreatmentPlanWithDoctor);

router.post(`/${currentVersion}/patients/latest-treatment-plans`, getLatestTreatmentPlansForPatients);

// Check message visibility for chat interface (no auth required)
router.post(`/${currentVersion}/check-message-visibility`, checkMessageVisibility);

// Check conversation visibility for chat interface (no auth required)
router.post(`/${currentVersion}/check-conversation-visibility`, checkConversationVisibility);

// GetStream webhook for message moderation (no auth required for webhooks)
router.post(`/${currentVersion}/webhook`, handleStreamWebhook);

// Chat notification endpoints
// Get chat notification state for a specific patient
router.get(`/${currentVersion}/notifications/:email`, getChatNotification);

// Get all chat notifications (with optional active filter)
router.get(`/${currentVersion}/notifications`, getAllChatNotifications);

// Mark chat notification as read for a specific patient
router.post(`/${currentVersion}/notifications/:email/mark-read`, markChatNotificationRead);

// Set chat notification when doctor sends message
router.post(`/${currentVersion}/notifications/set-message`, setChatNotificationForMessage);

// Doctor notification endpoints
// Set doctor notification when patient sends message
router.post(`/${currentVersion}/notifications/doctor/set-message`, setDoctorNotificationForMessage);

// Clear doctor notification when doctor reads patient messages
router.post(`/${currentVersion}/notifications/doctor/:email/clear`, clearDoctorNotificationForPatient);

// Get doctor notifications for a specific doctor
router.get(`/${currentVersion}/notifications/doctor/:doctorId`, getDoctorNotifications);

export default router;