# Sales Reporting Deployment Checklist

This checklist ensures a smooth deployment of the Stripe Sales Reporting system.

## Pre-Deployment Checklist

### 1. Code Review ✅
- [ ] All code has been reviewed and approved
- [ ] Tests are passing (unit, integration, E2E)
- [ ] No security vulnerabilities identified
- [ ] Performance impact assessed
- [ ] Documentation is complete and accurate

### 2. Environment Preparation
- [ ] Database migration file is ready: `migrations/create_stripe_payments_table.sql`
- [ ] Environment variables are documented
- [ ] Slack channel is created and configured
- [ ] Slack bot permissions are verified
- [ ] Cron job commands are prepared

### 3. Testing Verification
- [ ] Unit tests pass: `npm test -- --testPathPattern=sales`
- [ ] Integration tests pass
- [ ] Webhook integration tests pass
- [ ] Manual testing completed in staging environment
- [ ] Test reports successfully sent to Slack

## Deployment Steps

### Step 1: Database Migration

```bash
# Connect to your database
psql -h your-db-host -U your-db-user -d your-db-name

# Run the migration
\i migrations/create_stripe_payments_table.sql

# Verify table creation
\d stripe_payments

# Check indexes
\di stripe_payments*
```

**Verification:**
```sql
-- Should return table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'stripe_payments';

-- Should return 4 indexes
SELECT indexname FROM pg_indexes WHERE tablename = 'stripe_payments';
```

### Step 2: Environment Variables

Add to your environment configuration:

```bash
# Production environment
SALES_REPORTING_ENABLED=true
SLACK_SALES_REPORT_CHANNEL=C_YOUR_PROD_CHANNEL_ID
SALES_DATA_RETENTION_DAYS=365

# Verify existing variables are present
SLACK_TOKEN=xoxb-your-production-slack-token
STRIPE_API_KEY=sk_live_your_production_stripe_key
```

**Verification:**
```bash
# Check environment variables are loaded
curl https://your-domain.com/api/sales/v1/status
```

### Step 3: Code Deployment

```bash
# Deploy your application code
# (Follow your existing deployment process)

# Verify API endpoints are available
curl https://your-domain.com/api/sales/v1/status

# Expected response should include:
# - service: "Sales Reporting"
# - enabled: true
# - configuration with "Configured" status
```

### Step 4: Slack Integration Testing

```bash
# Send test report
curl -X POST https://your-domain.com/api/sales/v1/test-report

# Check Slack channel for test report
# Should see: "🧪 TEST REPORT - This is a test of the sales reporting system"
```

**Verification Checklist:**
- [ ] Test report appears in correct Slack channel
- [ ] Report formatting looks professional
- [ ] Bot permissions are working correctly
- [ ] No error messages in application logs

### Step 5: Webhook Integration Verification

```bash
# Check recent webhook processing logs
grep "Payment data stored for sales reporting" /var/log/your-app.log

# Verify webhook endpoint is processing correctly
# (Process a test payment through your system)

# Check database for stored payment data
psql -c "SELECT COUNT(*) FROM stripe_payments WHERE created_at >= CURRENT_DATE;"
```

### Step 6: Cron Job Setup

```bash
# SSH to your production server
ssh user@your-production-server

# Edit crontab
crontab -e

# Add cron jobs (adjust domain as needed):
0 9 * * * curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report >> /var/log/sales-reports.log 2>&1
0 9 * * 1 curl -X POST https://your-domain.com/api/sales/v1/trigger-weekly-report >> /var/log/sales-reports.log 2>&1
0 9 1 * * curl -X POST https://your-domain.com/api/sales/v1/trigger-monthly-report >> /var/log/sales-reports.log 2>&1

# Verify cron jobs are scheduled
crontab -l

# Create log file if it doesn't exist
sudo touch /var/log/sales-reports.log
sudo chmod 644 /var/log/sales-reports.log
```

**Verification:**
```bash
# Check cron service is running
sudo systemctl status cron

# Test manual execution
curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report

# Check log file
tail -f /var/log/sales-reports.log
```

## Post-Deployment Verification

### 1. System Health Check

```bash
# Overall system health
curl https://your-domain.com/health

# Sales reporting status
curl https://your-domain.com/api/sales/v1/status

# Expected status response:
{
  "service": "Sales Reporting",
  "enabled": true,
  "environment": "production",
  "configuration": {
    "slackChannel": "Configured",
    "slackToken": "Configured",
    "stripeApiKey": "Configured"
  }
}
```

### 2. Database Verification

```sql
-- Check table exists and has correct structure
\d stripe_payments

-- Check indexes exist
SELECT indexname FROM pg_indexes WHERE tablename = 'stripe_payments';

-- Should return 4 indexes:
-- - stripe_payments_pkey
-- - idx_stripe_payments_created_at
-- - idx_stripe_payments_status  
-- - idx_stripe_payments_email
-- - idx_stripe_payments_date_status
```

### 3. Webhook Integration Test

```bash
# Process a test payment (if possible)
# OR check recent webhook logs

# Verify payment data is being stored
psql -c "SELECT id, stripe_payment_intent_id, status, created_at FROM stripe_payments ORDER BY created_at DESC LIMIT 5;"
```

### 4. Report Generation Test

```bash
# Send test daily report
curl -X POST https://your-domain.com/api/sales/v1/test-report

# Check Slack channel for report
# Verify report formatting and data accuracy
```

### 5. Cron Job Verification

```bash
# Check cron jobs are scheduled
crontab -l

# Check cron service logs
sudo tail -f /var/log/cron

# Wait for next scheduled run or test manually
curl -X POST https://your-domain.com/api/sales/v1/trigger-daily-report

# Check sales report log
tail -f /var/log/sales-reports.log
```

## Monitoring Setup

### 1. Log Monitoring

```bash
# Set up log rotation
sudo nano /etc/logrotate.d/sales-reports

# Add content:
/var/log/sales-reports.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
```

### 2. Health Check Monitoring

Add to your monitoring system:
```bash
# Health check endpoint
GET https://your-domain.com/api/sales/v1/status

# Should return 200 status
# Should include "enabled": true
```

### 3. Alert Setup

Configure alerts for:
- Failed cron job executions
- Webhook processing errors
- Slack API failures
- Database connection issues

## Rollback Plan

If issues occur after deployment:

### 1. Disable Sales Reporting
```bash
# Set environment variable
SALES_REPORTING_ENABLED=false

# Restart application
# (Follow your restart process)
```

### 2. Remove Cron Jobs
```bash
# Edit crontab
crontab -e

# Comment out or remove sales reporting cron jobs
# Save and exit
```

### 3. Database Rollback (if needed)
```sql
-- Only if database issues occur
-- Remove the table (this will lose data)
DROP TABLE IF EXISTS stripe_payments;
```

### 4. Code Rollback
```bash
# Revert to previous application version
# (Follow your rollback process)
```

## Success Criteria

Deployment is successful when:

- [ ] All API endpoints return expected responses
- [ ] Database table is created with proper indexes
- [ ] Test reports are successfully sent to Slack
- [ ] Webhook integration stores payment data correctly
- [ ] Cron jobs are scheduled and executable
- [ ] System status endpoint shows all components as "Configured"
- [ ] No errors in application logs
- [ ] Performance impact is within acceptable limits

## Troubleshooting Common Issues

### Issue: Cron jobs not executing
**Solution:**
```bash
# Check cron service
sudo systemctl status cron
sudo systemctl restart cron

# Check cron logs
sudo tail -f /var/log/cron

# Verify network connectivity
curl -I https://your-domain.com/health
```

### Issue: Slack reports not appearing
**Solution:**
```bash
# Check Slack configuration
curl https://your-domain.com/api/sales/v1/status

# Test Slack integration
curl -X POST https://your-domain.com/api/sales/v1/test-report

# Check application logs
grep -i slack /var/log/your-app.log
```

### Issue: Database connection errors
**Solution:**
```bash
# Check database connectivity
psql -h your-db-host -U your-db-user -d your-db-name -c "SELECT 1;"

# Check table exists
psql -c "\d stripe_payments"

# Check application database configuration
```

### Issue: Webhook not storing data
**Solution:**
```bash
# Check webhook logs
grep "Payment data stored" /var/log/your-app.log

# Verify feature flag
echo $SALES_REPORTING_ENABLED

# Check recent webhook calls
grep "setZohoInvoicePaymentStatus" /var/log/your-app.log
```

## Post-Deployment Tasks

### Week 1
- [ ] Monitor daily report generation
- [ ] Check Slack channel for reports
- [ ] Review application logs for errors
- [ ] Verify webhook data collection

### Week 2  
- [ ] Verify weekly report generation
- [ ] Check report accuracy against known data
- [ ] Monitor system performance impact
- [ ] Review cron job execution logs

### Month 1
- [ ] Verify monthly report generation
- [ ] Analyze report trends and accuracy
- [ ] Review data retention and cleanup
- [ ] Optimize performance if needed

## Documentation Updates

After successful deployment:
- [ ] Update README with production URLs
- [ ] Document any environment-specific configurations
- [ ] Update monitoring and alerting documentation
- [ ] Share access information with relevant team members