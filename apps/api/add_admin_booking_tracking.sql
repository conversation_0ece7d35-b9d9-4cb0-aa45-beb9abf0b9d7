-- Migration: Add admin booking tracking to PatientSlot table
-- This allows tracking which admin booked a patient

-- Add columns to track admin bookings
ALTER TABLE PatientSlot 
ADD COLUMN IF NOT EXISTS "bookedByAdminId" TEXT,
ADD COLUMN IF NOT EXISTS "bookingType" VARCHAR(20) DEFAULT 'patient';

-- Add foreign key constraint to link to Dr table
ALTER TABLE PatientSlot 
ADD CONSTRAINT fk_booked_by_admin
    FOREIGN KEY ("bookedByAdminId") 
    REFERENCES Dr("accessID")
    ON DELETE SET NULL;

-- Add constraint to ensure bookingType has valid values
ALTER TABLE PatientSlot 
ADD CONSTRAINT chk_booking_type 
CHECK ("bookingType" IN ('patient', 'admin'));

-- Add index for faster lookups by admin
CREATE INDEX IF NOT EXISTS idx_patient_slot_admin_booking 
ON PatientSlot("bookedByAdminId") 
WHERE "bookedByAdminId" IS NOT NULL;

-- Add index for booking type
CREATE INDEX IF NOT EXISTS idx_patient_slot_booking_type 
ON PatientSlot("bookingType");

-- Add comments for documentation
COMMENT ON COLUMN PatientSlot."bookedByAdminId" IS 'ID of the admin who booked this appointment (NULL for patient bookings)';
COMMENT ON COLUMN PatientSlot."bookingType" IS 'Type of booking: patient (self-booked) or admin (booked by admin)';
