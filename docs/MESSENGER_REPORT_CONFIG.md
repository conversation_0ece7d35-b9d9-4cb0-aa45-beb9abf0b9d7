# Messenger Report Configuration

## Environment Variables

### SLACK_MESSENGER_REPORT_CHANNEL

**Description**: Slack channel ID where messenger reports will be sent

**Required**: No (has fallback)

**Default**: `C0904E5SW4W`

**Example**: 
```bash
SLACK_MESSENGER_REPORT_CHANNEL=C08VC12V80N
```

## How to Find Slack Channel ID

1. Open Slack in your browser
2. Navigate to the desired channel
3. Look at the URL: `https://app.slack.com/client/T.../C08VC12V80N`
4. The channel ID is the part after the last `/` (e.g., `C08VC12V80N`)

## Usage

The messenger report will be sent to the configured channel when generated via:

```bash
curl -X POST http://localhost:5000/api/report/v1.0/messenger-image
```

## Configuration in Different Environments

### Development (.env.local)
```bash
SLACK_MESSENGER_REPORT_CHANNEL=C08VC12V80N
```

### Production
Set the environment variable in your deployment configuration:
```bash
export SLACK_MESSENGER_REPORT_CHANNEL=C0904E5SW4W
```

## Related Configuration

Other Slack channel configurations available:
- `SLACK_REPORT_CHANNEL` - General reports
- `SLACK_EVENT_REPORTING_CHANNEL` - Event reports  
- `SLACK_AVAILABILITY_REPORT_CHANNEL` - Availability reports
- `SLACK_SALES_REPORT_CHANNEL` - Sales reports
- `SLACK_WEEKLY_REPORT_CHANNEL` - Weekly reports
