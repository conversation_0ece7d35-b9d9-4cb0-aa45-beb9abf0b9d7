import { Request<PERSON>and<PERSON> } from 'express-serve-static-core';
import { catchAll } from '../../utils/catchAll';
import availabilityReportingService from '../../services/availabilityReporting.service';
import { logger } from '../../config/logger';
import httpStatus from 'http-status';

/**
 * Trigger the weekly availability report (designed to be called by cron job)
 */
export const triggerAvailabilityReport: RequestHandler = catchAll(async (_req, res) => {
  try {
   

    await availabilityReportingService.sendWeeklyAvailabilityReport();

    res.status(httpStatus.OK).json({
      success: true,
      message: 'Availability report sent successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to trigger availability report:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to send availability report',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * Get availability data for a specific date range
 */
export const getAvailabilityData: RequestHandler = catchAll(async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      res.status(httpStatus.BAD_REQUEST).json({
        success: false,
        message: 'startDate and endDate query parameters are required (format: YYYY-MM-DD)'
      });
      return;
    }

    const availabilityData = await availabilityReportingService.getAvailabilityForDateRange(
      startDate as string,
      endDate as string
    );

    res.status(httpStatus.OK).json({
      success: true,
      data: {
        startDate,
        endDate,
        availability: availabilityData
      }
    });
  } catch (error) {
    logger.error('Failed to get availability data:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve availability data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get availability reporting system status
 */
export const getSystemStatus: RequestHandler = catchAll(async (_req, res) => {
  try {
    const status = {
      service: 'Availability Reporting',
      schedulingMethod: 'Server-level cron job',
      cronExpression: '0 9 * * 1', // Every Monday at 9:00 AM
      timezone: 'Australia/Sydney',
      endpoint: '/api/availability/trigger-report',
      lastChecked: new Date().toISOString(),
      instructions: {
        setup: 'Add to server crontab: 0 9 * * 1 curl -X POST http://localhost:5000/api/availability/trigger-report',
        manual: 'POST /api/availability/trigger-report',
        test: 'POST /api/availability/test-report'
      }
    };

    res.status(httpStatus.OK).json({
      success: true,
      data: status
    });
  } catch (error) {
    logger.error('Failed to get system status:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to retrieve system status',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Send a test availability report
 */
export const sendTestReport: RequestHandler = catchAll(async (_req, res) => {
  try {
    logger.info('Test availability report triggered via API');

    await availabilityReportingService.sendTestReport();

    res.status(httpStatus.OK).json({
      success: true,
      message: 'Test availability report sent successfully'
    });
  } catch (error) {
    logger.error('Failed to send test availability report:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to send test availability report',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Debug report data (temporary endpoint)
 */
export const debugReportData: RequestHandler = catchAll(async (_req, res) => {
  try {
    // Get the same data that the report uses but return it as JSON
    const reportData = await availabilityReportingService.generateFourWeekReport();

    res.status(httpStatus.OK).json({
      success: true,
      data: reportData
    });
  } catch (error) {
    logger.error('Failed to get debug report data:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to get debug report data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Send test report with specific start date
 */
export const sendTestReportWithDate: RequestHandler = catchAll(async (req, res) => {
  try {
    const { startDate } = req.query;

    if (!startDate) {
      res.status(httpStatus.BAD_REQUEST).json({
        success: false,
        message: 'startDate query parameter is required (format: YYYY-MM-DD)'
      });
      return;
    }

    logger.info(`Test availability report with custom date triggered: ${startDate}`);

    await availabilityReportingService.sendTestReportWithDates(startDate as string);

    res.status(httpStatus.OK).json({
      success: true,
      message: `Test availability report sent successfully starting from ${startDate}`
    });
  } catch (error) {
    logger.error('Failed to send test availability report with custom date:', error);
    res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Failed to send test availability report with custom date',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
