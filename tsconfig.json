{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "allowJs": true, "importHelpers": true, "jsx": "react", "strict": true, "alwaysStrict": true, "sourceMap": false, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitAny": false, "noImplicitThis": false, "strictNullChecks": true, "outDir": "./build", "plugins": [{"transform": "typescript-transform-paths"}, {"transform": "typescript-transform-paths", "afterDeclarations": true}]}, "include": ["src/**/*", "__tests__/**/*", "__tests__/mock"], "exclude": ["node_modules", "build"]}