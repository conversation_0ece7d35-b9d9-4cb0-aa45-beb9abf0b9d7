import { catchAll } from '../../utils/catchAll';
import { RequestHandler } from 'express';
import { addMissingDays, createTimeSlots, DaySchedule } from '../../helpers/range';
import { db } from '../../utils/db';
import { UrlEncryptionHelper } from '../../helpers/encryption';
import httpStatus from 'http-status';
import { ApiError } from '../../utils/ApiError';

// TODO @calendar: Add createdAt, updatedAt for all tables

export const postAvailabilities: RequestHandler = catchAll(async (req, res) => {
  // Must also update consultation date.
  // if the consultation is completed, create a new one.
  // if user already exist with a past date and uncompleted consultation, create new one
  // if user already exist with a valid date and uncompleted consultation, update

  const rangeData = req.body as DaySchedule[];
  const client = await db.connect();

  try {
    await client.query('BEGIN'); // Start transaction

    for (const range of rangeData) {
      // Insert Range data

      const query = `
            WITH selected_row AS (
              SELECT * FROM range
              WHERE id = $1
            )
            DELETE FROM range
            WHERE id = $1
            RETURNING *;
          `;

      await client.query(query, [range.range_id]);

      const rangeInsertQuery = `
          INSERT INTO Range (id, day, date, start, "end", interval, availability, "noShowAvailability", status, "doctorID", "createdAt", "updatedAt")
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (id)
          DO UPDATE SET
            day = EXCLUDED.day,
            date = EXCLUDED.date,
            start = EXCLUDED.start,
            "end" = EXCLUDED.end,
            interval = EXCLUDED.interval,
            availability = EXCLUDED.availability,
            "noShowAvailability" = EXCLUDED."noShowAvailability",
            status = EXCLUDED.status,
            "doctorID" = EXCLUDED."doctorID",
            "updatedAt"= CURRENT_TIMESTAMP
          RETURNING id`;
      const rangeValues = [
        range.range_id,
        range.day,
        range.date,
        range.start,
        range.end,
        range.interval,
        range.availability,
        range.noShowAvailability || range.availability, // Use regular availability as default for no-show availability
        'active',
        range.doctorID || null,
      ];
      const rangeRes = await client.query(rangeInsertQuery, rangeValues);
      const insertedRangeId = rangeRes.rows[0].id;

      const slots = createTimeSlots(range.start, range.end, range);

      if (slots.length > 0) {
        // Prepare slot data for batch insert
        const slotValues = slots.map((slot) => [insertedRangeId, slot.slot, slot.remaining, slot.noShowRemaining]);

        // Flatten the slotValues array to a single array of values
        const flattenedSlotValues = slotValues.flat();

        // Create dynamic VALUES clause for bulk insert
        const values = slotValues.map((_, idx) => `($${idx * 4 + 1}, $${idx * 4 + 2}, $${idx * 4 + 3}, $${idx * 4 + 4}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`).join(', ');

        // Dynamic query for batch insert
        const slotInsertQuery = `
            INSERT INTO Slot (range_id, slot, remaining, "noShowRemaining", "createdAt", "updatedAt")
            VALUES ${values}
            ON CONFLICT (range_id, slot)
          DO UPDATE SET
            remaining = EXCLUDED.remaining,
            "noShowRemaining" = EXCLUDED."noShowRemaining",
            "updatedAt"= CURRENT_TIMESTAMP
          `;

        // Execute batch insert for slots
        await client.query(slotInsertQuery, flattenedSlotValues);
      }
    }

    await client.query('COMMIT'); // Commit transaction
    res.status(200).send({ message: 'Availabilities successfully created' });
  } catch (err) {
    await client.query('ROLLBACK');
    console.error(err);
    res.status(500).send({ error: 'Error inserting availabilities' });
  } finally {
    client.release(); // Release client
  }
});

export const deleteAvailability: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const id = req.params.id;

  try {
    const query = `SELECT * from patientslot WHERE range_id = $1`;

    const result = await client.query(query, [id]);
    if (result.rows.length > 0) {
      res.status(200).send(result.rows);
    } else {
      const query = `
            DELETE FROM Range
            WHERE id = $1
            RETURNING *;
          `;

      await client.query(query, [id]);
      res.status(200).send([]);
    }
  } catch (error) {
    console.error('Error fetching future ranges:', error);
    throw error;
  } finally {
    client.release();
  }
});

export const deleteAvailabilityForce: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const id = req.params.id;

  try {
    const query = `
            DELETE FROM Range
            WHERE id = $1
            RETURNING *;
          `;

    await client.query(query, [id]);
    res.status(200).send([]);
  } catch (error) {
    console.error('Error fetching future ranges:', error);
    throw error;
  } finally {
    client.release();
  }
});

export const getAvailabilitiesForPatient: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const doctorID = req.query.doctorID as string;

  try {
    // If doctorID is provided, return doctor-specific availabilities (for admin use)
    if (doctorID) {
      const query = `
          WITH SlotData AS (
            SELECT
              s.range_id,
              jsonb_agg(
                jsonb_build_object(
                  'slot', s.slot,
                  'remaining', s.remaining,
                  'noShowRemaining', s."noShowRemaining",
                  'id', s.id,
                  'range_id', s.range_id
                )
              ) AS slots
            FROM Slot s
            GROUP BY s.range_id
          )
          SELECT
              r.id AS range_id,
              r.day,
              r.date,
              r.start,
              r."end",
              r.interval,
              r.availability,
              r."noShowAvailability",
              r.status,
              r."doctorID",
              d.username AS "doctorName",
              COALESCE(sd.slots, '[]'::jsonb) AS slots
          FROM Range r
          LEFT JOIN Dr d ON r."doctorID" = d.id
          LEFT JOIN SlotData sd ON r.id = sd.range_id
          WHERE r.date::DATE >= CURRENT_DATE
          AND r."doctorID" = $1; -- Fetch only future dates for specific doctor
        `;

      const result = await client.query(query, [doctorID]);
      const completeList = addMissingDays(result.rows);
      res.status(200).send(completeList);
      return;
    }

    // For patient-facing requests (no doctorID), return aggregated availabilities
    const aggregatedQuery = `
        WITH SlotData AS (
          SELECT
            s.range_id,
            s.slot,
            s.remaining,
            s."noShowRemaining",
            s.id,
            r.date,
            r.day,
            r.start,
            r."end",
            r.interval,
            r."doctorID"
          FROM Slot s
          JOIN Range r ON s.range_id = r.id
          WHERE r.date::DATE >= CURRENT_DATE
          AND r.status = 'active'
          AND r."doctorID" IS NOT NULL
        ),
        AggregatedSlots AS (
          SELECT
            date,
            day,
            slot,
            start,
            "end",
            interval,
            SUM(remaining) as total_remaining,
            SUM("noShowRemaining") as total_noshow_remaining,
            -- Collect all range_ids and slot_ids that contribute to this aggregated slot
            jsonb_agg(
              jsonb_build_object(
                'range_id', range_id,
                'slot_id', id,
                'doctorID', "doctorID",
                'remaining', remaining
              )
            ) as source_slots
          FROM SlotData
          GROUP BY date, day, slot, start, "end", interval
          HAVING SUM(remaining) > 0  -- Only include slots with availability
        ),
        DayRanges AS (
          SELECT
            date,
            day,
            MIN(start) as start,
            MAX("end") as "end",
            MIN(interval) as interval,
            -- Create a single range_id for the entire day
            CONCAT('aggregated_', date) as aggregated_range_id,
            jsonb_agg(
              jsonb_build_object(
                'slot', slot,
                'remaining', total_remaining,
                'noShowRemaining', total_noshow_remaining,
                'id', CONCAT('aggregated_', date, '_', REPLACE(slot, ' - ', '_'), '_slot'),
                'range_id', CONCAT('aggregated_', date),
                'source_slots', source_slots
              )
              ORDER BY slot
            ) as slots
          FROM AggregatedSlots
          GROUP BY date, day
        )
        SELECT
            aggregated_range_id as range_id,
            day,
            date,
            start,
            "end",
            interval,
            -- Calculate total availability as sum of all slots for this day
            (SELECT SUM((slot_data->>'remaining')::int)
             FROM jsonb_array_elements(slots) as slot_data) as availability,
            (SELECT SUM((slot_data->>'noShowRemaining')::int)
             FROM jsonb_array_elements(slots) as slot_data) as "noShowAvailability",
            'active' as status,
            NULL as "doctorID",  -- Hide doctor information for patients
            NULL as "doctorName", -- Hide doctor information for patients
            slots
        FROM DayRanges
        ORDER BY date, day;
      `;

    const result = await client.query(aggregatedQuery);
    const completeList = addMissingDays(result.rows);
    res.status(200).send(completeList);
    return;
  } catch (error) {
    console.error('Error fetching future ranges:', error);
    throw error;
  } finally {
    client.release();
  }
});

export const getAvailabilitiesForAdmin: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();
  try {
    const query = `
        SELECT
            r.id AS range_id,
            r.day,
            r.date,
            r.start,
            r."end",
            r.interval,
            r.availability,
            r."noShowAvailability",
            r.status,
            r."doctorID",
            d.name AS "doctorName"
        FROM Range r
        LEFT JOIN Dr d ON r."doctorID" = d.id
        WHERE r.date::DATE >= CURRENT_DATE;
      `;

    const result = await client.query(query);
    res.status(200).send(result.rows);
    return;
  } catch (error) {
    console.error('Error fetching future ranges:', error);
    throw error;
  } finally {
    client.release();
  }
});

export const postDecryptedUrl: RequestHandler = catchAll(async (req, res) => {
  const leadID = req.body.leadID;
  try {
    if (leadID) {
      const urlId = UrlEncryptionHelper.decryptLeadId(leadID);
      res.status(200).send({ leadID: urlId });
      return;
    }
    res.status(500).send({ leadID: undefined });
  } catch (error) {
    const e = error as Error;
    res.status(500).send({ leadID: undefined });
    throw new ApiError(httpStatus.BAD_REQUEST, e.message);
  }
});
