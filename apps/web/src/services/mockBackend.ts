import axios from "axios";
import AxiosMockAdapter from "axios-mock-adapter";
import routes from "./apiRoutes";
import mockData from "./mockData";

const { patients, patient, doctor } = mockData;
const moxios = axios.create();
const axiosMockAdapterInstance = new AxiosMockAdapter(moxios, {
  delayResponse: 1000,
});
axiosMockAdapterInstance
  .onGet(routes.GET_PATIENTS)
  .reply(200, patients)
  .onGet(new RegExp(`^${routes.GET_PATIENT}/[A-z0-9]+$`))
  .reply(200, patient)
  .onGet(new RegExp(`^${routes.GET_DOCTOR}/.*`))
  .reply(200, doctor);

export default moxios;
