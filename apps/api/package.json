{"name": "api", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"test": "jest", "dev": "nodemon --config restart.json", "start": "node index.ts", "prod": "node build/index.js", "checks": "tsc -p tsconfig.build.json", "build": "tsc -p tsconfig.build.json", "lint": "eslint ./src/ --fix", "format": "prettier 'src/**/*.{ts,json}' --write", "prepare": "husky install", "type-check": "tsc", "precommit": "pnpm type-check && lint-staged && pnpm test && pnpm checks && tsc -b", "docker:start:db": "docker compose -f 'docker-compose.db.yml' up --build --force-recreate -d", "docker:stop:db": "docker compose -f 'docker-compose.db.yml' down"}, "lint-staged": {"*.{js,ts}": ["eslint --fix"], "*.json,.{eslintrc,prettierrc}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --parser json --write"], "*.{md,yml}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --single-quote --write"]}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.850.0", "@aws-sdk/s3-request-presigner": "^3.850.0", "@slack/web-api": "^7.9.1", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-serve-static-core": "^5.0.0", "@types/form-data": "^2.5.2", "@types/luxon": "^3.4.2", "@types/multer": "^1.4.12", "@types/node": "^22.7.5", "@types/useragent": "^2.3.4", "@types/ws": "^8.5.13", "axios": "^1.7.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.21.1", "form-data": "^4.0.1", "http-status": "^1.7.4", "luxon": "^3.5.0", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "1.4.5-lts.1", "nodemailer": "^6.9.16", "openai": "^4.98.0", "papaparse": "^5.5.2", "pg": "^8.13.1", "puppeteer": "^24.14.0", "stream-chat": "^8.57.6", "stripe": "^17.5.0", "tslib": "^2.7.0", "twilio": "^5.5.1", "ua-parser-js": "^2.0.3", "useragent": "^2.3.0", "uuid": "^11.0.3", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.12.0", "@types/eslint__js": "^8.42.3", "@types/jest": "^29.5.13", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.11", "@types/puppeteer": "^7.0.4", "@typescript-eslint/eslint-plugin": "^8.9.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.8.3", "eslint-plugin-prettier": "^5.2.1", "globals": "^15.11.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "lint-staged": "^15.2.10", "nodemon": "^3.1.7", "prettier": "^3.3.3", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.1", "typescript": "^5.6.3", "typescript-eslint": "^8.9.0"}}