// utils/eventCache.ts
import { openDB } from 'idb';
import { UserEvent } from '../types';

export const getDB = () => openDB('event-tracker', 1, {
  upgrade(db) {
    db.createObjectStore('events', { keyPath: 'id', autoIncrement: true });
  }
});

export const addEvent = async (event: UserEvent) => {
  const db = await getDB();
  await db.add('events', event);
};

export const getAllEvents = async () => {
  const db = await getDB();
  return db.getAll('events');
};

export const clearEvents = async () => {
  const db = await getDB();
  await db.clear('events');
};