import { RequestHand<PERSON> } from 'express-serve-static-core';
import { TOPICS } from '../../helpers/topics';
import { WebSocketManager } from '../../helpers/webSocketManager';
import { ApiError } from '../../utils/ApiError';
import { db } from '../../utils/db';
import { AxiosError } from 'axios';
import httpStatus from 'http-status';
import { catchAll } from '../../utils/catchAll';

// Run this every Day at 3AM Australian Time

export const emptyQueue: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  const patientQueue = `DELETE FROM PatientQueue;`;
  const patientQueueDetails = `DELETE FROM PatientQueueDetails;`;
  const patientAdmission = `DELETE FROM Admission;`;

  try {
    await client.query('BEGIN');
    await client.query(patientQueue);
    await client.query(patientQueueDetails);
    await client.query(patientAdmission);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.EMPTY_QUEUE, []);
    res.status(200).send([]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});
